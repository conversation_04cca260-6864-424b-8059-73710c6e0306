# 后端请求地址
VITE_API_BASE=http://**************:6001/api
# 服务前缀
VITE_API_BASE_PUB=/langwell-pub-server
VITE_API_BASE_SYS=/langwell-sys-server
VITE_API_BASE_NOTE=/langwell-notes-server
VITE_API_BASE_AI=/langwell-ai-server
VITE_API_BASE_DOC=/langwell-doc-server
# API凭据key
VITE_API_HEADER_KEY=SA-TOKEN

# 授权方式
VITE_AUTHORIZE_MODE=account
# AI后端请求地址
VITE_AI_API_BASE=http://192.168.213.176/v1
VITE_AI_CHAT_SECRET=app-8WsQjtYZYzOH9bRSFK8MCmaw
VITE_AI_WRITER_SECRET=app-qyv0Ild0MwU9IqRadtMUSbrU
VITE_AI_REPLY_SECRET=app-WCdZA48CUCNMnAcioFEv6Ivs

# 租户用户信息
VITE_USERINFO_BASS=http://**************:18760/api
# 官网页面地址
# VITE_OFFICIAL_URL=http://**************:6008
VITE_OFFICIAL_URL=https://copilot.sino-bridge.com:85


# 授权相关配置
VITE_CORP_ID=wwd7e14e07bf973c80
VITE_AGENT_ID=1000085
VITE_AUTHORIZE_DOMAIN=https://scrm.sino-bridge.com:8098

# 文件前缀
VITE_FILE_PREFIX=https://copilot.sino-bridge.com:90
# 是否开启实验功能
VITE_ENABLE_LAB_FEATURES=true
# 工具地址
VITE_TOOLBOX_URL=http://**************:6001/toolbox

# mqtt配置
VITE_MQTT_PROTOCOL=ws
VITE_MQTT_HOST=**************
VITE_MQTT_PORT=8085
VITE_MQTT_USERNAME=your-mqtt-username
VITE_MQTT_PASSWORD=your-mqtt-password
VITE_MQTT_PATH=/mqtt
