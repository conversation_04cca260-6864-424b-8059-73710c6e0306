// defaultPrompts.ts
import { getConfig } from "@/api/common";
interface Prompt {
  id: string;
  content: string;
  agentName: string;
  appKey: string;
  agentDesc: string;
  title: string;
  default: boolean;
  isIns: boolean;
  agentId?: string;
}

async function fetchAppKey() {
  const res = await getConfig({});
  if (res.code === 200) {
    return res.data["ai-assistant"];
  } else {
    return {
      "universal-Assistidt-appkey": "",
      "universal-Assistant-id": "",
    };
  }
}

// 导出函数而不是变量
export async function getDefaultPrompts(): Promise<Prompt[]> {
  const data = await fetchAppKey();
  const appKey = data["universal-Assistidt-appkey"];
  const agentId = data["universal-Assistant-id"];
  return [
    {
      id: "100180",
      content: "请将下列内容翻译成${lang}：\n${content}",
      agentName: "通用助手",
      appKey,
      agentId,
      agentDesc: "",
      title: "翻译",
      default: true,
      isIns: true,
    },
    {
      id: "100175",
      content: "请对以下内容进行总结，不要超过200字：\n${content}",
      agentName: "通用助手",
      appKey,
      agentId,
      agentDesc: "",
      title: "总结",
      default: true,
      isIns: true,
    },
    {
      id: "100179",
      content: "解释以下内容，不要超过200字：\n${content}",
      agentName: "通用助手",
      appKey,
      agentId,
      agentDesc: "",
      title: "解释",
      default: true,
      isIns: true,
    },
    {
      id: "100177",
      content: "系统性地回答这个问题，给出你的思维链路，不要超过200字：\n${content}",
      agentName: "通用助手",
      appKey,
      agentId,
      agentDesc: "",
      title: "回答问题",
      default: true,
      isIns: true,
    },
  ];
}
