/** 写作助手配置选项 */

// 可选择的格式
export const formatList: Array<string> = [
  "邮件",
  "周报",
  "测试报告",
  "行研报告",
  "大纲",
  "报价方案",
  "工作量评估",
  "代码评审",
];

// 可选择的语气
export const toneList: Array<string> = ["详细的", "笼统的", "专业的", "全面的"];

// 可选择的长度
export const lengthList: Array<string> = ["不超过200字", "200字到400字之间", "长度不限"];

export type LangType = {
  key: string;
  label: string;
  en: string;
};

// 可选择的语言列表
export const langList: Array<LangType> = [
  {
    key: "0",
    label: "中文(简体)",
    en: "chiness(Simplified)",
  },
  {
    key: "1",
    label: "中文(繁體)",
    en: "chiness(traditional)",
  },
  {
    key: "2",
    label: "英语",
    en: "English",
  },
  {
    key: "3",
    label: "西班牙语",
    en: "Spanish",
  },
  {
    key: "4",
    label: "法语",
    en: "French",
  },
  {
    key: "5",
    label: "日语",
    en: "Japanese",
  },
];

// 翻译的键值
export const TIPLANGKEY = "2";
// 默认的语言
export const LANGDEFAULTKEY = "0";
// 选中文字，悬浮框默认提示词：翻译、解释
export const SELECTEDDEFAULTKEY = ["1", "2"];

export type TipType = {
  key: string;
  label: string;
  tip: string;
  default: boolean;
};
