/** 侧边栏菜单配置数据及组件主文件 */
import { IMenuInfo } from "@/types/global";
import { JSX } from "react";
import "./index.less";

type MenuIconType = {
  selected: JSX.Element;
  unselected: JSX.Element;
};

/** 聊天Icon */
const ChatSVGIcons: MenuIconType = {
  selected: (
    <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g id="新增" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="素材切图" transform="translate(-1627.000000, -194.000000)">
          <g id="AI对话icon_未选中备份" transform="translate(1627.000000, 194.000000)">
            <path d="M0,0 L32,0 L32,32 L0,32 L0,0 Z" fill="#D8D8D8" opacity="0"></path>
            <g id="group-bubble-chat" transform="translate(4.000000, 4.000000)">
              <path d="M0,0 L24,0 L24,24 L0,24 L0,0 Z" id="路径"></path>
              <g
                id="icon"
                className="stroke-theme"
                transform="translate(2.750000, 3.027100)"
                strokeDasharray="0,0"
                strokeLinejoin="round"
                strokeWidth="1.2"
              >
                <path
                  d="M11.7549249,12.9065495 L15.3013221,15.7603559 C15.6284715,16.0236149 16.1147853,15.7907362 16.1147853,15.3708172 L16.1147853,12.3668814 C16.1147853,12.1041727 16.3277522,11.8912048 16.5904609,11.8912048 C17.6412946,11.8912048 18.4931642,11.0393353 18.4931642,9.98850155 L18.4931642,2 C18.4931642,0.895430803 17.5977336,0 16.4931642,0 L4.49316416,0 C3.38859413,0 2.49316416,0.895430565 2.49316416,2 L2.49316416,7.02264214"
                  id="路径"
                  fillRule="nonzero"
                ></path>
                <path
                  className="fill-theme"
                  d="M0,8.47253462 C0,7.64410718 0.671572804,6.97253462 1.5,6.97253462 L10.1950102,6.97253462 C11.0234375,6.97253462 11.6950102,7.64410772 11.6950102,8.47253462 L11.6950083,14.1642723 C11.6950083,14.9926991 11.0234356,15.6642708 10.1950083,15.6642708 L6.60013247,15.6642708 C6.4945879,15.6642708 6.39174986,15.6976704 6.30634594,15.759685 L3.12247372,18.0716281 C2.79194856,18.3116355 2.32868791,18.0755152 2.32868791,17.6670413 L2.32868791,16.1642708 C2.32868791,15.8881287 2.10483027,15.6642708 1.82868791,15.6642708 L1.5,15.6642708 C0.67157191,15.6642708 0,14.9926982 0,14.1642708 L0,8.47253462 Z"
                  id="路径"
                ></path>
              </g>
            </g>
            <path
              d="M11.793013,16 L11.8138036,16.0204511 L13.4066187,20.9589579 L13.4066187,20.9794093 C13.4066187,20.9931364 13.3997356,21 13.3859696,21 L12.3308815,21 L12.3102325,20.9794093 L11.9584894,19.7295142 L10.4068311,19.7295142 L10.0550881,20.9794093 C10.0550881,21 10.055088,21 10.0344388,21 L9.02064916,21 L9,20.9794093 L9,20.9589579 L10.593098,16.0204511 C10.593098,16 10.5930981,16 10.6137472,16 L11.793013,16 Z M11.1724062,16.8401739 C11.0482283,17.3524305 10.9034014,17.9057294 10.7792235,18.397535 L10.6343963,18.8893403 L11.7309243,18.8893403 L11.5860969,18.397535 C11.5033587,18.090209 11.4206208,17.7622918 11.3378827,17.4549658 C11.2759351,17.2500352 11.2344952,17.0451045 11.1724062,16.8401739 Z M15,20.9589579 L15,16.0204511 C15,16 14.9793501,16 14.9585595,16 L13.9654194,16 L13.9239797,16.0204511 L13.9239797,20.9588177 C13.9239797,20.9792897 13.9446288,20.9792897 13.9654194,20.9792897 L14.9584188,20.9792897 C14.9792094,20.9794092 15,20.979409 15,20.9589579 Z"
              id="形状结合"
              fill="#FFFFFF"
            ></path>
          </g>
        </g>
      </g>
    </svg>
  ),
  unselected: (
    <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-1565.000000, -194.000000)">
          <g transform="translate(1565.000000, 194.000000)">
            <path d="M0,0 L32,0 L32,32 L0,32 L0,0 Z" fill="#D8D8D8" opacity="0"></path>
            <g transform="translate(4.000000, 4.000000)">
              <path d="M0,0 L24,0 L24,24 L0,24 L0,0 Z"></path>
              <g
                className="stroke-theme"
                transform="translate(2.750000, 3.027100)"
                strokeDasharray="0,0"
                strokeLinejoin="round"
                strokeWidth="1.2"
              >
                <path
                  d="M11.7549249,12.9065495 L15.3013221,15.7603559 C15.6284715,16.0236149 16.1147853,15.7907362 16.1147853,15.3708172 L16.1147853,12.3668814 C16.1147853,12.1041727 16.3277522,11.8912048 16.5904609,11.8912048 C17.6412946,11.8912048 18.4931642,11.0393353 18.4931642,9.98850155 L18.4931642,2 C18.4931642,0.895430803 17.5977336,0 16.4931642,0 L4.49316416,0 C3.38859413,0 2.49316416,0.895430565 2.49316416,2 L2.49316416,7.02264214"
                  fillRule="nonzero"
                ></path>
                <path d="M0,8.47253462 C0,7.64410718 0.671572804,6.97253462 1.5,6.97253462 L10.1950102,6.97253462 C11.0234375,6.97253462 11.6950102,7.64410772 11.6950102,8.47253462 L11.6950083,14.1642723 C11.6950083,14.9926991 11.0234356,15.6642708 10.1950083,15.6642708 L6.60013247,15.6642708 C6.4945879,15.6642708 6.39174986,15.6976704 6.30634594,15.759685 L3.12247372,18.0716281 C2.79194856,18.3116355 2.32868791,18.0755152 2.32868791,17.6670413 L2.32868791,16.1642708 C2.32868791,15.8881287 2.10483027,15.6642708 1.82868791,15.6642708 L1.5,15.6642708 C0.67157191,15.6642708 0,14.9926982 0,14.1642708 L0,8.47253462 Z"></path>
              </g>
            </g>
            <path
              className="fill-theme"
              d="M11.793013,16 L11.8138036,16.0204511 L13.4066187,20.9589579 L13.4066187,20.9794093 C13.4066187,20.9931364 13.3997356,21 13.3859696,21 L12.3308815,21 L12.3102325,20.9794093 L11.9584894,19.7295142 L10.4068311,19.7295142 L10.0550881,20.9794093 C10.0550881,21 10.055088,21 10.0344388,21 L9.02064916,21 L9,20.9794093 L9,20.9589579 L10.593098,16.0204511 C10.593098,16 10.5930981,16 10.6137472,16 L11.793013,16 Z M11.1724062,16.8401739 C11.0482283,17.3524305 10.9034014,17.9057294 10.7792235,18.397535 L10.6343963,18.8893403 L11.7309243,18.8893403 L11.5860969,18.397535 C11.5033587,18.090209 11.4206208,17.7622918 11.3378827,17.4549658 C11.2759351,17.2500352 11.2344952,17.0451045 11.1724062,16.8401739 Z M15,20.9589579 L15,16.0204511 C15,16 14.9793501,16 14.9585595,16 L13.9654194,16 L13.9239797,16.0204511 L13.9239797,20.9588177 C13.9239797,20.9792897 13.9446288,20.9792897 13.9654194,20.9792897 L14.9584188,20.9792897 C14.9792094,20.9794092 15,20.979409 15,20.9589579 Z"
            ></path>
          </g>
        </g>
      </g>
    </svg>
  ),
};
/** 便签Icon */
const NotesSVGIcons: MenuIconType = {
  selected: (
    <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-1627.000000, -242.000000)">
          <g transform="translate(1627.000000, 242.000000)">
            <path d="M0,0 L32,0 L32,32 L0,32 L0,0 Z" id="路径" fill="#D8D8D8" opacity="0"></path>
            <g id="book" transform="translate(5.000000, 5.000000)">
              <path d="M0,0 L22,0 L22,22 L0,22 L0,0 Z" id="路径"></path>
              <g transform="translate(2.750000, 1.750000)" strokeDasharray="0,0" strokeWidth="1.2">
                <path
                  className="fill-theme"
                  d="M8.25,3.14079559 C8.25,3.02479584 8.20580802,2.91231062 8.12542542,2.82867704 C5.95698247,0.572533986 3.49571051,-0.178768863 0.415866648,0.0347356925 C0.180127653,0.0510778677 0,0.249487344 0,0.485792111 L0,14.0514098 C0,14.310569 0.215097897,14.5175043 0.474249264,14.515491 C3.12462789,14.4949006 5.40685755,15.0777468 7.43664296,16.6979979 C7.75339951,16.9508449 8.25,16.7366294 8.25,16.3313316 L8.25,3.14079559 Z"
                  transform="translate(4.125000, 8.403459) scale(-1, 1) translate(-4.125000, -8.403459) "
                ></path>
                <path
                  className="stroke-theme"
                  d="M8.25,4.14079559 C8.25,4.02479584 8.29419198,3.91231062 8.37457458,3.82867704 C10.5430175,1.57253399 13.0042898,0.821231137 16.0841336,1.03473569 C16.3198726,1.05107787 16.5,1.24948734 16.5,1.48579211 L16.5,15.0514098 C16.5,15.310569 16.2849023,15.5175043 16.025751,15.515491 C13.3753723,15.4949006 11.0931424,16.0777468 9.06335704,17.6979979 C8.74660049,17.9508449 8.25,17.7366294 8.25,17.3313316 L8.25,4.14079559 Z"
                ></path>
                <path
                  d="M6,4.50000007 C5.05939136,3.63301504 3.24948871,3.27437408 2,3.25000007"
                  stroke="#FFFFFF"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  transform="translate(4.000000, 3.875000) scale(-1, 1) translate(-4.000000, -3.875000) "
                ></path>
                <path
                  className="stroke-theme"
                  d="M14.5,14.0000001 C13.5593914,13.133015 11.7494887,12.7743741 10.5,12.7500001"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  transform="translate(12.500000, 13.375000) scale(-1, 1) translate(-12.500000, -13.375000) "
                ></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
  unselected: (
    <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-1568.000000, -242.000000)">
          <g transform="translate(1568.000000, 242.000000)">
            <path d="M0,0 L32,0 L32,32 L0,32 L0,0 Z" fill="#D8D8D8" opacity="0"></path>
            <g transform="translate(5.000000, 5.000000)">
              <path d="M0,0 L22,0 L22,22 L0,22 L0,0 Z"></path>
              <g
                transform="translate(2.750000, 1.750000)"
                className="stroke-theme"
                strokeDasharray="0,0"
                strokeWidth="1.2"
              >
                <path
                  d="M8.25,3.14079559 C8.25,3.02479584 8.20580802,2.91231062 8.12542542,2.82867704 C5.95698247,0.572533986 3.49571051,-0.178768863 0.415866648,0.0347356925 C0.180127653,0.0510778677 0,0.249487344 0,0.485792111 L0,14.0514098 C0,14.310569 0.215097897,14.5175043 0.474249264,14.515491 C3.12462789,14.4949006 5.40685755,15.0777468 7.43664296,16.6979979 C7.75339951,16.9508449 8.25,16.7366294 8.25,16.3313316 L8.25,3.14079559 Z"
                  transform="translate(4.125000, 8.403459) scale(-1, 1) translate(-4.125000, -8.403459) "
                ></path>
                <path
                  d="M8.25,4.14079559 C8.25,4.02479584 8.29419198,3.91231062 8.37457458,3.82867704 C10.5430175,1.57253399 13.0042898,0.821231137 16.0841336,1.03473569 C16.3198726,1.05107787 16.5,1.24948734 16.5,1.48579211 L16.5,15.0514098 C16.5,15.310569 16.2849023,15.5175043 16.025751,15.515491 C13.3753723,15.4949006 11.0931424,16.0777468 9.06335704,17.6979979 C8.74660049,17.9508449 8.25,17.7366294 8.25,17.3313316 L8.25,4.14079559 Z"
                  id="路径"
                ></path>
                <path
                  d="M6,4.50000007 C5.05939136,3.63301504 3.24948871,3.27437408 2,3.25000007"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  transform="translate(4.000000, 3.875000) scale(-1, 1) translate(-4.000000, -3.875000) "
                ></path>
                <path
                  d="M14.5,14.0000001 C13.5593914,13.133015 11.7494887,12.7743741 10.5,12.7500001"
                  id="路径备份"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  transform="translate(12.500000, 13.375000) scale(-1, 1) translate(-12.500000, -13.375000) "
                ></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
};
/** 写作Icon */
const WriteSVGIcons: MenuIconType = {
  selected: (
    <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-1627.000000, -290.000000)">
          <g transform="translate(1627.000000, 290.000000)">
            <path d="M0,0 L32,0 L32,32 L0,32 L0,0 Z" fill="#D8D8D8" opacity="0"></path>
            <g transform="translate(4.000000, 4.000000)">
              <path d="M0,0 L24,0 L24,24 L0,24 L0,0 Z"></path>
              <path
                className="fill-theme"
                d="M3.2967208,18.7146687 C6.70452606,13.283788 7.13168565,11.1064234 12.523022,5.37115202 C14.4795806,3.28977668 16.9549297,1.83272601 19.9490693,1 C20.1611118,3.19508699 19.7110483,4.88219829 18.5988789,6.06133391 C14.1912762,10.7343282 10.2727046,12.0429104 7.79735553,13.8833954 C5.32200643,15.7238805 5.54519523,16.8416529 4.87194296,17.5643655 C4.19869068,18.2870782 -0.111084463,24.1455494 3.2967208,18.7146687 Z"
                strokeWidth="1.2"
              ></path>
              <path
                className="stroke-theme"
                d="M4.5,22.5 C7.25087646,21.1666667 8.91754312,20.5 9.5,20.5 C10.3736853,20.5 7.50596261,23.0438284 9.5,22.5 C11.4940374,21.9561716 10.6349161,21.9203586 14.25,21.5 C16.6600559,21.2197609 19.0767226,20.5530943 21.5,19.5"
                strokeWidth="1.2"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
  unselected: (
    <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-1565.000000, -290.000000)">
          <g transform="translate(1565.000000, 290.000000)">
            <path d="M0,0 L32,0 L32,32 L0,32 L0,0 Z" fill="#D8D8D8" opacity="0"></path>
            <g transform="translate(4.000000, 4.000000)">
              <path d="M0,0 L24,0 L24,24 L0,24 L0,0 Z"></path>
              <path
                className="stroke-theme"
                d="M3.2967208,18.7146687 C6.70452606,13.283788 7.13168565,11.1064234 12.523022,5.37115202 C14.4795806,3.28977668 16.9549297,1.83272601 19.9490693,1 C20.1611118,3.19508699 19.7110483,4.88219829 18.5988789,6.06133391 C14.1912762,10.7343282 10.2727046,12.0429104 7.79735553,13.8833954 C5.32200643,15.7238805 5.54519523,16.8416529 4.87194296,17.5643655 C4.19869068,18.2870782 -0.111084463,24.1455494 3.2967208,18.7146687 Z"
                strokeWidth="1.2"
              ></path>
              <path
                className="stroke-theme"
                d="M4.5,22.5 C7.25087646,21.1666667 8.91754312,20.5 9.5,20.5 C10.3736853,20.5 7.50596261,23.0438284 9.5,22.5 C11.4940374,21.9561716 10.6349161,21.9203586 14.25,21.5 C16.6600559,21.2197609 19.0767226,20.5530943 21.5,19.5"
                strokeWidth="1.2"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
};
/** 提问Icon */
const QuestionSVGIcons: MenuIconType = {
  selected: (
    <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-1627.000000, -386.000000)">
          <g transform="translate(1627.000000, 386.000000)">
            <path d="M0,0 L32,0 L32,32 L0,32 L0,0 Z" fill="#000000" opacity="0"></path>
            <g transform="translate(4.000000, 4.000000)">
              <path d="M0,0 L24,0 L24,24 L0,24 L0,0 Z"></path>
              <g transform="translate(2.750000, 3.000000)">
                <path
                  className="fill-theme"
                  d="M0,2 C0,0.895430565 0.895430565,0 2,0 L16.5,0 C17.6045716,0 18.5,0.895430565 18.5,2 L18.5,11.7492075 C18.5,12.8537768 17.6045693,13.7492075 16.5,13.7492075 L9.41238689,13.7492075 C9.30684209,13.7492075 9.20400466,13.7826064 9.11860085,13.8446217 L3.54378676,17.8927269 C3.2132616,18.132735 2.75,17.8966157 2.75,17.488142 L2.75,14.2492075 C2.75,13.9730651 2.52614236,13.7492075 2.25,13.7492075 L2,13.7492075 C0.895430565,13.7492075 0,12.8537769 0,11.7492075 L0,2 Z"
                  strokeWidth="1.2"
                  fillRule="nonzero"
                  strokeLinejoin="round"
                  strokeDasharray="0,0"
                ></path>
                <path
                  d="M9.88197401,10.9149688 C9.88197401,11.3310478 9.54467536,11.6683465 9.12859637,11.6683465 C8.71251737,11.6683465 8.37521872,11.3310478 8.37521872,10.9149688 C8.37521872,10.4988899 8.71251737,10.1615912 9.12859637,10.1615912 C9.54467536,10.1615912 9.88197401,10.4988899 9.88197401,10.9149688 Z M9.22744167,2 C10.8967546,2 12.25,3.39966584 12.25,5.12624122 C12.25,6.26376265 11.8212585,6.89786193 10.8216577,7.74124834 C10.8029287,7.75701678 10.788882,7.76884311 10.7772144,7.77866655 L10.717335,7.82908597 L10.6742621,7.8653577 C10.041006,8.39984372 9.85525637,8.66423199 9.85525637,9.11370196 L8.59962696,9.11370196 C8.59962696,8.15968015 8.9785295,7.6203662 9.8804014,6.85916087 C9.95506981,6.79628109 9.95506981,6.79628109 10.0279692,6.73490619 C10.7624533,6.11520483 10.9943706,5.77220432 10.9943706,5.12624122 C10.9943706,4.11691875 10.2032896,3.2987013 9.22744167,3.2987013 C8.36566471,3.2987013 7.63331684,3.94162212 7.48692684,4.80904332 L6.25,4.58572673 C6.5008764,3.09918046 7.75294732,2 9.22744167,2 Z"
                  fill="#FFFFFF"
                ></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
  unselected: (
    <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-1565.000000, -386.000000)">
          <g transform="translate(1565.000000, 386.000000)">
            <path d="M0,0 L32,0 L32,32 L0,32 L0,0 Z" fill="#000000" opacity="0"></path>
            <g transform="translate(4.000000, 4.000000)">
              <path d="M0,0 L24,0 L24,24 L0,24 L0,0 Z"></path>
              <g transform="translate(2.750000, 3.000000)">
                <path
                  className="stroke-theme"
                  d="M0,2 C0,0.895430565 0.895430565,0 2,0 L16.5,0 C17.6045716,0 18.5,0.895430565 18.5,2 L18.5,11.7492075 C18.5,12.8537768 17.6045693,13.7492075 16.5,13.7492075 L9.41238689,13.7492075 C9.30684209,13.7492075 9.20400466,13.7826064 9.11860085,13.8446217 L3.54378676,17.8927269 C3.2132616,18.132735 2.75,17.8966157 2.75,17.488142 L2.75,14.2492075 C2.75,13.9730651 2.52614236,13.7492075 2.25,13.7492075 L2,13.7492075 C0.895430565,13.7492075 0,12.8537769 0,11.7492075 L0,2 Z"
                  strokeWidth="1.2"
                  strokeLinejoin="round"
                  strokeDasharray="0,0"
                  fillRule="nonzero"
                ></path>
                <path
                  className="fill-theme"
                  d="M9.88197401,10.9149688 C9.88197401,11.3310478 9.54467536,11.6683465 9.12859637,11.6683465 C8.71251737,11.6683465 8.37521872,11.3310478 8.37521872,10.9149688 C8.37521872,10.4988899 8.71251737,10.1615912 9.12859637,10.1615912 C9.54467536,10.1615912 9.88197401,10.4988899 9.88197401,10.9149688 Z M9.22744167,2 C10.8967546,2 12.25,3.39966584 12.25,5.12624122 C12.25,6.26376265 11.8212585,6.89786193 10.8216577,7.74124834 C10.8029287,7.75701678 10.788882,7.76884311 10.7772144,7.77866655 L10.717335,7.82908597 L10.6742621,7.8653577 C10.041006,8.39984372 9.85525637,8.66423199 9.85525637,9.11370196 L8.59962696,9.11370196 C8.59962696,8.15968015 8.9785295,7.6203662 9.8804014,6.85916087 C9.95506981,6.79628109 9.95506981,6.79628109 10.0279692,6.73490619 C10.7624533,6.11520483 10.9943706,5.77220432 10.9943706,5.12624122 C10.9943706,4.11691875 10.2032896,3.2987013 9.22744167,3.2987013 C8.36566471,3.2987013 7.63331684,3.94162212 7.48692684,4.80904332 L6.25,4.58572673 C6.5008764,3.09918046 7.75294732,2 9.22744167,2 Z"
                ></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
};
/** 提示词Icon */
const PromptSVGIcons: MenuIconType = {
  selected: (
    <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-1627.000000, -434.000000)">
          <g transform="translate(1627.000000, 434.000000)">
            <path d="M0,0 L32,0 L32,32 L0,32 L0,0 Z" fill="#FFFFFF" opacity="0"></path>
            <g className="fill-theme" transform="translate(6.000000, 5.000000)">
              <path
                d="M7.60615073,8.33538926 L6.19910202,5.79544936 L3.62589632,4.40653127 L6.19910202,3.01765157 L7.60615073,0.477673287 L9.01323832,3.01763238 L11.5864051,4.40653127 L9.01323832,5.79544936 L7.60615073,8.33538926 Z M7.11643478,4.89188111 L7.60813385,5.77817712 L8.09983293,4.89188111 L8.99772326,4.40653127 L8.09985237,3.92118144 L7.6081533,3.03488543 L7.11645423,3.92118144 L6.21854445,4.40653127 L7.11643478,4.89188111 Z M11.6486209,1.01100156 L12.6922906,0.709812141 L12.3871416,1.73998588 L12.6922906,2.77015962 L11.6486209,2.46705107 L10.6049707,2.77015962 L10.9120444,1.73996669 L10.6049707,0.709812141 L11.6486209,1.01100156 Z M5.06992453,0.226343059 L5.85121861,0 L5.62185625,0.771186153 L5.85119917,1.5423915 L5.06990509,1.31601006 L4.28863046,1.5423915 L4.51795393,0.771186153 L4.2886499,0 L5.06992453,0.226343059 Z M17.6214521,11.4127802 L18.8657484,11.0542311 L18.5025145,12.2824791 L18.8657484,13.510727 L17.6214521,13.148362 L16.3771173,13.510727 L16.7442556,12.2824791 L16.3771173,11.0542311 L17.6214521,11.4127802 Z"
                fillRule="nonzero"
              ></path>
              <g transform="translate(0.000000, 2.257896)">
                <path d="M12.646,4.446 L12.7860482,4.60460947 L14.9636005,7.06110528 L15.306,7.447 L3.18428154,19.4120631 C2.74013403,19.8516533 2.01877971,19.8520991 1.57379175,19.4135078 L0.332296361,18.1875258 C-0.0813221118,17.77815 -0.108896677,17.1326507 0.250064813,16.6913054 L0.332901679,16.6000167 L12.646,4.446 Z M18.4262083,0.328596079 L19.6677036,1.55457812 C20.0813221,1.96395389 20.1088967,2.60945313 19.7499352,3.05079847 L19.6670983,3.14208722 L16.21,6.555 L15.9241607,6.2326971 L13.7466085,3.77620129 L13.549,3.554 L16.8157185,0.330040761 C17.259866,-0.109549402 17.9812203,-0.109995194 18.4262083,0.328596079 Z"></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
  unselected: (
    <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-1565.000000, -434.000000)">
          <g transform="translate(1565.000000, 434.000000)">
            <path d="M0,0 L32,0 L32,32 L0,32 L0,0 Z" fill="#FFFFFF" opacity="0"></path>
            <g transform="translate(6.000000, 5.000000)">
              <path
                className="fill-theme"
                d="M7.12357109,8.49957937 L5.72676167,5.90960785 L3.17228153,4.49333092 L5.72676167,3.07709313 L7.12357109,0.487082473 L8.52041911,3.07707356 L11.0748606,4.49333092 L8.52041911,5.90960785 L7.12357109,8.49957937 Z M6.63741887,4.98824116 L7.12553979,5.89199539 L7.6136607,4.98824116 L8.50501696,4.49333092 L7.61368,3.99842069 L7.12555909,3.09466646 L6.63743818,3.99842069 L5.74606261,4.49333092 L6.63741887,4.98824116 Z M11.1366237,1.03091622 L12.1726984,0.723793988 L11.8697701,1.77426004 L12.1726984,2.8247261 L11.1366237,2.51564692 L10.1005682,2.8247261 L10.4054074,1.77424047 L10.1005682,0.723793988 L11.1366237,1.03091622 Z M4.60580135,0.230801554 L5.38140985,0 L5.15371659,0.786376943 L5.38139055,1.57277345 L4.60578205,1.34193276 L3.83019286,1.57277345 L4.05784751,0.786376943 L3.83021216,0 L4.60580135,0.230801554 Z M17.0659898,11.6375886 L18.3012311,11.2719769 L17.9406406,12.5244188 L18.3012311,13.7768606 L17.0659898,13.4073578 L15.8307101,13.7768606 L16.1951767,12.5244188 L15.8307101,11.2719769 L17.0659898,11.6375886 Z"
                fillRule="nonzero"
              ></path>
              <g className="stroke-theme" transform="translate(0.000000, 2.735727)" strokeDasharray="0,0">
                <path
                  d="M17.4244252,0.363440338 L18.6513128,1.60739282 C18.8446616,1.80343097 18.8446616,2.11842901 18.6513128,2.31446716 L2.29248393,18.9008326 C2.09723074,19.0988016 1.77846119,19.1010032 1.58049221,18.90575 C1.57884178,18.9041222 1.57720261,18.902483 1.57557482,18.9008326 L0.348687244,17.6568801 C0.155338405,17.460842 0.155338405,17.1458439 0.348687244,16.9498058 L16.7075161,0.363440338 C16.9027693,0.165471353 17.2215388,0.163269764 17.4195078,0.358522953 C17.4211582,0.360150741 17.4227974,0.361789908 17.4244252,0.363440338 Z"
                  strokeWidth="1.2"
                ></path>
                <line x1="12.7425588" y1="3.8395928" x2="14.9042647" y2="6.34447655" strokeWidth="1.28333333"></line>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
};
/** 设置-图标 */
export const SettingIcon = (
  <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g transform="translate(-1565.000000, -626.000000)">
        <g transform="translate(1565.000000, 626.000000)">
          <path
            d="M6,0 L26,0 C29.3137085,-6.08718376e-16 32,2.6862915 32,6 L32,26 C32,29.3137085 29.3137085,32 26,32 L6,32 C2.6862915,32 2.02906125e-16,29.3137085 0,26 L0,6 C-4.05812251e-16,2.6862915 2.6862915,4.05812251e-16 6,0 Z"
            fill="#FFFFFF"
            opacity="0"
          ></path>
          <g transform="translate(4.000000, 4.000000)">
            <path d="M0,0 L24,0 L24,24 L0,24 L0,0 Z"></path>
            <g
              className="stroke-theme"
              transform="translate(2.990723, 2.750000)"
              strokeDasharray="0,0"
              strokeWidth="1.2"
            >
              <path
                d="M7.1463809,0 C6.89103937,0 6.67672205,0.19239682 6.64927769,0.446259201 L6.35069037,3.20819163 C5.90906763,3.40279174 5.49281693,3.64439154 5.10827446,3.92665482 L2.56569386,2.80366182 C2.33212018,2.70049834 2.05834103,2.78990436 1.93067038,3.01103663 L0.067029634,6.23895693 C-0.0606411458,6.46008921 -0.00117930628,6.74189186 0.204949664,6.89259052 L2.4485023,8.53283024 C2.42304468,8.76839256 2.40998292,9.0076704 2.40998292,9.25 C2.40998292,9.49232674 2.42304444,9.73160362 2.44850159,9.96716309 L0.204949664,11.6074018 C-0.00117930628,11.7581005 -0.0606411458,12.0399036 0.067029634,12.2610359 L1.93067038,15.4889555 C2.05834103,15.7100878 2.33212018,15.7994938 2.56569386,15.696331 L5.10826922,14.5733404 C5.49281311,14.8556051 5.90906572,15.0972061 6.35069037,15.2918072 L6.64927769,18.0537415 C6.67672205,18.3076038 6.89103937,18.5 7.1463809,18.5 L10.873662,18.5 C11.1290035,18.5 11.3433218,18.3076038 11.3707657,18.0537415 L11.6694412,15.2909937 C12.110507,15.0964775 12.526248,14.855073 12.9103479,14.5730972 L15.453476,15.696332 C15.6870499,15.7994947 15.9608288,15.7100887 16.0884991,15.4889565 L17.9521408,12.2610369 C18.0798111,12.0399046 18.0203495,11.7581015 17.8142204,11.6074028 L15.5698481,9.96656418 C15.5952625,9.73119736 15.6083021,9.4921217 15.6083021,9.25 C15.6083021,9.0078764 15.5952625,8.76879787 15.5698471,8.53342915 L17.8142204,6.89258957 C18.0203495,6.74189091 18.0798111,6.46008825 17.9521408,6.23895597 L16.0884991,3.01103568 C15.9608288,2.7899034 15.6870499,2.70049739 15.453476,2.80366087 L12.9103422,3.92689824 C12.5262432,3.64492369 12.1105051,3.40352106 11.6694412,3.20900536 L11.3707657,0.446259111 C11.3433218,0.19239673 11.1290035,0 10.873662,0 L7.1463809,0 Z"
                strokeLinejoin="round"
                fillRule="nonzero"
              ></path>
              <path d="M12.0087888,9.25 C12.0087888,10.9068543 10.6656431,12.25 9.00878884,12.25 C7.35193456,12.25 6.00878884,10.9068543 6.00878884,9.25 C6.00878884,7.59314573 7.35193456,6.25 9.00878884,6.25 C10.6656431,6.25 12.0087888,7.59314573 12.0087888,9.25 Z"></path>
            </g>
          </g>
        </g>
      </g>
    </g>
  </svg>
);
/** 设置Icon */
const SettingSVGIcons: MenuIconType = {
  selected: (
    <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-1565.000000, -626.000000)">
          <g transform="translate(1565.000000, 626.000000)">
            <path
              d="M6,0 L26,0 C29.3137085,-6.08718376e-16 32,2.6862915 32,6 L32,26 C32,29.3137085 29.3137085,32 26,32 L6,32 C2.6862915,32 2.02906125e-16,29.3137085 0,26 L0,6 C-4.05812251e-16,2.6862915 2.6862915,4.05812251e-16 6,0 Z"
              fill="#FFFFFF"
              opacity="0"
            ></path>
            <g transform="translate(4.000000, 4.000000)">
              <path d="M0,0 L24,0 L24,24 L0,24 L0,0 Z"></path>
              <g
                className="stroke-theme"
                transform="translate(2.990723, 2.750000)"
                strokeDasharray="0,0"
                strokeWidth="1.2"
              >
                <path
                  d="M7.1463809,0 C6.89103937,0 6.67672205,0.19239682 6.64927769,0.446259201 L6.35069037,3.20819163 C5.90906763,3.40279174 5.49281693,3.64439154 5.10827446,3.92665482 L2.56569386,2.80366182 C2.33212018,2.70049834 2.05834103,2.78990436 1.93067038,3.01103663 L0.067029634,6.23895693 C-0.0606411458,6.46008921 -0.00117930628,6.74189186 0.204949664,6.89259052 L2.4485023,8.53283024 C2.42304468,8.76839256 2.40998292,9.0076704 2.40998292,9.25 C2.40998292,9.49232674 2.42304444,9.73160362 2.44850159,9.96716309 L0.204949664,11.6074018 C-0.00117930628,11.7581005 -0.0606411458,12.0399036 0.067029634,12.2610359 L1.93067038,15.4889555 C2.05834103,15.7100878 2.33212018,15.7994938 2.56569386,15.696331 L5.10826922,14.5733404 C5.49281311,14.8556051 5.90906572,15.0972061 6.35069037,15.2918072 L6.64927769,18.0537415 C6.67672205,18.3076038 6.89103937,18.5 7.1463809,18.5 L10.873662,18.5 C11.1290035,18.5 11.3433218,18.3076038 11.3707657,18.0537415 L11.6694412,15.2909937 C12.110507,15.0964775 12.526248,14.855073 12.9103479,14.5730972 L15.453476,15.696332 C15.6870499,15.7994947 15.9608288,15.7100887 16.0884991,15.4889565 L17.9521408,12.2610369 C18.0798111,12.0399046 18.0203495,11.7581015 17.8142204,11.6074028 L15.5698481,9.96656418 C15.5952625,9.73119736 15.6083021,9.4921217 15.6083021,9.25 C15.6083021,9.0078764 15.5952625,8.76879787 15.5698471,8.53342915 L17.8142204,6.89258957 C18.0203495,6.74189091 18.0798111,6.46008825 17.9521408,6.23895597 L16.0884991,3.01103568 C15.9608288,2.7899034 15.6870499,2.70049739 15.453476,2.80366087 L12.9103422,3.92689824 C12.5262432,3.64492369 12.1105051,3.40352106 11.6694412,3.20900536 L11.3707657,0.446259111 C11.3433218,0.19239673 11.1290035,0 10.873662,0 L7.1463809,0 Z"
                  strokeLinejoin="round"
                  fillRule="nonzero"
                ></path>
                <path d="M12.0087888,9.25 C12.0087888,10.9068543 10.6656431,12.25 9.00878884,12.25 C7.35193456,12.25 6.00878884,10.9068543 6.00878884,9.25 C6.00878884,7.59314573 7.35193456,6.25 9.00878884,6.25 C10.6656431,6.25 12.0087888,7.59314573 12.0087888,9.25 Z"></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
  unselected: (
    <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-1565.000000, -626.000000)">
          <g transform="translate(1565.000000, 626.000000)">
            <path
              d="M6,0 L26,0 C29.3137085,-6.08718376e-16 32,2.6862915 32,6 L32,26 C32,29.3137085 29.3137085,32 26,32 L6,32 C2.6862915,32 2.02906125e-16,29.3137085 0,26 L0,6 C-4.05812251e-16,2.6862915 2.6862915,4.05812251e-16 6,0 Z"
              fill="#FFFFFF"
              opacity="0"
            ></path>
            <g transform="translate(4.000000, 4.000000)">
              <path d="M0,0 L24,0 L24,24 L0,24 L0,0 Z"></path>
              <g
                className="stroke-theme"
                transform="translate(2.990723, 2.750000)"
                strokeDasharray="0,0"
                strokeWidth="1.2"
              >
                <path
                  d="M7.1463809,0 C6.89103937,0 6.67672205,0.19239682 6.64927769,0.446259201 L6.35069037,3.20819163 C5.90906763,3.40279174 5.49281693,3.64439154 5.10827446,3.92665482 L2.56569386,2.80366182 C2.33212018,2.70049834 2.05834103,2.78990436 1.93067038,3.01103663 L0.067029634,6.23895693 C-0.0606411458,6.46008921 -0.00117930628,6.74189186 0.204949664,6.89259052 L2.4485023,8.53283024 C2.42304468,8.76839256 2.40998292,9.0076704 2.40998292,9.25 C2.40998292,9.49232674 2.42304444,9.73160362 2.44850159,9.96716309 L0.204949664,11.6074018 C-0.00117930628,11.7581005 -0.0606411458,12.0399036 0.067029634,12.2610359 L1.93067038,15.4889555 C2.05834103,15.7100878 2.33212018,15.7994938 2.56569386,15.696331 L5.10826922,14.5733404 C5.49281311,14.8556051 5.90906572,15.0972061 6.35069037,15.2918072 L6.64927769,18.0537415 C6.67672205,18.3076038 6.89103937,18.5 7.1463809,18.5 L10.873662,18.5 C11.1290035,18.5 11.3433218,18.3076038 11.3707657,18.0537415 L11.6694412,15.2909937 C12.110507,15.0964775 12.526248,14.855073 12.9103479,14.5730972 L15.453476,15.696332 C15.6870499,15.7994947 15.9608288,15.7100887 16.0884991,15.4889565 L17.9521408,12.2610369 C18.0798111,12.0399046 18.0203495,11.7581015 17.8142204,11.6074028 L15.5698481,9.96656418 C15.5952625,9.73119736 15.6083021,9.4921217 15.6083021,9.25 C15.6083021,9.0078764 15.5952625,8.76879787 15.5698471,8.53342915 L17.8142204,6.89258957 C18.0203495,6.74189091 18.0798111,6.46008825 17.9521408,6.23895597 L16.0884991,3.01103568 C15.9608288,2.7899034 15.6870499,2.70049739 15.453476,2.80366087 L12.9103422,3.92689824 C12.5262432,3.64492369 12.1105051,3.40352106 11.6694412,3.20900536 L11.3707657,0.446259111 C11.3433218,0.19239673 11.1290035,0 10.873662,0 L7.1463809,0 Z"
                  strokeLinejoin="round"
                  fillRule="nonzero"
                ></path>
                <path d="M12.0087888,9.25 C12.0087888,10.9068543 10.6656431,12.25 9.00878884,12.25 C7.35193456,12.25 6.00878884,10.9068543 6.00878884,9.25 C6.00878884,7.59314573 7.35193456,6.25 9.00878884,6.25 C10.6656431,6.25 12.0087888,7.59314573 12.0087888,9.25 Z"></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
};

export const PinIcon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    version="1.1"
    width="15.769599914550781"
    height="25.98624038696289"
    viewBox="0 0 15.769599914550781 25.98624038696289"
  >
    <defs>
      <filter
        id="master_svg0_480_76723"
        filterUnits="objectBoundingBox"
        colorInterpolationFilters="sRGB"
        x="0"
        y="0"
        width="15.769599914550781"
        height="25.98624038696289"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy="-0.699999988079071" dx="0.699999988079071" />
        <feGaussianBlur stdDeviation="0.07000000029802322" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0" />
        <feBlend mode="normal" in2="shape" result="effect1_innerShadow" />
      </filter>
      <linearGradient
        x1="0.14024460315704346"
        y1="0.26349076628685"
        x2="0.8998119831085205"
        y2="0.8498528599739075"
        id="master_svg1_1_0441"
      >
        <stop offset="0%" stopColor="#1888FF" stopOpacity="1" />
        <stop offset="100%" stopColor="#2F54EB" stopOpacity="1" />
      </linearGradient>
    </defs>
    <g transform="matrix(-1,0,0,1,31.539199829101562,0)" filter="url(#master_svg0_480_76723)">
      <path
        d="M31.53919991455078,18.5998L31.53919991455078,21.2173C31.53919991455078,23.8403,29.403399914550782,25.9862,26.692999914550782,25.9862C23.982559914550784,25.9862,21.84785991455078,23.8403,21.84785991455078,21.2173L21.847839914550782,10.5706C21.847839914550782,9.9344,22.34063991455078,9.45728,22.996959914550782,9.45728C23.65439991455078,9.45728,24.147199914550782,9.9344,24.147199914550782,10.5706L24.147209914550782,21.1366C24.14284991455078,22.5439,25.285719914550782,23.6853,26.692999914550782,23.679C28.10059991455078,23.6859,29.244199914550784,22.5443,29.239899914550783,21.1366L29.23979991455078,7.63056C29.23979991455078,4.68944,26.77579991455078,2.22544,23.65439991455078,2.22544C20.53295991455078,2.22544,18.06895991455078,4.61216,18.06895991455078,7.63056L18.06895991455078,10.8898L15.769599914550781,10.8898L15.769599914550781,7.63056C15.769599914550781,3.41824,19.30095991455078,0,23.65439991455078,0C28.00779991455078,0,31.53919991455078,3.41824,31.53919991455078,7.63056L31.53919991455078,18.5998Z"
        fill="url(#master_svg1_1_0441)"
        fillOpacity="1"
      />
    </g>
  </svg>
);

/** 聊天-add */
export const ChatAddSVGIcon = (
  <svg width="11" height="11" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      className="fill-theme"
      d="M10.353 4.853H6.14719V0.647003C6.14719 0.258763 5.88824 0 5.5 0C5.11176 0 4.853 0.258763 4.853 0.647003V4.853H0.647003C0.258763 4.853 0 5.11176 0 5.5C0 5.88824 0.258763 6.147 0.647003 6.147H4.853V10.353C4.853 10.7412 5.11176 11 5.5 11C5.88824 11 6.147 10.7412 6.147 10.353V6.14719H10.353C10.7412 6.14719 11 5.88843 11 5.50019C11.0002 5.11176 10.7412 4.853 10.353 4.853Z"
    />
  </svg>
);
/** OCRIcon */
const OCRSVGIcons: MenuIconType = {
  selected: (
    <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-1627.000000, -338.000000)">
          <g transform="translate(1627.000000, 338.000000)">
            <rect fill="#000000" fillRule="nonzero" opacity="0" x="0" y="0" width="32" height="32" rx="7"></rect>
            <g transform="translate(4.000000, 4.000000)">
              <path d="M0,0 L24,0 L24,24 L0,24 L0,0 Z"></path>
              <path
                className="stroke-theme"
                d="M21.5,15.71875 L21.5,18.5 C21.5,20.1568542 20.1568542,21.5 18.5,21.5 L15.71875,21.5 L15.71875,21.5 M21.5,8.78125 L21.5,6 C21.5,4.34314575 20.1568542,3 18.5,3 L15.71875,3 L15.71875,3 M3,15.71875 L3,18.5 C3,20.1568542 4.34314575,21.5 6,21.5 L8.78125,21.5 L8.78125,21.5 M3,8.78125 L3,6 C3,4.34314575 4.34314575,3 6,3 L8.78125,3 L8.78125,3"
                strokeWidth="1.2"
                strokeLinecap="round"
                strokeDasharray="0,0"
              ></path>
              <rect className="fill-theme" x="5" y="5" width="14" height="14" rx="2.15384615"></rect>
              <line
                x1="15.75"
                y1="9.25"
                x2="8.25000019"
                y2="9.25048828"
                stroke="#FFFFFF"
                strokeWidth="1.2"
                strokeLinecap="round"
                strokeDasharray="0,0"
                fillRule="nonzero"
              ></line>
              <line
                x1="12"
                y1="13.25"
                x2="8.25000019"
                y2="13.2504883"
                stroke="#FFFFFF"
                strokeWidth="1.2"
                strokeLinecap="round"
                strokeDasharray="0,0"
                fillRule="nonzero"
              ></line>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
  unselected: (
    <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-1565.000000, -338.000000)">
          <g transform="translate(1565.000000, 338.000000)">
            <rect fill="#000000" fillRule="nonzero" opacity="0" x="0" y="0" width="32" height="32" rx="7"></rect>
            <g transform="translate(4.000000, 4.000000)">
              <path d="M0,0 L24,0 L24,24 L0,24 L0,0 Z"></path>
              <g
                className="stroke-theme"
                transform="translate(2.750000, 2.750000)"
                strokeDasharray="0,0"
                strokeLinecap="round"
              >
                <path
                  d="M18.5,12.71875 L18.5,15.5 C18.5,17.1568542 17.1568542,18.5 15.5,18.5 L12.71875,18.5 L12.71875,18.5 M18.5,5.78125 L18.5,3 C18.5,1.34314575 17.1568542,-4.05812251e-16 15.5,0 L12.71875,0 L12.71875,0 M0,12.71875 L0,15.5 C2.02906125e-16,17.1568542 1.34314575,18.5 3,18.5 L5.78125,18.5 L5.78125,18.5 M0,5.78125 L0,3 C-2.02906125e-16,1.34314575 1.34314575,2.02906125e-16 3,0 L5.78125,0 L5.78125,0"
                  strokeWidth="1.2"
                ></path>
                <line x1="12.75" y1="6.25" x2="5.25000019" y2="6.25048828" strokeWidth="1.5" fillRule="nonzero"></line>
                <line x1="9" y1="10.25" x2="5.25000019" y2="10.2504883" strokeWidth="1.5" fillRule="nonzero"></line>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
};

/** 我的Icon */
const MineSVGIcons: MenuIconType = {
  selected: (
    <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-1627.000000, -580.000000)">
          <g transform="translate(1627.000000, 580.000000)">
            <path d="M0,0 L32,0 L32,32 L0,32 L0,0 Z" fill="#FFFFFF" opacity="0"></path>
            <g transform="translate(4.000000, 4.000000)">
              <path d="M0,0 L24,0 L24,24 L0,24 L0,0 Z"></path>
              <g
                className="stroke-theme"
                transform="translate(4.546875, 3.000000)"
                strokeDasharray="0,0"
                strokeWidth="1.2"
              >
                <path d="M10.9541016,3.4000001 C10.9541016,5.27776825 9.38709827,6.8 7.45410164,6.8 C5.52110501,6.8 3.95410164,5.27776825 3.95410164,3.4000001 C3.95410164,1.52223194 5.52110501,0 7.45410164,0 C9.38709827,0 10.9541016,1.52223194 10.9541016,3.4000001 Z"></path>
                <path
                  className="fill-theme"
                  d="M1.67789853,10.4766691 C1.96963573,9.795949 2.97739571,9.56379033 3.66072011,9.84937436 C4.47211051,10.1884814 5.69584739,10.4999988 7.45361423,10.4999988 C9.21138096,10.4999988 10.4351182,10.1884815 11.2465086,9.84937443 C11.929833,9.56379043 12.9375938,9.79595025 13.229331,10.4766704 L14.6614323,13.8182413 C15.5098345,15.7978466 14.0577438,17.9999992 11.9039974,17.9999992 L3.00323081,17.9999992 C0.849484444,17.9999992 -0.602606073,15.7978466 0.245796189,13.8182413 L1.67789853,10.4766691 Z"
                  fillRule="nonzero"
                ></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
  unselected: (
    <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-1565.000000, -580.000000)">
          <g transform="translate(1565.000000, 580.000000)">
            <path d="M0,0 L32,0 L32,32 L0,32 L0,0 Z" fill="#FFFFFF" opacity="0"></path>
            <g transform="translate(4.000000, 4.000000)">
              <path d="M0,0 L24,0 L24,24 L0,24 L0,0 Z"></path>
              <g
                className="stroke-theme"
                transform="translate(4.546875, 3.000000)"
                strokeDasharray="0,0"
                strokeWidth="1.2"
              >
                <path d="M10.9541016,3.4000001 C10.9541016,5.27776825 9.38709827,6.8 7.45410164,6.8 C5.52110501,6.8 3.95410164,5.27776825 3.95410164,3.4000001 C3.95410164,1.52223194 5.52110501,0 7.45410164,0 C9.38709827,0 10.9541016,1.52223194 10.9541016,3.4000001 Z"></path>
                <path
                  d="M1.67789853,10.4766691 C1.96963573,9.795949 2.97739571,9.56379033 3.66072011,9.84937436 C4.47211051,10.1884814 5.69584739,10.4999988 7.45361423,10.4999988 C9.21138096,10.4999988 10.4351182,10.1884815 11.2465086,9.84937443 C11.929833,9.56379043 12.9375938,9.79595025 13.229331,10.4766704 L14.6614323,13.8182413 C15.5098345,15.7978466 14.0577438,17.9999992 11.9039974,17.9999992 L3.00323081,17.9999992 C0.849484444,17.9999992 -0.602606073,15.7978466 0.245796189,13.8182413 L1.67789853,10.4766691 Z"
                  fillRule="nonzero"
                ></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
};

/** 划词-引入便签 */
export const NotesBubbleIcon = (
  <svg
    width="13.1333346px"
    height="12.7934006px"
    viewBox="0 0 13.1333346 12.7934006"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g transform="translate(-872.600000, -471.600000)">
        <g transform="translate(855.000000, 453.000000)">
          <g transform="translate(12.000000, 12.000000)">
            <g transform="translate(4.000000, 5.000000)">
              <path d="M0,0 L16,0 L16,16 L0,16 L0,0 Z"></path>
              <g transform="translate(2.000000, 2.000000)" fillRule="nonzero">
                <path
                  d="M0.600000024,0 L11.7333346,0 C12.0647054,-1.71894142e-16 12.3333346,0.268629161 12.3333346,0.600000024 L12.3333346,8.56613831 C12.3333346,8.89750917 12.0647054,9.16613833 11.7333346,9.16613833 L6.2749246,9.16613833 L6.2749246,9.16613833 C6.20456139,9.16613833 6.13600311,9.18840425 6.07906723,9.22974777 L2.36252451,11.9284846 C2.1421744,12.08849 1.83333333,11.9310772 1.83333333,11.6587613 L1.83333333,9.49947166 C1.83333333,9.31537676 1.68409491,9.16613833 1.5,9.16613833 L0.600000024,9.16613833 C0.268629161,9.16613833 2.62625832e-16,8.89750917 0,8.56613831 L0,0.600000024 C-4.05812267e-17,0.268629161 0.268629161,-2.92485681e-16 0.600000024,0 Z"
                  stroke="#000000"
                  strokeWidth="0.8"
                  strokeLinejoin="round"
                  strokeDasharray="0,0"
                ></path>
                <path
                  d="M3.94382105,6 C4.39325263,6 4.75281053,5.64706186 4.75281053,5.20588144 C4.75281053,4.80882216 4.4382,4.45588403 4.03370526,4.45588403 L3.80898947,4.45588403 C3.80898947,4.32353093 3.80898947,3.66176547 4.52808421,3.30882733 L4.34831579,3 C3.53932632,3.35293814 3,4.0147036 3,4.80882216 C3,5.25000259 3.13483158,5.51470877 3.35954737,5.77941496 C3.44943157,5.91176805 3.6741579,6 3.94382105,6 L3.94382105,6 Z M6.19101053,6 C6.64045263,6 7,5.64706186 7,5.20588144 C7,4.80882216 6.68538947,4.45588403 6.28089473,4.45588403 L6.05617895,4.45588403 C6.05617895,4.32353093 6.05617895,3.66176547 6.77528421,3.30882733 L6.59550526,3 C5.78651579,3.35293814 5.24718947,4.0147036 5.24718947,4.80882216 C5.24718947,5.25000259 5.38202105,5.51470877 5.60673684,5.77941496 C5.69663158,5.91176805 5.92134737,6 6.19101053,6 L6.19101053,6 Z"
                  fill="#09121F"
                ></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
  </svg>
);
/** 划词-新建便签 */
export const NotesAddIcon = (
  <svg width="12px" height="12px" viewBox="0 0 12 12" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g transform="translate(-873.200000, -503.200000)">
        <g transform="translate(855.000000, 453.000000)">
          <g transform="translate(12.000000, 42.000000)">
            <g transform="translate(4.000000, 6.000000)">
              <path d="M0,0 L16,0 L16,16 L0,16 L0,0 Z"></path>
              <g transform="translate(2.200000, 2.200000)">
                <path
                  d="M0,0.375 L0,11.625 C0,11.8324219 0.167578125,12 0.375,12 L7.93945312,12 C8.13867187,12 8.3296875,11.9214844 8.4703125,11.7808594 L11.7808594,8.4703125 C11.9214844,8.3296875 12,8.13867187 12,7.93945312 L12,0.375 C12,0.167578125 11.8324219,0 11.625,0 L0.375,0 C0.167578125,0 0,0.167578125 0,0.375 Z M11.203125,0.75 C11.2289062,0.75 11.25,0.77109375 11.25,0.796875 L11.25,7.3125 C11.25,7.415625 11.165625,7.5 11.0625,7.5 L9,7.5 C8.17148437,7.5 7.5,8.17148437 7.5,9 L7.5,11.0625 C7.5,11.165625 7.415625,11.25 7.3125,11.25 L0.796875,11.25 C0.77109375,11.25 0.75,11.2289062 0.75,11.203125 L0.75,0.796875 C0.75,0.77109375 0.77109375,0.75 0.796875,0.75 L11.203125,0.75 Z M10.5,8.25 L8.25,10.5 L8.25,9 C8.25,8.58632812 8.58632812,8.25 9,8.25 L10.5,8.25 Z"
                  fill="#121212"
                  fillRule="nonzero"
                ></path>
                <line x1="4" y1="5.5" x2="8" y2="5.5" stroke="#121212"></line>
                <line
                  x1="4"
                  y1="5.5"
                  x2="8"
                  y2="5.5"
                  stroke="#121212"
                  transform="translate(6.000000, 5.500000) rotate(90.000000) translate(-6.000000, -5.500000) "
                ></line>
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
  </svg>
);

export const ReturnIcon = (
  <svg width="20" height="20" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g transform="translate(-742.000000, -282.000000)">
        <g transform="translate(742.000000, 282.000000)">
          <path d="M0,0 L20,0 L20,20 L0,20 L0,0 Z" fill="#FFFFFF" opacity="0"></path>
          <g transform="translate(6.072642, 3.541667)" className="fill-color">
            <path d="M6.897707,0.213443189 C7.12500891,0.473216076 7.09868512,0.868067691 6.83891218,1.09536896 L1.6057253,5.67440788 C1.13142521,6.08942032 1.13142531,6.82726343 1.60572351,7.24227428 L6.83891218,11.8213153 C7.09868512,12.0486164 7.12500891,12.4434678 6.897707,12.7032407 C6.67040588,12.9630136 6.27555451,12.9893374 6.01578157,12.7620355 L0.782592756,8.18299532 C-0.260866222,7.26996899 -0.260862845,5.64671238 0.782594445,4.73368724 L6.01578157,0.154648162 C6.27555451,-0.0726531578 6.67040588,-0.0463297226 6.897707,0.213443189 Z"></path>
          </g>
        </g>
      </g>
    </g>
  </svg>
);

// 提示词
export const promptIconSvg = (
  <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="Vector">
      <path d="M10.1411 8.99218H8.61054V10.9798H10.1411V8.99218Z" fill="#2E7DF3" />
      <path
        className="fill-theme"
        d="M14.217 0H2.78327C1.24853 0 0 1.24878 0 2.78323V14.2168C0 15.7515 1.24853 17 2.78327 17H14.2167C15.7515 17 17 15.7515 17 14.2168V2.78244C16.9992 1.24746 15.7517 0 14.217 0ZM3.35883 3.25874C3.48303 3.14084 3.64923 3.07757 3.82017 3.08229C3.99137 3.08702 4.15337 3.15975 4.27074 3.28447L4.94398 3.99761C5.06161 4.1218 5.12515 4.28774 5.12017 4.45894C5.11518 4.63013 5.04271 4.79214 4.91798 4.90951C4.79851 5.0232 4.63992 5.08622 4.47476 5.08595C4.29726 5.08622 4.12764 5.01296 4.00581 4.88377L3.33257 4.17064C3.0889 3.91148 3.09967 3.50293 3.35883 3.25874ZM6.3918 12.7456L4.80429 13.606C4.59056 13.7213 4.33008 13.7071 4.13 13.5695C3.92992 13.4312 3.82489 13.192 3.85798 12.9512L4.6625 7.03264H3.63637C3.28058 7.03264 2.99123 6.74355 2.99123 6.3875C2.99123 6.03146 3.28058 5.74211 3.63637 5.74211H5.40138C5.58755 5.74211 5.76504 5.82272 5.88767 5.96267C6.01029 6.10262 6.06621 6.28957 6.04153 6.47363L5.30922 11.8639L5.77817 11.6089C6.09221 11.4391 6.48371 11.5551 6.65254 11.8691C6.82085 12.1842 6.70453 12.5757 6.3918 12.7456ZM7.96514 12.2701C7.60909 12.2701 7.31974 11.9807 7.31974 11.625V8.34679C7.31974 7.99075 7.60909 7.70166 7.96514 7.70166H10.7862C11.142 7.70166 11.4314 7.99075 11.4314 8.34679V11.625C11.4314 11.9807 11.142 12.2701 10.7862 12.2701H7.96514ZM7.19501 6.33998C7.19501 5.98394 7.48437 5.69459 7.84016 5.69459H10.7925C11.1483 5.69459 11.4377 5.98394 11.4377 6.33998C11.4377 6.69576 11.1483 6.98511 10.7925 6.98511H7.84042C7.48437 6.98537 7.19501 6.69707 7.19501 6.33998ZM13.7943 12.7154C13.7943 13.5585 13.1081 14.2457 12.2637 14.2457H11.7593C11.4035 14.2457 11.1142 13.9563 11.1142 13.6005C11.1142 13.2445 11.4035 12.9551 11.7593 12.9551H12.2637C12.3961 12.9551 12.5034 12.8477 12.5034 12.7154V4.71389C12.5034 4.4676 12.3422 4.25912 12.1529 4.25912H7.43264C7.07659 4.25912 6.7875 3.96977 6.7875 3.61373C6.7875 3.25769 7.07686 2.9686 7.43264 2.9686H12.1519C13.0575 2.9686 13.7929 3.75158 13.7929 4.71416V12.7157H13.7943V12.7154Z"
      />
    </g>
  </svg>
);
// 金牌
export const goldMedal = (
  <svg width="35.9763226px" height="47.2768956px" viewBox="0 0 35.9763226 47.2768956">
    <defs>
      <linearGradient x1="50%" y1="0.298247466%" x2="50%" y2="100%" id="linearGradient-1">
        <stop stopColor="#00A2FF" offset="0%"></stop>
        <stop stopColor="#008DFF" offset="100%"></stop>
      </linearGradient>
      <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
        <stop stopColor="#30B3FF" offset="0%"></stop>
        <stop stopColor="#23A0FF" offset="100%"></stop>
      </linearGradient>
      <path
        d="M10.1538462,27 L26.7692308,27 C27.7888334,27 28.6153846,27.8265512 28.6153846,28.8461538 L28.6153846,44.8591432 C28.6153846,45.8787458 27.7888334,46.7052971 26.7692308,46.7052971 C26.4549144,46.7052971 26.1458026,46.625047 25.8711807,46.4721498 L18.3295385,42.2733 L18.3295385,42.2733 L11.0697866,46.4216795 C10.184521,46.9275394 9.05679092,46.6199704 8.55093097,45.7347048 C8.39153427,45.4557572 8.30769231,45.1400415 8.30769231,44.8187643 L8.30769231,28.8461538 C8.30769231,27.8265512 9.13424354,27 10.1538462,27 Z"
        id="path-3"
      ></path>
      <mask
        id="mask-4"
        maskContentUnits="userSpaceOnUse"
        maskUnits="objectBoundingBox"
        x="0"
        y="0"
        width="20.3076923"
        height="21"
        fill="white"
      >
        <use></use>
      </mask>
      <linearGradient x1="50%" y1="1.66662034%" x2="50%" y2="100%" id="linearGradient-5">
        <stop stopColor="#2EABFF" offset="0%"></stop>
        <stop stopColor="#4EB3FF" offset="100%"></stop>
      </linearGradient>
      <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-6">
        <stop stopColor="#A9FCFF" offset="0%"></stop>
        <stop stopColor="#62DCFF" offset="100%"></stop>
      </linearGradient>
      <path
        d="M34.4496998,10.8295854 L34.4496998,26.0934916 C34.4496998,27.0828424 33.9218874,27.9970403 33.0650844,28.4917158 L19.8461538,36.1236689 C18.9893509,36.6183443 17.9337261,36.6183443 17.0769231,36.1236689 L3.85799255,28.4917158 C3.00118956,27.9970403 2.47337716,27.0828424 2.47337716,26.0934916 L2.47337716,10.8295854 C2.47337716,9.8402345 3.00118956,8.9260366 3.85799255,8.43136117 L17.0769231,0.799408065 C17.9337261,0.304732633 18.9893509,0.304732633 19.8461538,0.799408065 L33.0650844,8.43136117 C33.9218874,8.9260366 34.4496998,9.8402345 34.4496998,10.8295854 Z"
        id="path-7"
      ></path>
      <filter x="-2.8%" y="-5.6%" width="105.6%" height="116.6%" filterUnits="objectBoundingBox" id="filter-8">
        <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
        <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
        <feColorMatrix
          values="0 0 0 0 0   0 0 0 0 0.647058824   0 0 0 0 0.874509804  0 0 0 1 0"
          type="matrix"
          in="shadowBlurOuter1"
        ></feColorMatrix>
      </filter>
      <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-9">
        <stop stopColor="#C9FBFF" offset="0%"></stop>
        <stop stopColor="#A5EDFF" offset="100%"></stop>
      </linearGradient>
      <path
        d="M31.2520675,12.6757392 L31.2520675,24.2473377 C31.2520675,25.2366886 30.7242551,26.1508865 29.8674521,26.6455619 L19.8461538,32.4313612 C18.9893509,32.9260366 17.9337261,32.9260366 17.0769231,32.4313612 L7.05562481,26.6455619 C6.19882182,26.1508865 5.67100942,25.2366886 5.67100942,24.2473377 L5.67100942,12.6757392 C5.67100942,11.6863883 6.19882182,10.7721904 7.05562481,10.277515 L17.0769231,4.49171576 C17.9337261,3.99704033 18.9893509,3.99704033 19.8461538,4.49171576 L29.8674521,10.277515 C30.7242551,10.7721904 31.2520675,11.6863883 31.2520675,12.6757392 Z"
        id="path-10"
      ></path>
      <filter x="1.6%" y="-3.6%" width="96.8%" height="107.3%" filterUnits="objectBoundingBox" id="filter-11">
        <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
        <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
        <feColorMatrix
          values="0 0 0 0 0   0 0 0 0 0.764705882   0 0 0 0 0.952941176  0 0 0 1 0"
          type="matrix"
          in="shadowBlurOuter1"
        ></feColorMatrix>
      </filter>
      <text
        id="text-12"
        fontFamily="DingTalk-JinBuTi, DingTalk JinBuTi"
        fontSize="18.4615385"
        fontStyle="italic"
        fontWeight="normal"
        fill="#0A9CEA"
      >
        <tspan x="13.3846154" y="23.0769231">
          2
        </tspan>
      </text>
      <filter x="-27.3%" y="-11.1%" width="154.5%" height="122.2%" filterUnits="objectBoundingBox" id="filter-13">
        <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
        <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
        <feColorMatrix
          values="0 0 0 0 0.482352941   0 0 0 0 0.91372549   0 0 0 0 1  0 0 0 1 0"
          type="matrix"
          in="shadowBlurOuter1"
        ></feColorMatrix>
      </filter>
    </defs>
    <g id="0.2.素材" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g id="素材切图" transform="translate(-490.473377, -755.428401)">
        <g id="2" transform="translate(490.000000, 756.000000)">
          <path d="M0,0 L37,0 L37,48 L0,48 L0,0 Z" id="路径"></path>
          <use
            id="路径"
            stroke="url(#linearGradient-2)"
            mask="url(#mask-4)"
            strokeWidth="0.923076923"
            fill="url(#linearGradient-1)"
            strokeDasharray="0,0"
          ></use>
          <path
            d="M12.7202029,28.1538462 L24.202874,28.1538462 C25.2224767,28.1538462 26.0490279,28.9803974 26.0490279,30 L26.0490279,42.2757255 C26.0490279,42.7855268 25.6357523,43.1988024 25.125951,43.1988024 C24.9687928,43.1988024 24.8142369,43.1586773 24.6769259,43.0822287 L18.3629011,39.5668615 L18.3629011,39.5668615 L12.2550962,43.0569936 C11.8124634,43.3099236 11.2485983,43.156139 10.9956684,42.7135062 C10.91597,42.5740324 10.874049,42.4161746 10.874049,42.255536 L10.874049,30 C10.874049,28.9803974 11.7006003,28.1538462 12.7202029,28.1538462 Z"
            id="路径"
            fill="url(#linearGradient-5)"
          ></path>
          <g id="路径">
            <use fill="black" fillOpacity="1" filter="url(#filter-8)"></use>
            <use fill="url(#linearGradient-6)" fillRule="evenodd"></use>
          </g>
          <g id="路径">
            <use fill="black" fillOpacity="1" filter="url(#filter-11)"></use>
            <use fill="url(#linearGradient-9)" fillRule="evenodd"></use>
          </g>
          <g fill="#0A9CEA" fillOpacity="1">
            <use filter="url(#filter-13)"></use>
            <use></use>
          </g>
        </g>
      </g>
    </g>
  </svg>
);
// 银牌
export const silverMedal = (
  <svg width="35.9763226px" height="47.2768956px" viewBox="0 0 35.9763226 47.2768956">
    <defs>
      <linearGradient x1="50%" y1="0.298247466%" x2="50%" y2="100%" id="linearGradient-1">
        <stop stopColor="#00A2FF" offset="0%"></stop>
        <stop stopColor="#008DFF" offset="100%"></stop>
      </linearGradient>
      <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
        <stop stopColor="#30B3FF" offset="0%"></stop>
        <stop stopColor="#23A0FF" offset="100%"></stop>
      </linearGradient>
      <path
        d="M10.1538462,27 L26.7692308,27 C27.7888334,27 28.6153846,27.8265512 28.6153846,28.8461538 L28.6153846,44.8591432 C28.6153846,45.8787458 27.7888334,46.7052971 26.7692308,46.7052971 C26.4549144,46.7052971 26.1458026,46.625047 25.8711807,46.4721498 L18.3295385,42.2733 L18.3295385,42.2733 L11.0697866,46.4216795 C10.184521,46.9275394 9.05679092,46.6199704 8.55093097,45.7347048 C8.39153427,45.4557572 8.30769231,45.1400415 8.30769231,44.8187643 L8.30769231,28.8461538 C8.30769231,27.8265512 9.13424354,27 10.1538462,27 Z"
        id="path-3"
      ></path>
      <mask
        id="mask-4"
        maskContentUnits="userSpaceOnUse"
        maskUnits="objectBoundingBox"
        x="0"
        y="0"
        width="20.3076923"
        height="21"
        fill="white"
      >
        <use></use>
      </mask>
      <linearGradient x1="50%" y1="1.66662034%" x2="50%" y2="100%" id="linearGradient-5">
        <stop stopColor="#2EABFF" offset="0%"></stop>
        <stop stopColor="#4EB3FF" offset="100%"></stop>
      </linearGradient>
      <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-6">
        <stop stopColor="#A9FCFF" offset="0%"></stop>
        <stop stopColor="#62DCFF" offset="100%"></stop>
      </linearGradient>
      <path
        d="M34.4496998,10.8295854 L34.4496998,26.0934916 C34.4496998,27.0828424 33.9218874,27.9970403 33.0650844,28.4917158 L19.8461538,36.1236689 C18.9893509,36.6183443 17.9337261,36.6183443 17.0769231,36.1236689 L3.85799255,28.4917158 C3.00118956,27.9970403 2.47337716,27.0828424 2.47337716,26.0934916 L2.47337716,10.8295854 C2.47337716,9.8402345 3.00118956,8.9260366 3.85799255,8.43136117 L17.0769231,0.799408065 C17.9337261,0.304732633 18.9893509,0.304732633 19.8461538,0.799408065 L33.0650844,8.43136117 C33.9218874,8.9260366 34.4496998,9.8402345 34.4496998,10.8295854 Z"
        id="path-7"
      ></path>
      <filter x="-2.8%" y="-5.6%" width="105.6%" height="116.6%" filterUnits="objectBoundingBox" id="filter-8">
        <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
        <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
        <feColorMatrix
          values="0 0 0 0 0   0 0 0 0 0.647058824   0 0 0 0 0.874509804  0 0 0 1 0"
          type="matrix"
          in="shadowBlurOuter1"
        ></feColorMatrix>
      </filter>
      <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-9">
        <stop stopColor="#C9FBFF" offset="0%"></stop>
        <stop stopColor="#A5EDFF" offset="100%"></stop>
      </linearGradient>
      <path
        d="M31.2520675,12.6757392 L31.2520675,24.2473377 C31.2520675,25.2366886 30.7242551,26.1508865 29.8674521,26.6455619 L19.8461538,32.4313612 C18.9893509,32.9260366 17.9337261,32.9260366 17.0769231,32.4313612 L7.05562481,26.6455619 C6.19882182,26.1508865 5.67100942,25.2366886 5.67100942,24.2473377 L5.67100942,12.6757392 C5.67100942,11.6863883 6.19882182,10.7721904 7.05562481,10.277515 L17.0769231,4.49171576 C17.9337261,3.99704033 18.9893509,3.99704033 19.8461538,4.49171576 L29.8674521,10.277515 C30.7242551,10.7721904 31.2520675,11.6863883 31.2520675,12.6757392 Z"
        id="path-10"
      ></path>
      <filter x="1.6%" y="-3.6%" width="96.8%" height="107.3%" filterUnits="objectBoundingBox" id="filter-11">
        <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
        <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
        <feColorMatrix
          values="0 0 0 0 0   0 0 0 0 0.764705882   0 0 0 0 0.952941176  0 0 0 1 0"
          type="matrix"
          in="shadowBlurOuter1"
        ></feColorMatrix>
      </filter>
      <text
        id="text-12"
        fontFamily="DingTalk-JinBuTi, DingTalk JinBuTi"
        fontSize="18.4615385"
        fontStyle="italic"
        fontWeight="normal"
        fill="#0A9CEA"
      >
        <tspan x="13.3846154" y="23.0769231">
          2
        </tspan>
      </text>
      <filter x="-27.3%" y="-11.1%" width="154.5%" height="122.2%" filterUnits="objectBoundingBox" id="filter-13">
        <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
        <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
        <feColorMatrix
          values="0 0 0 0 0.482352941   0 0 0 0 0.91372549   0 0 0 0 1  0 0 0 1 0"
          type="matrix"
          in="shadowBlurOuter1"
        ></feColorMatrix>
      </filter>
    </defs>
    <g id="0.2.素材" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g id="素材切图" transform="translate(-490.473377, -755.428401)">
        <g id="2" transform="translate(490.000000, 756.000000)">
          <path d="M0,0 L37,0 L37,48 L0,48 L0,0 Z" id="路径"></path>
          <use
            id="路径"
            stroke="url(#linearGradient-2)"
            mask="url(#mask-4)"
            strokeWidth="0.923076923"
            fill="url(#linearGradient-1)"
            strokeDasharray="0,0"
          ></use>
          <path
            d="M12.7202029,28.1538462 L24.202874,28.1538462 C25.2224767,28.1538462 26.0490279,28.9803974 26.0490279,30 L26.0490279,42.2757255 C26.0490279,42.7855268 25.6357523,43.1988024 25.125951,43.1988024 C24.9687928,43.1988024 24.8142369,43.1586773 24.6769259,43.0822287 L18.3629011,39.5668615 L18.3629011,39.5668615 L12.2550962,43.0569936 C11.8124634,43.3099236 11.2485983,43.156139 10.9956684,42.7135062 C10.91597,42.5740324 10.874049,42.4161746 10.874049,42.255536 L10.874049,30 C10.874049,28.9803974 11.7006003,28.1538462 12.7202029,28.1538462 Z"
            id="路径"
            fill="url(#linearGradient-5)"
          ></path>
          <g id="路径">
            <use fill="black" fillOpacity="1" filter="url(#filter-8)"></use>
            <use fill="url(#linearGradient-6)" fillRule="evenodd"></use>
          </g>
          <g id="路径">
            <use fill="black" fillOpacity="1" filter="url(#filter-11)"></use>
            <use fill="url(#linearGradient-9)" fillRule="evenodd"></use>
          </g>
          <g fill="#0A9CEA" fillOpacity="1">
            <use filter="url(#filter-13)"></use>
            <use></use>
          </g>
        </g>
      </g>
    </g>
  </svg>
);
// 铜牌
export const bronzeMedal = (
  <svg width="35.9763226px" height="47.2768956px" viewBox="0 0 35.9763226 47.2768956">
    <defs>
      <linearGradient x1="50%" y1="0.298247466%" x2="50%" y2="100%" id="linearGradient-1">
        <stop stopColor="#FF5800" offset="0%"></stop>
        <stop stopColor="#FF5600" offset="100%"></stop>
      </linearGradient>
      <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
        <stop stopColor="#FF6B1E" offset="0%"></stop>
        <stop stopColor="#FF7731" offset="100%"></stop>
      </linearGradient>
      <path
        d="M10.1538462,27 L26.7692308,27 C27.7888334,27 28.6153846,27.8265512 28.6153846,28.8461538 L28.6153846,44.8591432 C28.6153846,45.8787458 27.7888334,46.7052971 26.7692308,46.7052971 C26.4549144,46.7052971 26.1458026,46.625047 25.8711807,46.4721498 L18.3295385,42.2733 L18.3295385,42.2733 L11.0697866,46.4216795 C10.184521,46.9275394 9.05679092,46.6199704 8.55093097,45.7347048 C8.39153427,45.4557572 8.30769231,45.1400415 8.30769231,44.8187643 L8.30769231,28.8461538 C8.30769231,27.8265512 9.13424354,27 10.1538462,27 Z"
        id="path-3"
      ></path>
      <mask
        id="mask-4"
        maskContentUnits="userSpaceOnUse"
        maskUnits="objectBoundingBox"
        x="0"
        y="0"
        width="20.3076923"
        height="21"
        fill="white"
      >
        <use></use>
      </mask>
      <linearGradient x1="50%" y1="1.66662034%" x2="50%" y2="100%" id="linearGradient-5">
        <stop stopColor="#FF884A" offset="0%"></stop>
        <stop stopColor="#FF9159" offset="100%"></stop>
      </linearGradient>
      <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-6">
        <stop stopColor="#FFB38F" offset="0%"></stop>
        <stop stopColor="#FF854A" offset="100%"></stop>
      </linearGradient>
      <path
        d="M34.4496998,10.8295854 L34.4496998,26.0934916 C34.4496998,27.0828424 33.9218874,27.9970403 33.0650844,28.4917158 L19.8461538,36.1236689 C18.9893509,36.6183443 17.9337261,36.6183443 17.0769231,36.1236689 L3.85799255,28.4917158 C3.00118956,27.9970403 2.47337716,27.0828424 2.47337716,26.0934916 L2.47337716,10.8295854 C2.47337716,9.8402345 3.00118956,8.9260366 3.85799255,8.43136117 L17.0769231,0.799408065 C17.9337261,0.304732633 18.9893509,0.304732633 19.8461538,0.799408065 L33.0650844,8.43136117 C33.9218874,8.9260366 34.4496998,9.8402345 34.4496998,10.8295854 Z"
        id="path-7"
      ></path>
      <filter x="-2.8%" y="-5.6%" width="105.6%" height="116.6%" filterUnits="objectBoundingBox" id="filter-8">
        <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
        <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
        <feColorMatrix
          values="0 0 0 0 1   0 0 0 0 0.42745098   0 0 0 0 0.137254902  0 0 0 1 0"
          type="matrix"
          in="shadowBlurOuter1"
        ></feColorMatrix>
      </filter>
      <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-9">
        <stop stopColor="#FFDAC7" offset="0%"></stop>
        <stop stopColor="#FFBA97" offset="100%"></stop>
      </linearGradient>
      <path
        d="M31.2520675,12.6757392 L31.2520675,24.2473377 C31.2520675,25.2366886 30.7242551,26.1508865 29.8674521,26.6455619 L19.8461538,32.4313612 C18.9893509,32.9260366 17.9337261,32.9260366 17.0769231,32.4313612 L7.05562481,26.6455619 C6.19882182,26.1508865 5.67100942,25.2366886 5.67100942,24.2473377 L5.67100942,12.6757392 C5.67100942,11.6863883 6.19882182,10.7721904 7.05562481,10.277515 L17.0769231,4.49171576 C17.9337261,3.99704033 18.9893509,3.99704033 19.8461538,4.49171576 L29.8674521,10.277515 C30.7242551,10.7721904 31.2520675,11.6863883 31.2520675,12.6757392 Z"
        id="path-10"
      ></path>
      <filter x="1.6%" y="-3.6%" width="96.8%" height="107.3%" filterUnits="objectBoundingBox" id="filter-11">
        <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
        <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
        <feColorMatrix
          values="0 0 0 0 1   0 0 0 0 0.48627451   0 0 0 0 0.223529412  0 0 0 1 0"
          type="matrix"
          in="shadowBlurOuter1"
        ></feColorMatrix>
      </filter>
      <text
        id="text-12"
        fontFamily="DingTalk-JinBuTi, DingTalk JinBuTi"
        fontSize="18.4615385"
        fontStyle="italic"
        fontWeight="normal"
        fill="#EA6444"
      >
        <tspan x="12.9230769" y="23.0769231">
          3
        </tspan>
      </text>
      <filter x="-27.3%" y="-11.1%" width="154.5%" height="122.2%" filterUnits="objectBoundingBox" id="filter-13">
        <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
        <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
        <feColorMatrix
          values="0 0 0 0 1   0 0 0 0 0.57254902   0 0 0 0 0.356862745  0 0 0 1 0"
          type="matrix"
          in="shadowBlurOuter1"
        ></feColorMatrix>
      </filter>
    </defs>
    <g id="0.2.素材" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g id="素材切图" transform="translate(-545.473377, -755.428401)">
        <g id="3" transform="translate(545.000000, 756.000000)">
          <path d="M0,0 L37,0 L37,48 L0,48 L0,0 Z" id="路径"></path>
          <use
            id="路径"
            stroke="url(#linearGradient-2)"
            mask="url(#mask-4)"
            strokeWidth="0.923076923"
            fill="url(#linearGradient-1)"
            strokeDasharray="0,0"
          ></use>
          <path
            d="M12.7202029,28.1538462 L24.202874,28.1538462 C25.2224767,28.1538462 26.0490279,28.9803974 26.0490279,30 L26.0490279,42.2757255 C26.0490279,42.7855268 25.6357523,43.1988024 25.125951,43.1988024 C24.9687928,43.1988024 24.8142369,43.1586773 24.6769259,43.0822287 L18.3629011,39.5668615 L18.3629011,39.5668615 L12.2550962,43.0569936 C11.8124634,43.3099236 11.2485983,43.156139 10.9956684,42.7135062 C10.91597,42.5740324 10.874049,42.4161746 10.874049,42.255536 L10.874049,30 C10.874049,28.9803974 11.7006003,28.1538462 12.7202029,28.1538462 Z"
            id="路径"
            fill="url(#linearGradient-5)"
          ></path>
          <g id="路径">
            <use fill="black" fillOpacity="1" filter="url(#filter-8)"></use>
            <use fill="url(#linearGradient-6)" fillRule="evenodd"></use>
          </g>
          <g id="路径">
            <use fill="black" fillOpacity="1" filter="url(#filter-11)"></use>
            <use fill="url(#linearGradient-9)" fillRule="evenodd"></use>
          </g>
          <g fill="#EA6444" fillOpacity="1">
            <use filter="url(#filter-13)"></use>
            <use></use>
          </g>
        </g>
      </g>
    </g>
  </svg>
);
export const noLove = (
  <svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1">
    <g id="0.2.素材" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g id="素材切图" transform="translate(-545.000000, -602.000000)">
        <g id="Love" transform="translate(545.000000, 602.000000)">
          <g
            id="Icon"
            transform="translate(2.499593, 3.907877)"
            stroke="#121212"
            strokeDasharray="0,0"
            strokeLinejoin="round"
          >
            <path
              d="M13.8693317,1.13068291 C12.361754,-0.376894486 9.91748917,-0.376894288 8.40991177,1.13068341 L7.57243074,1.96816463 C7.53243297,2.00816241 7.46758368,2.0081624 7.42758589,1.96816463 L6.59010408,1.13068311 C5.08252618,-0.376894586 2.6382604,-0.376893592 1.1306831,1.13068401 C-0.376894598,2.6382619 -0.3768943,5.08252679 1.1306836,6.59010449 L7.13789699,12.5973169 C7.33788591,12.7973058 7.66213236,12.7973058 7.86212124,12.5973169 L13.8693332,6.59010369 C15.3769106,5.082526 15.3769094,2.63826031 13.8693317,1.13068291 Z"
              id="路径"
            ></path>
          </g>
        </g>
      </g>
    </g>
  </svg>
);
export const love = (
  <svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <g id="0.2.素材" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g id="素材切图" transform="translate(-572.000000, -602.000000)">
        <g id="Love" transform="translate(572.000000, 602.000000)">
          <g id="Icon" transform="translate(1.874593, 3.282877)" fill="red" fillRule="nonzero">
            <path
              d="M14.9362739,1.31374118 C13.1846181,-0.437913959 10.3446253,-0.437913661 8.59297035,1.31374168 L8.12500792,1.781704 L7.6570455,1.31374138 C5.90539016,-0.437914158 3.06539622,-0.437912866 1.31374128,1.31374228 C-0.437914059,3.06539801 -0.437913661,5.90539095 1.31374188,7.65704629 L7.32095557,13.6642583 C7.76502209,14.1083256 8.48499614,14.1083256 8.92906266,13.6642583 L14.9362755,7.6570455 C16.6879304,5.90538976 16.6879288,3.06539593 14.9362739,1.31374118 Z"
              id="路径"
            ></path>
          </g>
        </g>
      </g>
    </g>
  </svg>
);

// 聊天菜单id
export const CHAT_MENU_ID = "0";
// 写作菜单id
export const WRITER_MENU_ID = "1";
// 提问菜单id
export const QUESTION_MENU_ID = "2";
// OCR菜单id
export const OCR_MENU_ID = "3";
// 便签菜单id
export const NOTE_MENU_ID = "4";
// 提示词菜单id
export const PROMPT_MENU_ID = "5";
// 我的菜单id
export const LOGIN_MENU_ID = "6";
// 设置菜单id
export const SETUP_MENU_ID = "7";
// 个人知识库id
export const KNOWLEDGE_MENU_ID = "8";
export const TOOL_MENU_ID = "9";
//网页智能填充
export const FORM_FILLING_MENU_ID = "10";
// 菜单列表
export const navButtons: IMenuInfo[] = [
  {
    id: CHAT_MENU_ID,
    title: "聊天",
    selectedImg: "AIChatFilled",
    unselectedImg: "AIChatOutlined",
    permissions: "ai:assistant:chat",
    url: "/chat",
  },
  {
    id: WRITER_MENU_ID,
    title: "写作",
    selectedImg: "WriteFiild",
    unselectedImg: "WriteOutlined",
    permissions: "ai:assistant:writing",
    url: "/write",
  },
  {
    id: QUESTION_MENU_ID,
    title: "提问",
    selectedImg: "QuestionFilled",
    unselectedImg: "QuestionOutlined",
    permissions: "ai:assistant:question",
    url: "/questioning",
  },
  {
    id: OCR_MENU_ID,
    title: "OCR",
    selectedImg: "QRscanFilled",
    unselectedImg: "QRscanOutlined",
    permissions: "ai:assistant:ocr",
    url: "/ocr",
  },
  {
    id: NOTE_MENU_ID,
    title: "便签",
    selectedImg: "NoteFilled",
    unselectedImg: "NoteOutlined",
    permissions: "ai:assistant:note",
    url: "/note",
  },
  {
    id: PROMPT_MENU_ID,
    title: "提示词",
    selectedImg: "PromptFilled",
    unselectedImg: "PromptOutlined",
    permissions: "ai:assistant:cueWord",
    url: "/prompt",
  },
  {
    id: LOGIN_MENU_ID,
    title: "登录",
    selectedImg: "MineFilled",
    unselectedImg: "MineOutlined",
    permissions: "",
    url: "/mine",
  },
  {
    id: "7",
    title: "设置",
    selectedImg: "QRscanFilled",
    unselectedImg: "QRscanOutlined",
    permissions: "",
    url: "/setup",
  },
  {
    id: "8",
    title: "知识库",
    selectedImg: "knowledgeBaseFilled",
    unselectedImg: "knowledgeBaseOutlined",
    permissions: "ai:assistant:knowledge",
    url: "/knowledge",
  },
  {
    id: "9",
    title: "场景",
    selectedImg: "ShoppingFilled",
    unselectedImg: "ShoppingOutlined",
    permissions: "ai:assistant:tools",
    url: "/tool",
  },
  {
    id: FORM_FILLING_MENU_ID,
    title: "快填",
    selectedImg: "formFillingFilled",
    unselectedImg: "formFillingOutlined",
    permissions: "ai:assistant:fill",
    url: "/formFilling",
  },
];
