/** 关联 */
export const RelationSVGIcon = (
  <svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
    <g stroke="none" strokeWidth="1" fill="none">
      <g transform="translate(-627.000000, -216.000000)" className="fill-color">
        <g transform="translate(627.000000, 216.000000)">
          <rect opacity="0" x="0" y="0" width="20" height="20"></rect>
          <path d="M16.0000126,8.5 C17.1046931,8.5 18,7.6046875 18,6.5 C18,5.396875 17.1031306,4.5 16.0000126,4.5 C14.895332,4.5 14.0000251,5.3953125 14.0000251,6.5 C14.0000251,6.79375 14.0640872,7.0734375 14.1765865,7.325 C14.2187737,7.41875 14.1984614,7.5296875 14.1250243,7.603125 L13.7469017,7.98125 C13.6578398,8.0703125 13.5140907,8.0796875 13.4156538,8 C12.820345,7.5296875 12.0672248,7.25 11.2500424,7.25 C10.9562942,7.25 10.6703585,7.2859375 10.3969227,7.3546875 C10.2891109,7.38125 10.1766116,7.3328125 10.120362,7.2375 L10.0172376,7.059375 C9.96411297,6.96875 9.9750504,6.8546875 10.0422375,6.7734375 C10.3328607,6.4203125 10.5062971,5.9671875 10.5000499,5.4734375 C10.4859847,4.390625 9.59849026,3.509375 8.51568456,3.49992628 C7.41256649,3.490625 6.51413464,4.3765625 6.50007222,5.4734375 C6.48600981,6.559375 7.38287918,7.4828125 8.46880986,7.4984375 C8.61568394,7.5 8.75787054,7.4875 8.89693217,7.459375 C9.0031815,7.4375 9.10943084,7.4859375 9.16411799,7.5796875 L9.19849278,7.6390625 C9.26099238,7.7484375 9.23286756,7.8875 9.13286819,7.9625 C8.47037235,8.4671875 7.99225035,9.203125 7.82037643,10.05 C7.79693908,10.165625 7.69381473,10.2484375 7.57506547,10.2484375 L6.10476221,10.2484375 C6.00163785,10.2484375 5.90788844,10.1859375 5.87195117,10.0890625 C5.58914044,9.3390625 4.86726998,8.8046875 4.0204003,8.7953125 C2.91728222,8.7859375 2.00791294,9.68125 2.00004992,10.7828125 C1.99228803,11.89375 2.89071989,12.796875 4.00008792,12.796875 C4.88601986,12.796875 5.63757764,12.2203125 5.90007599,11.4234375 C5.93445078,11.3203125 6.02976268,11.25 6.1375745,11.25 L7.57506547,11.25 C7.69381473,11.25 7.79693908,11.334375 7.82037643,11.4515625 C7.968813,12.18125 8.34381064,12.8265625 8.86724486,13.3140625 C8.95161933,13.3921875 8.97036921,13.5203125 8.91255707,13.6203125 L8.5734967,14.2078125 C8.51568456,14.30625 8.40006029,14.3546875 8.28912349,14.3234375 C8.11568708,14.275 7.93443822,14.25 7.7453769,14.25 C6.65632124,14.2515625 5.76101436,15.1375 5.74851444,16.228125 C5.73601452,17.3484375 6.64538381,18.259375 7.76568927,18.25 C8.85161995,18.240625 9.73911438,17.3546875 9.74848932,16.26875 C9.75317679,15.83125 9.61567766,15.4265625 9.38130413,15.0953125 C9.32505448,15.015625 9.32036701,14.9109375 9.36880421,14.8265625 L9.75786426,14.153125 C9.81880138,14.0484375 9.94536309,14.0015625 10.0594249,14.04375 C10.429735,14.178125 10.8297325,14.25 11.2469174,14.25 C11.7922265,14.25 12.3078482,14.125 12.7672204,13.903125 C12.8859696,13.8453125 13.0281562,13.8890625 13.0937808,14.003125 L13.6719022,15.003125 C13.2609673,15.3703125 13.0000314,15.9046875 13.0000314,16.5 C13.0000314,17.6078125 13.9062757,18.5078125 15.0140813,18.5000505 C16.1031369,18.4921875 16.9859439,17.6140625 17.0000063,16.5265625 C17.0140687,15.43125 16.1093869,14.50625 15.0140813,14.5 C14.9172069,14.5 14.821895,14.50625 14.7281455,14.51875 C14.6265837,14.5328125 14.5281468,14.484375 14.4765846,14.396875 L13.8640885,13.3359375 C13.8109638,13.24375 13.8219012,13.128125 13.8906508,13.0484375 C14.4250225,12.434375 14.7484579,11.63125 14.7484579,10.7515625 C14.7484579,10.14375 14.5937714,9.571875 14.3203356,9.0734375 C14.2672109,8.9765625 14.2859608,8.85625 14.3640853,8.778125 L14.8078325,8.334375 C14.8843946,8.2578125 15.0015813,8.240625 15.1000182,8.2890625 C15.3718915,8.4234375 15.6765771,8.5 16.0000126,8.5 L16.0000126,8.5 Z M16.0000188,5.5 C16.5515716,5.5 17.0000063,5.9484375 17.0000063,6.5 C17.0000063,7.0515625 16.5515716,7.5 16.0000188,7.5 C15.4484535,7.5 15.0000188,7.0515625 15.0000188,6.5 C15.0000188,5.9484375 15.4484535,5.5 16.0000188,5.5 Z M4.0000942,11.796875 C3.44852889,11.796875 3.0000942,11.3484375 3.0000942,10.796875 C3.0000942,10.2453125 3.44852889,9.796875 4.0000942,9.796875 C4.55164696,9.796875 5.00008164,10.2453125 5.00008164,10.796875 C5.00008164,11.3484375 4.55164696,11.796875 4.0000942,11.796875 Z M7.75007065,17.25 C7.19850534,17.25 6.75007065,16.8015625 6.75007065,16.25 C6.75007065,15.6984375 7.19850534,15.25 7.75007065,15.25 C8.30162341,15.25 8.75005809,15.6984375 8.75005809,16.25 C8.75005809,16.8015625 8.30162341,17.25 7.75007065,17.25 Z M15.0000251,15.5 C15.5515779,15.5 16.0000126,15.9484375 16.0000126,16.5 C16.0000126,17.0515625 15.5515779,17.5 15.0000251,17.5 C14.4484598,17.5 14.0000251,17.0515625 14.0000251,16.5 C14.0000251,15.9484375 14.4484598,15.5 15.0000251,15.5 Z M8.50006594,6.5 C7.94850063,6.5 7.50006594,6.0515625 7.50006594,5.5 C7.50006594,4.9484375 7.94850063,4.5 8.50006594,4.5 C9.0516187,4.5 9.50005338,4.9484375 9.50005338,5.5 C9.50005338,6.0515625 9.0516187,6.5 8.50006594,6.5 Z M13.0172188,12.5171875 C12.5453468,12.990625 11.9172257,13.25 11.2500424,13.25 C10.5828591,13.25 9.95473803,12.990625 9.48286599,12.5171875 C9.00943146,12.0453125 8.75005809,11.4171875 8.75005809,10.75 C8.75005809,10.0828125 9.00943146,9.4546875 9.48286599,8.9828125 C9.95473803,8.509375 10.5828591,8.25 11.2500424,8.25 C11.9172257,8.25 12.5453468,8.509375 13.0172188,8.9828125 C13.4906533,9.4546875 13.7500267,10.0828125 13.7500267,10.75 C13.7500267,11.4171875 13.4906533,12.0453125 13.0172188,12.5171875 Z"></path>
        </g>
      </g>
    </g>
  </svg>
);

/** 评论 */
export const CommentSVGIcon = (
  <svg width="20" height="20" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <g stroke="none" strokeWidth="1" fill="none">
      <g transform="translate(-657.000000, -216.000000)">
        <g transform="translate(657.000000, 216.000000)">
          <g transform="translate(3.291667, 3.499999)" strokeDasharray="0,0" strokeLinejoin="round">
            <path
              d="M0,1.54355828 C0,0.691074733 0.67762306,0 1.51351336,0 L12.4864866,0 C13.322377,0 14,0.691074641 14,1.54355828 L14,9.6472381 C14,10.4997216 13.3223754,11.1907962 12.4864852,11.1907962 L9.45857873,11.1907962 C9.33997699,11.1907962 9.22823741,11.2475104 9.15672362,11.3440044 L7.30185439,13.8467926 C7.15045998,14.0510701 6.84953857,14.0510686 6.69814417,13.8467911 L4.84327493,11.3440044 C4.77176115,11.2475104 4.66002157,11.1907962 4.54141982,11.1907962 L1.51351336,11.1907962 C0.67762306,11.1907962 0,10.4997216 0,9.6472381 L0,1.54355828 Z"
              className="stroke-color"
            ></path>
            <line x1="2.08024089" y1="4.10000025" x2="8.88857422" y2="4.00000025" stroke="#000000"></line>
          </g>
        </g>
      </g>
    </g>
  </svg>
);

/** 删除 */
export const DeleteSVGIcon = ({ width = 20, height = 20 }) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
    <g stroke="none" strokeWidth="1" fill="none">
      <g transform="translate(-657.000000, -356.000000)">
        <g transform="translate(657.000000, 356.000000)">
          <g transform="translate(3.333740, 2.499669)" strokeDasharray="0,0" className="stroke-color">
            <path d="M11.2325867,14.2390848 C11.1953077,14.6698639 10.8347451,15.0005715 10.402356,15.0005715 L2.93064018,15.0005715 C2.49825105,15.0005715 2.13768811,14.6698639 2.10040917,14.2390848 L1.04125977,2.00002046 L12.2917366,2.00002046 L11.2325867,14.2390848 Z"></path>
            <line x1="0" y1="2.10002031" x2="13.3333333" y2="2.00002031" strokeWidth="1.25"></line>
            <path
              d="M3.69820241,0.532845557 L4.08892613,0.199427816 C4.23976681,0.0707104678 4.43156368,0 4.629859,0 L8.70310144,0 C8.90139656,0 9.09319318,0.0707106168 9.24403426,0.199427965 L9.6347578,0.532845954 C10.2239704,1.03564064 9.86840722,2.00008492 9.09382737,2.00008492 L4.23913426,2.00008492 C3.46455462,2.00008492 3.10898994,1.03564024 3.69820241,0.532845557 Z"
              strokeWidth="1.25"
            ></path>
          </g>
        </g>
      </g>
    </g>
  </svg>
);
/** 复制 */
export const CopySVGIcon = ({ width = 20, height = 20 }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width || 20}
    height={height || 20}
    fill="none"
    version="1.1"
    viewBox="0 0 9.625 12.25"
  >
    <g>
      <path
        d="M9.1875,0C9.1875,0,1.85937,0,1.85937,0C1.79922,0,1.75,0.0492187,1.75,0.109375C1.75,0.109375,1.75,0.875,1.75,0.875C1.75,0.935156,1.79922,0.984375,1.85937,0.984375C1.85937,0.984375,8.64062,0.984375,8.64062,0.984375C8.64062,0.984375,8.64062,10.3906,8.64062,10.3906C8.64062,10.4508,8.68984,10.5,8.75,10.5C8.75,10.5,9.51562,10.5,9.51562,10.5C9.57578,10.5,9.625,10.4508,9.625,10.3906C9.625,10.3906,9.625,0.4375,9.625,0.4375C9.625,0.195508,9.42949,0,9.1875,0ZM7.4375,1.75C7.4375,1.75,0.4375,1.75,0.4375,1.75C0.195508,1.75,0,1.94551,0,2.1875C0,2.1875,0,9.44316,0,9.44316C0,9.55937,0.0464843,9.67012,0.128516,9.75215C0.128516,9.75215,2.49785,12.1215,2.49785,12.1215C2.52793,12.1516,2.56211,12.1762,2.59902,12.1967C2.59902,12.1967,2.59902,12.2227,2.59902,12.2227C2.59902,12.2227,2.65645,12.2227,2.65645,12.2227C2.7043,12.2404,2.75488,12.25,2.80684,12.25C2.80684,12.25,7.4375,12.25,7.4375,12.25C7.67949,12.25,7.875,12.0545,7.875,11.8125C7.875,11.8125,7.875,2.1875,7.875,2.1875C7.875,1.94551,7.67949,1.75,7.4375,1.75ZM2.59766,10.8309C2.59766,10.8309,1.42051,9.65234,1.42051,9.65234C1.42051,9.65234,2.59766,9.65234,2.59766,9.65234C2.59766,9.65234,2.59766,10.8309,2.59766,10.8309ZM6.89062,11.2656C6.89062,11.2656,3.47266,11.2656,3.47266,11.2656C3.47266,11.2656,3.47266,9.32422,3.47266,9.32422C3.47266,9.02207,3.22793,8.77734,2.92578,8.77734C2.92578,8.77734,0.984375,8.77734,0.984375,8.77734C0.984375,8.77734,0.984375,2.73438,0.984375,2.73438C0.984375,2.73438,6.89062,2.73438,6.89062,2.73438C6.89062,2.73438,6.89062,11.2656,6.89062,11.2656Z"
        fill="#000000"
        fillOpacity="0.8799999952316284"
      />
    </g>
  </svg>
);
/** 查看详情 */
export const InfoSVGIcon = ({ width = 20, height = 20 }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    version="1.1"
    width={width || 20}
    height={height || 20}
    viewBox="0 0 12.250750541687012 11.293580055236816"
  >
    <g>
      <path
        d="M8.76349,1.66858C9.77111,1.94065,10.6899,2.54358,11.3475,3.44592C12.5697,5.12893,12.5383,7.38889,11.3338,9.02405L11.3338,10.8561C11.3338,11.0981,11.1383,11.2936,10.8963,11.2936C10.8498,11.2936,10.8047,11.2854,10.7609,11.2717L9.02325,10.7057C7.559,11.1897,5.96212,10.9231,4.74396,10.0358L4.75079,10.0303C4.28048,10.0043,3.80743,9.91135,3.34943,9.74866L1.61857,10.2942C1.38888,10.3666,1.14279,10.2395,1.07033,10.0084C1.05665,9.96741,1.04982,9.92366,1.04982,9.87991L1.03888,8.06702C-0.303698,6.32932,-0.359753,3.88342,0.956849,2.06506C2.59064,-0.173021,5.73243,-0.66931,7.97052,0.957643C8.26583,1.17092,8.5297,1.41018,8.76349,1.66995L8.76349,1.66858ZM3.37677,8.7096L3.54083,8.77796C5.24298,9.44104,7.20353,8.88596,8.29865,7.38342C9.60431,5.58694,9.20372,3.06448,7.3963,1.75061C5.59435,0.446315,3.06779,0.845534,1.74982,2.65296C0.667005,4.14456,0.743567,6.17756,1.90021,7.5885L2.00958,7.71155L2.02325,9.13342L3.37677,8.7096ZM10.3358,8.68225L10.4451,8.54553C11.491,7.24944,11.5471,5.39553,10.5545,4.03381C10.3098,3.69338,10.0213,3.41174,9.70685,3.18616L9.69864,3.18069C10.3057,4.72288,10.1361,6.53167,9.09161,7.97131C8.43126,8.87639,7.51525,9.5053,6.5049,9.81565L6.50763,9.81702C7.26368,10.0289,8.0799,10.0057,8.84552,9.72131L9.00958,9.65296L10.3494,10.1041L10.3358,8.68225ZM3.13034,4.48546C2.81452,4.48546,2.56979,4.73019,2.56979,5.03234C2.56979,5.33448,2.81452,5.57921,3.13034,5.57921C3.41882,5.57921,3.66354,5.33448,3.66354,5.03234C3.66354,4.73019,3.41882,4.48546,3.13034,4.48546ZM5.0444,4.48546C4.72858,4.48546,4.48386,4.73019,4.48386,5.03234C4.48386,5.33448,4.72858,5.57921,5.0444,5.57921C5.33288,5.57921,5.57761,5.33448,5.57761,5.03234C5.57761,4.73019,5.33288,4.48546,5.0444,4.48546ZM6.95847,4.48546C6.64265,4.48546,6.39792,4.73019,6.39792,5.03234C6.39792,5.33448,6.64265,5.57921,6.95847,5.57921C7.24694,5.57921,7.49167,5.33448,7.49167,5.03234C7.49167,4.73019,7.24694,4.48546,6.95847,4.48546Z"
        fillRule="evenodd"
        fill="#000000"
        fillOpacity="0.8799999952316284"
      />
    </g>
  </svg>
);
/** 换肤 */
export const ThemeSVGIcon = ({ width = 20, height = 20 }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width || 20}
    height={height || 20}
    fill="none"
    version="1.1"
    viewBox="0 0 10.9921875 10.554688453674316"
  >
    <g>
      <path
        d="M10.3906,7.57367e-7C10.3906,7.57367e-7,7.57148,7.57367e-7,7.57148,7.57367e-7C7.33359,7.57367e-7,7.12168,0.162696,7.06562,0.400587C6.89473,1.12246,6.24805,1.64063,5.49609,1.64063C4.74414,1.64063,4.09746,1.12246,3.92656,0.400587C3.8999,0.286485,3.83539,0.184776,3.74353,0.112033C3.65167,0.0392896,3.53788,-0.000199427,3.4207,7.57367e-7C3.4207,7.57367e-7,0.601562,7.57367e-7,0.601562,7.57367e-7C0.442018,7.57367e-7,0.289008,0.0633794,0.176194,0.176194C0.0633787,0.289009,0,0.442019,0,0.601563C0,0.601563,0,4.04688,0,4.04688C0,4.20642,0.0633787,4.35943,0.176194,4.47224C0.289008,4.58506,0.442018,4.64844,0.601562,4.64844C0.601562,4.64844,1.62695,4.64844,1.62695,4.64844C1.62695,4.64844,1.62695,9.95313,1.62695,9.95313C1.62695,10.1127,1.69033,10.2657,1.80315,10.3785C1.91596,10.4913,2.06897,10.5547,2.22852,10.5547C2.22852,10.5547,8.76367,10.5547,8.76367,10.5547C8.92322,10.5547,9.07623,10.4913,9.18904,10.3785C9.30186,10.2657,9.36523,10.1127,9.36523,9.95313C9.36523,9.95313,9.36523,4.64844,9.36523,4.64844C9.36523,4.64844,10.3906,4.64844,10.3906,4.64844C10.5502,4.64844,10.7032,4.58506,10.816,4.47224C10.9288,4.35943,10.9922,4.20642,10.9922,4.04688C10.9922,4.04688,10.9922,0.601563,10.9922,0.601563C10.9922,0.442019,10.9288,0.289009,10.816,0.176194C10.7032,0.0633794,10.5502,7.57367e-7,10.3906,7.57367e-7C10.3906,7.57367e-7,10.3906,7.57367e-7,10.3906,7.57367e-7ZM10.0078,3.66406C10.0078,3.66406,8.38086,3.66406,8.38086,3.66406C8.38086,3.66406,8.38086,9.57031,8.38086,9.57031C8.38086,9.57031,2.61133,9.57031,2.61133,9.57031C2.61133,9.57031,2.61133,3.66406,2.61133,3.66406C2.61133,3.66406,0.984375,3.66406,0.984375,3.66406C0.984375,3.66406,0.984375,0.984376,0.984375,0.984376C0.984375,0.984376,3.08027,0.984376,3.08027,0.984376C3.46582,1.95781,4.41328,2.625,5.49609,2.625C6.57891,2.625,7.52637,1.95781,7.91191,0.984376C7.91191,0.984376,10.0078,0.984376,10.0078,0.984376C10.0078,0.984376,10.0078,3.66406,10.0078,3.66406Z"
        fill="#000000"
        fillOpacity="0.8799999952316284"
      />
    </g>
  </svg>
);
/** 删除 */
export const DelSVGIcon = ({ width = 20, height = 20 }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    version="1.1"
    width={width || 20}
    height={height || 20}
    viewBox="0 0 10.5 10.93754768371582"
  >
    <g>
      <path
        d="M3.17188,0.984379C3.17188,0.984379,3.0625,0.984379,3.0625,0.984379C3.12266,0.984379,3.17188,0.93516,3.17188,0.875004C3.17188,0.875004,3.17188,0.984379,3.17188,0.984379C3.17188,0.984379,7.32812,0.984379,7.32812,0.984379C7.32812,0.984379,7.32812,0.875004,7.32812,0.875004C7.32812,0.93516,7.37734,0.984379,7.4375,0.984379C7.4375,0.984379,7.32812,0.984379,7.32812,0.984379C7.32812,0.984379,7.32812,1.96876,7.32812,1.96876C7.32812,1.96876,8.3125,1.96876,8.3125,1.96876C8.3125,1.96876,8.3125,0.875004,8.3125,0.875004C8.3125,0.392385,7.92012,0,7.4375,0C7.4375,0,3.0625,0,3.0625,0C2.57988,0,2.1875,0.392385,2.1875,0.875004C2.1875,0.875004,2.1875,1.96876,2.1875,1.96876C2.1875,1.96876,3.17188,1.96876,3.17188,1.96876C3.17188,1.96876,3.17188,0.984379,3.17188,0.984379ZM10.0625,1.96876C10.0625,1.96876,0.4375,1.96876,0.4375,1.96876C0.195508,1.96876,0,2.16427,0,2.40626C0,2.40626,0,2.84376,0,2.84376C0,2.90392,0.0492187,2.95314,0.109375,2.95314C0.109375,2.95314,0.935156,2.95314,0.935156,2.95314C0.935156,2.95314,1.27285,10.1036,1.27285,10.1036C1.29473,10.5698,1.68027,10.9375,2.14648,10.9375C2.14648,10.9375,8.35352,10.9375,8.35352,10.9375C8.82109,10.9375,9.20527,10.5711,9.22715,10.1036C9.22715,10.1036,9.56484,2.95314,9.56484,2.95314C9.56484,2.95314,10.3906,2.95314,10.3906,2.95314C10.4508,2.95314,10.5,2.90392,10.5,2.84376C10.5,2.84376,10.5,2.40626,10.5,2.40626C10.5,2.16427,10.3045,1.96876,10.0625,1.96876ZM8.24824,9.95317C8.24824,9.95317,2.25176,9.95317,2.25176,9.95317C2.25176,9.95317,1.9209,2.95314,1.9209,2.95314C1.9209,2.95314,8.5791,2.95314,8.5791,2.95314C8.5791,2.95314,8.24824,9.95317,8.24824,9.95317Z"
        fill="#000000"
        fillOpacity="0.8799999952316284"
      />
    </g>
  </svg>
);

/** 协作 */
export const CooperationSVGIcon = ({ width = 20, height = 20 }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    version="1.1"
    width={width || 20}
    height={height || 20}
    viewBox="0 0 9.84375 10.71875"
  >
    <g>
      <path
        d="M8.20312,7.4375C7.81348,7.4375,7.45391,7.57422,7.17227,7.80254C7.17227,7.80254,4.33945,5.75312,4.33945,5.75312C4.38689,5.49277,4.38689,5.22598,4.33945,4.96563C4.33945,4.96563,7.17227,2.91621,7.17227,2.91621C7.45391,3.14453,7.81348,3.28125,8.20312,3.28125C9.1082,3.28125,9.84375,2.5457,9.84375,1.64062C9.84375,0.735547,9.1082,0,8.20312,0C7.29805,0,6.5625,0.735547,6.5625,1.64062C6.5625,1.79922,6.58437,1.95098,6.62676,2.0959C6.62676,2.0959,3.93613,4.04414,3.93613,4.04414C3.53691,3.51504,2.90254,3.17188,2.1875,3.17188C0.978906,3.17188,0,4.15078,0,5.35938C0,6.56797,0.978906,7.54687,2.1875,7.54687C2.90254,7.54687,3.53691,7.20371,3.93613,6.67461C3.93613,6.67461,6.62676,8.62285,6.62676,8.62285C6.58437,8.76777,6.5625,8.9209,6.5625,9.07812C6.5625,9.9832,7.29805,10.7188,8.20312,10.7188C9.1082,10.7188,9.84375,9.9832,9.84375,9.07812C9.84375,8.17305,9.1082,7.4375,8.20312,7.4375ZM8.20312,0.929687C8.59551,0.929687,8.91406,1.24824,8.91406,1.64062C8.91406,2.03301,8.59551,2.35156,8.20312,2.35156C7.81074,2.35156,7.49219,2.03301,7.49219,1.64062C7.49219,1.24824,7.81074,0.929687,8.20312,0.929687ZM2.1875,6.5625C1.52441,6.5625,0.984375,6.02246,0.984375,5.35938C0.984375,4.69629,1.52441,4.15625,2.1875,4.15625C2.85059,4.15625,3.39062,4.69629,3.39062,5.35938C3.39062,6.02246,2.85059,6.5625,2.1875,6.5625ZM8.20312,9.78906C7.81074,9.78906,7.49219,9.47051,7.49219,9.07812C7.49219,8.68574,7.81074,8.36719,8.20312,8.36719C8.59551,8.36719,8.91406,8.68574,8.91406,9.07812C8.91406,9.47051,8.59551,9.78906,8.20312,9.78906Z"
        fill="#000000"
        fillOpacity="0.8799999952316284"
      />
    </g>
  </svg>
);

/** 关联 */
export const LinkSVGIcon = (
  <svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
    <g stroke="none" strokeWidth="1" fill="none">
      <g transform="translate(-657.000000, -300.000000)">
        <g transform="translate(657.000000, 300.000000)">
          <g transform="translate(3.000000, 5.000000)" strokeDasharray="0,0" className="stroke-color">
            <path d="M9.27375509,0.702167468 C10.2419213,-0.234055823 11.8130683,-0.234055823 12.7812345,0.702167468 L12.9510882,0.876155867 C13.0699858,0.983862971 13.1888834,1.1081404 13.2992883,1.22413266 C14.2844399,2.367485 14.2334838,4.07422834 13.1294346,5.15129939 L10.6410776,7.57885181 C10.3183555,7.93511377 9.95316999,8.24994992 9.55401374,8.5150751 C8.41599379,9.18617321 6.90429565,9.0370403 5.91914405,8.07596152 C4.76413873,6.94089435 4.76413873,5.10158841 5.91914405,3.96652124"></path>
            <path d="M8.41314358,5.67251958 C9.38130981,4.72801113 9.38130981,3.20354135 8.41314358,2.25903289 L8.24328986,2.0850445 C8.12439225,1.96905223 8.00549465,1.86134513 7.88659704,1.75363802 C6.91843082,0.958262482 5.48316685,0.850555378 4.40459571,1.496798 C4.03091751,1.75363802 3.68271738,2.05190385 3.37698068,2.39159548 L1.37270674,4.34689368 C1.00752123,4.66172983 0.684799157,5.00970663 0.413033199,5.39910924 C-0.206932893,6.41818415 -0.138991403,7.80180618 0.684799157,8.76288495 C0.803696763,8.88716238 0.92259437,9.00315465 1.04149198,9.11914691 C1.16038958,9.23513918 1.27928719,9.35113145 1.40667748,9.45883855 C2.37484371,10.2293586 3.80161499,10.1796476 4.70183972,9.29313531"></path>
          </g>
        </g>
      </g>
    </g>
  </svg>
);

// 生成
export const GenerateIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    version="1.1"
    width="13.570743560791016"
    height="13.569181442260742"
    viewBox="0 0 13.570743560791016 13.569181442260742"
  >
    <g>
      <path
        d="M13.4323,6.57918C13.4323,6.57918,0.361989,0.0260516,0.361989,0.0260516C0.308864,-0.000510853,0.247927,-0.0067608,0.190114,0.0073017C0.0573017,0.0401142,-0.0255108,0.174489,0.00730166,0.308864C0.00730166,0.308864,1.35418,5.81199,1.35418,5.81199C1.37449,5.8948,1.43543,5.96199,1.51668,5.98855C1.51668,5.98855,3.82449,6.78074,3.82449,6.78074C3.82449,6.78074,1.51824,7.57293,1.51824,7.57293C1.43699,7.60105,1.37605,7.66668,1.3573,7.74949C1.3573,7.74949,0.00730166,13.2604,0.00730166,13.2604C-0.00676084,13.3182,-0.000510767,13.3792,0.0260517,13.4307C0.0869892,13.5542,0.236989,13.6042,0.361989,13.5432C0.361989,13.5432,13.4323,7.02761,13.4323,7.02761C13.4807,7.00418,13.5198,6.96355,13.5448,6.91668C13.6057,6.79168,13.5557,6.64168,13.4323,6.57918ZM1.54793,11.6948C1.54793,11.6948,2.33386,8.4823,2.33386,8.4823C2.33386,8.4823,6.94636,6.89949,6.94636,6.89949C6.9823,6.88699,7.01199,6.85886,7.02449,6.82136C7.04636,6.75574,7.01199,6.68543,6.94636,6.66199C6.94636,6.66199,2.33386,5.08074,2.33386,5.08074C2.33386,5.08074,1.55105,1.88074,1.55105,1.88074C1.55105,1.88074,11.3636,6.80105,11.3636,6.80105C11.3636,6.80105,1.54793,11.6948,1.54793,11.6948C1.54793,11.6948,1.54793,11.6948,1.54793,11.6948Z"
        fill="#000000"
        fillOpacity="0.8799999952316284"
      />
    </g>
  </svg>
);
// 更多
export const MoreSVGIcon = (
  <svg width="13" height="3.33333333" viewBox="0 0 13 3.33333333" xmlns="http://www.w3.org/2000/svg">
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g transform="translate(-784.500000, -391.333618)">
        <g transform="translate(670.000000, 382.000000)">
          <g transform="translate(113.000000, 3.000000)">
            <path d="M0,0 L16,0 L16,16 L0,16 L0,0 Z"></path>
            <g
              transform="translate(1.833333, 6.666951)"
              strokeDasharray="0,0"
              strokeWidth="0.666666667"
              className="stroke-color"
            >
              <path d="M2.66666667,1.33333333 C2.66666667,2.069713 2.069713,2.66666667 1.33333333,2.66666667 C0.596953667,2.66666667 0,2.069713 0,1.33333333 C0,0.596953667 0.596953667,0 1.33333333,0 C2.069713,0 2.66666667,0.596953667 2.66666667,1.33333333 Z"></path>
              <path d="M7.5,1.33333333 C7.5,2.069713 6.90304633,2.66666667 6.16666667,2.66666667 C5.430287,2.66666667 4.83333333,2.069713 4.83333333,1.33333333 C4.83333333,0.596953667 5.430287,0 6.16666667,0 C6.90304633,0 7.5,0.596953667 7.5,1.33333333 Z"></path>
              <path d="M12.3333333,1.33333333 C12.3333333,2.069713 11.7363797,2.66666667 11,2.66666667 C10.2636203,2.66666667 9.66666667,2.069713 9.66666667,1.33333333 C9.66666667,0.596953667 10.2636203,0 11,0 C11.7363797,0 12.3333333,0.596953667 12.3333333,1.33333333 Z"></path>
            </g>
          </g>
        </g>
      </g>
    </g>
  </svg>
);

export const BoldSVGIcon = (
  <svg width="16" height="16" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
    <path
      className="stroke-color"
      d="M875.786667 625.873333a257.08 257.08 0 0 0-175.92-149.44 236.926667 236.926667 0 0 0 92.266666-107.926666 233.526667 233.526667 0 0 0 0-182.346667A236.666667 236.666667 0 0 0 667.173333 61.2 231.853333 231.853333 0 0 0 576 42.666667H149.333333a21.333333 21.333333 0 0 0-21.333333 21.333333v896a21.333333 21.333333 0 0 0 21.333333 21.333333h490.666667a252.986667 252.986667 0 0 0 99.46-20.213333 258.286667 258.286667 0 0 0 136.326667-136.326667 254.806667 254.806667 0 0 0 0-198.92zM170.666667 85.333333h405.333333c105.866667 0 192 86.133333 192 192s-86.133333 192-192 192H170.666667z m469.333333 853.333334H170.666667V512h469.333333c117.633333 0 213.333333 95.7 213.333333 213.333333s-95.7 213.333333-213.333333 213.333334z"
    />
  </svg>
);

export const ItalicSVGIcon = (
  <svg width="16" height="16" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
    <path
      className="stroke-color"
      d="M704 42.666667H490.666667a21.333333 21.333333 0 0 0 0 42.666666h80.886666L409.013333 938.666667H320a21.333333 21.333333 0 0 0 0 42.666666h213.333333a21.333333 21.333333 0 0 0 0-42.666666H452.446667L614.986667 85.333333H704a21.333333 21.333333 0 0 0 0-42.666666z"
    />
  </svg>
);

export const UnderlineSVGIcon = (
  <svg width="16" height="16" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
    <path
      className="stroke-color"
      d="M917.333333 981.333333H106.666667a21.333333 21.333333 0 0 1 0-42.666666h810.666666a21.333333 21.333333 0 0 1 0 42.666666z m-405.333333-128a383.7 383.7 0 0 1-384-384V64a21.333333 21.333333 0 0 1 42.666667 0v405.333333c0 188.213333 153.12 341.333333 341.333333 341.333334s341.333333-153.12 341.333333-341.333334V64a21.333333 21.333333 0 0 1 42.666667 0v405.333333a383.7 383.7 0 0 1-384 384z"
    />
  </svg>
);

export const StrikethroughSVGIcon = (
  <svg width="16" height="16" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
    <path
      className="stroke-color"
      d="M512 981.333333c-94.9 0-185.886667-23.02-256.193333-64.806666-72.333333-43-117.12-102.28-126.126667-166.92a21.333333 21.333333 0 0 1 42.26-5.88c7.18 51.54 44.706667 99.88 105.673333 136.12C341.413333 917.78 424.666667 938.666667 512 938.666667c92.806667 0 179.766667-23.193333 244.853333-65.333334 62.22-40.26 96.48-92.826667 96.48-148 0-32.666667-11.76-64.086667-34.953333-93.44a21.333333 21.333333 0 0 1 33.48-26.453333C880.74 642 896 683.446667 896 725.333333c0 35.893333-10.76 70.553333-32 103.013334-19.913333 30.453333-48.166667 57.653333-84 80.833333C708.126667 955.706667 612.933333 981.333333 512 981.333333z m405.333333-469.333333H106.666667a21.333333 21.333333 0 0 1 0-42.666667h165.606666a346.853333 346.853333 0 0 1-41.046666-23.4C199.333333 424.666667 174.193333 399.733333 156.466667 371.8 137.58 342 128 310.246667 128 277.333333s9.58-64.666667 28.466667-94.466666c17.726667-27.933333 42.866667-52.866667 74.76-74.133334C295.133333 66.126667 379.693333 42.666667 469.333333 42.666667a485.793333 485.793333 0 0 1 115.206667 13.586666c35.86 8.76 69.113333 21.506667 98.826667 37.88 29.9 16.48 55.333333 36.14 75.613333 58.433334 21.206667 23.333333 36.133333 48.86 44.36 75.886666a21.333333 21.333333 0 1 1-40.813333 12.426667c-12.84-42.16-48.266667-81.006667-99.753334-109.38C608.74 101.726667 540.046667 85.333333 469.333333 85.333333c-81.333333 0-157.466667 20.92-214.44 58.9C200.58 180.446667 170.666667 227.713333 170.666667 277.333333s29.913333 96.886667 84.226666 133.1C311.866667 448.413333 388 469.333333 469.333333 469.333333h448a21.333333 21.333333 0 0 1 0 42.666667z"
    />
  </svg>
);

export const UlSVGIcon = (
  <svg width="16" height="16" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
    <path
      className="stroke-color"
      d="M917.333333 213.333333H320a21.333333 21.333333 0 0 1 0-42.666666h597.333333a21.333333 21.333333 0 0 1 0 42.666666z m21.333334 320a21.333333 21.333333 0 0 0-21.333334-21.333333H320a21.333333 21.333333 0 0 0 0 42.666667h597.333333a21.333333 21.333333 0 0 0 21.333334-21.333334z m0 341.333334a21.333333 21.333333 0 0 0-21.333334-21.333334H320a21.333333 21.333333 0 0 0 0 42.666667h597.333333a21.333333 21.333333 0 0 0 21.333334-21.333333zM149.333333 128a64 64 0 1 0 64 64 64 64 0 0 0-64-64z m0 341.333333a64 64 0 1 0 64 64 64 64 0 0 0-64-64z m0 341.333334a64 64 0 1 0 64 64 64 64 0 0 0-64-64z"
    />
  </svg>
);

export const OlSVGIcon = (
  <svg width="16" height="16" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
    <path
      className="stroke-color"
      d="M170.666667 981.333333a85.426667 85.426667 0 0 1-85.333334-85.333333 21.333333 21.333333 0 0 1 42.666667 0 42.666667 42.666667 0 1 0 42.666667-42.666667 21.333333 21.333333 0 0 1 0-42.666666 42.666667 42.666667 0 1 0-42.666667-42.666667 21.333333 21.333333 0 0 1-42.666667 0 85.333333 85.333333 0 1 1 141.72 64A85.3 85.3 0 0 1 170.666667 981.333333z m746.666666-85.333333H362.666667a21.333333 21.333333 0 0 1 0-42.666667h554.666666a21.333333 21.333333 0 0 1 0 42.666667zM234.666667 640H106.666667a21.333333 21.333333 0 0 1-21.333334-21.333333c0-21.153333 8.713333-42.966667 25.193334-63.093334 13.113333-16 30.846667-30.56 49.926666-40.966666 4-2.2 8.12-4.32 12.08-6.373334C200.9 493.533333 213.333333 485.413333 213.333333 469.333333a42.666667 42.666667 0 0 0-85.333333 0 21.333333 21.333333 0 0 1-42.666667 0 85.333333 85.333333 0 0 1 170.666667 0c0 19.853333-7.853333 37.06-23.333333 51.146667-11.873333 10.806667-26.42 18.346667-40.486667 25.633333-3.933333 2.04-7.653333 4-11.286667 5.946667-18.84 10.28-37.846667 27.333333-47.1 45.273333H234.666667a21.333333 21.333333 0 0 1 0 42.666667z m682.666666-85.333333H362.666667a21.333333 21.333333 0 0 1 0-42.666667h554.666666a21.333333 21.333333 0 0 1 0 42.666667zM192 298.666667a21.333333 21.333333 0 0 1-21.333333-21.333334V128h-21.333334a21.333333 21.333333 0 0 1 0-42.666667h42.666667a21.333333 21.333333 0 0 1 21.333333 21.333334v170.666666a21.333333 21.333333 0 0 1-21.333333 21.333334z m725.333333-85.333334H362.666667a21.333333 21.333333 0 0 1 0-42.666666h554.666666a21.333333 21.333333 0 0 1 0 42.666666z"
    />
  </svg>
);

export const ClearFormatSVGIcon = (
  <svg width="16" height="16" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
    <path
      className="stroke-color"
      d="M970.105263 970.105263H53.894737V485.052632h916.210526v485.052631zM107.789474 916.210526h808.421052v-377.263158H107.789474v377.263158z"
    />
    <path
      className="stroke-color"
      d="M970.105263 538.947368H53.894737V323.368421h301.810526V53.894737h307.2v269.473684H970.105263v215.578947zM107.789474 485.052632h808.421052V377.263158h-301.810526V107.789474H409.6v269.473684H107.789474v107.789474z"
    />
    <path
      className="stroke-color"
      d="M700.631579 646.736842v323.368421h53.894737v-323.368421h-53.894737z m-323.368421 269.473684h53.894737v-107.789473H377.263158v107.789473z m-107.789474 53.894737h53.894737v-269.473684H269.473684v269.473684z"
    />
  </svg>
);
// 生成-禁用
export const GenerateDisabledIcon = (
  <svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g transform="translate(-629.000000, -330.000000)">
        <g transform="translate(629.000000, 330.000000)">
          <path d="M0,0 L20,0 L20,20 L0,20 L0,0 Z" fill="#FFFFFF" opacity="0"></path>
          <g fill="#40000000" transform="translate(2.248509, 3.098043)">
            <path d="M1.46787316,0.0930472563 C0.605027679,-0.290398811 -0.281934747,0.579942568 0.0851114007,1.44988488 L2.35145728,6.82139 C2.37330437,6.87317054 2.37330457,6.93158309 2.35145688,6.98336443 L0.085111053,12.3548706 C-0.281934946,13.2248131 0.605027878,14.0951538 1.46787266,13.7117068 L14.6484884,7.85428127 C15.4733435,7.48771906 15.4733515,6.31703973 14.6484915,5.95047315 L1.46787316,0.0930472563 Z M5.66813151,6.27726198 C5.32295386,6.27726198 5.04313151,6.55708353 5.04313151,6.90226237 C5.04313151,7.24743923 5.32295347,7.52726237 5.66813151,7.52726237 L8.16813151,7.52726237 C8.51330916,7.52726237 8.79313151,7.24744082 8.79313151,6.90226237 C8.79313151,6.55708472 8.51330996,6.27726198 8.16813151,6.27726198 L5.66813151,6.27726198 Z"></path>
          </g>
        </g>
      </g>
    </g>
  </svg>
);
// 展开
export const NoteExpandIcon = (
  <svg
    width="10.979934px"
    height="6.32125096px"
    viewBox="0 0 10.979934 6.32125096"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g transform="translate(-710.510028, -250.178740)">
        <g transform="translate(716.000000, 254.000000) scale(1, -1) translate(-716.000000, -254.000000) translate(706.000000, 244.000000)">
          <path d="M0,0 L20,0 L20,20 L0,20 L0,0 Z" id="路径"></path>
          <g
            id="Icon"
            transform="translate(9.999995, 10.660635) scale(-1, -1) rotate(0.000005) translate(-9.999995, -10.660635) translate(4.510028, 7.500010)"
            fill="#121212"
            fillRule="nonzero"
          >
            <path
              d="M4.90080005,0.24375 L0.24580005,4.89875 C-0.27919995,5.42375 0.0924667165,6.32125 0.834966717,6.32125 L10.1449667,6.32125 C10.8874667,6.32125 11.2591334,5.42375 10.7341334,4.89875 L6.07913338,0.24375 C5.75413338,-0.08125 5.22580005,-0.08125 4.90080005,0.24375 Z"
              id="路径"
            ></path>
          </g>
        </g>
      </g>
    </g>
  </svg>
);
// 折叠
export const NoteFoldIcon = (
  <svg
    width="10.979934px"
    height="6.32125096px"
    viewBox="0 0 10.979934 6.32125096"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g transform="translate(-753.510028, -250.178740)">
        <g transform="translate(759.000000, 254.000000) scale(1, -1) translate(-759.000000, -254.000000) translate(749.000000, 244.000000)">
          <path d="M0,0 L20,0 L20,20 L0,20 L0,0 Z" id="路径"></path>
          <g
            id="Icon"
            transform="translate(9.999995, 10.660635) scale(-1, -1) rotate(0.000005) translate(-9.999995, -10.660635) translate(4.510028, 7.500010)"
            fill="#121212"
            fillRule="nonzero"
          >
            <path
              d="M4.90080005,0.24375 L0.24580005,4.89875 C-0.27919995,5.42375 0.0924667165,6.32125 0.834966717,6.32125 L10.1449667,6.32125 C10.8874667,6.32125 11.2591334,5.42375 10.7341334,4.89875 L6.07913338,0.24375 C5.75413338,-0.08125 5.22580005,-0.08125 4.90080005,0.24375 Z"
              id="路径"
              transform="translate(5.489967, 3.160625) scale(1, -1) translate(-5.489967, -3.160625) "
            ></path>
          </g>
        </g>
      </g>
    </g>
  </svg>
);
// 复制
export const NoteCopyIcon = ({ width = 20, height = 20 }) => (
  <svg width={width || 20} height={height || 20} viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g transform="translate(-657.000000, -244.000000)" fillRule="nonzero">
        <g transform="translate(657.000000, 244.000000)">
          <rect fill="#000000" opacity="0" x="0" y="0" width="20" height="20"></rect>
          <path
            d="M7.9736883,3 L15.7105368,3 C16.4226837,3 17,3.5773163 17,4.28946323 L17,12.0263117 C17,12.7384654 16.4226904,13.3157922 15.7105368,13.3157922 L7.9736883,13.3157922 C7.26152788,13.3157922 6.6842078,12.7384721 6.6842078,12.0263117 L6.6842078,4.28946323 C6.6842078,3.57730956 7.26153463,3 7.9736883,3 L7.9736883,3 Z M7.9736883,4.10526407 C7.87195512,4.10526407 7.78947187,4.18773005 7.78947187,4.28946323 L7.78947187,12.0263117 C7.78947187,12.0751689 7.80888033,12.1220251 7.84342761,12.1565724 C7.87797489,12.1911197 7.92483106,12.2105281 7.9736883,12.2105281 L15.7105368,12.2105281 C15.81227,12.2105281 15.8947359,12.1280449 15.8947359,12.0263117 L15.8947359,4.28946323 C15.8947359,4.18773679 15.8122632,4.10526407 15.7105368,4.10526407 L7.9736883,4.10526407 Z M12.2105281,14.4210563 C12.2105281,14.115846 12.4579499,13.8684242 12.7631602,13.8684242 C13.0683704,13.8684242 13.3157922,14.115846 13.3157922,14.4210563 L13.3157922,15.7105368 C13.3157922,16.4226904 12.7384654,17 12.0263117,17 L4.28946323,17 C3.5773163,17 3,16.4226837 3,15.7105368 L3,7.9736883 C3,7.26153463 3.57730956,6.6842078 4.28946323,6.6842078 L5.57894373,6.6842078 C5.88415398,6.6842078 6.13157577,6.93162959 6.13157577,7.23683983 C6.13157577,7.54205008 5.88415398,7.78947187 5.57894373,7.78947187 L4.28946323,7.78947187 C4.18773005,7.7894814 4.10526407,7.87195512 4.10526407,7.9736883 L4.10526407,15.7105368 C4.1052736,15.8122632 4.18773679,15.8947264 4.28946323,15.8947359 L12.0263117,15.8947359 C12.1280449,15.8947359 12.2105186,15.81227 12.2105281,15.7105368 L12.2105281,14.4210563 Z"
            fill="#121212"
          ></path>
        </g>
      </g>
    </g>
  </svg>
);
// 关系
export const NoteRelationIcon = (
  <svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g transform="translate(-627.000000, -216.000000)" fill="#000000" fillRule="nonzero">
        <g transform="translate(627.000000, 216.000000)">
          <rect opacity="0" x="0" y="0" width="20" height="20"></rect>
          <path
            d="M16.0000126,8.5 C17.1046931,8.5 18,7.6046875 18,6.5 C18,5.396875 17.1031306,4.5 16.0000126,4.5 C14.895332,4.5 14.0000251,5.3953125 14.0000251,6.5 C14.0000251,6.79375 14.0640872,7.0734375 14.1765865,7.325 C14.2187737,7.41875 14.1984614,7.5296875 14.1250243,7.603125 L13.7469017,7.98125 C13.6578398,8.0703125 13.5140907,8.0796875 13.4156538,8 C12.820345,7.5296875 12.0672248,7.25 11.2500424,7.25 C10.9562942,7.25 10.6703585,7.2859375 10.3969227,7.3546875 C10.2891109,7.38125 10.1766116,7.3328125 10.120362,7.2375 L10.0172376,7.059375 C9.96411297,6.96875 9.9750504,6.8546875 10.0422375,6.7734375 C10.3328607,6.4203125 10.5062971,5.9671875 10.5000499,5.4734375 C10.4859847,4.390625 9.59849026,3.509375 8.51568456,3.49992628 C7.41256649,3.490625 6.51413464,4.3765625 6.50007222,5.4734375 C6.48600981,6.559375 7.38287918,7.4828125 8.46880986,7.4984375 C8.61568394,7.5 8.75787054,7.4875 8.89693217,7.459375 C9.0031815,7.4375 9.10943084,7.4859375 9.16411799,7.5796875 L9.19849278,7.6390625 C9.26099238,7.7484375 9.23286756,7.8875 9.13286819,7.9625 C8.47037235,8.4671875 7.99225035,9.203125 7.82037643,10.05 C7.79693908,10.165625 7.69381473,10.2484375 7.57506547,10.2484375 L6.10476221,10.2484375 C6.00163785,10.2484375 5.90788844,10.1859375 5.87195117,10.0890625 C5.58914044,9.3390625 4.86726998,8.8046875 4.0204003,8.7953125 C2.91728222,8.7859375 2.00791294,9.68125 2.00004992,10.7828125 C1.99228803,11.89375 2.89071989,12.796875 4.00008792,12.796875 C4.88601986,12.796875 5.63757764,12.2203125 5.90007599,11.4234375 C5.93445078,11.3203125 6.02976268,11.25 6.1375745,11.25 L7.57506547,11.25 C7.69381473,11.25 7.79693908,11.334375 7.82037643,11.4515625 C7.968813,12.18125 8.34381064,12.8265625 8.86724486,13.3140625 C8.95161933,13.3921875 8.97036921,13.5203125 8.91255707,13.6203125 L8.5734967,14.2078125 C8.51568456,14.30625 8.40006029,14.3546875 8.28912349,14.3234375 C8.11568708,14.275 7.93443822,14.25 7.7453769,14.25 C6.65632124,14.2515625 5.76101436,15.1375 5.74851444,16.228125 C5.73601452,17.3484375 6.64538381,18.259375 7.76568927,18.25 C8.85161995,18.240625 9.73911438,17.3546875 9.74848932,16.26875 C9.75317679,15.83125 9.61567766,15.4265625 9.38130413,15.0953125 C9.32505448,15.015625 9.32036701,14.9109375 9.36880421,14.8265625 L9.75786426,14.153125 C9.81880138,14.0484375 9.94536309,14.0015625 10.0594249,14.04375 C10.429735,14.178125 10.8297325,14.25 11.2469174,14.25 C11.7922265,14.25 12.3078482,14.125 12.7672204,13.903125 C12.8859696,13.8453125 13.0281562,13.8890625 13.0937808,14.003125 L13.6719022,15.003125 C13.2609673,15.3703125 13.0000314,15.9046875 13.0000314,16.5 C13.0000314,17.6078125 13.9062757,18.5078125 15.0140813,18.5000505 C16.1031369,18.4921875 16.9859439,17.6140625 17.0000063,16.5265625 C17.0140687,15.43125 16.1093869,14.50625 15.0140813,14.5 C14.9172069,14.5 14.821895,14.50625 14.7281455,14.51875 C14.6265837,14.5328125 14.5281468,14.484375 14.4765846,14.396875 L13.8640885,13.3359375 C13.8109638,13.24375 13.8219012,13.128125 13.8906508,13.0484375 C14.4250225,12.434375 14.7484579,11.63125 14.7484579,10.7515625 C14.7484579,10.14375 14.5937714,9.571875 14.3203356,9.0734375 C14.2672109,8.9765625 14.2859608,8.85625 14.3640853,8.778125 L14.8078325,8.334375 C14.8843946,8.2578125 15.0015813,8.240625 15.1000182,8.2890625 C15.3718915,8.4234375 15.6765771,8.5 16.0000126,8.5 L16.0000126,8.5 Z M16.0000188,5.5 C16.5515716,5.5 17.0000063,5.9484375 17.0000063,6.5 C17.0000063,7.0515625 16.5515716,7.5 16.0000188,7.5 C15.4484535,7.5 15.0000188,7.0515625 15.0000188,6.5 C15.0000188,5.9484375 15.4484535,5.5 16.0000188,5.5 Z M4.0000942,11.796875 C3.44852889,11.796875 3.0000942,11.3484375 3.0000942,10.796875 C3.0000942,10.2453125 3.44852889,9.796875 4.0000942,9.796875 C4.55164696,9.796875 5.00008164,10.2453125 5.00008164,10.796875 C5.00008164,11.3484375 4.55164696,11.796875 4.0000942,11.796875 Z M7.75007065,17.25 C7.19850534,17.25 6.75007065,16.8015625 6.75007065,16.25 C6.75007065,15.6984375 7.19850534,15.25 7.75007065,15.25 C8.30162341,15.25 8.75005809,15.6984375 8.75005809,16.25 C8.75005809,16.8015625 8.30162341,17.25 7.75007065,17.25 Z M15.0000251,15.5 C15.5515779,15.5 16.0000126,15.9484375 16.0000126,16.5 C16.0000126,17.0515625 15.5515779,17.5 15.0000251,17.5 C14.4484598,17.5 14.0000251,17.0515625 14.0000251,16.5 C14.0000251,15.9484375 14.4484598,15.5 15.0000251,15.5 Z M8.50006594,6.5 C7.94850063,6.5 7.50006594,6.0515625 7.50006594,5.5 C7.50006594,4.9484375 7.94850063,4.5 8.50006594,4.5 C9.0516187,4.5 9.50005338,4.9484375 9.50005338,5.5 C9.50005338,6.0515625 9.0516187,6.5 8.50006594,6.5 Z M13.0172188,12.5171875 C12.5453468,12.990625 11.9172257,13.25 11.2500424,13.25 C10.5828591,13.25 9.95473803,12.990625 9.48286599,12.5171875 C9.00943146,12.0453125 8.75005809,11.4171875 8.75005809,10.75 C8.75005809,10.0828125 9.00943146,9.4546875 9.48286599,8.9828125 C9.95473803,8.509375 10.5828591,8.25 11.2500424,8.25 C11.9172257,8.25 12.5453468,8.509375 13.0172188,8.9828125 C13.4906533,9.4546875 13.7500267,10.0828125 13.7500267,10.75 C13.7500267,11.4171875 13.4906533,12.0453125 13.0172188,12.5171875 Z"
            id="形状"
          ></path>
        </g>
      </g>
    </g>
  </svg>
);
// 重新生成
export const NoteRegenerateIcon = ({ width = 20, height = 20 }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    version="1.1"
    width={width || 20}
    height={height || 20}
    viewBox="0 0 14 14"
  >
    <defs>
      <clipPath id="master_svg0_530_74211/1_11783">
        <rect x="0" y="0" width="14" height="14" rx="0" />
      </clipPath>
    </defs>
    <g clipPath="url(#master_svg0_530_74211/1_11783)">
      <g>
        <path
          d="M10.36605,11.471828359375C11.64575,10.471048359375,12.46875,8.912458359375,12.46875,7.162458359375C12.46875,4.142338359375,10.02836,1.699175069375,7.00823,1.695073504575C3.98399,1.690971939375,1.53125,4.139608359375,1.53125,7.162458359375C1.53125,8.874178359375,2.317386,10.401318359375,3.54923,11.403468359375C3.59708,11.441748359375,3.66681,11.433548359375,3.70509,11.385698359375C3.70509,11.385698359375,4.24376,10.695268359375,4.24376,10.695268359375C4.28068,10.648788359375,4.27247,10.581788359375,4.22736,10.543508359375C4.11661,10.453278359375,4.00997,10.356208359375,3.90743,10.253668359375C3.50769,9.855308359375,3.18916,9.383088359375,2.9695400000000003,8.863238359375C2.7398499999999997,8.325928359375,2.625,7.753078359375,2.625,7.162458359375C2.625,6.571828359375,2.7398499999999997,5.998978359375,2.9681699999999998,5.460308359375C3.1882900000000003,4.939408359374999,3.50411,4.4718283593749995,3.90607,4.069878359375C4.30802,3.667928359375,4.7756,3.3521083593750003,5.2965,3.1319883593749998C5.83654,2.9036683593749997,6.4094,2.788828359375,7.00002,2.788828359375C7.59065,2.788828359375,8.163509999999999,2.9036683593749997,8.70218,3.1319883593749998C9.22308,3.3521083593750003,9.69066,3.667928359375,10.09262,4.069878359375C10.49457,4.4718283593749995,10.81039,4.939408359374999,11.03051,5.460308359375C11.25883,5.998978359375,11.37368,6.571828359375,11.37368,7.162458359375C11.37368,7.753078359375,11.25883,8.325928359375,11.03051,8.864608359375C10.81089,9.384458359375,10.49236,9.856678359375,10.09262,10.255038359375C9.96547,10.382178359375,9.83148,10.501128359375,9.69203,10.610498359375C9.69203,10.610498359375,9.135580000000001,9.898198359375,9.135580000000001,9.898198359375C9.12298,9.881908359375,9.10604,9.869518359375,9.08671,9.862428359375C9.06738,9.855338359375,9.04644,9.853858359375,9.026309999999999,9.858138359375C9.006170000000001,9.862428359375,8.98765,9.872308359375,8.97288,9.886648359375C8.95811,9.900988359375,8.94768,9.919208359375,8.94281,9.939218359375C8.94281,9.939218359375,8.401399999999999,12.156768359375,8.401399999999999,12.156768359375C8.38499,12.225168359375,8.43694,12.292168359375,8.50667,12.292168359375C8.50667,12.292168359375,10.78988,12.303068359375,10.78988,12.303068359375C10.88149,12.303068359375,10.93344,12.197768359375,10.87602,12.126668359375C10.87602,12.126668359375,10.36605,11.471828359375,10.36605,11.471828359375Z"
          fill="#000000"
          fillOpacity="0.44999998807907104"
        />
      </g>
    </g>
  </svg>
);
// 复制
export const CopyIcon = ({ width = 20, height = 20 }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    version="1.1"
    width={width || 20}
    height={height || 20}
    viewBox="0 0 14 14"
  >
    <defs>
      <clipPath id="master_svg0_530_74219/1_11783">
        <rect x="0" y="0" width="14" height="14" rx="0" />
      </clipPath>
    </defs>
    <g clipPath="url(#master_svg0_530_74219/1_11783)">
      <g>
        <path
          d="M11.375,0.875C11.375,0.875,4.04687,0.875,4.04687,0.875C3.98672,0.875,3.9375,0.9242187,3.9375,0.984375C3.9375,0.984375,3.9375,1.75,3.9375,1.75C3.9375,1.810156,3.98672,1.859375,4.04687,1.859375C4.04687,1.859375,10.82812,1.859375,10.82812,1.859375C10.82812,1.859375,10.82812,11.2656,10.82812,11.2656C10.82812,11.3258,10.87734,11.375,10.9375,11.375C10.9375,11.375,11.70312,11.375,11.70312,11.375C11.76328,11.375,11.8125,11.3258,11.8125,11.2656C11.8125,11.2656,11.8125,1.3125,11.8125,1.3125C11.8125,1.070508,11.61699,0.875,11.375,0.875ZM9.625,2.625C9.625,2.625,2.625,2.625,2.625,2.625C2.383008,2.625,2.1875,2.82051,2.1875,3.0625C2.1875,3.0625,2.1875,10.31816,2.1875,10.31816C2.1875,10.43437,2.2339843,10.54512,2.316016,10.62715C2.316016,10.62715,4.68535,12.9965,4.68535,12.9965C4.71543,13.0266,4.7496100000000006,13.0512,4.786519999999999,13.0717C4.786519999999999,13.0717,4.786519999999999,13.0977,4.786519999999999,13.0977C4.786519999999999,13.0977,4.8439499999999995,13.0977,4.8439499999999995,13.0977C4.8918,13.1154,4.94238,13.125,4.994339999999999,13.125C4.994339999999999,13.125,9.625,13.125,9.625,13.125C9.866990000000001,13.125,10.0625,12.9295,10.0625,12.6875C10.0625,12.6875,10.0625,3.0625,10.0625,3.0625C10.0625,2.82051,9.866990000000001,2.625,9.625,2.625ZM4.785159999999999,11.7059C4.785159999999999,11.7059,3.60801,10.52734,3.60801,10.52734C3.60801,10.52734,4.785159999999999,10.52734,4.785159999999999,10.52734C4.785159999999999,10.52734,4.785159999999999,11.7059,4.785159999999999,11.7059ZM9.07812,12.1406C9.07812,12.1406,5.660159999999999,12.1406,5.660159999999999,12.1406C5.660159999999999,12.1406,5.660159999999999,10.19922,5.660159999999999,10.19922C5.660159999999999,9.89707,5.415430000000001,9.65234,5.11328,9.65234C5.11328,9.65234,3.171875,9.65234,3.171875,9.65234C3.171875,9.65234,3.171875,3.60938,3.171875,3.60938C3.171875,3.60938,9.07812,3.60938,9.07812,3.60938C9.07812,3.60938,9.07812,12.1406,9.07812,12.1406Z"
          fill="#000000"
          fillOpacity="0.44999998807907104"
        />
      </g>
    </g>
  </svg>
);
export const ReturnIcon = (
  <svg width="20" height="20" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g transform="translate(-742.000000, -282.000000)">
        <g transform="translate(742.000000, 282.000000)">
          <path d="M0,0 L20,0 L20,20 L0,20 L0,0 Z" fill="#FFFFFF" opacity="0"></path>
          <g transform="translate(6.072642, 3.541667)" className="fill-color">
            <path d="M6.897707,0.213443189 C7.12500891,0.473216076 7.09868512,0.868067691 6.83891218,1.09536896 L1.6057253,5.67440788 C1.13142521,6.08942032 1.13142531,6.82726343 1.60572351,7.24227428 L6.83891218,11.8213153 C7.09868512,12.0486164 7.12500891,12.4434678 6.897707,12.7032407 C6.67040588,12.9630136 6.27555451,12.9893374 6.01578157,12.7620355 L0.782592756,8.18299532 C-0.260866222,7.26996899 -0.260862845,5.64671238 0.782594445,4.73368724 L6.01578157,0.154648162 C6.27555451,-0.0726531578 6.67040588,-0.0463297226 6.897707,0.213443189 Z"></path>
          </g>
        </g>
      </g>
    </g>
  </svg>
);

export const NoteSvgIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    version="1.1"
    width="14.666666984558105"
    height="11.250785827636719"
    viewBox="0 0 14.666666984558105 11.250785827636719"
  >
    <g>
      <path
        d="M6.58622,0L4.63161,0C4.56349,0,4.51109,0.0524024,4.51633,0.115285L4.51633,0.843678L3.69361,0.843678C3.26915,0.843678,2.92854,1.18953,2.92854,1.60875L2.92854,2.30046C2.9233,2.69872,2.8971,3.91446,1.98005,5.97387C1.6342,6.74942,1.03157,7.33109,0.591392,7.75555C0.407984,7.93372,0.250777,8.08568,0.135492,8.22717C-0.016475,8.42106,-0.0426762,8.70403,0.0673688,8.92936C0.166933,9.12849,0.360822,9.25426,0.575672,9.25426L2.92854,9.25426L2.92854,10.4857C2.92854,10.9102,3.27439,11.2508,3.69361,11.2508L13.9016,11.2508C14.3261,11.2508,14.6667,10.9049,14.6667,10.4857L14.6667,1.61399L14.6667,1.57731C14.6667,1.56683,14.6667,1.55111,14.6614,1.53539C14.6247,1.14237,14.2998,0.843678,13.9016,0.843678L6.70151,0.843678L6.70151,0.115285C6.70151,0.0524024,6.6491,0,6.58622,0ZM4.51633,1.89173L4.51633,2.62536C4.51633,2.68824,4.56873,2.74064,4.63161,2.74064L6.58622,2.74064C6.6491,2.74064,6.70151,2.68824,6.70151,2.62536L6.70151,1.89173L13.6029,1.89173C13.6029,1.94413,13.5977,2.00177,13.5924,2.05941L13.5924,2.06465C13.5924,2.09609,13.5872,2.1223,13.5872,2.15374L13.5872,2.1747C13.5872,2.2009,13.5819,2.2271,13.5819,2.2533L13.5819,2.28474C13.5819,2.31094,13.5767,2.33191,13.5767,2.35811C13.5767,2.36859,13.5767,2.37907,13.5715,2.39479C13.5715,2.42099,13.5662,2.44719,13.5662,2.46815C13.5662,2.47863,13.5662,2.48911,13.561,2.50483C13.561,2.53627,13.5557,2.56248,13.5557,2.59392L13.5557,2.62012C13.5505,2.66204,13.5505,2.69872,13.5453,2.74064L13.5453,2.74588C13.54,2.78257,13.54,2.82449,13.5348,2.86117C13.5348,2.87165,13.5295,2.88737,13.5295,2.90309C13.5243,2.92929,13.5243,2.96073,13.519,2.99218C13.519,3.0079,13.5138,3.02362,13.5138,3.03934C13.5086,3.06554,13.5086,3.09174,13.5033,3.12318C13.5033,3.1389,13.4981,3.15986,13.4981,3.17558C13.4928,3.20178,13.4928,3.23323,13.4876,3.25943C13.4876,3.27515,13.4824,3.29611,13.4824,3.31183C13.4771,3.34327,13.4771,3.36947,13.4719,3.40091C13.4719,3.41663,13.4666,3.4376,13.4666,3.45332C13.4614,3.49,13.4562,3.52144,13.4509,3.55812C13.4509,3.5686,13.4457,3.58432,13.4457,3.5948C13.4404,3.64196,13.43,3.69437,13.4247,3.74153C13.4247,3.75201,13.4195,3.76249,13.4195,3.77297C13.4142,3.80965,13.409,3.85157,13.4038,3.88826C13.3985,3.90398,13.3985,3.92494,13.3933,3.9459C13.388,3.97734,13.3828,4.01402,13.3776,4.04546C13.3723,4.06642,13.3723,4.08738,13.3671,4.10835L13.3514,4.20267C13.3461,4.22363,13.3461,4.24459,13.3409,4.26555C13.3356,4.29699,13.3304,4.33368,13.3252,4.36512C13.3199,4.38608,13.3199,4.40704,13.3147,4.428C13.3094,4.46468,13.299,4.50136,13.2937,4.5328C13.2885,4.55377,13.2885,4.57473,13.2832,4.59045C13.2728,4.63761,13.2675,4.68477,13.257,4.72669C13.257,4.73717,13.2518,4.74765,13.2518,4.75813C13.2413,4.81578,13.2308,4.87342,13.2151,4.93106C13.2099,4.94678,13.2099,4.9625,13.2046,4.97822C13.1942,5.02015,13.1889,5.06207,13.1784,5.10399L13.1627,5.16687C13.1522,5.20355,13.147,5.24024,13.1365,5.27692C13.1313,5.29788,13.126,5.32408,13.1208,5.34504C13.1103,5.38172,13.1051,5.4184,13.0946,5.45509C13.0893,5.48129,13.0841,5.50225,13.0789,5.52845C13.0684,5.56513,13.0631,5.60181,13.0527,5.64373C13.0474,5.6647,13.0422,5.6909,13.0369,5.71186L13.0055,5.83762L12.9898,5.90051L12.9426,6.08915C12.5601,7.50402,11.3077,7.9704,11.2553,7.99136L11.2448,7.9966C10.857,8.14333,10.4483,8.21669,10.0343,8.21669L1.62372,8.21669C2.0639,7.77651,2.59316,7.17912,2.93378,6.40881C3.60977,4.88914,3.95038,3.55288,3.97134,2.32142L3.97134,1.89173L4.51633,1.89173ZM3.97658,10.2185L13.6396,10.2185L13.6396,7.17912C13.6186,7.22104,13.5977,7.26296,13.5715,7.30489C13.5662,7.31537,13.5557,7.32585,13.5505,7.33633C13.5295,7.36777,13.5138,7.39921,13.4929,7.43065C13.4876,7.44637,13.4771,7.45685,13.4667,7.47257C13.4509,7.49878,13.43,7.52498,13.4142,7.55118C13.4038,7.5669,13.3933,7.57738,13.3828,7.5931C13.3671,7.6193,13.3461,7.64026,13.3304,7.66646C13.3199,7.67694,13.3094,7.69266,13.299,7.70314L13.2361,7.78175C13.2308,7.78699,13.2204,7.79747,13.2151,7.80795C13.1837,7.83939,13.1575,7.87607,13.126,7.90751C13.126,7.91275,13.1208,7.91799,13.1156,7.92323C13.0946,7.95468,13.0684,7.98088,13.0422,8.00708L12.9164,8.13284C12.8955,8.15381,12.8797,8.17477,12.8588,8.19049L12.8273,8.22193C12.8064,8.24289,12.7854,8.26385,12.7645,8.27957C12.754,8.28481,12.7487,8.29529,12.7383,8.30053C12.6754,8.34769,12.6177,8.4001,12.5601,8.44202C12.5496,8.4525,12.5444,8.45774,12.5339,8.46298L12.471,8.51014C12.4605,8.51538,12.45,8.52586,12.4396,8.5311C12.4186,8.54158,12.4029,8.5573,12.3819,8.56778C12.3714,8.57302,12.361,8.5835,12.3505,8.58874C12.3295,8.59922,12.3138,8.61495,12.2928,8.62543C12.2824,8.63067,12.2771,8.63591,12.2666,8.64115C12.2457,8.65687,12.2247,8.66735,12.2038,8.68307C12.1985,8.68831,12.1933,8.68831,12.188,8.69355L12.1094,8.74071C12.1042,8.74595,12.0989,8.74595,12.0937,8.75119C12.078,8.76167,12.057,8.77215,12.0413,8.78263C12.0308,8.78787,12.0256,8.79311,12.0151,8.79835C12.0046,8.80883,11.9889,8.81931,11.9732,8.82455C11.9627,8.82455,11.9575,8.82979,11.947,8.83504C11.9313,8.84027,11.9208,8.85075,11.9051,8.856C11.8998,8.86123,11.8893,8.86123,11.8841,8.86648C11.8684,8.87696,11.8579,8.8822,11.8422,8.88744C11.8369,8.88744,11.8317,8.89268,11.8265,8.89268C11.8055,8.90316,11.7898,8.91364,11.7741,8.91888C11.7688,8.92412,11.7688,8.92412,11.7636,8.92412C11.7531,8.92936,11.7374,8.9346,11.7269,8.93984C11.7217,8.93984,11.7164,8.94508,11.7112,8.94508C11.7007,8.95032,11.6902,8.95032,11.685,8.95556C11.6797,8.95556,11.6745,8.9608,11.6692,8.9608C11.6588,8.9608,11.6535,8.96604,11.6483,8.96604Q11.643,8.97128,11.6378,8.97128C11.6326,8.97128,11.6273,8.97652,11.6221,8.97652Q11.6168,8.98176,11.6116,8.98176C11.6064,8.98176,11.6064,8.987,11.6011,8.987L11.5959,8.987C11.2134,9.12849,10.8203,9.21758,10.4221,9.25426C10.3959,9.25426,10.3644,9.2595,10.333,9.2595C10.2282,9.26474,10.1234,9.26998,10.0186,9.26998L3.97658,9.26998L3.97658,10.2185Z"
        fillRule="evenodd"
        fill="#000000"
        fillOpacity="0.8799999952316284"
      />
    </g>
  </svg>
);

export const TipSvgIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="currentColor"
    version="1.1"
    width="12.378684997558594"
    height="14"
    viewBox="0 0 12.378684997558594 14"
  >
    <g>
      <path
        d="M3.61384,0L3.12882,0.144329L2.64382,0L2.78616,0.491752L2.6438,0.983516L3.12881,0.839162L3.61383,0.983516L3.47145,0.491752L3.61384,0ZM3.82977,3.69549L4.70325,5.3151L5.57675,3.69549L7.17414,2.80983L5.57675,1.92419L4.70325,0.304573L3.82977,1.92421L2.23236,2.80983L3.82977,3.69549ZM7.21284,0.644694L7.86074,0.452638L7.67131,1.10954L7.86073,1.76643L7.21284,1.57315L6.56496,1.76643L6.75559,1.10952L6.56496,0.452638L7.21284,0.644694ZM10.2949,1.81094L0.330369,11.914Q0.0824354,12.1654,0.0325718,12.324Q-0.0325718,12.5312,0.0325716,12.7384Q0.0824349,12.897,0.330369,13.1484L0.832267,13.6573Q1.0861,13.9147,1.24743,13.9663Q1.45805,14.0337,1.66867,13.9663Q1.83,13.9147,2.08383,13.6573L12.0483,3.55422Q12.2963,3.30283,12.3461,3.14423Q12.4113,2.93702,12.3461,2.72981Q12.2962,2.5712,12.0483,2.31982L11.5464,1.81094Q11.2926,1.55358,11.1313,1.50195Q10.9206,1.43454,10.71,1.50195Q10.5487,1.55358,10.2949,1.81094ZM4.39925,3.11933L4.70449,3.68448L5.00973,3.11933L5.56713,2.80984L5.00974,2.50035L4.7045,1.9352L4.39926,2.50035L3.84185,2.80984L4.39925,3.11933ZM0.777133,12.5312Q0.80868,12.4977,0.864638,12.441L8.33689,4.86478L8.97382,5.60283L1.54956,13.1304Q1.49206,13.1887,1.45805,13.2215Q1.42404,13.1887,1.36654,13.1304L0.864638,12.6215Q0.808689,12.5648,0.777133,12.5312ZM9.53918,5.02961L11.514,3.02728Q11.57,2.97055,11.6016,2.93702Q11.57,2.90349,11.514,2.84676L11.0121,2.33788Q10.9547,2.27959,10.9206,2.2467Q10.8866,2.27958,10.8291,2.33788L8.90224,4.29156L9.53918,5.02961ZM10.9206,7.27744L11.693,7.04881L11.4675,7.83201L11.693,8.61521L10.9206,8.38414L10.1481,8.61521L10.376,7.83201L10.1481,7.04881L10.9206,7.27744Z"
        fillRule="evenodd"
        fill="#000000"
        fillOpacity="1"
      />
    </g>
  </svg>
);

export const AiSvgIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="14" height="14" viewBox="0 0 14 14">
    <g>
      <path
        d="M10.6458,12.3295L8.85695,12.3295L7.93098,13.5417Q7.58091,14,6.99998,14Q6.41906,14,6.06898,13.5417L5.14302,12.3295L3.35415,12.3295Q1.96715,12.3295,0.985246,11.3655Q-0.0000178193,10.3981,-0.0000178193,9.02839L-0.0000180801,3.30112Q-0.0000180801,1.93139,0.985246,0.964038Q1.96715,-0.0000177672,3.35415,-0.0000178019L10.6458,-0.0000175238Q12.0328,-0.0000175238,13.0147,0.964038Q14,1.93139,14,3.30112L14,9.02839Q14,10.3981,13.0147,11.3655Q12.0328,12.3295,10.6458,12.3295ZM1.59826,1.58841Q2.3249,0.874982,3.35415,0.874982L10.6458,0.874982Q11.6751,0.874982,12.4017,1.58841Q13.125,2.29853,13.125,3.30112L13.125,9.02839Q13.125,9.90565,12.5712,10.559Q12.4921,10.6523,12.4017,10.7411Q11.6751,11.4545,10.6458,11.4545L8.64061,11.4545C8.50426,11.4545,8.37571,11.5181,8.29294,11.6264L8.16161,11.7984L7.23565,13.0105Q7.14822,13.125,6.99998,13.125Q6.85174,13.125,6.76432,13.0105L5.83835,11.7984L5.70703,11.6264C5.62426,11.5181,5.4957,11.4545,5.35936,11.4545L3.35415,11.4545Q2.3249,11.4545,1.59826,10.7411Q0.874982,10.031,0.874982,9.02839L0.874982,3.30112Q0.874982,2.29854,1.59826,1.58841ZM7.4979,4.3979Q7.44518,4.08331,7.11721,4.08331L6.18012,4.08331Q5.88143,4.08331,5.77015,4.35129L4.0834,8.45831L4.83893,8.45831L5.34846,7.19417L7.2402,7.19417L7.43347,8.45831L8.16557,8.45831L7.4979,4.3979ZM9.22563,4.08331L9.91672,4.08331L9.3779,8.45831L8.6868,8.45831L9.22563,4.08331ZM5.60618,6.55337L6.34999,4.72414L6.85953,4.72414L7.14065,6.55337L5.60618,6.55337Z"
        fillRule="evenodd"
        fill="#000000"
        fillOpacity="1"
      />
    </g>
  </svg>
);

export const SettingSvgIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    version="1.1"
    width="13.253130912780762"
    height="13.999029159545898"
    viewBox="0 0 13.253130912780762 13.999029159545898"
  >
    <g>
      <path
        d="M13.0781,8.77891C13.0781,8.77891,12.0547,7.90391,12.0547,7.90391C12.1031,7.60703,12.1281,7.30391,12.1281,7.00078C12.1281,6.69766,12.1031,6.39453,12.0547,6.09766C12.0547,6.09766,13.0781,5.22266,13.0781,5.22266C13.1553,5.15657,13.2106,5.06856,13.2365,4.97031C13.2625,4.87206,13.2579,4.76824,13.2234,4.67266C13.2234,4.67266,13.2094,4.63203,13.2094,4.63203C12.9276,3.84457,12.5057,3.11459,11.9641,2.47734C11.9641,2.47734,11.9359,2.44453,11.9359,2.44453C11.8702,2.36727,11.7827,2.31173,11.6847,2.28523C11.5868,2.25873,11.4832,2.26252,11.3875,2.29609C11.3875,2.29609,10.1172,2.74766,10.1172,2.74766C9.64844,2.36328,9.125,2.06016,8.55938,1.84766C8.55938,1.84766,8.31407,0.519531,8.31407,0.519531C8.29556,0.419597,8.24708,0.32766,8.17508,0.255934C8.10307,0.184208,8.01095,0.136089,7.91094,0.117969C7.91094,0.117969,7.86875,0.110156,7.86875,0.110156C7.05469,-0.0367187,6.19844,-0.0367187,5.38438,0.110156C5.38438,0.110156,5.34219,0.117969,5.34219,0.117969C5.24219,0.136089,5.15006,0.184208,5.07806,0.255934C5.00605,0.32766,4.95757,0.419597,4.93907,0.519531C4.93907,0.519531,4.69219,1.85391,4.69219,1.85391C4.13108,2.06645,3.60854,2.36941,3.14532,2.75078C3.14532,2.75078,1.86563,2.29609,1.86563,2.29609C1.76995,2.26225,1.66624,2.25833,1.56828,2.28484C1.47031,2.31136,1.38274,2.36705,1.31719,2.44453C1.31719,2.44453,1.28907,2.47734,1.28907,2.47734C0.748044,3.11504,0.326216,3.8449,0.0437532,4.63203C0.0437532,4.63203,0.0296906,4.67266,0.0296906,4.67266C-0.0406219,4.86797,0.0171907,5.08672,0.175003,5.22266C0.175003,5.22266,1.21094,6.10703,1.21094,6.10703C1.1625,6.40078,1.13907,6.70078,1.13907,6.99922C1.13907,7.29922,1.1625,7.59922,1.21094,7.89141C1.21094,7.89141,0.175003,8.77578,0.175003,8.77578C0.097809,8.84187,0.0425601,8.92988,0.0166031,9.02813C-0.00935385,9.12637,-0.00478907,9.23019,0.0296906,9.32578C0.0296906,9.32578,0.0437532,9.36641,0.0437532,9.36641C0.326566,10.1539,0.745316,10.8805,1.28907,11.5211C1.28907,11.5211,1.31719,11.5539,1.31719,11.5539C1.3829,11.6312,1.47048,11.6867,1.56838,11.7132C1.66629,11.7397,1.76992,11.7359,1.86563,11.7023C1.86563,11.7023,3.14532,11.2477,3.14532,11.2477C3.61094,11.6305,4.13125,11.9336,4.69219,12.1445C4.69219,12.1445,4.93907,13.4789,4.93907,13.4789C4.95757,13.5788,5.00605,13.6708,5.07806,13.7425C5.15006,13.8142,5.24219,13.8623,5.34219,13.8805C5.34219,13.8805,5.38438,13.8883,5.38438,13.8883C6.20592,14.0359,7.04721,14.0359,7.86875,13.8883C7.86875,13.8883,7.91094,13.8805,7.91094,13.8805C8.01095,13.8623,8.10307,13.8142,8.17508,13.7425C8.24708,13.6708,8.29556,13.5788,8.31407,13.4789C8.31407,13.4789,8.55938,12.1508,8.55938,12.1508C9.12477,11.9388,9.65117,11.6347,10.1172,11.2508C10.1172,11.2508,11.3875,11.7023,11.3875,11.7023C11.4832,11.7362,11.5869,11.7401,11.6849,11.7136C11.7828,11.6871,11.8704,11.6314,11.9359,11.5539C11.9359,11.5539,11.9641,11.5211,11.9641,11.5211C12.5078,10.8789,12.9266,10.1539,13.2094,9.36641C13.2094,9.36641,13.2234,9.32578,13.2234,9.32578C13.2938,9.13359,13.2359,8.91484,13.0781,8.77891C13.0781,8.77891,13.0781,8.77891,13.0781,8.77891ZM10.9453,6.28203C10.9844,6.51797,11.0047,6.76016,11.0047,7.00234C11.0047,7.24453,10.9844,7.48672,10.9453,7.72266C10.9453,7.72266,10.8422,8.34922,10.8422,8.34922C10.8422,8.34922,12.0094,9.34766,12.0094,9.34766C11.8324,9.7553,11.6091,10.1412,11.3438,10.4977C11.3438,10.4977,9.89375,9.98359,9.89375,9.98359C9.89375,9.98359,9.40313,10.3867,9.40313,10.3867C9.02969,10.693,8.61407,10.9336,8.16407,11.1023C8.16407,11.1023,7.56875,11.3258,7.56875,11.3258C7.56875,11.3258,7.28907,12.8414,7.28907,12.8414C6.84777,12.8914,6.40224,12.8914,5.96094,12.8414C5.96094,12.8414,5.68125,11.3227,5.68125,11.3227C5.68125,11.3227,5.09063,11.0961,5.09063,11.0961C4.64532,10.9273,4.23125,10.6867,3.86094,10.382C3.86094,10.382,3.37032,9.97734,3.37032,9.97734C3.37032,9.97734,1.91094,10.4961,1.91094,10.4961C1.64532,10.1383,1.42344,9.75234,1.24532,9.34609C1.24532,9.34609,2.425,8.33828,2.425,8.33828C2.425,8.33828,2.32344,7.71328,2.32344,7.71328C2.28594,7.48047,2.26563,7.23984,2.26563,7.00234C2.26563,6.76328,2.28438,6.52422,2.32344,6.29141C2.32344,6.29141,2.425,5.66641,2.425,5.66641C2.425,5.66641,1.24532,4.65859,1.24532,4.65859C1.42188,4.25078,1.64532,3.86641,1.91094,3.50859C1.91094,3.50859,3.37032,4.02734,3.37032,4.02734C3.37032,4.02734,3.86094,3.62266,3.86094,3.62266C4.23125,3.31797,4.64532,3.07734,5.09063,2.90859C5.09063,2.90859,5.68282,2.68516,5.68282,2.68516C5.68282,2.68516,5.9625,1.16641,5.9625,1.16641C6.40157,1.11641,6.85,1.11641,7.29063,1.16641C7.29063,1.16641,7.57032,2.68203,7.57032,2.68203C7.57032,2.68203,8.16563,2.90547,8.16563,2.90547C8.61407,3.07422,9.03125,3.31484,9.40469,3.62109C9.40469,3.62109,9.89532,4.02422,9.89532,4.02422C9.89532,4.02422,11.3453,3.51016,11.3453,3.51016C11.6109,3.86797,11.8328,4.25391,12.0109,4.66016C12.0109,4.66016,10.8438,5.65859,10.8438,5.65859C10.8438,5.65859,10.9453,6.28203,10.9453,6.28203ZM6.62813,4.09609C5.10938,4.09609,3.87813,5.32734,3.87813,6.84609C3.87813,8.36484,5.10938,9.59609,6.62813,9.59609C8.14688,9.59609,9.37813,8.36484,9.37813,6.84609C9.37813,5.32734,8.14688,4.09609,6.62813,4.09609ZM7.86563,8.0836C7.70332,8.24637,7.51043,8.37545,7.29805,8.4634C7.08567,8.55136,6.858,8.59645,6.62813,8.59609C6.16094,8.59609,5.72188,8.41328,5.39063,8.0836C5.22785,7.92129,5.09877,7.72839,5.01082,7.51602C4.92287,7.30364,4.87777,7.07596,4.87813,6.84609C4.87813,6.37891,5.06094,5.93984,5.39063,5.60859C5.72188,5.27734,6.16094,5.09609,6.62813,5.09609C7.09532,5.09609,7.53438,5.27734,7.86563,5.60859C8.0284,5.7709,8.15748,5.9638,8.24544,6.17617C8.33339,6.38855,8.37849,6.61622,8.37813,6.84609C8.37813,7.31328,8.19532,7.75235,7.86563,8.0836Z"
        fill="#000000"
        fillOpacity="0.8799999952316284"
      />
    </g>
  </svg>
);
