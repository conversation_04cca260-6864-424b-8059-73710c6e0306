/** 配置管理路由主文件 */
import { createHashRouter } from "react-router-dom";
import Layout from "@/entrypoints/options/components/Layout";

/** 配置管理路由表 */
const optionsRouters = createHashRouter([
  {
    path: "/",
    element: <Layout />,
    children: [
      {
        path: "/prompt",
        element: <Layout />,
        children: [
          {
            path: "personal",
            element: <Layout />,
          },
          {
            path: "collection",
            element: <Layout />,
          },
        ],
      },
    ],
  },
]);

export default optionsRouters;
