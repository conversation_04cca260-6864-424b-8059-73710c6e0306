import React, { useEffect, useState } from "react";
import { Flex, message, Select, Typography } from "antd";
import { ShortcutKeyEnum } from "@/config/enums/ShortcutKeyEnum";
import { cacheGet, cacheSet } from "@/utils/browserStorage";
import "./index.less";

const ShortcutKey: React.FC = () => {
  const [sendMessageKey, setsendMessageKey] = useState("");
  useEffect(() => {
    cacheGet(ShortcutKeyEnum.SENDMESSAGEKEY).then((res) => {
      setsendMessageKey(res || "Enter");
    });
  }, []);

  const handleChange = (value: string) => {
    cacheSet(ShortcutKeyEnum.SENDMESSAGEKEY, value).then(() => {
      setsendMessageKey(value);
      message.open({
        type: "success",
        content: "修改成功",
      });
    });
  };
  return (
    <div style={{ position: "relative", height: "100%" }}>
      <Flex className="setup-content-right" vertical>
        <Flex className="setup-content-header" style={{ border: "none" }} justify="space-between">
          <h1 className="setup-content-title">快捷键设置</h1>
        </Flex>
        <Flex className="shortcutkey-main" justify="center">
          <Flex className="shortcutkey-main-item" align="center" justify="space-between">
            <Typography.Title level={5} style={{ margin: 0 }}>
              发送消息
            </Typography.Title>
            <Select
              value={sendMessageKey}
              onChange={handleChange}
              style={{ width: 320, height: 32, marginRight: 32 }}
              options={[
                { value: "Enter", label: "Enter" },
                { value: "ctrlEnter", label: "Ctrl/Command + Enter" },
              ]}
            />
          </Flex>
        </Flex>
      </Flex>
    </div>
  );
};

export default ShortcutKey;
