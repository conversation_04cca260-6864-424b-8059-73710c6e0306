.message-con {
  .setup-content-header {
    .ant-row {
      gap: var(--ant-margin-xs);
    }
    .ant-form {
      .ant-form-item {
        margin-bottom: 0px;
      }
    }
  }
  .message-table-mess {
    height: calc(100vh - 260px);
    overflow-y: auto;
    .tag-status {
      color: var(--ant-color-warning);
    }
  }
  .message-operate {
    margin-bottom: var(--ant-margin);
    .tag-num {
      padding: var(--ant-padding-xxs) var(--ant-padding-xs);
      font-size: var(--ant-font-size-sm);
    }
  }
}
.message-modal-mess {
  .custom-link-button {
    padding: 0px !important;
  }
  .modal-title {
    font-size: var(--ant-font-size-lg);
    color: var(--ant-color-text-heading);
  }
  .modal-con {
    margin-top: var(--ant-margin-md);
    font-size: var(--ant-font-size);
    color: var(--ant-color-text-description);
    max-height: 500px;
    overflow-y:auto;
  }
  .modal-con-email{
    font-size: var(--ant-font-size);
    color: var(--ant-color-text-description);
    max-height: 500px;
    overflow-y:auto;
  }
  .jump-button {
    margin-top: var(--ant-margin-md);
  }
}
