/** 个人设置 */
import { allRead, delBatch, noticeClear, noticePage, noticeRead, getNoticeList } from "@/api/message.ts";
import { ExclamationCircleOutlined, MinusCircleOutlined, SearchOutlined } from "@ant-design/icons";
import {
  Button,
  Col,
  DatePicker,
  Flex,
  Form,
  Input,
  Modal,
  Pagination,
  Popconfirm,
  Row,
  Table,
  Tag,
  message,
} from "antd";
import React, { useEffect, useRef, useState } from "react";
import { cacheGet } from "@/utils/browserStorage";
import "../../index.less";
import "@/components/Markdown/index.less";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import remarkBreaks from "remark-breaks";
import { getRefereshToken } from "@/utils/auth.ts";
import { debounce } from "@/utils/debounce";
import "./index.less";

// Tab页签数据配置
const SetupComponent: React.FC = () => {
  const searchParamsInit = {
    pageNum: 1,
    pageSize: 10,
    entity: {
      content: "",
      readFlag: "",
      sendTime: "",
      sendBy: "",
    },
  };
  // 列表数据的初始化数据
  const pageDataInit = {
    // count: 0,
    page: {
      current: 1,
      size: 10,
      total: 0,
      pages: 0,
      records: [],
    },
  };
  const [form] = Form.useForm();
  // 查询数据的请求参数
  const [searchParams, setSearchParams] = useState(searchParamsInit);
  // 消息列表数据
  const [promptPageData, setPromptPageData] = useState(pageDataInit);
  const [count, setCount] = useState(0); // 未读消息数量
  const [detailData, setDetailData]: any = useState({});
  const [isModalOpen, setIsModalOpen] = useState(false); // 是否打开弹框
  const [tableLoading, setTableLoading] = useState(false); // loading
  // 页面首次加载或请求参数变化时，获取用户信息并获取消息列表数据
  const init = (searchParams: any) => {
    handlePromptList(searchParams);
    getCount();
  };

  const debounceSearch = useRef(debounce((args) => init(args), 800)).current;

  useEffect(() => {
    debounceSearch(searchParams);
  }, [searchParams]);

  /** 分页查询消息列表 */
  const handlePromptList = (params: any) => {
    setTableLoading(true);
    noticePage(params).then((res) => {
      if (res.code === 200) {
        setPromptPageData({
          page: res.data,
        });
      } else {
        message.open({
          type: "error",
          content: res.msg,
        });
      }
      setTableLoading(false);
    });
  };
  /** 获取未读消息数量 */
  const getCount = () => {
    cacheGet("userInfo").then((res) => {
      if (res) {
        let userInfo = JSON.parse(res) || {};
        getNoticeList({ readFlag: "read_flag_false", busiType: "", recipientIds: [userInfo.id] })
          .then((res) => {
            setCount(res.data.length);
          })
          .catch(() => {});
      }
    });
  };
  // 删除消息列表
  const deleteMessage = (type: number, item?: any) => {
    let ids = [];
    if (type == 1) {
      ids = [item.id];
    } else {
      ids = selectedRowKeys;
    }
    delBatch(ids).then((res) => {
      if (res.code == "200") {
        message.open({
          type: "success",
          content: "删除成功！",
        });
        setSearchParams({
          ...searchParams,
          ...{
            pageNum: 1,
          },
        });
      } else {
        message.open({
          type: "error",
          content: res.msg,
        });
      }
    });
  };
  // 清空列表消息
  const clearData = () => {
    noticeClear({}).then((res) => {
      if (res.code == "200") {
        message.open({
          type: "success",
          content: "清空成功！",
        });
        setSearchParams({
          ...searchParams,
          ...{
            pageNum: 1,
          },
        });
      } else {
        message.open({
          type: "error",
          content: res.msg,
        });
      }
    });
  };
  // 单个已读
  const noteRead = (item) => {
    noticeRead([item.noticeRecId]).then((res) => {
      if (res.code == "200") {
        setSearchParams({
          ...searchParams,
          ...{
            pageNum: 1,
          },
        });
      } else {
        message.open({
          type: "error",
          content: res.msg,
        });
      }
    });
  };
  // 批量已读
  const allReadEve = () => {
    allRead({}).then((res) => {
      if (res.code == "200") {
        message.open({
          type: "success",
          content: "全部已读成功！",
        });
        setSearchParams({
          ...searchParams,
          ...{
            pageNum: 1,
          },
        });
      } else {
        message.open({
          type: "error",
          content: res.msg,
        });
      }
    });
  };
  const processContent = (raw: string) => {
    // 按 <think> 拆分
    // const parts = raw.split("<think>");
    // let beforeThink = parts[0] || "";
    // let afterThink = parts.length > 1 ? "<think>" + parts.slice(1).join("<think>") : "";

    // // 处理 think 之前的部分，把 \n 转成换行
    // beforeThink = beforeThink.replace(/\\n/g, "\n");

    // // 拼接回去
    // return beforeThink + afterThink;
    const parts = raw.split("<think>");
    let beforeThink = parts[0] || "";
    let afterThink = parts.length > 1 ? "<think>" + parts.slice(1).join("<think>") : "";

    // 把转义的 \n 变成真实换行
    beforeThink = beforeThink.replace(/\\n/g, "\n");

    // 在 "校验结果：" 后强制加两个换行 (Markdown 规则: 两个换行才能分段)
    beforeThink = beforeThink.replace(/校验结果：/g, "校验结果：\n\n");

    return beforeThink + afterThink;
  };
  // 查看详情，并打开弹框
  const getDetail = (item) => {
    noteRead(item);
    setIsModalOpen(true);
    if (item.busiType == "busi_type_email_validation") {
      const processed = processContent(item?.msgContent);
      item.msgContent = processed;
      setDetailData(item);
    } else {
      setDetailData(item);
    }
  };
  // 表头
  const columns = [
    { title: "消息标题", dataIndex: "msgTitle", ellipsis: true },
    { title: "消息内容", dataIndex: "msgContent", ellipsis: true },
    { title: "接收时间", dataIndex: "msgTime", width: 180 },
    {
      title: "状态",
      dataIndex: "readFlag",
      width: 100,
      render: (text, record) => (
        <Flex>
          {text == "read_flag_false" ? (
            <Tag color="gold" className="tag-status">
              未读
            </Tag>
          ) : (
            <Flex>
              <Tag>已读</Tag>
            </Flex>
          )}
        </Flex>
      ),
    },
    {
      title: "操作",
      dataIndex: "action",
      width: 170,
      render: (text, record) => (
        <Flex wrap gap="small">
          <Popconfirm
            title="删除"
            description="确认删除该数据吗？"
            onConfirm={() => {
              deleteMessage(1, record);
            }}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger className="custom-link-button">
              删除
            </Button>
          </Popconfirm>
          <Button
            type="link"
            onClick={() => {
              getDetail(record);
            }}
            className="custom-link-button"
          >
            详情
          </Button>
          {/* {searchParams.entity.readFlag == "read_flag_false" && (
            <Button
              type="link"
              onClick={() => {
                noteRead(record);
              }}
              className="custom-link-button"
            >
              标为已读
            </Button>
          )} */}
        </Flex>
      ),
    },
  ];
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]); // 复选框选中
  // const [popoverVisible, setPopoverVisible] = useState(null); // 鼠标悬浮
  const hasSelected = selectedRowKeys.length > 0;
  const [loading, setLoading] = useState(false);
  // 复选框选中
  // const onSelectChange = (newSelectedRowKeys) => {
  //   setSelectedRowKeys(newSelectedRowKeys);
  // };
  // // 复选框选中
  // const rowSelection = {
  //   selectedRowKeys,
  //   onChange: onSelectChange,
  // };
  // 关闭弹框
  const handleCancel = () => {
    setIsModalOpen(false);
  };
  const jump = async () => {
    if (detailData.busiType === "busi_type_version") {
      // 跳转官网登录页面
      window.open(`${import.meta.env.VITE_OFFICIAL_URL}/version?form=plugin`, "_blank");
      return;
    }
    if (detailData.busiType === "busi_type_knowledge") {
      // 跳转至场景页面
      const url = new URL(detailData.skipPath);
      const refreshToken = await getRefereshToken();
      const token = await getToken();
      const tenantId = await getTenantId();
      // 添加参数
      url.searchParams.set("messageId", detailData.busiId);
      url.searchParams.set("refreshToken", refreshToken);
      url.searchParams.set("tenantId", tenantId);
      url.searchParams.set("token", token);
      window.open(`${url}`, "_blank");
      return;
    }
    browser.tabs.create({ url: detailData.parameter });
  };
  // const HtmlContent = ({ htmlString }) => (
  //   <div dangerouslySetInnerHTML={{ __html: htmlString }} />
  // );

  // 点击提交按钮
  const onFinish = (values) => {
    let obj = {
      sendTime: "",
      sendBy: "",
    };
    if (values.sendTime) {
      obj.sendTime = values.sendTime.format("YYYY-MM-DD");
    }
    obj.sendBy = values.sendBy;
    setSearchParams({
      ...searchParams,
      entity: {
        ...searchParams.entity,
        ...obj,
      },
    });
  };
  // 点击重置
  const reset = () => {
    setSearchParams(searchParamsInit);
  };
  /** 已读未读 */
  const handleTabChange = (targetKey: string) => {
    let obj = {
      readFlag: targetKey,
    };
    setSearchParams({
      ...searchParams,
      ...{
        pageNum: 1,
        pageSize: 10,
      },
      entity: {
        ...searchParams.entity,
        ...obj,
      },
    });
  };
  return (
    <>
      <Flex className="message-con setup-content-right" vertical>
        {/* header区 */}
        <Flex className="setup-content-header" justify="space-between" align="center">
          <h1 className="setup-content-title">消息管理</h1>
          <Form form={form} onFinish={onFinish} layout="horizontal">
            <Row>
              <Col>
                <Form.Item label="" name="sendTime">
                  <DatePicker
                    placeholder="接收日期"
                    size="large"
                    value={searchParams.entity.sendTime}
                    onChange={(e) => {
                      setSearchParams({
                        ...searchParams,
                        entity: { ...searchParams.entity, sendTime: e ? e.format("YYYY-MM-DD") : "" },
                      });
                    }}
                  />
                </Form.Item>
              </Col>
              <Col>
                <Input
                  size="large"
                  allowClear
                  placeholder="搜索消息"
                  value={searchParams.entity.content}
                  onChange={(e) => {
                    setSearchParams({
                      ...searchParams,
                      entity: { ...searchParams.entity, content: e.target.value.trim() },
                    });
                  }}
                  prefix={<SearchOutlined />}
                  style={{ width: "310px" }}
                />
              </Col>
            </Row>
          </Form>
        </Flex>
        {/* table数据区 */}
        <div className="setup-content-main">
          <Flex vertical>
            <Flex align="center" className="message-operate">
              {/* <Popconfirm
                title="删除"
                description="确认删除该数据吗？"
                onConfirm={() => {
                  deleteMessage(2);
                }}
                okText="确定"
                cancelText="取消"
              >
                <Button type="primary" disabled={!hasSelected} loading={loading}>
                  批量删除
                </Button>
              </Popconfirm> */}
              {count && count > 0 ? (
                <Flex>
                  <Tag bordered={false} color="warning" icon={<ExclamationCircleOutlined />} className="tag-num">
                    未读消息{count}
                  </Tag>
                  <Button type="link" loading={loading} onClick={allReadEve}>
                    全部已读
                  </Button>
                </Flex>
              ) : (
                <Flex>
                  <Tag bordered={false} icon={<MinusCircleOutlined />} className="tag-num">
                    未读消息0
                  </Tag>
                  <Button type="link" disabled>
                    全部已读
                  </Button>
                </Flex>
              )}
              {/* <Popconfirm
                title="清空"
                description="清空消息后不可恢复,确认清空吗？"
                onConfirm={clearData}
                okText="确定"
                cancelText="取消"
              >
                <Button type="primary" loading={loading}>
                  清空
                </Button>
              </Popconfirm> */}
            </Flex>
            <Table
              className="message-table-mess"
              // rowSelection={rowSelection}
              columns={columns}
              dataSource={promptPageData.page?.records}
              rowKey="id"
              loading={tableLoading}
              pagination={false}
              // onRow={(record) => ({
              //   onMouseEnter: () => {
              //     setPopoverVisible(record.id);
              //   },
              //   onMouseLeave: () => {
              //     setPopoverVisible(null);
              //   },
              // })}
              // components={{
              //   body: {
              //     row: ({ children, ...rest }, row) => {
              //       const { record } = rest;
              //       const rowKey = rest['data-row-key'];
              //       return (
              //         <Popover
              //           open={popoverVisible === rowKey}
              //           placement="right"
              //           title= {<HtmlContent htmlString={children[0]?.props?.record.msgTitle} />}
              //           content={<HtmlContent htmlString={children[0]?.props?.record.msgContent} />}
              //         >
              //           <tr {...rest}>{children}{record}</tr>
              //         </Popover>
              //       );
              //     },
              //   },
              // }}
            />
          </Flex>
          {promptPageData.page?.records && promptPageData.page?.records.length > 0 && (
            <Pagination
              className="pagination"
              showSizeChanger
              showQuickJumper
              onChange={(page, pageSize) => {
                setSearchParams({ ...searchParams, pageNum: page, pageSize });
              }}
              current={promptPageData.page?.current}
              pageSize={promptPageData.page?.size}
              total={promptPageData.page?.total}
              showTotal={(total) => `共 ${total} 条`}
            />
          )}
        </div>
      </Flex>
      {/* 新增/编辑提示词的模态框表单 */}
      <Modal
        open={isModalOpen}
        centered={true}
        width={700}
        className="message-modal-mess sino-markdown-body"
        footer={null}
        onCancel={handleCancel}
      >
        <div className="modal-title" dangerouslySetInnerHTML={{ __html: detailData.msgTitle }}></div>
        {detailData?.busiType == "busi_type_email_validation" ? (
          <div className="modal-con-email">
            <ReactMarkdown remarkPlugins={[remarkGfm, remarkBreaks]} rehypePlugins={[rehypeRaw]}>
              {detailData?.msgContent}
            </ReactMarkdown>
          </div>
        ) : (
          <div
            className="modal-con"
            dangerouslySetInnerHTML={{
              __html: detailData.msgContent?.replace(
                /chrome-extension:\/\/[a-zA-Z0-9]+\/images\/textEditor\//g,
                `chrome-extension://${browser.runtime.id}/images/textEditor/`,
              ),
            }}
          ></div>
        )}

        {detailData.busiType == "busi_type_knowledge" ? (
          <Button type="link" onClick={jump} className="custom-link-button jump-button">
            跳转至该业务场景
          </Button>
        ) : (
          <>
            {detailData?.busiType != "busi_type_email_validation" && (
              <Button type="link" onClick={jump} className="custom-link-button jump-button">
                {detailData.busiType === "busi_type_version" ? "跳转至官网" : "跳转至该便签"}
              </Button>
            )}
          </>
        )}
      </Modal>
    </>
  );
};

export default SetupComponent;
