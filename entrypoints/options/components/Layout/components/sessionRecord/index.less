@import "@/assets/styles/variables";
.session-record-main {
  padding-top: var(--ant-margin);
  width: 100%;
  cursor: default;
  display: block !important;
  position: relative;
  z-index: 2;
  .session-record {
    gap: var(--ant-margin);
    color: var(--ant-color-text);
    margin-bottom: var(--ant-margin-lg);
    flex-wrap: wrap;

    .disabled-tag {
      cursor: not-allowed !important;
      opacity: 0.6;
      pointer-events: none;
    }
  }
  .session-record-operate {
    margin-bottom: var(--ant-margin);
  }
  .clear-btn {
    border: none !important;
  }
  .setup-content-table {
    /* 提示词管理表格 */
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(460px, 1fr));
    grid-template-rows: repeat(auto-fit, 160px);
    justify-items: flex-start;
    gap: var(--ant-margin);
    // 高度现在由JavaScript动态计算
    // height: calc(100vh - 280px);
    overflow: auto;
    flex-wrap: wrap;
    position: relative;
    &.empty {
      display: flex;
    }
  }

  .session-content-wrapper {
    width: 100%;
    height: 100%;
    position: relative;
  }

  .loading-more-container {
    width: 100%;
    padding: var(--ant-padding) 0;
    display: flex;
    justify-content: center;
    align-items: center;
    grid-column: 1 / -1; /* 让加载更多区域占据整行 */
    min-height: 40px; /* 确保有最小高度 */
    margin-top: 10px; /* 增加与卡片的间距 */
  }

  .loading-more {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--ant-padding-sm) var(--ant-padding);
    color: var(--ant-color-text-quaternary);
    font-size: var(--ant-font-size-sm);
    position: relative;
    padding: 6px 12px;

    &::before, &::after {
      content: "";
      display: block;
      height: 1px;
      width: 80px;
      background: var(--ant-color-split);
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }

    &::before {
      left: calc(50% - 120px);
    }

    &::after {
      right: calc(50% - 120px);
    }

    &.no-more {
      color: var(--ant-color-text-quaternary);
      font-size: var(--ant-font-size-sm);
      opacity: 0.8;

      &::before, &::after {
        width: 60px;
        background: var(--ant-color-split);
        opacity: 0.6;
      }

      &::before {
        left: calc(50% - 100px);
      }

      &::after {
        right: calc(50% - 100px);
      }
    }
  }
  .ant-card-bordered {
    .ant-card-head {
      padding: 0px;
    }
    .ant-card-head-title {
      padding: 0;
    }
    .ant-card-extra {
      padding: 0;
    }
  }
  .setup-content-list-title {
    margin-bottom: 11px;
  }

  .setup-content-list {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    .setup-content-list-item {
      width: calc(20vw - 40px);
      height: 43px;
      margin-bottom: 11px;
      background-color: #fff;
      border-radius: 10px;
      padding: 12px 16px;

      .setup-list-item-top {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .setup-list-item-top-label {
          font-family: @side-panel-font-family-bold;
          font-size: 16px;
          margin-bottom: 7px;
          height: 19px;
          line-height: 19px;
        }

        .setup-list-item-top-icon {
          display: flex;
          height: 26px;

          img {
            cursor: pointer;
            width: 12px;
            height: 12px;
            margin-right: 8px;
          }
        }
      }

      .setup-list-item-left-tip {
        color: #999;
        width: 17vw;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
  .ant-card {
    min-width: 200px;
    width: 100%;
    background: var(--ant-color-fill-quaternary);
    border-radius: var(--ant-border-radius) !important;
    border: 1px solid var(--ant-color-fill-quaternary) !important;
    padding: var(--ant-padding) !important;
    cursor: pointer;

    .ant-card-head {
      min-height: 40px;
      height: 40px;

      border-bottom: none;
      .ant-card-head-wrapper {
        justify-content: space-between;
        .ant-card-head-title {
          align-items: center;
          display: flex;
          flex: 1 !important;
          font-size: 14px;
          gap: 5px;
          margin-right: 5px;
          min-width: 1px;
          .title {
            font-family: @side-panel-font-family-bold;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: var(--ant-font-size-lg);
            color: var(--ant-color-text-base);
          }
        }

        .ant-card-extra {
          /* 操作区 */
          margin: 0;
          display: flex;
          gap: 5px;
          /* 卡片头部的操作按钮 */
          align-items: center;
          justify-content: center;
          flex-wrap: nowrap;

          .collection-options {
            /* 收藏选项 */
            display: flex;
            align-items: center;
            gap: 5px;
          }
        }
      }
    }

    .ant-card-body {
      padding: 0px;
      .ant-checkbox-wrapper {
        width: 100%;
        flex: 1;
        > span:last-child {
          width: 100%;
          display: inline-block;
        }
      }
      .title-con {
        width: 100%;
        flex: 1;
      }
      .title {
        flex: 1;
        vertical-align: text-bottom;
        font-size: var(--ant-font-size-lg);
        color: var(--ant-color-text-base);
        font-family: @side-panel-font-family-bold;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
      .title-check {
        margin-right: var(--ant-margin-xs);
      }
      .prompt-card-content {
        margin-top: var(--ant-margin-xxs);
        font-size: var(--ant-font-size);
        color: var(--ant-color-text-tertiary);
        line-height: var(--ant-line-height);
        display: -webkit-box;
        overflow: hidden;
        height: 4.5em;
        text-overflow: ellipsis;
        line-clamp: 3;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        word-break: break-all;
      }
    }
    .prompt-card-footer {
      margin-top: var(--ant-margin-sm);
      .operte-icon {
        opacity: 0;
        gap: var(--ant-margin-xxs);
        .anticon {
          font-size: var(--ant-font-size);
          color: var(--ant-color-text);
        }
      }
    }
    .cur-card-footer-tit {
      color: var(--ant-color-primary);
      font-size: var(--ant-font-size-sm);
      padding: 1px var(--ant-padding-xs);
      background: var(--ant-blue-1);
      border-radius: var(--ant-border-radius-sm);
    }
    &:hover {
      .operte-icon {
        opacity: 1;
      }
    }
  }
}
