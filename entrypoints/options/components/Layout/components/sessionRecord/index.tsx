/** 对话记录*/
import React, { useEffect, useState, useRef, useCallback } from "react";
import { LoadingOutlined } from "@ant-design/icons";
import { Card, Checkbox, Flex, message, Spin, Tag } from "antd";
import { Chat } from "@/types/chat";
import "../../index.less";
import "./index.less";
import { getConversationHistoryList } from "@/api/chat";
import EmptyData from "@/components/EmptyData";
import { listAgents } from "@/api/knowdge.ts";
import { debounce } from "@/utils/debounce";
// 列表查询的初始化参数
interface ChatSearchParam {
  page: string;
  size: string;
  title: string;
  agentId: string;
}

const searchParamsInit: ChatSearchParam = {
  page: "1",
  size: "20",
  title: "",
  agentId: "",
};

// Tab页签数据配置
const SetupComponent: React.FC = () => {
  // 查询数据的请求参数
  const [searchParams, setSearchParams] = useState<ChatSearchParam>(searchParamsInit);
  // 聊天记录数据
  const [promptPageData, setPromptPageData] = useState([]);
  const [agentList, setAgentList] = useState<Array<Chat>>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [isDelete, setIsDelete] = useState(false);
  // tag选中的数据
  const [selectedTags, setSelectedTags] = React.useState<string[]>([]);
  const CheckboxGroup = Checkbox.Group;
  // 多选
  const [plainOptions, setPlainOptions] = useState<number[]>([]);
  const [checkedList, setCheckedList] = useState<string[]>([]);

  // 分页相关状态
  const [isLastPage, setIsLastPage] = useState<boolean>(false);
  const currentRequestPage = useRef<string | null>(null);
  const stopRequestList = useRef<boolean>(false);
  const listRef = useRef<HTMLDivElement>(null);
  const sessionRecordRef = useRef<HTMLDivElement>(null);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [tableHeight, setTableHeight] = useState<string>("calc(100vh - 280px)");

  // 防抖加载更多
  const loadMore = useCallback(() => {
    if (loading || stopRequestList.current || isLastPage) return;
    setLoadingMore(true);
    handlePromptList(searchParams);
  }, [loading, isLastPage, searchParams]);

  const debouncedLoadMore = useCallback(debounce(loadMore, 300), [loadMore]);
  // 页面首次加载或请求参数变化时，获取用户信息并获取提示词数据
  useEffect(() => {
    getAllAgentPageHandler();
  }, []);

  // 动态计算滚动区域高度
  const updateTableHeight = useCallback(() => {
    if (sessionRecordRef.current) {
      // 获取session-record的高度
      const sessionRecordHeight = sessionRecordRef.current.querySelector('.session-record')?.getBoundingClientRect().height || 0;
      // 计算滚动区域高度：100vh - (session-record高度 + 头部高度 + 底部留白)
      const newHeight = `calc(100vh - ${sessionRecordHeight + 200}px)`;
      setTableHeight(newHeight);
      console.log('Updated table height:', newHeight);
    }
  }, []);

  // 初始化时计算高度
  useEffect(() => {
    // 初始化时计算一次
    // 使用setTimeout确保在DOM渲染后计算
    const timer = setTimeout(() => {
      updateTableHeight();
    }, 0);

    // 添加窗口大小变化事件监听
    window.addEventListener('resize', updateTableHeight);

    // 清理函数
    return () => {
      clearTimeout(timer);
      window.removeEventListener('resize', updateTableHeight);
    };
  }, [updateTableHeight]);

  // 当标签切换或数据加载时重新计算高度
  useEffect(() => {
    // 使用setTimeout确保在DOM更新后计算
    const timer = setTimeout(() => {
      updateTableHeight();
    }, 100);

    // 使用MutationObserver监听.session-record元素的变化
    if (sessionRecordRef.current) {
      const observer = new MutationObserver(() => {
        updateTableHeight();
      });

      // 监听子元素变化
      observer.observe(sessionRecordRef.current, { childList: true, subtree: true });

      return () => {
        clearTimeout(timer);
        observer.disconnect();
      };
    }

    return () => clearTimeout(timer);
  }, [selectedTags, promptPageData, updateTableHeight]);

  /** agnetList */
  const getAllAgentPageHandler = () => {
    // 聊天记录
    listAgents({}).then((res: any) => {
      if (res.code == "200") {
        let data = [...res.data];
        setAgentList(data);
        setSelectedTags([data[0].agentName]);
        // 直接设置搜索参数
        setSearchParams({ ...searchParams, agentId: data[0].id });
      } else {
        message.open({
          type: "error",
          content: res.msg,
        });
      }
    });
  };
  useEffect(() => {
    // 切换 agentId 时立即清空列表
    setPromptPageData([]);
    // 重置分页状态
    stopRequestList.current = false;
    currentRequestPage.current = null;
    setSearchParams({
      ...searchParams,
      page: "1",
    });
    handlePromptList(
      {
        ...searchParams,
        page: "1",
      },
      true,
    );
  }, [searchParams.agentId]);

  /** 聊天记录 */
  const handlePromptList = (params = searchParams, first = false) => {
    // 获取聊天记录
    if (!params.agentId) {
      return;
    }

    // 防止重复请求同一页
    if (currentRequestPage.current === params.page && !first) {
      setLoading(false);
      setLoadingMore(false);
      return;
    }

    // 记录当前请求页码
    currentRequestPage.current = params.page;

    // 设置加载状态
    if (first) {
      setLoading(true);
    }

    getConversationHistoryList(params)
      .then((res: any) => {
        if (res.code == "200") {
          // 获取分页信息
          const { content, last } = res.data;
          const newList = content || [];

          // 更新分页状态
          setIsLastPage(last || false);

          if (newList.length === 0) {
            // 如果是第一页且数据为空，清空列表
            if (first || params.page === "1") {
              setPromptPageData([]);
            }
            setLoading(false);
            setLoadingMore(false);
            currentRequestPage.current = null; // 重置请求页码
            return;
          }

          let arr: number[] = [];
          newList.forEach((item: any) => {
            arr.push(item.conversationId);
          });
          setPlainOptions(arr);

          if (first || params.page === "1") {
            // 第一页数据直接替换
            setPromptPageData(newList);
          } else {
            // 合并列表数据
            setPromptPageData((prev) => [...prev, ...newList]);
          }

          // 如果是最后一页，或者没有数据了，停止加载
          if (last || newList.length === 0) {
            stopRequestList.current = true;
          } else {
            // 更新页码，准备加载下一页
            const nextPage = (parseInt(params.page) || 1) + 1;
            setSearchParams({
              ...params,
              page: nextPage.toString(),
            });
          }
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      })
      .catch(() => {
        // 如果是第一页请求失败，清空列表
        if (first || params.page === "1") {
          setPromptPageData([]);
        }
        setLoading(false);
        setLoadingMore(false);
        currentRequestPage.current = null; // 重置请求页码
      })
      .finally(() => {
        setLoading(false);
        setLoadingMore(false);
        currentRequestPage.current = null; // 重置请求页码
      });
  };
  // tag 点击处理
  const handleChange = (item: any) => {
    // 如果正在加载中或上拉加载更多数据时，不执行任何操作
    if (loading || loadingMore) return;

    setSelectedTags([]);
    setSelectedTags([item.agentName]);
    setSearchParams({ ...searchParams, agentId: item.id });
  };

  /** 删除会话 */
  // const handlePromptDelete = (prompt?: any) => {
  //   let obj = { apiKey: "", ids: [] };
  //   if (prompt) {
  //     obj.apiKey = prompt.apikey;
  //     obj.ids = [prompt.id];
  //   } else {
  //     if (checkedList.length < 1) {
  //       message.open({
  //         type: "warning",
  //         content: "请先选择要删除的聊天",
  //       });
  //       return;
  //     }
  //     obj.apiKey = searchParams.apiKey;
  //     obj.ids = checkedList;
  //   }
  //   delBatch(obj)
  //     .then((res: any) => {
  //       if (res.code == "200") {
  //         message.open({
  //           type: "success",
  //           content: "删除成功！",
  //         });
  //       } else {
  //         message.open({
  //           type: "error",
  //           content: res.msg,
  //         });
  //       }
  //     })
  //     .catch(() => {
  //       message.open({
  //         type: "error",
  //         content: "删除失败！",
  //       });
  //     });
  // };
  // 全选/ 取消全选
  // const onCheckAllChange: CheckboxProps["onChange"] = (e) => {
  //   setCheckedList(e.target.checked ? plainOptions : []);
  // };
  // 单个选中
  const onChange = (list: string[]) => {
    setCheckedList(list);
  };
  // 切换删除状态
  const toggleDelete = () => {
    setIsDelete((isDelete) => !isDelete);
    if (isDelete) {
      setCheckedList([]);
    }
  };

  // 监听滚动事件
  useEffect(() => {
    if (!listRef.current) return;

    const handleScroll = () => {
      if (loading || stopRequestList.current || isLastPage) return;

      const { scrollTop, clientHeight, scrollHeight } = listRef.current || {
        scrollTop: 0,
        clientHeight: 0,
        scrollHeight: 0,
      };

      // 当滚动到距离底部近的位置时，提前加载下一页
      if (scrollTop + clientHeight >= scrollHeight - 100) {
        debouncedLoadMore();
      }
    };

    // 监听滚动事件
    listRef.current.addEventListener("scroll", handleScroll);
    return () => {
      // 清理函数，组件卸载时移除事件监听
      listRef.current?.removeEventListener("scroll", handleScroll);
    };
  }, [debouncedLoadMore, isLastPage, loading]);
  return (
    <>
      <Flex className="setup-content-right" vertical>
        {/* header区 */}
        <Flex className="setup-content-header" justify="space-between">
          <h1 className="setup-content-title">聊天历史</h1>
          {/*<Input size="large" placeholder="搜索聊天记录" prefix={<SearchOutlined />} style={{ width: "310px" }} />*/}
        </Flex>
        {/* table数据区 */}
        <Flex className="session-record-main" ref={sessionRecordRef}>
          <Flex className="session-record">
            {Array.isArray(agentList) &&
              agentList.map((item, index) => {
                return (
                  <Tag.CheckableTag
                    key={index}
                    checked={selectedTags.includes(item.agentName)}
                    onChange={() => handleChange(item)}
                    className={loading || loadingMore ? "disabled-tag" : ""}
                  >
                    {item.agentName}
                  </Tag.CheckableTag>
                );
              })}
          </Flex>
          {/* {promptPageData && promptPageData.length > 0 && (
            <Flex justify="space-between" className="session-record-operate">
              <Flex align="center">
                <span></span>
                {isDelete && (
                  <Flex align="center">
                    <Checkbox indeterminate={indeterminate} onChange={onCheckAllChange} checked={checkAll}>
                      全选
                    </Checkbox>
                    {checkedList && checkedList.length > 0 && (
                      <Flex className="operte-icon">
                        <Popconfirm
                          onConfirm={() => {
                            handlePromptDelete();
                          }}
                          okText="确定"
                          cancelText="取消"
                          title="确定要删除当前选择内容?"
                        >
                          <Button icon={<DeleteOutlined />} type="text"></Button>
                        </Popconfirm>
                      </Flex>
                    )}
                  </Flex>
                )}
              </Flex>
              <Button className="setup-content-title-btn" onClick={batchDelete}>
                {isDelete ? "取消批量删除" : "批量删除"}
              </Button>
            </Flex>
          )} */}

          <Spin spinning={loading} indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />}>
            <Flex vertical className="session-content-wrapper">
              {promptPageData && promptPageData.length > 0 ? (
                <>
                  <CheckboxGroup value={checkedList} onChange={onChange} style={{ width: "100%", display: "flow" }}>
                    <Flex className="setup-content-table" ref={listRef} style={{ height: tableHeight }}>
                      {promptPageData.map((item, index: number) => {
                        return (
                          <Card className="session-card" key={index}>
                            <Flex className="footer-tootle-wcl" justify="space-between" align="center">
                              <Flex align="center" className="title-con">
                                {isDelete ? (
                                  <Checkbox value={item.conversationId}>
                                    <div className="title">{item.title}</div>
                                  </Checkbox>
                                ) : (
                                  <div className="title">{item.title}</div>
                                )}
                              </Flex>
                            </Flex>
                            <Flex className="prompt-card-content">{item.conversations}</Flex>
                            <Flex className="prompt-card-footer" justify="space-between" align="center">
                              <Flex className="cur-card-footer-tit">{item.agentName}</Flex>
                              {/* <Flex className="operte-icon">
                                <Popconfirm
                                  onConfirm={() => {
                                    handlePromptDelete(item);
                                  }}
                                  okText="确定"
                                  cancelText="取消"
                                  title="确定要删除该聊天记录吗?"
                                >
                                  <Button icon={<DeleteOutlined />} type="text"></Button>
                                </Popconfirm>
                              </Flex> */}
                            </Flex>
                          </Card>
                        );
                      })}

                      {/* 加载更多区域 - 移到列表内部 */}
                      <Flex className="loading-more-container" justify="center">
                        {loadingMore && !isLastPage ? (
                          <div className="loading-more">
                            <LoadingOutlined style={{ marginRight: 8, fontSize: 14 }} />
                            <span>加载更多</span>
                          </div>
                        ) : isLastPage ? (
                          <div className="loading-more no-more">没有更多数据了</div>
                        ) : null}
                      </Flex>
                    </Flex>
                  </CheckboxGroup>
                </>
              ) : (
                <Flex className="setup-content-table empty" style={{ height: tableHeight }}>
                  <EmptyData description={"暂无聊天记录"} />
                </Flex>
              )}
            </Flex>
          </Spin>
        </Flex>
      </Flex>
    </>
  );
};

export default SetupComponent;
