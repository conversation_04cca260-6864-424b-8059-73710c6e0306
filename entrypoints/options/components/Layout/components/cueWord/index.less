@import "@/assets/styles/variables";
.setup-content-main {
  padding-top: var(--ant-margin);
  position: relative;
  z-index: 2;
  .setup-content-title-btn {
    position: absolute;
    right: 0px;
    top: var(--ant-margin-lg);
  }
  .agent-list-cue {
    gap: var(--ant-margin);
    color: var(--ant-color-text);
    margin-bottom: var(--ant-margin-lg);
    flex-wrap: wrap;
  }
  /* 提示词管理主区域 */
  width: 100%;
  cursor: default;
  display: block !important;
  /* 去除tabs的外边距，将这部分距离挪到改到table */
  .ant-tabs {
    flex: 1;
  }
  .ant-tabs-nav {
    margin-bottom: var(--ant-margin-lg);
    &::before {
      border-bottom: 0px;
    }
  }

  .setup-content-table {
    /* 提示词管理表格 */
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    grid-template-rows: repeat(auto-fit, 160px);
    justify-items: flex-start;
    gap: var(--ant-margin);
    height: calc(100vh - 328px);
    overflow: auto;
    &.empty {
      display: flex;
    }
    .ant-card-bordered {
      .ant-card-head {
        padding: 0px;
      }
      .ant-card-head-title {
        padding: 0;
      }
      .ant-card-extra {
        padding: 0;
      }
    }
  }
  .setup-content-list-title {
    margin-bottom: 11px;
  }

  .setup-content-list {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    .setup-content-list-item {
      width: calc(20vw - 40px);
      height: 43px;
      margin-bottom: 11px;
      background-color: #fff;
      border-radius: 10px;
      padding: 12px 16px;

      .setup-list-item-top {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .setup-list-item-top-label {
          font-family: @side-panel-font-family-bold;
          font-size: 16px;
          margin-bottom: 7px;
          height: 19px;
          line-height: 19px;
        }

        .setup-list-item-top-icon {
          display: flex;
          height: 26px;

          img {
            cursor: pointer;
            width: 12px;
            height: 12px;
            margin-right: 8px;
          }
        }
      }

      .setup-list-item-left-tip {
        color: #999;
        width: 17vw;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
