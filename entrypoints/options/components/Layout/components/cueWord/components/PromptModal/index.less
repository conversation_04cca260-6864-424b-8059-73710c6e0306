@import "@/assets/styles/variables";
.setup-model {
  .global-detail-tipBtn {
    .global-detail-tipBtn-title {
      margin-right: 20px;
    }

    .global-detail-tipBtn-content,
    .global-detail-tipBtn-lang {
      height: 26px;
      line-height: 26px;
      margin-right: 20px;
      cursor: pointer;
      padding: 0 6px;
      background-color: #fff;
      color: var(--ant-color-primary-text);
      border: 1px solid var(--ant-color-primary-text);
      border-radius: 4px;
    }
  }
  .global-btn {
    margin-bottom: 0px !important;
  }

  .setup-model-btn {
    display: flex;
    justify-content: end;
    // width: 100%;

    .setup-model-btn-cancel {
      margin-right: 20px !important;
    }
  }

  .setup-model-item-tip {
    position: relative;

    .setup-model-btn-explain {
      cursor: pointer;
      width: 16px;
      height: 16px;
      position: absolute;
      top: 8px;
      left: 45px;
      z-index: 100;
    }
    .content {
      margin-bottom: var(--ant-margin-md);
    }
    .tips {
      margin-top: 2px;
      line-height: var(--ant-line-height);
      font-size: var(--ant-font-size);
      color: var(--ant-color-text-description);
      margin-top: -22px;
    }
  }

  .setup-model-tip-title,
  .setup-model-tip-title1 {
    margin-bottom: var(--ant-margin-sm);
  }

  .setup-model-tip-content {
    padding: var(--ant-padding) var(--ant-padding-md);
    background-color: #f0f5fd;
    border-radius: var(--ant-border-radius);
    font-size: var(--ant-font-size);
    color: var(--ant-color-text-description);
    span {
      color: var(--ant-blue-7);
      font-family: @side-panel-font-family-bold;
    }
  }
  .check-tips {
    margin-bottom: var(--ant-margin-sm);
  }
  .setup-model-tip-title1 {
    margin-top: 18px;
  }
  .footer {
    gap: var(--ant-margin-xs);
  }
}
