@import "@/assets/styles/variables";

.card-cue {
  /* 提示词卡片 */
  // box-shadow: rgba(0, 0, 0, 0.1) 0 0 10px 1px;
  min-width: 200px;
  width: 100%;
  cursor: pointer;
  background: var(--ant-color-fill-quaternary);
  border-radius: var(--ant-border-radius) !important;
  border: 1px solid var(--ant-color-fill-quaternary) !important;
  padding: var(--ant-padding) !important;
  .ant-card-head {
    /* 提示词卡片头部 */
    // background-color: #F0F5FD;
    min-height: 40px;
    height: 40px;

    border-bottom: none;
    .ant-card-head-wrapper {
      justify-content: space-between;
      .ant-card-head-title {
        /* 提示词标题 */
        align-items: center;
        display: flex;
        flex: 1 !important;
        font-size: 14px;
        gap: 5px;
        margin-right: 5px;
        min-width: 1px;
        // .tile-icon{
        //   background: #2E7DF3;
        // }
        .title {
          font-family: @side-panel-font-family-bold;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: var(--ant-font-size-lg);
          color: var(--ant-color-text-base);
        }
      }

      .ant-card-extra {
        /* 操作区 */
        margin: 0;
        display: flex;
        gap: 5px;
        /* 卡片头部的操作按钮 */
        align-items: center;
        justify-content: center;
        flex-wrap: nowrap;

        .collection-options {
          /* 收藏选项 */
          display: flex;
          align-items: center;
          gap: 5px;
        }
      }
    }
  }

  .ant-card-body {
    /* 提示词卡片主体 */
    padding: 0px;
    .title {
      vertical-align: text-bottom;
      font-size: var(--ant-font-size-lg);
      color: var(--ant-color-text-base);
      font-family: @side-panel-font-family-bold;
      margin-left: var(--ant-margin-xs);
    }
    .prompt-card-content {
      /* 提示词内容，超出省略 */
      margin-top: var(--ant-margin-xxs);
      font-size: var(--ant-font-size);
      color: var(--ant-color-text-tertiary);
      line-height: var(--ant-line-height);
      display: -webkit-box;
      overflow: hidden;
      height: 4.5em;
      text-overflow: ellipsis;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      word-break: break-all;
    }

    .prompt-card-footer {
      /* 提示词卡片页脚 */
      margin-top: var(--ant-margin-sm);

      .name-avatar,
      .name-avatar-img {
        /* 创建人姓名头像 */
        border-radius: 5px;
        color: #fff;
        font-family: @side-panel-font-family-bold;
        text-align: center;
        height: 18px;
        width: 18px;
      }
      .name-avatar {
        background-color: @primary-color;
      }

      .times {
        /* 收藏次数 */
        color: var(--ant-color-text-tertiary);
        font-size: var(--ant-font-size-sm);
        margin-right: var(--ant-margin-xxs);
      }

      .date {
        /* 提示词创建时间 */
        color: darken(@disabled-color, 15%);
        font-family: @side-panel-font-family-bold;
        margin-left: auto;
      }
    }
  }
  .footer-tootle-wcl {
    width: 100%;
    .times {
      /* 收藏次数 */
      color: var(--ant-color-text-tertiary);
      font-size: var(--ant-font-size-sm);
      margin-right: var(--ant-margin-xxs);
    }
    .operte-icon {
      opacity: 0;
      gap: var(--ant-margin-xxs);
      .anticon {
        font-size: var(--ant-font-size);
        color: var(--ant-color-text);
      }
    }
  }
  .footer-circle-wcl {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: var(--ant-margin-xs);
  }
  .footer-circle-name {
    color: var(--ant-color-text);
    font-size: var(--ant-font-size-sm);
  }
  .cur-card-footer-tit {
    color: var(--ant-color-primary);
    font-size: var(--ant-font-size-sm);
    padding: 1px var(--ant-padding-xs);
    background: var(--ant-blue-1);
    border-radius: var(--ant-border-radius-sm);
  }
  &:hover {
    .operte-icon {
      opacity: 1;
    }
  }
}
.prompt-page-container .prompt-list .ant-card .ant-card-head {
  padding: 0px !important;
}
