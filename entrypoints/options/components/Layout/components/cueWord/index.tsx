/** 提示词*/
import React, { useEffect, useState } from "react";
import { LoadingOutlined, PlusOutlined, SearchOutlined } from "@ant-design/icons";
import { Button, Flex, Input, message, Pagination, Spin, Tabs, TabsProps, Tag } from "antd";
import PromptCard from "./components/PromptCard";
import PromptModal from "./components/PromptModal";
import { Prompt, PromptSearchParam } from "@/types/prompt";
import "../../index.less";
import "./index.less";
import { pagePrompts } from "@/api/prompt.ts";
import EmptyData from "@/components/EmptyData";
import { listAgents } from "@/api/knowdge.ts";
// 列表查询的初始化参数
const searchParamsInit: PageAPIRequest<PromptSearchParam> = {
  pageNum: 1,
  pageSize: 10,
  entity: {
    title: "",
    status: 0,
    type: "prompt_type_all",
    collection: false,
    agentId: null,
    query: "",
    createBy: JSON.parse(localStorage.getItem("user"))?.id,
  },
};

// 列表数据的初始化数据
const pageDataInit: PageAPIResponse<Prompt> = {
  current: 1,
  size: 10,
  total: 0,
  pages: 0,
  records: [],
  count: "",
};

// Tab页签数据配置
const SetupComponent: React.FC = () => {
  // 查询数据的请求参数
  const [searchParams, setSearchParams] = useState<PageAPIRequest<PromptSearchParam>>(searchParamsInit);
  // 当前显示的提示词分页数据
  const [promptPageData, setPromptPageData] = useState<PageAPIResponse<Prompt>>(pageDataInit);
  // 弹窗显隐状态
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  // 当前选中的提示词
  const [currentPrompt, setCurrentPrompt] = useState<Prompt>(null);
  const [agentList, setAgentList] = useState<Array<Prompt>>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [queryContent, setQueryContent] = useState("");
  // tag选中的数据
  const [selectedTags, setSelectedTags] = React.useState<string[]>([]);
  // 页面首次加载或请求参数变化时，获取用户信息并获取提示词数据
  useEffect(() => {}, [searchParams]);

  useEffect(() => {
    getAllAgentPageHandler();
  }, []);

  /** agnetList */
  const getAllAgentPageHandler = () => {
    // 获取个人提示词
    listAgents({}).then((res) => {
      if (res.code == "200") {
        if (res.data && res.data.length > 0) {
          let data = [...res.data];
          setSearchParams((prevParams) => ({
            ...prevParams,
            entity: { ...prevParams.entity, agentId: data[0]?.id, query: "" },
          }));
          setAgentList(data);
          setSelectedTags([data[0].agentName]);
        }
      } else {
        setLoading(false);
        message.open({
          type: "error",
          content: res.msg,
        });
      }
    });
  };
  useEffect(() => {
    if (searchParams.entity.agentId || searchParams.entity.agentId == 0) {
      handlePromptList();
    }
  }, [
    searchParams.entity.agentId,
    searchParams.entity.type,
    searchParams.entity.query,
    searchParams.pageNum,
    searchParams.pageSize,
  ]);

  /** 分页查询提示词 */
  const handlePromptList = async () => {
    // 获取个人提示词
    setLoading(false);
    await pagePrompts(searchParams)
      .then((res) => {
        if (res.code == "200") {
          setLoading(false);
          setPromptPageData(res.data);
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      })
      .catch(() => {
        setLoading(false);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  /** 处理点击新增提示词 */
  const handleAddClick = () => {
    setModalOpen(true);
    setCurrentPrompt(null);
  };
  // tag 点击处理
  const handleChange = (item) => {
    setSelectedTags([]);
    setSelectedTags([item.agentName]);
    setSearchParams({ ...searchParams, entity: { ...searchParams.entity, agentId: item.id } });
  };

  /** 处理卡片编辑提示词 */
  const handleEditClick = (prompt: Prompt) => {
    setModalOpen(true);
    setCurrentPrompt(prompt);
  };
  const collHandlerLoading = () => {
    setLoading(true);
  };
  const PromptTable: React.FC<{ type: string }> = ({ type }) => {
    return (
      <>
        <Flex className="agent-list-cue">
          {Array.isArray(agentList) &&
            agentList.map((item, index) => {
              return (
                <Tag.CheckableTag
                  key={index}
                  checked={selectedTags.includes(item.agentName)}
                  onChange={() => handleChange(item)}
                >
                  {item.agentName}
                </Tag.CheckableTag>
              );
            })}
        </Flex>
        <Spin spinning={loading} indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />}>
          {promptPageData && promptPageData.total !== 0 ? (
            <div className="setup-content-table">
              {promptPageData.records.map((item: Prompt, index: number) => {
                return (
                  <PromptCard
                    type={type}
                    key={index}
                    prompt={item}
                    deletable={true}
                    editable={true}
                    releasable={true}
                    onEdit={handleEditClick}
                    onSubmitSuccess={handlePromptList}
                    // collHandler={collHandlerLoading}
                  ></PromptCard>
                );
              })}
            </div>
          ) : (
            <div className="setup-content-table empty">
              <EmptyData description={"没有找到提示词"} />
            </div>
          )}
        </Spin>
        <Pagination
          className="pagination"
          showSizeChanger
          showQuickJumper
          onChange={(page, pageSize) => {
            setSearchParams({ ...searchParams, pageNum: page, pageSize });
          }}
          current={promptPageData.current}
          pageSize={promptPageData.size}
          total={promptPageData.total}
          showTotal={(total) => `共 ${total} 条`}
        />
      </>
    );
  };

  const tabItem: TabsProps["items"] = [
    {
      key: "0",
      label: "全部",
      children: <PromptTable type="prompt_type_all"></PromptTable>,
    },
    {
      key: "1",
      label: "我创建的",
      children: <PromptTable type="prompt_type_created"></PromptTable>,
    },
    {
      key: "2",
      label: "我收藏的",
      children: <PromptTable type="prompt_type_coll"></PromptTable>,
    },
    {
      key: "3",
      label: "广场",
      children: <PromptTable type="prompt_type_square"></PromptTable>,
    },
  ];

  /** 处理页签变更 */
  const handleTabChange = (targetKey: string) => {
    let typeList = ["prompt_type_all", "prompt_type_created", "prompt_type_coll", "prompt_type_square"];
    setSearchParams({
      ...searchParams,
      entity: {
        ...searchParams.entity,
        collection: targetKey !== "0",
        type: typeList[targetKey],
        createBy: targetKey !== "0" ? "" : JSON.parse(localStorage.getItem("user"))?.id,
      },
    });
  };

  // 搜索提示词
  const getQuery = (e) => {
    setQueryContent(e.target.value.trim());
    setSearchParams({ ...searchParams, pageNum: 1, entity: { ...searchParams.entity, query: e.target.value.trim() } });
  };
  return (
    <>
      <Flex className="setup-content-right" vertical>
        {/* header区 */}
        <Flex className="setup-content-header" justify="space-between">
          <h1 className="setup-content-title">提示词管理</h1>
          <Input
            size="large"
            value={queryContent}
            onChange={getQuery}
            allowClear
            placeholder="搜索提示词"
            prefix={<SearchOutlined />}
            style={{ width: "310px" }}
          />
        </Flex>
        {/* table数据区 */}
        <Flex className="setup-content-main" align="center">
          <Tabs items={tabItem} onChange={handleTabChange}></Tabs>
          <Button type="primary" onClick={handleAddClick} className="setup-content-title-btn" icon={<PlusOutlined />}>
            新建提示词
          </Button>
        </Flex>
      </Flex>

      {/* 新增/编辑提示词的模态框表单 */}
      <PromptModal
        open={modalOpen}
        setOpen={setModalOpen}
        agentPar={agentList}
        prompt={currentPrompt}
        onSubmitSuccess={handlePromptList}
      />
    </>
  );
};

export default SetupComponent;
