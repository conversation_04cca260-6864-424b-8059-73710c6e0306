.task-con {
  .setup-content-header {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start !important;
    align-items: flex-start !important;
    gap: var(--ant-margin-md);
    border-bottom: 1px solid var(--ant-color-border);
  }

  .setup-content-search-form-con {
    .ant-row {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between !important;
      align-items: center;
    }

    .ant-form {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between !important;
      align-items: center;
      padding-top: var(--ant-margin-md);

      .ant-form-item {
        margin-bottom: 0px;
      }
    }
  }

  .setup-content-main {
    height: 100%;
    overflow-y: auto;
    padding-top: 0 !important;
  }

  .task-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(30%, 1fr));
    gap: var(--ant-margin-md);
    margin-bottom: var(--ant-margin-lg);
    overflow-y: auto;
    padding-right: var(--ant-margin-xs);

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  .task-card {
    border-radius: var(--ant-border-radius-lg);
    transition: all 0.3s ease;
    padding: var(--ant-padding-xs);
    border: 1px solid var(--ant-color-border);
    width: 100%;

    .task-card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--ant-margin-xs);

      .task-title {
        font-size: var(--ant-font-size-md);
        font-weight: var(--ant-font-weight-strong);
        color: var(--ant-color-text-heading);
        flex: 1;
        margin-right: var(--ant-margin-sm);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .task-status {
        font-size: var(--ant-font-size-sm);
        border-radius: var(--ant-border-radius-lg);
      }
    }

    .task-card-content {
      .task-info-item {
        display: flex;
        margin-bottom: var(--ant-margin-xxs);
        align-items: center;

        .task-label {
          color: var(--ant-color-text-description);
          font-size: var(--ant-font-size-sm);
          min-width: 60px;
          margin-right: var(--ant-margin-xs);
        }

        .task-value {
          color: var(--ant-color-text);
          font-size: var(--ant-font-size-sm);

          &.task-link {
            max-width: 60%;
            color: var(--ant-color-primary);
            cursor: pointer;
            background-color: #f0f2f5;
            padding: var(--ant-padding-xxs) var(--ant-padding-xs);
            border-radius: 8px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            &:hover {
              color: var(--ant-color-primary-hover);
            }
          }
        }
      }
    }

    .task-card-actions {
      display: flex;
      justify-content: flex-end;

      .ant-btn-link {
        padding: var(--ant-padding-xxs) var(--ant-padding-xs);
        font-size: var(--ant-font-size-sm);
        height: auto;
        line-height: 1.5;

        &.ant-btn-dangerous {
          color: var(--ant-color-error);

          &:hover {
            color: var(--ant-color-error-hover);
          }
        }
      }
    }
  }

  .pagination {
    display: flex;
    justify-content: center;
    margin-top: var(--ant-margin-lg);
    position: sticky;
    bottom: 0;
    background: #fff;
    padding: var(--ant-margin-sm) 0;
    z-index: 10;
  }

  // 任务详情页面样式
  .task-detail-con {
    .search-filter-panel {
      background: #fff;
      padding: var(--ant-margin-lg);
      border-radius: var(--ant-border-radius-lg);
      margin-bottom: var(--ant-margin-lg);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .ant-form-item {
        margin-bottom: var(--ant-margin-md);
      }

      .ant-form-item-label {
        font-weight: var(--ant-font-weight-strong);
        color: var(--ant-color-text-heading);
      }
    }

    .collection-table {
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: var(--ant-font-weight-strong);
        color: var(--ant-color-text-heading);
      }

      .ant-table-tbody > tr > td {
        padding: var(--ant-padding-sm) var(--ant-padding-md);
      }

      .custom-link-button {
        padding: 0 !important;
        height: auto;
        line-height: 1.5;
        color: var(--ant-color-primary);

        &:hover {
          color: var(--ant-color-primary-hover);
        }
      }
    }
  }
}
// 新增任务弹窗样式
.add-task-modal {
  width: 800px;
  font-size: var(--ant-font-size-sm);
  top: 40px !important;

  .ant-modal-header {
    .ant-modal-title {
      font-size: var(--ant-font-size-lg);
      color: var(--ant-color-text-heading);
    }
  }

  .add-task-form {
    .ant-form-item {
      margin-bottom: var(--ant-margin-lg);
      display: flex;
      align-items: flex-start;

      .ant-form-item-label {
        color: var(--ant-color-text-heading);
        margin-bottom: var(--ant-margin-xs);
        text-align: left;
        min-width: 110px;
        &::before {
          content: "" !important;
        }
      }

      .ant-form-item-control {
        width: 800px;
        min-width: 0;
      }

      .ant-form-item-control-input {
        font-size: var(--ant-font-size-sm);
        min-height: 32px;
      }

      // 知识库选择器样式
      .ant-select-multiple {
        .ant-select-selection-item {
          background-color: var(--ant-color-primary-bg);
          border-color: var(--ant-color-primary);
          color: var(--ant-color-primary);
        }
      }

      // 新增字段样式优化
      .ant-form-item {
        &:nth-child(3),
        &:nth-child(4),
        &:nth-child(5) {
          .ant-form-item-label {
            width: 120px;
          }
        }
      }

      // 自动采集复选框样式
      .auto-collect-option {
        display: flex;
        align-items: center;
        gap: 8px;

        input[type="checkbox"] {
          width: 16px;
          height: 16px;
          margin: 0;
          cursor: pointer;
        }

        label {
          margin: 0;
          cursor: pointer;
          color: var(--ant-color-text);
          font-size: var(--ant-font-size-sm);
          user-select: none;
        }
      }
    }

    .url-table-container {
      border: 1px solid var(--ant-color-border);
      border-radius: var(--ant-border-radius-base);
      overflow: hidden;
      background: #fff;
      margin-top: var(--ant-margin-xs);
      width: 100%;
      font-size: var(--ant-font-size-sm);

      .url-table {
        width: 100%;
        display: flex;
        flex-direction: column;

        .url-table-header {
          display: flex;
          background: #fafafa;
          border-bottom: 1px solid var(--ant-color-border);
          min-height: 32px;

          .url-header-cell {
            flex: 1;
            font-weight: var(--ant-font-weight-strong);
            color: var(--ant-color-text-heading);
            text-align: center;
            border-right: 1px solid var(--ant-color-border);
            font-size: var(--ant-font-size-sm);
            display: flex;
            align-items: center;
            justify-content: center;

            &:last-child {
              border-right: none;
            }
          }
        }

        .url-table-body {
          max-height: 110px;
          overflow-y: auto;
          .url-table-row {
            display: flex;
            border-bottom: 1px solid var(--ant-color-border);
            background: #fff;
            min-height: 32px;

            &:last-child {
              border-bottom: none;
            }

            &:hover {
              background: #fafafa;
            }

            .url-cell {
              flex: 1;
              display: flex;
              align-items: center;
              justify-content: center;
              border-right: 1px solid var(--ant-color-border);

              &:last-child {
                border-right: none;
              }

              .ant-input {
                border: none;
                box-shadow: none;
                text-align: center;
                width: 100%;
                background: transparent;

                &:focus {
                  box-shadow: none;
                  border: none;
                }
              }

              input[type="checkbox"] {
                margin: 0;
                width: 16px;
                height: 16px;
              }

              .ant-btn-link {
                padding: 0 var(--ant-padding-xs);
                height: auto;
                line-height: 1.5;
                font-size: var(--ant-font-size-sm);
                margin: 0 var(--ant-margin-xs);
              }
            }
          }
        }
      }

      .url-table-footer {
        border-top: 1px solid var(--ant-color-border);
        text-align: center;
        height: 32px;

        .add-url-btn {
          color: var(--ant-color-primary);
          font-weight: var(--ant-font-weight-strong);
          font-size: var(--ant-font-size-sm);

          &:hover {
            color: var(--ant-color-primary-hover);
          }
        }
      }
    }

    .modal-footer {
      display: flex;
      justify-content: flex-end;
    }
  }
}

/* 任务详情弹窗样式 */
.task-detail-modal {
  top: 50px !important;

  .ant-modal-content {
    padding: 0 24px;
    border-radius: 8px;
  }

  .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 24px;
    margin-bottom: 0 !important;
  }

  .ant-modal-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
  }

  .ant-modal-body {
    padding: 10px 24px;
  }
}

.task-detail-content {
  .task-detail-section {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .task-detail-subtitle {
    font-size: 14px;
    font-weight: 600;
    color: #262626;
    padding-bottom: 8px;
  }

  .task-detail-row {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .task-detail-label {
    // width: 100px;
    width: 130px;
    font-weight: 500;
    color: #595959;
    flex-shrink: 0;
  }

  .task-detail-value {
    flex: 1;
    color: #262626;
    word-break: break-all;
  }
}

.url-list-table {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  overflow: hidden;

  .url-list-header {
    display: flex;
    background-color: #fafafa;
    border-bottom: 1px solid #f0f0f0;

    .url-list-header-cell {
      flex: 1;
      padding: 12px 16px;
      font-weight: 500;
      color: #262626;
      text-align: center;
      border-right: 1px solid #f0f0f0;

      &:last-child {
        border-right: none;
      }
    }
  }

  .url-list-body {
    max-height: 150px;
    overflow-y: auto;
    .url-list-row {
      display: flex;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .url-list-cell {
        flex: 1;
        padding: 12px 16px;
        color: #262626;
        text-align: center;
        border-right: 1px solid #f0f0f0;
        word-break: break-all;

        &:last-child {
          border-right: none;
        }
      }

      // 网址列（第一列）单行省略
      .url-list-cell:first-child {
        text-align: left;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: normal;
      }
    }

    .url-list-empty {
      display: flex;

      .url-list-cell {
        flex: 1;
        padding: 40px 16px;
        color: #8c8c8c;
        text-align: center;
        font-style: italic;
      }
    }
  }
}

.modal-footer {
  text-align: right;
}

// 收集信息详情弹窗样式
.detail-info-modal {
  .detail-info-content {
    .detail-info-section {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .detail-info-subtitle {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;
    }

    .detail-info-row {
      display: flex;
      align-items: flex-start;
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .detail-info-label {
      width: 140px;
      font-weight: 500;
      color: #595959;
      flex-shrink: 0;
    }

    .detail-info-value {
      flex: 1;
      color: #262626;
      word-break: break-all;
    }

    .detail-info-table {
      .ant-table-thead > tr > th {
        background-color: #fafafa;
        font-weight: 600;
        color: #262626;
      }

      .ant-table-tbody > tr > td {
        padding: 12px 16px;
      }

      .ant-table-tbody > tr:hover > td {
        background-color: #f5f5f5;
      }
    }
  }
}
