/** 任务管理 */
import {
  <PERSON>ton,
  Col,
  Flex,
  Form,
  Input,
  Pagination,
  Row,
  Card,
  Tag,
  Space,
  Table,
  DatePicker,
  Modal,
  Select,
  Spin,
  message,
  Tooltip,
  Radio,
} from "antd";
import { SearchOutlined, PlusOutlined, ArrowLeftOutlined, EyeOutlined, DownloadOutlined } from "@ant-design/icons";
import React, { useState, useEffect, useRef } from "react";

const { RangePicker } = DatePicker;
import "../../index.less";
import "./index.less";
import {
  getTaskList,
  getUrlList,
  getTaskDetail,
  deleteTask,
  addTask,
  updateTask,
} from "../../../../../../api/taskCollection";
import { getTeamList } from "../../../../../../api/knowdge";
import { getCurrentUserInfo } from "../../../../../../api/user";

// 本地URL项类型（编辑态包含可选 urlId）
type UrlItem = {
  url: string;
  isWholeSite: boolean;
  urlId?: string;
};

// 任务管理组件
const TaskCollectionComponent: React.FC = () => {
  const [form] = Form.useForm();
  const [detailSearchForm] = Form.useForm();
  const [currentView, setCurrentView] = useState<"list" | "detail">("list");
  const [taskDetailInfo, setTaskDetailInfo] = useState<any>({});
  const [isAddTaskModalOpen, setIsAddTaskModalOpen] = useState(false);
  const [urlList, setUrlList] = useState<UrlItem[]>([{ url: "", isWholeSite: false }]);

  // 详情页数据状态
  const [detailData, setDetailData] = useState({
    records: [],
    total: 0,
    size: 10,
    current: 1,
    pages: 0,
  });

  // 详情页搜索参数
  const [detailSearchParams, setDetailSearchParams] = useState({
    pageNum: 1,
    pageSize: 10,
    id: "",
    rootUrl: "",
    account: "",
    startDate: "",
    endDate: "",
  });

  // 详情页加载状态
  const [detailLoading, setDetailLoading] = useState(false);

  // 防止重复请求的标志
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // 任务详情弹窗状态
  const [isTaskDetailModalOpen, setIsTaskDetailModalOpen] = useState(false);
  const [taskDetailData, setTaskDetailData] = useState<any>(null);
  const [taskDetailLoading, setTaskDetailLoading] = useState(false);

  // 收集信息详情弹窗状态
  const [isDetailInfoModalOpen, setIsDetailInfoModalOpen] = useState(false);
  const [detailInfoData, setDetailInfoData] = useState<any>(null);

  // 编辑任务相关状态
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingTaskId, setEditingTaskId] = useState<string>("");

  // 知识库相关状态
  const [knowledgeList, setKnowledgeList] = useState<any[]>([]);
  const [knowledgeLoading, setKnowledgeLoading] = useState(false);
  const [selectedKnowledgeIds, setSelectedKnowledgeIds] = useState<string>("");

  // 当前用户信息
  const [currentUser, setCurrentUser] = useState<any>(null);

  // 自动采集状态
  const [isAutomaticChecked, setIsAutomaticChecked] = useState(false);

  // 新增/编辑弹窗中网址列表容器引用，用于滚动到底部
  const urlTableBodyRef = useRef<HTMLDivElement | null>(null);

  const [searchParams, setSearchParams] = useState({
    pageNum: 1,
    pageSize: 9,
    taskName: "",
  });
  // 顶部搜索输入本地状态（不使用表单）
  const [searchTaskName, setSearchTaskName] = useState("");

  // 任务数据状态
  const [taskData, setTaskData] = useState({
    records: [],
    total: 0,
    size: 10,
    current: 1,
    pages: 0,
  });

  // 加载状态
  const [loading, setLoading] = useState(false);

  // 获取当前用户信息（返回用户对象）
  const fetchCurrentUser = async (): Promise<any | null> => {
    try {
      const response = await getCurrentUserInfo({});
      if ((response as any).code === 200) {
        const user = (response as any).data;
        setCurrentUser(user);
        return user;
      } else {
        console.error("获取用户信息失败:", (response as any).msg);
        return null;
      }
    } catch (error) {
      console.error("获取用户信息出错:", error);
      return null;
    }
  };

  // 获取知识库列表
  const fetchKnowledgeList = async () => {
    try {
      setKnowledgeLoading(true);
      const response = await getTeamList({});

      if ((response as any).code === 200) {
        const knowledgeData = (response as any).data || [];
        setKnowledgeList(knowledgeData);
      } else {
        console.error("获取知识库列表失败:", (response as any).msg);
      }
    } catch (error) {
      console.error("获取知识库列表出错:", error);
    } finally {
      setKnowledgeLoading(false);
    }
  };

  // 获取任务列表数据
  const fetchTaskList = async () => {
    try {
      setLoading(true);
      const response = await getTaskList({
        pageNum: searchParams.pageNum,
        pageSize: searchParams.pageSize,
        taskName: searchParams.taskName,
      });

      if (response.code === 200) {
        response.data.records.unshift({
          createTime: "2025-09-07 15:27:18",
          gatherInfo: "啊",
          id: "1",
          knowledgeHouse: "测试库",
          knowledgeHouseId: "1960633519650717698",
          overallTaskStatus: "成功",
          taskName: "邮件",
          urlCount: 1,
          urlId: "595333",
          emailHost: "imap.163.com",
          emailPort: "993",
          emailSsl: "1",
          emailAccount: "<EMAIL>",
          emailPassword: "WNXesK3hsAeyy3aw",
        });
        console.log(response.data.records, "策划师");
        setTaskData(response.data);
      } else {
        console.error("获取任务列表失败:", response.msg);
        message.error("获取任务列表失败，请重试");
      }
    } catch (error) {
      console.error("获取任务列表出错:", error);
      message.error("获取任务列表失败，请重试");
    } finally {
      setLoading(false);
    }
  };

  // 监听搜索参数变化，重新获取数据
  useEffect(() => {
    fetchTaskList();
  }, [searchParams.pageNum, searchParams.pageSize, searchParams.taskName]);

  // 获取详情页数据
  const fetchDetailData = async (params?: any) => {
    try {
      setDetailLoading(true);
      const searchParams = params || detailSearchParams;

      const response = await getUrlList({
        id: searchParams.id,
        rootUrl: searchParams.rootUrl,
        account: searchParams.account,
        startDate: searchParams.startDate,
        endDate: searchParams.endDate,
        pageNum: searchParams.pageNum,
        pageSize: searchParams.pageSize,
      });

      if (response.code === 200) {
        setDetailData(response.data);
      } else {
        console.error("获取详情数据失败:", response.msg);
      }
    } catch (error) {
      console.error("获取详情数据出错:", error);
    } finally {
      setDetailLoading(false);
    }
  };

  // 监听详情页搜索参数变化，重新获取数据（排除初始加载）
  useEffect(() => {
    if (currentView === "detail" && detailSearchParams.id && !isInitialLoad) {
      fetchDetailData();
    }
  }, [
    detailSearchParams.pageNum,
    detailSearchParams.pageSize,
    detailSearchParams.rootUrl,
    detailSearchParams.account,
    detailSearchParams.startDate,
    detailSearchParams.endDate,
  ]);

  // 组件初始化时获取知识库列表
  useEffect(() => {
    fetchKnowledgeList();
  }, []);

  // 监听知识库列表变化，自动设置编辑时的选中状态
  useEffect(() => {
    if (isEditMode && editingTaskId && knowledgeList.length > 0) {
      // 重新获取任务详情来设置选中的知识库
      const setKnowledgeSelection = async () => {
        try {
          const response = await getTaskDetail(editingTaskId);
          if (response.code === 200) {
            const taskDetail = response.data;
            if (taskDetail.knowledgeHouse) {
              const knowledgeNames = taskDetail.knowledgeHouse.split(/[,，]/);
              const selectedIds = knowledgeList
                .filter((item) => {
                  // 尝试多种字段名
                  const itemName = item.libName || item.name || item.title || "";
                  if (!itemName) return false;

                  // 精确匹配
                  const exactMatch = knowledgeNames.some((name) => name.trim() === itemName.trim());
                  if (exactMatch) {
                    return true;
                  }

                  // 包含匹配（如果精确匹配失败）
                  const containsMatch = knowledgeNames.some(
                    (name) => itemName.includes(name.trim()) || name.trim().includes(itemName),
                  );
                  if (containsMatch) {
                    return true;
                  }

                  return false;
                })
                .map((item) => item.id);

              setSelectedKnowledgeIds(selectedIds.length > 0 ? selectedIds[0] : "");
            }
          }
        } catch (error) {
          console.error("设置知识库选中状态出错:", error);
        }
      };
      setKnowledgeSelection();
    }
  }, [isEditMode, editingTaskId, knowledgeList]);

  // 顶部搜索不使用表单，保留空位（onFinish 已移除）

  // 点击重置
  const reset = () => {
    setSearchParams({
      pageNum: 1,
      pageSize: 10,
      taskName: "",
    });
    form.resetFields();
  };

  // 分页变化
  const handlePageChange = (page: number, pageSize: number) => {
    setSearchParams({
      ...searchParams,
      pageNum: page,
      pageSize,
    });
  };

  // 详情页分页变化
  const handleDetailPageChange = (page: number, pageSize: number) => {
    setDetailSearchParams({
      ...detailSearchParams,
      pageNum: page,
      pageSize,
    });
  };

  // 详情页搜索提交
  const handleDetailSearch = (values: any) => {
    // 构建新的搜索参数
    const newSearchParams = {
      ...detailSearchParams,
      rootUrl: values?.rootUrl || "",
      account: values?.account || "",
      startDate: values?.startDate?.[0]?.format("YYYY-MM-DD") || "",
      endDate: values?.startDate?.[1]?.format("YYYY-MM-DD") || "",
      pageNum: 1,
    };

    // 更新搜索参数，让useEffect来处理数据获取
    setDetailSearchParams(newSearchParams);
  };

  // 详情页重置
  const handleDetailReset = () => {
    // 重置搜索参数
    setDetailSearchParams({
      ...detailSearchParams,
      rootUrl: "",
      account: "",
      startDate: "",
      endDate: "",
      pageNum: 1,
    });
    // 重置表单
    detailSearchForm.resetFields();
    // 让useEffect来处理数据获取，避免重复请求
  };

  // 新增任务
  const handleAddTask = () => {
    // 重置编辑模式状态
    setIsEditMode(false);
    setEditingTaskId("");

    setIsAddTaskModalOpen(true);
    // 获取当前用户信息
    fetchCurrentUser();
    // 设置表单默认值
    form.setFieldsValue({
      automatic: "否", // 默认不自动采集
    });
    // 重置checkbox状态
    setIsAutomaticChecked(false);
  };

  // 关闭新增任务弹窗
  const handleCloseAddTaskModal = () => {
    setIsAddTaskModalOpen(false);
    // 重置知识库选择状态
    setSelectedKnowledgeIds("");
    setUrlList([{ url: "", isWholeSite: false }]);
    // 不重置搜索表单，保持用户的搜索状态
    form.resetFields();
    // 重置编辑模式状态
    setIsEditMode(false);
    setEditingTaskId("");
    // 重置checkbox状态
    setIsAutomaticChecked(false);
  };

  // 关闭任务详情弹窗
  const handleCloseTaskDetailModal = () => {
    setIsTaskDetailModalOpen(false);
    setTaskDetailData(null);
  };

  // 关闭收集信息详情弹窗
  const handleCloseDetailInfoModal = () => {
    setIsDetailInfoModalOpen(false);
    setDetailInfoData(null);
  };

  // 提交新增任务
  const handleSubmitAddTask = async (values: any) => {
    try {
      // 验证至少有一个有效的URL
      const validUrls = urlList.filter((url) => url.url.trim() !== "");
      if (validUrls.length === 0) {
        message.error("请至少添加一个有效的网址");
        return;
      }

      // 验证URL格式
      const invalidUrls = validUrls.filter((url) => {
        try {
          new URL(url.url);
          return false;
        } catch {
          return true;
        }
      });

      if (invalidUrls.length > 0) {
        message.error("请检查网址格式是否正确");
        return;
      }

      if (values.taskName.trim() === "") {
        message.error("任务名称不能为空");
        return;
      }

      if (values.gatherInfo.trim() === "") {
        message.error("收集信息不能为空");
        return;
      }

      // 获取选中的知识库名称 - 单选模式
      const selectedKnowledgeItem = knowledgeList.find((item) => item.id === selectedKnowledgeIds);
      const selectedKnowledgeNames = selectedKnowledgeItem
        ? selectedKnowledgeItem.libName || selectedKnowledgeItem.name || "未命名知识库"
        : "";

      // 构建任务数据，按照接口要求的格式
      const taskData = {
        taskName: values.taskName,
        knowledgeHouse: selectedKnowledgeNames,
        knowledgeHouseId: selectedKnowledgeItem?.id,
        gatherInfo: values.gatherInfo,
        autoExecute: isAutomaticChecked ? "是" : "否", // 根据复选框状态设置
        urlList: validUrls.map((url) => {
          const effectiveUser = currentUser; // 这里优先使用已缓存的用户
          if (isEditMode) {
            return {
              rootUrl: url.url, // 当前网址
              urlId: url.urlId, // 传入urlId
              automatic: isAutomaticChecked ? "是" : "否", // 根据复选框状态设置
              allTake: url.isWholeSite ? "是" : "否", // 是否整站获取
              account: effectiveUser?.nickName || effectiveUser?.email || "unknown", // 默认使用当前用户名称
            };
          } else {
            // 新增模式：只有rootUrl字段
            return {
              rootUrl: url.url,
              automatic: isAutomaticChecked ? "是" : "否", // 根据复选框状态设置
              allTake: url.isWholeSite ? "是" : "否", // 是否整站获取
              account: effectiveUser?.nickName || effectiveUser?.email || "unknown", // 默认使用当前用户名称
            };
          }
        }),
      };
      // 根据编辑模式调用不同的接口
      let response;
      if (isEditMode) {
        // 编辑模式：调用更新接口
        const updateData = {
          ...taskData,
          id: editingTaskId, // 添加任务ID
          category: "2",
        };
        response = await updateTask(updateData);
      } else {
        // 新增模式：调用新增接口
        response = await addTask(taskData);
      }

      if (response.code === 200) {
        // 操作成功
        const successMessage = isEditMode ? "任务更新成功！" : "任务创建成功！";
        message.success(successMessage);

        // 关闭弹窗并重置状态
        setIsAddTaskModalOpen(false);
        setSelectedKnowledgeIds("");
        setUrlList([{ url: "", isWholeSite: false }]);
        setIsEditMode(false);
        setEditingTaskId("");

        // 刷新任务列表
        fetchTaskList();
      } else {
        // 操作失败
        const errorMessage = isEditMode ? "更新失败" : "创建失败";
        message.error(`${errorMessage}：${response.msg || "未知错误"}`);
      }
    } catch (error) {
      console.error("创建任务出错:", error);
      message.error("创建任务时发生错误，请重试");
    }
  };

  // 查看详情
  const handleViewDetail = async (record: any) => {
    console.log(record.id, "策划师");
    if (record.id == "1") {
      setTaskDetailData({
        id: "275333",
        taskName: "邮件",
        gatherInfo: "啊",
        knowledgeHouseId: "1960633519650717698",
        knowledgeHouse: "测试库",
        autoExecute: "是",
        overallTaskStatus: "成功",
        createTime: "2025-09-07 15:27:18",
        account: "朱亚伟1",
        urlList: [
          {
            urlId: "595333",
            url: "https://baidu.com",
            allTake: "否",
            automatic: "是",
            taskStatus: "成功",
          },
        ],
        emailHost: "imap.163.com",
        emailPort: "993",
        emailSsl: "是",
        emailAccount: "<EMAIL>",
        emailPassword: "WNXesK3hsAeyy3aw",
      });
      setIsTaskDetailModalOpen(true);
    } else {
      try {
        setTaskDetailLoading(true);
        const response = await getTaskDetail(record.id);

        if (response.code === 200) {
          setTaskDetailData(response.data);
          setIsTaskDetailModalOpen(true);
        } else {
          console.error("获取任务详情失败:", response.msg);
        }
      } catch (error) {
        console.error("获取任务详情出错:", error);
      } finally {
        setTaskDetailLoading(false);
      }
    }
  };

  // 跳转到收集信息详情页
  const handleCollectionInfoClick = (record: any) => {
    // 设置任务详情信息
    setTaskDetailInfo({
      taskId: record.id,
      taskTitle: record.taskName,
      collectionInfo: record.gatherInfo,
      urlCount: record.urlCount,
      createTime: record.createTime,
    });

    // 设置详情页搜索参数
    const newDetailSearchParams = {
      ...detailSearchParams,
      id: record.id,
      pageNum: 1,
      pageSize: 10,
    };
    setDetailSearchParams(newDetailSearchParams);

    // 切换到详情页视图
    setCurrentView("detail");

    // 设置初始加载标志
    setIsInitialLoad(false);

    // 立即获取详情数据
    fetchDetailData(newDetailSearchParams);
  };

  // 编辑任务
  const handleEditTask = async (record: any) => {
    try {
      // 获取任务详情
      const response = await getTaskDetail(record.id);

      if (response.code === 200) {
        const taskDetail = response.data;

        // 设置编辑模式
        setIsEditMode(true);
        setEditingTaskId(record.id);

        // 解析URL列表 - 根据返回数据结构调整
        const urlListData = taskDetail.urlList || [];
        const formattedUrlList = urlListData.map((url: any) => ({
          url: url.url || "", // 使用 url 字段
          isWholeSite: url.allTake === "是",
          urlId: url.urlId, // 保存返回的urlId
        }));
        setUrlList(formattedUrlList.length > 0 ? formattedUrlList : [{ url: "", isWholeSite: false, urlId: "" }]);

        // 解析知识库名称并设置选中状态
        let selectedKnowledgeId = "";
        if (taskDetail.knowledgeHouse) {
          const knowledgeNames = taskDetail.knowledgeHouse.split(/[,，]/);

          const selectedIds = knowledgeList
            .filter((item) => {
              // 尝试多种字段名
              const itemName = item.libName || item.name || item.title || "";
              if (!itemName) return false;

              // 精确匹配
              const exactMatch = knowledgeNames.some((name) => name.trim() === itemName.trim());
              if (exactMatch) {
                return true;
              }

              // 包含匹配（如果精确匹配失败）
              const containsMatch = knowledgeNames.some(
                (name) => itemName.includes(name.trim()) || name.trim().includes(itemName),
              );
              if (containsMatch) {
                return true;
              }

              return false;
            })
            .map((item) => item.id);

          selectedKnowledgeId = selectedIds.length > 0 ? selectedIds[0] : "";
          setSelectedKnowledgeIds(selectedKnowledgeId);
        }

        // 设置表单数据
        form.setFieldsValue({
          taskName: taskDetail.taskName,
          gatherInfo: taskDetail.gatherInfo,
          automatic: taskDetail.autoExecute === "是" || taskDetail.automatic === "是" ? "是" : "否", // 根据任务详情设置
          knowledgeBase: selectedKnowledgeId || undefined, // 设置知识库字段
        });

        // 设置checkbox状态 - 根据任务详情中的自动采集字段设置
        const isAutomatic = taskDetail.autoExecute === "是" || taskDetail.automatic === "是";
        setIsAutomaticChecked(isAutomatic);

        // 打开弹窗
        setIsAddTaskModalOpen(true);

        // 获取当前用户信息
        fetchCurrentUser();
      } else {
        message.error(`获取任务详情失败：${response.msg || "未知错误"}`);
      }
    } catch (error) {
      console.error("获取任务详情出错:", error);
      message.error("获取任务详情时发生错误，请重试");
    }
  };

  // 删除任务
  const handleDeleteTask = async (record: any) => {
    // 使用 Ant Design 的确认对话框
    Modal.confirm({
      title: "确认删除",
      content: `确定要删除任务"${record.taskName}"吗？此操作不可恢复。`,
      okText: "确定删除",
      cancelText: "取消",
      okType: "danger",
      onOk: async () => {
        try {
          // 调用删除接口
          const response = await deleteTask(record.id);

          if (response.code === 200) {
            // 删除成功，刷新任务列表
            message.success("任务删除成功！");

            // 如果当前页只有一条数据且不是第一页，则跳转到上一页
            if (taskData.records.length === 1 && searchParams.pageNum > 1) {
              setSearchParams({
                ...searchParams,
                pageNum: searchParams.pageNum - 1,
              });
            } else {
              // 否则刷新当前页
              fetchTaskList();
            }
          } else {
            // 删除失败
            message.error(`删除失败：${response.msg || "未知错误"}`);
          }
        } catch (error) {
          console.error("删除任务出错:", error);
          message.error("删除任务时发生错误，请重试");
        }
      },
    });
  };

  // 返回任务列表页
  const handleBackToList = () => {
    setCurrentView("list");
    setTaskDetailInfo({});
    setDetailData({
      records: [],
      total: 0,
      size: 10,
      current: 1,
      pages: 0,
    });
    setDetailSearchParams({
      pageNum: 1,
      pageSize: 10,
      id: "",
      rootUrl: "",
      account: "",
      startDate: "",
      endDate: "",
    });
    setIsInitialLoad(true);
  };

  // 搜索
  const handleSearch = () => {
    setSearchParams({
      ...searchParams,
      pageNum: 1, // 搜索时重置到第一页
      taskName: searchTaskName,
    });
    // 移除直接调用 fetchTaskList()，让 useEffect 来处理
  };

  // 搜索详情
  const handleSearchDetail = () => {
    setDetailSearchParams({
      ...detailSearchParams,
      rootUrl: form.getFieldsValue().rootUrl,
      account: form.getFieldsValue().account,
      startDate: form.getFieldsValue().startDate?.[0]?.format("YYYY-MM-DD") || "",
      endDate: form.getFieldsValue().startDate?.[1]?.format("YYYY-MM-DD") || "",
    });
    fetchDetailData();
  };

  // 添加网址行
  const handleAddUrl = () => {
    setUrlList([...urlList, { url: "", isWholeSite: false, urlId: "" }]);
    // 新增一行后滚动到底部，确保新行可见
    requestAnimationFrame(() => {
      const container = urlTableBodyRef.current;
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    });
  };

  // 删除网址行
  const handleDeleteUrl = (index: number) => {
    if (urlList.length > 1) {
      const newUrlList = urlList.filter((_, i) => i !== index);
      setUrlList(newUrlList);
    }
  };

  // 处理网址或复选框变化
  const handleUrlChange = (index: number, field: "url" | "isWholeSite", value: string | boolean) => {
    const newUrlList = [...urlList];
    if (field === "url") {
      // 处理url字段变化
      newUrlList[index] = { ...newUrlList[index], url: value as string };
    } else {
      // 处理isWholeSite字段变化
      newUrlList[index] = { ...newUrlList[index], isWholeSite: value as boolean };
    }
    setUrlList(newUrlList);
  };

  // 处理知识库选择变化 - 单选模式
  const handleKnowledgeChange = (value: string) => {
    setSelectedKnowledgeIds(value);
  };

  // 查看收集信息详情
  const handleDetailInfo = (record: any) => {
    setDetailInfoData(record);
    setIsDetailInfoModalOpen(true);
  };

  // 渲染任务列表页
  const renderTaskList = () => (
    <>
      {/* header区 */}
      <Flex className="setup-content-header">
        <h1 className="setup-content-title">任务管理</h1>
      </Flex>

      {/* 搜索区域（移除表单，保持样式不变） */}
      <div className="setup-content-search-form-con">
        <div
          className="setup-content-search-form"
          style={{ marginBottom: "var(--ant-margin-md)", marginTop: "var(--ant-margin-md)" }}
        >
          <Row gutter={16} align="middle">
            <Col>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                size="large"
                onClick={handleAddTask}
                className="setup-content-title-btn"
              >
                新增任务
              </Button>
            </Col>
            <Col>
              <Input
                size="large"
                allowClear
                placeholder="请输入任务名称,查询任务收集情况"
                prefix={<SearchOutlined />}
                onPressEnter={handleSearch}
                style={{ width: "400px" }}
                value={searchTaskName}
                onChange={(e) => setSearchTaskName(e.target.value)}
              />
            </Col>
          </Row>
        </div>
      </div>

      {/* 任务卡片区域 */}
      <div className="setup-content-main">
        <div className="task-grid">
          {loading ? (
            <div style={{ textAlign: "center", padding: "40px", color: "#999" }}>加载中...</div>
          ) : taskData.records.length === 0 ? (
            <div style={{ textAlign: "center", padding: "40px", color: "#999" }}>暂无数据</div>
          ) : (
            taskData.records.map((task) => (
              <div key={task.id} className="task-card">
                <div className="task-card-header">
                  <span className="task-title">{task.taskName}</span>
                  <Tag
                    color={
                      task.overallTaskStatus === "完成"
                        ? "success"
                        : task.overallTaskStatus === "失败"
                          ? "error"
                          : "processing"
                    }
                    className="task-status"
                  >
                    {task.overallTaskStatus}
                  </Tag>
                </div>
                <div className="task-card-content">
                  <div className="task-info-item">
                    <span className="task-label">网址数量:</span>
                    <span className="task-value">{task.urlCount}</span>
                  </div>
                  <div className="task-info-item">
                    <span className="task-label">收集信息:</span>
                    <span
                      className="task-value task-link"
                      onClick={() => handleCollectionInfoClick(task)}
                      style={{ cursor: "pointer" }}
                    >
                      {task.gatherInfo}
                    </span>
                  </div>
                  <div className="task-info-item">
                    <span className="task-label">创建时间:</span>
                    <span className="task-value">{task.createTime}</span>
                  </div>
                </div>
                <div className="task-card-actions">
                  <Space>
                    <Button type="link" onClick={() => handleViewDetail(task)}>
                      详情
                    </Button>
                    <Button type="link" onClick={() => handleEditTask(task)}>
                      编辑
                    </Button>
                    <Button type="link" danger onClick={() => handleDeleteTask(task)}>
                      删除
                    </Button>
                  </Space>
                </div>
              </div>
            ))
          )}
        </div>

        {/* 分页 */}
        <Pagination
          className="pagination"
          onChange={handlePageChange}
          current={taskData.current}
          pageSize={taskData.size}
          total={taskData.total}
          showTotal={(total) => `共 ${total} 条`}
        />
      </div>
    </>
  );

  // 渲染任务详情页
  const renderTaskDetail = () => (
    <>
      {/* header区 */}
      <Flex className="setup-content-header" vertical>
        <Flex align="center">
          <h1 className="setup-content-title">任务管理</h1>
        </Flex>
      </Flex>

      <div className="setup-content-search-form-con">
        <Button
          type="link"
          icon={<ArrowLeftOutlined style={{ color: "var(--ant-color-primary)" }} />}
          onClick={handleBackToList}
          style={{
            padding: 0,
            height: "auto",
            fontSize: "var(--ant-font-size-md)",
            marginTop: "var(--ant-margin-md)",
            marginBottom: "var(--ant-margin-md)",
            color: "var(--ant-color-text)",
          }}
        >
          返回任务列表页
        </Button>
      </div>

      {/* 搜索筛选区域 */}
      <div className="search-filter-panel">
        <Form form={detailSearchForm} onFinish={handleDetailSearch} layout="horizontal">
          <Row gutter={16}>
            <Col>
              <Form.Item label="收集网址" name="rootUrl">
                <Input placeholder="请输入网址" style={{ width: 200 }} allowClear />
              </Form.Item>
            </Col>
            <Col>
              <Form.Item label="收集账号" name="account">
                <Input placeholder="请输入账号" style={{ width: 200 }} allowClear />
              </Form.Item>
            </Col>
            <Col>
              <Form.Item label="收集时间" name="startDate">
                <RangePicker placeholder={["开始日期", "结束日期"]} style={{ width: 300 }} />
              </Form.Item>
            </Col>
            <Col>
              <Space>
                <Button type="primary" icon={<SearchOutlined />} htmlType="submit">
                  搜索
                </Button>
                <Button onClick={handleDetailReset}>重置</Button>
              </Space>
            </Col>
          </Row>
        </Form>
      </div>

      {/* 数据表格区域 */}
      <div className="setup-content-main">
        <Table
          columns={[
            {
              title: "收集网址",
              dataIndex: "rootUrl",
              key: "rootUrl",
              ellipsis: true,
              width: 200,
              render: (text) => (
                <Tooltip placement="topLeft" title={text}>
                  <span>{text}</span>
                </Tooltip>
              ),
            },
            {
              title: "收集信息",
              dataIndex: "gatherInfo",
              key: "gatherInfo",
              ellipsis: true,
              width: 200,
              render: (text) => (
                <Tooltip placement="topLeft" title={text}>
                  <span>{text}</span>
                </Tooltip>
              ),
            },
            {
              title: "收集时间",
              dataIndex: "createTime",
              key: "createTime",
              width: 180,
              sorter: true,
              render: (text: string) => <span>{text}</span>,
            },
            {
              title: "收集账号",
              dataIndex: "account",
              key: "account",
              width: 150,
            },
            {
              title: "操作",
              key: "action",
              width: 80,
              render: (_, record: any) => (
                <Space size="small">
                  <Button type="link" onClick={() => handleDetailInfo(record)}>
                    查看
                  </Button>
                </Space>
              ),
            },
          ]}
          dataSource={detailData.records}
          rowKey={(record) => `${record.rootUrl}-${record.createTime}`}
          pagination={false}
          loading={detailLoading}
          className="collection-table"
        />

        {/* 分页 */}
        <Pagination
          className="pagination"
          onChange={handleDetailPageChange}
          current={detailData.current}
          pageSize={detailData.size}
          total={detailData.total}
          showTotal={(total) => `共${total}条`}
          pageSizeOptions={["10", "20", "50"]}
        />
      </div>
    </>
  );

  return (
    <>
      <Flex className="task-con setup-content-right" vertical>
        {currentView === "list" ? renderTaskList() : renderTaskDetail()}
      </Flex>

      {/* 新增/编辑任务弹窗 */}
      <Modal
        title={isEditMode ? "编辑收集任务" : "新增收集任务"}
        open={isAddTaskModalOpen}
        onCancel={handleCloseAddTaskModal}
        footer={null}
        width={800}
        className="add-task-modal"
      >
        <Form form={form} onFinish={handleSubmitAddTask} layout="horizontal" className="add-task-form">
          {/* 任务名称 */}
          <Form.Item label="任务名称" name="taskName" rules={[{ required: true, message: "请输入任务名称" }]}>
            <Input placeholder="请输入任务名称" style={{ width: "100%" }} maxLength={255} showCount />
          </Form.Item>

          {/* 网址列表 */}
          <Form.Item label="网址列表" name="urlList">
            <div className="url-table-container">
              <div className="url-table">
                <div className="url-table-header">
                  <div className="url-header-cell">网址</div>
                  <div className="url-header-cell">是否整站获取</div>
                  <div className="url-header-cell">操作</div>
                </div>
                <div className="url-table-body" ref={urlTableBodyRef}>
                  {urlList.map((url, index) => (
                    <div key={index} className="url-table-row">
                      <div className="url-cell">
                        <Input
                          placeholder="请输入网址 (如: https://example.com)"
                          value={url.url}
                          onChange={(e) => handleUrlChange(index, "url", e.target.value)}
                        />
                      </div>
                      <div className="url-cell">
                        <input
                          type="checkbox"
                          checked={url.isWholeSite}
                          onChange={(e) => handleUrlChange(index, "isWholeSite", e.target.checked)}
                        />
                      </div>
                      <div className="url-cell">
                        <Space>
                          <Button type="link" size="small" danger onClick={() => handleDeleteUrl(index)}>
                            删除
                          </Button>
                        </Space>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              <div className="url-table-footer">
                <Button type="link" icon={<PlusOutlined />} className="add-url-btn" onClick={handleAddUrl}>
                  新增
                </Button>
              </div>
            </div>
          </Form.Item>

          {/* 邮件服务器地址 */}
          <Form.Item label="邮件服务器地址" name="emailServiceAddress">
            <Input placeholder="请输入邮件服务器地址" style={{ width: "100%" }} />
          </Form.Item>

          {/* 邮件服务器端口 */}
          <Form.Item label="邮件服务器端口" name="emailServiceProt">
            <Input placeholder="请输入邮件服务器端口" style={{ width: "100%" }} />
          </Form.Item>

          {/* 是否使用SSL */}
          <Form.Item label="是否使用SSL" name="ssl">
            {/* <Input placeholder="请选择是否使用SSL" style={{ width: "100%" }} /> */}
            <Radio.Group
              name="radiogroup"
              defaultValue={1}
              options={[
                { value: 1, label: "是" },
                { value: 2, label: "否" },
              ]}
            />
          </Form.Item>

          {/* 邮件账号 */}
          <Form.Item label="邮件账号" name="emailAccount">
            <Input placeholder="请输入邮件账号" style={{ width: "100%" }} />
          </Form.Item>

          {/* 邮件密码或授权码 */}
          <Form.Item label="邮件密码或授权码" name="emailPassword">
            <Input placeholder="请输入邮件密码或授权码" style={{ width: "100%" }} />
          </Form.Item>

          {/* 收集信息 */}
          <Form.Item label="收集信息" name="gatherInfo" rules={[{ required: true, message: "请输入收集信息" }]}>
            <Input.TextArea placeholder="请输入收集信息" rows={3} style={{ width: "100%" }} />
          </Form.Item>

          {/* 知识库 */}
          <Form.Item label="知识库" name="knowledgeBase" rules={[{ required: true, message: "请选择知识库" }]}>
            <Select
              placeholder="请选择知识库"
              style={{ width: "40%" }}
              loading={knowledgeLoading}
              value={selectedKnowledgeIds}
              onChange={handleKnowledgeChange}
              optionFilterProp="label"
              showSearch
              filterOption={(input, option) => (option?.label ?? "").toLowerCase().includes(input.toLowerCase())}
              options={knowledgeList.map((item: any) => ({
                label: item.libName || item.name || "未命名知识库",
                value: item.id,
              }))}
            />
          </Form.Item>

          {/* 是否自动采集 */}
          <Form.Item label="是否自动采集" name="automatic" rules={[{ required: true, message: "请选择是否自动采集" }]}>
            <div className="auto-collect-option">
              <input
                type="checkbox"
                id="automaticCheckbox"
                checked={isAutomaticChecked}
                onChange={(e) => {
                  const newValue = e.target.checked ? "是" : "否";
                  setIsAutomaticChecked(e.target.checked);
                  form.setFieldValue("automatic", newValue);
                  form.setFieldsValue({ automatic: newValue });
                }}
              />
              <label htmlFor="automaticCheckbox">启用自动采集</label>
            </div>
          </Form.Item>

          {/* 操作按钮 */}
          <div className="modal-footer">
            <Space>
              <Button onClick={handleCloseAddTaskModal}>取消</Button>
              <Button type="primary" htmlType="submit">
                {isEditMode ? "更新" : "确定"}
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>

      {/* 任务详情弹窗 */}
      <Modal
        title="任务详情"
        open={isTaskDetailModalOpen}
        onCancel={handleCloseTaskDetailModal}
        footer={null}
        width={800}
        className="task-detail-modal"
      >
        {taskDetailLoading ? (
          <div style={{ textAlign: "center", padding: "40px" }}>
            <Spin size="large" />
            <div style={{ marginTop: "16px" }}>加载中...</div>
          </div>
        ) : taskDetailData ? (
          <div className="task-detail-content">
            {/* 任务基本信息 */}
            <div className="task-detail-section">
              <div className="task-detail-row">
                <div className="task-detail-label">任务名称：</div>
                <div className="task-detail-value">{taskDetailData.taskName}</div>
              </div>
              <div className="task-detail-row">
                <div className="task-detail-label">收集信息：</div>
                <div className="task-detail-value">{taskDetailData.gatherInfo}</div>
              </div>
              <div className="task-detail-row">
                <div className="task-detail-label">知识库：</div>
                <div className="task-detail-value">{taskDetailData.knowledgeHouse}</div>
              </div>
              <div className="task-detail-row">
                <div className="task-detail-label">创建时间：</div>
                <div className="task-detail-value">{taskDetailData.createTime}</div>
              </div>
              <div className="task-detail-row">
                <div className="task-detail-label">账号：</div>
                <div className="task-detail-value">{taskDetailData.account}</div>
              </div>
              <div className="task-detail-row">
                <div className="task-detail-label">自动采集：</div>
                <div className="task-detail-value">{taskDetailData.autoExecute}</div>
              </div>
              <div className="task-detail-row">
                <div className="task-detail-label">任务状态：</div>
                <div className="task-detail-value">{taskDetailData.overallTaskStatus}</div>
              </div>
              {/* 策划师 */}
              <div className="task-detail-row">
                <div className="task-detail-label">邮件服务器地址：</div>
                <div className="task-detail-value">{taskDetailData.emailHost}</div>
              </div>
              <div className="task-detail-row">
                <div className="task-detail-label">邮件服务器端口：</div>
                <div className="task-detail-value">{taskDetailData.emailPort}</div>
              </div>
              <div className="task-detail-row">
                <div className="task-detail-label">是否使用SSL：</div>
                <div className="task-detail-value">{taskDetailData.emailSsl}</div>
              </div>
              <div className="task-detail-row">
                <div className="task-detail-label">邮件账号：</div>
                <div className="task-detail-value">{taskDetailData.emailAccount}</div>
              </div>
              <div className="task-detail-row">
                <div className="task-detail-label">邮件密码或授权码：</div>
                <div className="task-detail-value">{taskDetailData.emailPassword}</div>
              </div>
            </div>

            {/* 网址列表表格 */}
            {/* <div className="task-detail-section">
              <div className="task-detail-subtitle">网址列表</div>
              <div className="url-list-table">
                <div className="url-list-header">
                  <div className="url-list-header-cell">网址</div>
                  <div className="url-list-header-cell">是否整站获取</div>
                  <div className="url-list-header-cell">网址状态</div>
                </div>
                <div className="url-list-body">
                  {taskDetailData.urlList && taskDetailData.urlList.length > 0 ? (
                    taskDetailData.urlList.map((url: any, index: number) => (
                      <div key={index} className="url-list-row">
                        <div className="url-list-cell">
                          <Tooltip title={url.url} placement="topLeft">
                            <span>{url.url}</span>
                          </Tooltip>
                        </div>
                        <div className="url-list-cell">{url.allTake}</div>
                        <div className="url-list-cell">{url.taskStatus}</div>
                      </div>
                    ))
                  ) : (
                    <div className="url-list-empty">
                      <div className="url-list-cell">暂无数据</div>
                      <div className="url-list-cell">暂无数据</div>
                      <div className="url-list-cell">暂无数据</div>
                    </div>
                  )}
                </div>
              </div>
            </div> */}

            {/* 操作按钮 */}
            <div className="modal-footer">
              <Button onClick={handleCloseTaskDetailModal}>关闭</Button>
            </div>
          </div>
        ) : null}
      </Modal>

      {/* 收集信息详情弹窗 */}
      <Modal
        title="收集信息详情"
        open={isDetailInfoModalOpen}
        onCancel={handleCloseDetailInfoModal}
        footer={null}
        width={600}
        className="detail-info-modal"
      >
        {detailInfoData ? (
          <div className="detail-info-content">
            {/* 基本信息 */}
            <div className="detail-info-section">
              <div className="detail-info-row">
                <div className="detail-info-label">收集网址：</div>
                <div className="detail-info-value">{detailInfoData.rootUrl}</div>
              </div>
              <div className="detail-info-row">
                <div className="detail-info-label">收集信息：</div>
                <div className="detail-info-value">{detailInfoData.gatherInfo}</div>
              </div>
              <div className="detail-info-row">
                <div className="detail-info-label">收集时间：</div>
                <div className="detail-info-value">{detailInfoData.createTime}</div>
              </div>
              <div className="detail-info-row">
                <div className="detail-info-label">收集账号：</div>
                <div className="detail-info-value">{detailInfoData.account}</div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="modal-footer">
              <Button onClick={handleCloseDetailInfoModal}>关闭</Button>
            </div>
          </div>
        ) : null}
      </Modal>
    </>
  );
};

export default TaskCollectionComponent;
