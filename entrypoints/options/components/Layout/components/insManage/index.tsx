/** 对话记录*/
import React, { useEffect, useState } from "react";
import { LoadingOutlined, SearchOutlined, DragOutlined, EyeInvisibleOutlined, EyeOutlined } from "@ant-design/icons";
import { Card, Flex, message, Spin, Tag, Input, Tooltip } from "antd";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import "../../index.less";
import "./index.less";
import { getAcctIns, setInsConfig } from "@/api/ins";
import EmptyData from "@/components/EmptyData";
// 列表查询的初始化参数
const searchParamsInit = {
  queryContent: "",
  insShowType: "2",
};

// Tab页签数据配置
const SetupComponent: React.FC = () => {
  // 查询数据的请求参数
  const [searchParams, setSearchParams] = useState(searchParamsInit);
  const [insShowList, setInsShowList] = useState([]); // 显示list
  const [insHiddenList, setInsHiddenList] = useState([]); // 隐藏list
  // 指令数据
  const [insData, setInsData] = useState([{ id: 1, name: 1 }]);
  const [typeList, setTypeList] = useState([
    { id: 2, name: "聊天窗口" },
    { id: 1, name: "划词菜单" },
  ]);
  const [loading, setLoading] = useState<boolean>(true);
  // tag选中的数据
  const [selectedTags, setSelectedTags] = React.useState<string[]>(["聊天窗口"]);
  useEffect(() => {
    getAcctInsList();
  }, [searchParams]);

  // 处理拖拽逻辑
  const onDragEnd = (result) => {
    if (!result.destination) return; // 如果拖拽没有落到有效位置，则不做任何处理
    const newList = Array.from(insShowList);
    const [movedItem] = newList.splice(result.source.index, 1); // 移除拖拽的元素
    newList.splice(result.destination.index, 0, movedItem); // 插入到目标位置
    setInsShowList(newList); // 更新状态

    // 调用 setInsRank 时传递更新后的 showList 和 hiddenList
    setInsRank(newList, insHiddenList);
  };

  /** 指令列表 */
  const getAcctInsList = () => {
    setLoading(true);
    getAcctIns(searchParams)
      .then((res: any) => {
        if (res.code == "200") {
          setLoading(false);
          let arrHide = [];
          let arrShow = [];
          setInsData(res.data);
          res.data.forEach((item) => {
            if (item.accountShowFlag == 0) {
              arrHide.push(item);
            } else if (item.accountShowFlag == 1 || item.accountShowFlag == "" || item.accountShowFlag == null) {
              arrShow.push(item);
            }
          });
          setInsHiddenList(arrHide);
          setInsShowList(arrShow);
        } else {
          setInsData([]);
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      })
      .catch(() => {
        setLoading(false);
      })
      .finally(() => {
        setLoading(false);
      });
  };
  // tag 点击处理
  const handleChange = (item) => {
    setSelectedTags([]);
    setSelectedTags([item.name]);
    setSearchParams({ ...searchParams, insShowType: item.id });
  };

  // 关键词搜索
  const getQuery = (e) => {
    setSearchParams({ ...searchParams, queryContent: e.target.value.trim() });
  };

  // 提示词隐藏显示
  const setInsIsShow = (item, type) => {
    setInsShowList((prevShowList) => {
      const newShowList = type === 1 ? prevShowList.filter((i) => i.id !== item.id) : [item, ...prevShowList];

      setInsHiddenList((prevHiddenList) => {
        const newHiddenList = type === 1 ? [item, ...prevHiddenList] : prevHiddenList.filter((i) => i.id !== item.id);

        // 在这里调用 setInsRank，确保在 state 更新后执行
        setInsRank(newShowList, newHiddenList);

        return newHiddenList;
      });

      return newShowList;
    });
  };

  // 排序根隐藏显示
  const setInsRank = (newShowList, newHiddenList) => {
    let dataList = [...newShowList, ...newHiddenList];
    let arr = [];
    dataList.forEach((item, index) => {
      if (index < newShowList.length) {
        arr.push({
          insId: item.id,
          arrange: index + 1,
          showFlag: 1,
          insShowType: searchParams.insShowType,
          actCfgId: item.actCfgId,
        });
      } else {
        arr.push({
          insId: item.id,
          arrange: index + 1,
          showFlag: 0,
          insShowType: searchParams.insShowType,
          actCfgId: item.actCfgId,
        });
      }
    });
    setInsConfig(arr).then((res) => {
      if (res.code == "200") {
        getAcctInsList();
      } else {
        message.open({
          type: "error",
          content: res.msg,
        });
      }
    });
  };
  return (
    <>
      <Flex className="setup-content-right" vertical>
        {/* header区 */}
        <Flex className="setup-content-header" justify="space-between">
          <h1 className="setup-content-title">指令管理</h1>
          {/* <Input
            size="large"
            value={searchParams.queryContent}
            onChange={getQuery}
            allowClear
            placeholder="输入任意内容进行搜索"
            prefix={<SearchOutlined />}
            style={{ width: "310px" }}
          /> */}
        </Flex>
        {/* table数据区 */}
        <Flex className="ins-record-main">
          <Flex className="session-record">
            {Array.isArray(typeList) &&
              typeList.map((item, index) => {
                return (
                  <Tag.CheckableTag
                    key={index}
                    checked={selectedTags.includes(item.name)}
                    onChange={() => handleChange(item)}
                  >
                    {item.name}
                  </Tag.CheckableTag>
                );
              })}
          </Flex>
          <Flex className="ins-tips">
            在{"在列表中显示"}中的选项会出现在{searchParams.insShowType == "2" ? "聊天窗口中" : "划词菜单中"}
          </Flex>
          <Spin spinning={loading} indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />}>
            {insData && insData.length > 0 ? (
              <Flex className="ins-con">
                <Flex className="ins-con-show" vertical>
                  <Flex className="ins-con-tit" align="center">
                    在指令列表中显示
                  </Flex>
                  {insShowList && insShowList.length > 0 && (
                    <DragDropContext onDragEnd={onDragEnd}>
                      <Droppable droppableId="droppable-list">
                        {(provided) => (
                          <Flex {...provided.droppableProps} ref={provided.innerRef} vertical>
                            {insShowList.map((item, index) => (
                              <Draggable key={item.id} draggableId={item.id} index={index}>
                                {(provided) => (
                                  <Flex
                                    ref={provided.innerRef}
                                    {...provided.draggableProps}
                                    {...provided.dragHandleProps}
                                    className="ins-con-sign"
                                  >
                                    <Card className="ins-card">
                                      <Flex justify="space-between">
                                        <DragOutlined style={{ cursor: "grab" }} className="ins-icon-scale" />
                                        <Flex className="ins-name">{item.name}</Flex>
                                        <Tooltip placement="top" title="隐藏">
                                          <EyeInvisibleOutlined onClick={() => setInsIsShow(item, 1)} />
                                        </Tooltip>
                                      </Flex>
                                    </Card>
                                  </Flex>
                                )}
                              </Draggable>
                            ))}
                            {provided.placeholder}
                          </Flex>
                        )}
                      </Droppable>
                    </DragDropContext>
                  )}
                </Flex>
                <Flex className="ins-con-show" vertical>
                  <Flex className="ins-con-tit" align="center">
                    从列表中隐藏
                  </Flex>
                  {insHiddenList && insHiddenList.length > 0 && (
                    <Flex vertical>
                      {insHiddenList.map((item, index: number) => {
                        return (
                          <Flex className="ins-con-sign">
                            <Card className="ins-card">
                              <Flex justify="space-between">
                                <DragOutlined className="ins-icon-scale" />
                                <Flex className="ins-name">
                                  <span>{item.name}</span>
                                </Flex>
                                <Tooltip placement="top" title="显示">
                                  <EyeOutlined onClick={() => setInsIsShow(item, 2)} />
                                </Tooltip>
                              </Flex>
                            </Card>
                          </Flex>
                        );
                      })}
                    </Flex>
                  )}
                </Flex>
              </Flex>
            ) : (
              <Flex className="setup-content-table empty">
                <EmptyData description={"暂无指令信息"} />
              </Flex>
            )}
          </Spin>
        </Flex>
      </Flex>
    </>
  );
};

export default SetupComponent;
