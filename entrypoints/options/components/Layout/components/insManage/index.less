@import "@/assets/styles/variables";
.ins-record-main {
  padding-top: var(--ant-margin);
  height: calc(100% - 40px);
  overflow-y: auto;
  width: 100%;
  cursor: default;
  display: block !important;
  position: relative;
  z-index: 2;
  .session-record {
    gap: var(--ant-margin);
    color: var(--ant-color-text);
    margin-bottom: var(--ant-margin-lg);
    flex-wrap: wrap;
  }
  .session-record-operate {
    margin-bottom: var(--ant-margin);
  }
  .clear-btn {
    border: none !important;
  }
  .ins-card{
    width: 400px !important;
    background: var(--ant-color-bg-base);
    border-radius: var(--ant-border-radius) !important;
    border: 1px solid var(--ant-color-border) !important;
    margin-bottom: var(--ant-margin-sm)
  }
  .ins-tips{
    // font-size: var(--ant-font-size-lg);
    color: var(--ant-color-text-description);
    margin-bottom: var(--ant-margin-lg)
  }
  .ins-con{
    .ins-con-show{
      width: 450px;
      padding: var(--ant-padding-lg) var(--ant-padding-lg) 0px;
    }
    .ins-con-tit{
      font-size: var(--ant-font-size-lg);
      color: var(--ant-color-text);
      margin-bottom: var(--ant-margin-lg);
    }
    .ins-name {
      width:310px;
      color: var(--ant-color-text);
      span{
        width: 100%;
        display:inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .ins-con-sign{
      width:100%;
    }
    .ins-icon-scale{
      color: var(--ant-color-text-placeholder)
    }
  }
}
