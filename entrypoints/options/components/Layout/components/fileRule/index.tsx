import { useEffect, useRef, useState } from "react";
import {
  Switch,
  Button,
  List,
  Typography,
  Flex,
  Card,
  Divider,
  theme,
  Image,
  message,
  Spin,
  Radio,
  Tag,
  Input,
  Modal,
} from "antd";
import { CloseOutlined } from "@ant-design/icons";
import "./index.less";
import EmptyData from "@/components/EmptyData";
import SelectKnowledgeData from "./SelectKnowledge";

const { Text } = Typography;
const { useToken } = theme;
const FileRule = () => {
  const { token } = useToken();
  const [menuData, setMenuData] = useState({
    isFileRule: false,
    isFileCheck: false,
    isBusinessCheck: false,
    ruleFun: 1, // 1 正则，2 AI
    ruleList: [],
    salesContractRules: [], // 销售合同对比规则知识库
    businessContractRules: [], // 商务合同对比规则知识库
    contractCheckDomains: "", // 合同校验配置的域名
    businessCheckDomains: "", // 商务订单校验配置的域名
  });
  const [listLoading] = useState(false);
  const hasInitialized = useRef(false);
  const mentionsContainerRef = useRef<HTMLDivElement>(null);
  const [knowledModel, setknowledModel] = useState<boolean>(false);
  const [selectKnowledgeArr] = useState<any>([]);
  // 当前操作的规则类型和对应的知识库数组
  const [currentRuleType, setCurrentRuleType] = useState<"sales" | "business" | null>(null);
  const [, setCurrentRuleKnowledge] = useState<any[]>([]);
  const [selectKnIdsArr, setSelectKnIdsArr] = useState<any>([]); // 选中的知识信息 知识库带过来的

  useEffect(() => {
    if (!hasInitialized.current) {
      hasInitialized.current = true;
      return; // 跳过第一次执行
    }
    browser.storage.local.set({
      fileRule: {
        isFileRule: menuData?.isFileRule,
        isFileCheck: menuData?.isFileCheck,
        isBusinessCheck: menuData?.isBusinessCheck,
        ruleFun: menuData?.ruleFun || 1,
        // ruleList: menuData.ruleList.map((item) => item.domainUrl),
        ruleList: menuData?.ruleList.filter((item) => item != null),
        // 存储销售和商务合同规则
        salesContractRules: menuData?.salesContractRules || [],
        businessContractRules: menuData?.businessContractRules || [],
        contractCheckDomains: menuData?.contractCheckDomains || "",
        businessCheckDomains: menuData?.businessCheckDomains || "",
      },
    });
  }, [menuData]);

  // 打开知识库弹窗
  const handleOpenModal = (ruleType: "sales" | "business") => {
    setCurrentRuleType(ruleType);
    const currentKnowledge = ruleType === "sales" ? menuData.salesContractRules : menuData.businessContractRules;
    setCurrentRuleKnowledge(currentKnowledge);
    setknowledModel(true);
  };

  // 删除隐藏的网站
  const delWebsite = (item: any) => {
    setMenuData((prev) => ({
      ...prev,
      ruleList: prev.ruleList.filter((url) => url !== item),
    }));
  };

  // 文件规则开关
  const switchChange = (checked: boolean) => {
    setMenuData({ ...menuData, ...{ isFileRule: checked } });
  };

  // 合同校验开关
  const switchChange2 = (checked: boolean) => {
    setMenuData({ ...menuData, ...{ isFileCheck: checked } });
  };

  // 商务订单校验开关
  const switchChange3 = (checked: boolean) => {
    setMenuData({ ...menuData, ...{ isBusinessCheck: checked } });
  };

  // 子组件传来的数据
  const handleGetKnowledge = (data) => {
    const filterArr = data
      .filter((item) => item.isTypeKnow === "2")
      .map((item) => ({
        id: item.id,
        flag: "know",
        libName: item.fileName,
        libDesc: item.fileType,
        isTypeKnow: 2,
        libId: item.libId,
      }));

    setSelectKnIdsArr(filterArr);
  };

  // 渲染已选知识库标签
  const handleRenderTags = (knowledgeList: any[]) => {
    if (!knowledgeList || knowledgeList.length === 0) return;

    // console.log("knowledgeList2222", knowledgeList);

    return (
      <Flex vertical gap={token.padding}>
        {/* <Text strong>已选知识库规则</Text> */}
        <Flex wrap="wrap" gap={token.paddingSM}>
          {knowledgeList.map((item) => (
            <Tag
              key={item.id}
              style={{
                padding: `${token.paddingXS}px ${token.paddingSM}px`,
                borderRadius: token.borderRadius,
                backgroundColor: "#f0f8ff",
                border: `1px solid ${token.colorPrimary}`,
                color: token.colorPrimary,
                fontSize: token.fontSizeSM,
              }}
            >
              {item.libName || ""}
            </Tag>
          ))}
        </Flex>
      </Flex>
    );
  };

  // 关闭model
  const handleCloseMoal = () => {
    setknowledModel(false);
    setCurrentRuleType(null);
  };

  // 合同校验域名输入变化处理
  const handleContractDomainChange = (e) => {
    const value = e.target.value;
    setMenuData((prev) => ({
      ...prev,
      contractCheckDomains: value,
    }));
  };

  // 商务订单校验域名输入变化处理
  const handleBusinessDomainChange = (e) => {
    const value = e.target.value;
    setMenuData((prev) => ({
      ...prev,
      businessCheckDomains: value,
    }));
  };

  // 确认
  const handleConfirm = () => {
    if (!selectKnIdsArr || selectKnIdsArr.length === 0) {
      message.open({
        type: "warning",
        content: "知识库内容不能为空",
      });
      return;
    }
    // console.log("selectKnIdsArr", selectKnIdsArr);

    if (currentRuleType) {
      setMenuData((prev) => ({
        ...prev,
        salesContractRules: currentRuleType === "sales" ? selectKnIdsArr : prev.salesContractRules,
        businessContractRules: currentRuleType === "business" ? selectKnIdsArr : prev.businessContractRules,
      }));
    }

    // 可选：显示操作成功的提示
    message.open({
      type: "success",
      content: "知识库配置已保存",
    });

    // console.log("menuData", menuData);
    setSelectKnIdsArr([]);
    setknowledModel(false);
    setCurrentRuleType(null);
  };

  // 获取列表
  const getWordList = async () => {
    await browser.storage.local.get(["fileRule"]).then((result) => {
      // // console.log("result", result);
      if (result && Object.keys(result).length > 0 && result.constructor === Object) {
        setMenuData((prev) => ({
          ...prev,
          isFileRule: result?.fileRule?.isFileRule || false,
          isFileCheck: result?.fileRule?.isFileCheck || false,
          isBusinessCheck: result?.fileRule?.isBusinessCheck || false,
          ruleFun: result?.fileRule?.ruleFun || 1,
          ruleList: result.fileRule.ruleList.filter((item) => item) || [],
          salesContractRules: result?.fileRule?.salesContractRules || [],
          businessContractRules: result?.fileRule?.businessContractRules || [],
          contractCheckDomains: result?.fileRule?.contractCheckDomains || "",
          businessCheckDomains: result?.fileRule?.businessCheckDomains || "",
        }));
      } else {
        setMenuData(() => ({
          isFileRule: false,
          isFileCheck: false,
          isBusinessCheck: false,
          ruleFun: 1,
          ruleList: [],
          salesContractRules: [],
          businessContractRules: [],
          contractCheckDomains: "",
          businessCheckDomains: "",
        }));
      }
    });
  };

  useEffect(() => {
    getWordList();
  }, []);

  return (
    <>
      <Flex ref={mentionsContainerRef} vertical gap={token.padding} className="smart-menu">
        {/* 文本描述区域 */}
        <Card>
          <Flex justify="center" style={{ height: "120px" }}>
            <Image
              src={browser.runtime.getURL("/images/setup/rule.jpg")}
              alt="图片"
              style={{ width: "auto", height: "120px" }}
            />
          </Flex>
        </Card>

        <Spin spinning={listLoading} style={{ overflowY: "scroll" }}>
          {/* 文件规则开关 */}
          <Card style={{ marginBottom: 15 }}>
            <>
              <Flex vertical gap={token.paddingSM}>
                <Flex align="center" justify="space-between">
                  <Text style={{ fontSize: token.fontSizeLG }}>文件规则</Text>
                  <Switch value={menuData.isFileRule} onChange={switchChange} />
                </Flex>
                <Text type="secondary">开启时，在浏览器中上传文件时，文件操作规则会自动适配</Text>
              </Flex>
              {menuData.isFileRule && (
                <Flex style={{ marginTop: token.marginLG }} gap={token.marginLG} justify="space-between">
                  <Text style={{ fontSize: token.fontSizeLG }}>检测方式</Text>
                  <Flex>
                    <Radio.Group
                      name="radiogroup"
                      defaultValue={1}
                      value={menuData?.ruleFun || 1}
                      onChange={(e) => {
                        setMenuData({ ...menuData, ...{ ruleFun: e.target.value } });
                      }}
                      options={[
                        { value: 1, label: "正则检索" },
                        { value: 2, label: "AI检索" },
                      ]}
                    />
                  </Flex>
                </Flex>
              )}
            </>
            <Divider />
            {/* 网站禁用列表 */}
            {menuData.isFileRule && (
              <div>
                {menuData.ruleList.length > 0 ? (
                  <Flex vertical gap={token.padding}>
                    <Text strong>在网站上禁用</Text>
                    <List
                      dataSource={menuData.ruleList}
                      renderItem={(item, index) => (
                        <List.Item className="list-item" key={index}>
                          {/* <Text>{item.domainUrl}</Text> */}
                          <Text>{item}</Text>
                          <CloseOutlined
                            className="colse"
                            style={{ color: token.colorTextDescription, cursor: "pointer" }}
                            onClick={() => {
                              delWebsite(item);
                            }}
                          />
                        </List.Item>
                      )}
                    />
                  </Flex>
                ) : (
                  <EmptyData description={"暂无数据"} />
                )}
              </div>
            )}
          </Card>
          {/* 合同校验开关 */}
          <Card style={{ marginBottom: 15 }}>
            <>
              <Flex vertical gap={token.paddingSM} style={{ marginBottom: 15 }}>
                <Flex align="center" justify="space-between">
                  <Text style={{ fontSize: token.fontSizeLG, marginTop: "10px" }}>合同校验</Text>
                  <Switch value={menuData.isFileCheck} onChange={switchChange2} />
                </Flex>
                <Text type="secondary">开启时，在配置的域名下上传文件时，会校验文件与系统信息是否一致</Text>
              </Flex>

              {menuData.isFileCheck && (
                <>
                  <Flex vertical gap={token.paddingSM}>
                    <Flex align="center" justify="space-between">
                      <Text style={{ fontSize: token.fontSizeLG }}>配置域名</Text>
                      <Input
                        style={{ width: 620 }}
                        placeholder="请配置域名，以英文逗号隔开"
                        value={menuData.contractCheckDomains}
                        onChange={handleContractDomainChange}
                      />
                    </Flex>
                  </Flex>
                  <Divider style={{ margin: "15px 0" }} />

                  <Flex vertical gap={token.paddingSM}>
                    <Flex align="center" justify="space-between">
                      <Text style={{ fontSize: token.fontSizeLG }}>配置知识库规则</Text>
                      <Flex style={{ width: "400px" }}>{handleRenderTags(menuData.salesContractRules)}</Flex>
                      <Button type="primary" size="small" onClick={() => handleOpenModal("sales")}>
                        配置
                      </Button>
                    </Flex>
                  </Flex>
                </>
              )}
            </>
          </Card>
          {/* 商务合同对比规则 */}
          <Card>
            <>
              <Flex vertical gap={token.paddingSM} style={{ marginBottom: 15 }}>
                <Flex align="center" justify="space-between">
                  <Text style={{ fontSize: token.fontSizeLG, marginTop: "10px" }}>商务订单校验</Text>
                  <Switch value={menuData.isBusinessCheck} onChange={switchChange3} />
                </Flex>
                <Text type="secondary">开启时，在配置的域名下上传文件时，会校验文件与系统信息是否一致</Text>
              </Flex>

              {menuData.isBusinessCheck && (
                <>
                  <Flex vertical gap={token.paddingSM}>
                    <Flex align="center" justify="space-between">
                      <Text style={{ fontSize: token.fontSizeLG }}>配置域名</Text>
                      <Input
                        style={{ width: 620 }}
                        placeholder="请配置域名，以英文逗号隔开"
                        value={menuData.businessCheckDomains}
                        onChange={handleBusinessDomainChange}
                      />
                    </Flex>
                  </Flex>
                  <Divider style={{ margin: "15px 0" }} />

                  <Flex vertical gap={token.paddingSM}>
                    <Flex align="center" justify="space-between">
                      <Text style={{ fontSize: token.fontSizeLG }}>配置知识库规则</Text>
                      <Flex style={{ width: "400px" }}>{handleRenderTags(menuData.businessContractRules)}</Flex>
                      <Button type="primary" size="small" onClick={() => handleOpenModal("business")}>
                        配置
                      </Button>
                    </Flex>
                  </Flex>
                </>
              )}
            </>
          </Card>
        </Spin>
      </Flex>

      {knowledModel && (
        <Modal
          title="选择知识库"
          open={knowledModel}
          onOk={handleConfirm}
          onCancel={handleCloseMoal}
          okText="确认"
          cancelText="取消"
        >
          <SelectKnowledgeData
            openDrawer={true}
            selectKnowledgeArr={selectKnowledgeArr}
            selectKnIdsArr={selectKnIdsArr}
            onGetKnowledge={handleGetKnowledge}
          />
        </Modal>
      )}
    </>
  );
};

export default FileRule;
