import React, { useEffect } from "react";
import { Checkbox, Flex, theme } from "antd";
import "./index.less";
import { knowdgeSVGIcon } from "@/config/menu/knowdge";

type Props = {
  data: {
    id: string;
    title: string;
    ossId: string;
    createBy?: string;
  };
  checked?: boolean;
  knowledgeType?: string; // 当前tab栏切换  3企业 2 项目  4 个人
  onCheckChange?: (checked: boolean, value: any, key: string) => void;
};
const { useToken } = theme;
const KnowledgeCard: React.FC<Props> = ({ data, checked = false, onCheckChange }) => {
  const { token } = useToken();
  const fetchRequest = useFetchRequest();
  const fileExtensionHandler = (item: any) => {
    if (item.fileType === "pdf") {
      return <span className="extend-icon">{knowdgeSVGIcon.pdf}</span>;
    } else if (item.fileType === "docx" || item.fileType === "doc") {
      return <span className="extend-icon">{knowdgeSVGIcon.word}</span>;
    } else if (item.fileType === "xls" || item.fileType === "xlsx" || item.fileType === "csv") {
      return <span className="extend-icon">{knowdgeSVGIcon.excel}</span>;
    } else if (item.fileType === "txt") {
      return <span className="extend-icon">{knowdgeSVGIcon.txt}</span>;
    } else if (item.fileType === "pptx") {
      return <span className="extend-icon">{knowdgeSVGIcon.ppt}</span>;
    }
    return <span className="extend-icon"></span>;
  };
  const previewUrl = () => {
    fetchRequest({
      api: "onlinePreviewUrl",
      params: data?.id,
      callback: (res) => {
        if (res.code == 200) {
          window.open(res.data, "_blank");
        }
      },
    });
  };
  return (
    <Flex
      className="learn-card-chat"
      vertical
      gap={token.marginXXS}
      onClick={(e) => {
        e.stopPropagation();
        previewUrl();
      }}
    >
      <Flex justify="space-between">
        <Flex align="center" gap={token.marginXXS}>
          {fileExtensionHandler(data)}
        </Flex>
        <Checkbox
          value={data?.id}
          style={{
            opacity: checked ? 1 : 0,
            transition: "opacity 0.2s",
          }}
          className="knowledge-checkbox"
          checked={checked}
          onClick={(e) => e.stopPropagation()}
          onChange={(e) => {
            e.stopPropagation();
            onCheckChange?.(e.target.checked, data, "2");
          }}
        />
      </Flex>

      <Flex style={{ fontWeight: "bold", fontSize: token.fontSize }} vertical>
        <Flex className="knowledge-name">{data.title}</Flex>
      </Flex>
    </Flex>
  );
};

export default KnowledgeCard;
