/** 团队管理 */
import React, { useEffect, useRef, useState } from "react";
import {
  Button,
  Flex,
  Form,
  Input,
  message,
  Pagination,
  Popconfirm,
  Segmented,
  Spin,
  Switch,
  Tag,
  theme,
  Tooltip,
  Tree,
  Table,
} from "antd";
import {
  CarryOutOutlined,
  ClockCircleOutlined,
  CommentOutlined,
  DeleteOutlined,
  FormOutlined,
  LoadingOutlined,
  PlusOutlined,
  ProfileOutlined,
  SearchOutlined,
  SortDescendingOutlined,
} from "@ant-design/icons";
import "./index.less";
const { useToken } = theme;
const searchParamsInit = { pageNum: 1, pageSize: 10, entity: { content: "", readFlag: "", sendTime: "", sendBy: "" } };
// 列表数据的初始化数据
const pageDataInit = { count: 0, page: { current: 1, size: 10, total: 0, pages: 0, records: [] } };
const SetupComponent: React.FC = () => {
  const { token } = useToken();
  // 查询数据的请求参数
  const [searchParams, setSearchParams] = useState(searchParamsInit);
  const [tableLoading, setTableLoading] = useState(false); // loading
  const [queryContent, setQueryContent] = useState("");
  // 消息列表数据
  const [teamPageData, setPromptPageData] = useState(pageDataInit);
  // 页面首次加载或请求参数变化时，获取用户信息并获取消息列表数据
  useEffect(() => {
    // handlePromptList();
  }, [searchParams]);
  /** 分页查询消息列表 */
  const handlePromptList = () => {
    setTableLoading(true);
    // noticePage(searchParams).then((res) => {
    //   if (res.code == "200") {
    //     setPromptPageData(res.data);
    //   } else {
    //     message.open({
    //       type: "error",
    //       content: res.msg,
    //     });
    //   }
    //   setTableLoading(false);
    // });
  };
  const getQuery = (e) => {
    setQueryContent(e.target.value.trim());
  };
  const getData = (e) => {
    setSearchParams({ ...searchParams, pageNum: 1, entity: { ...searchParams.entity, query: e.target.value.trim() } });
  };
  const treeData = [
    {
      title: "产品研发中心",
      key: "0-0",
      icon: <CarryOutOutlined />,
      children: [
        {
          title: "产品部",
          key: "0-0-0",
          icon: <CarryOutOutlined />,
          children: [
            { title: "leaf", key: "0-0-0-0", icon: <CarryOutOutlined /> },
            {
              title: (
                <>
                  <div>multiple line title</div>
                  <div>multiple line title</div>
                </>
              ),
              key: "0-0-0-1",
              icon: <CarryOutOutlined />,
            },
            { title: "leaf", key: "0-0-0-2", icon: <CarryOutOutlined /> },
          ],
        },
        {
          title: "研发部",
          key: "0-0-1",
          icon: <CarryOutOutlined />,
          children: [{ title: "leaf", key: "0-0-1-0", icon: <CarryOutOutlined /> }],
        },
        {
          title: "设计部",
          key: "0-0-2",
          icon: <CarryOutOutlined />,
          children: [
            { title: "leaf", key: "0-0-2-0", icon: <CarryOutOutlined /> },
            { title: "leaf", key: "0-0-2-1", icon: <CarryOutOutlined /> },
          ],
        },
      ],
    },
    {
      title: "运营中心",
      key: "0-1",
      icon: <CarryOutOutlined />,
      children: [
        {
          title: "市场部",
          key: "0-1-0",
          icon: <CarryOutOutlined />,
          children: [
            { title: "leaf", key: "0-1-0-0", icon: <CarryOutOutlined /> },
            { title: "leaf", key: "0-1-0-1", icon: <CarryOutOutlined /> },
          ],
        },
      ],
    },
  ];
  // 表头
  const columns = [
    { title: "序号", dataIndex: "msgTitle", ellipsis: true },
    { title: "成员名称", dataIndex: "msgContent", ellipsis: true },
    { title: "手机号", dataIndex: "msgTime", ellipsis: true },
    { title: "邮箱", dataIndex: "msgTime", width: 180, ellipsis: true },
    { title: "所属部门", dataIndex: "msgTime", ellipsis: true },
    { title: "职位", dataIndex: "msgTime", ellipsis: true },
    // {
    //   title: "状态",
    //   dataIndex: "readFlag",
    //   width: 100,
    //   render: (text, record) => (
    //     <Flex>
    //       {text == "read_flag_false" ? (
    //         <Tag color="gold" className="tag-status">
    //           未读
    //         </Tag>
    //       ) : (
    //         <Flex>
    //           <Tag>已读</Tag>
    //         </Flex>
    //       )}
    //     </Flex>
    //   ),
    // },
  ];
  return (
    <>
      <Flex className="team-content-top setup-content-right" vertical>
        {/* header区 */}
        <Flex className="setup-content-header">
          <h1 className="setup-content-title">团队管理</h1>
          <Input
            size="large"
            value={queryContent}
            onChange={getQuery}
            onBlur={getData}
            allowClear
            placeholder="搜索成员姓名手机号等"
            prefix={<SearchOutlined />}
            style={{ width: "310px" }}
          />
        </Flex>
        <Flex className="team-content-body">
          <Flex
            vertical
            style={{
              width: "256px",
              padding: token.padding,
              borderRadius: token.borderRadiusLG,
              background: "rgba(0, 0, 0, 0.02)",
              gap: token.paddingXS,
            }}
          >
            <Flex>
              {/* onSelect={onSelect} */}
              <Tree treeData={treeData} showIcon={true} showLine={true} style={{ background: "none" }} />
            </Flex>
          </Flex>
          <div className="team-table">
            <Table
              className="message-table-mess"
              // rowSelection={rowSelection}
              columns={columns}
              dataSource={teamPageData.page?.records}
              rowKey="id"
              loading={tableLoading}
              pagination={false}
              // onRow={(record) => ({
              //   onMouseEnter: () => {
              //     setPopoverVisible(record.id);
              //   },
              //   onMouseLeave: () => {
              //     setPopoverVisible(null);
              //   },
              // })}
              // components={{
              //   body: {
              //     row: ({ children, ...rest }, row) => {
              //       const { record } = rest;
              //       const rowKey = rest['data-row-key'];
              //       return (
              //         <Popover
              //           open={popoverVisible === rowKey}
              //           placement="right"
              //           title= {<HtmlContent htmlString={children[0]?.props?.record.msgTitle} />}
              //           content={<HtmlContent htmlString={children[0]?.props?.record.msgContent} />}
              //         >
              //           <tr {...rest}>{children}{record}</tr>
              //         </Popover>
              //       );
              //     },
              //   },
              // }}
            />
            {teamPageData.page?.records && teamPageData.page?.records.length > 0 && (
              <Pagination
                className="pagination"
                showSizeChanger
                showQuickJumper
                onChange={(page, pageSize) => {
                  setSearchParams({ ...searchParams, pageNum: page, pageSize });
                }}
                current={teamPageData.page?.current}
                pageSize={teamPageData.page?.size}
                total={teamPageData.page?.total}
                showTotal={(total) => `共 ${total} 条`}
              />
            )}
          </div>
        </Flex>
      </Flex>
    </>
  );
};
export default SetupComponent;
