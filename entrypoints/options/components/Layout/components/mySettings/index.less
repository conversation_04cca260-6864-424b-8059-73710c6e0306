.setup-content {
  .setup-content-right {
    .setup-setting {
      border-bottom: 0px;
    }
    .settings-info {
      width: 608px;
      margin-left: 200px;
      padding: var(--ant-padding-lg);
      max-height: calc(100vh - 160px);
      overflow-y: auto;
    }
    .settings-avatar {
      margin-bottom: var(--ant-margin-md);
      cursor: pointer;
      img {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        overflow: hidden;
      }
    }
    .form-item {
      gap: var(--ant-margin-xs);
      padding-bottom: var(--ant-padding-xs);
      border-bottom: 1px solid var(--ant-color-split);
      margin-bottom: var(--ant-margin-md);
      .form-item-label {
        color: var(--ant-color-text-base);
        font-size: var(--ant-font-size-heading-4);
        line-height: var(--ant-line-height-heading-4);
      }
      .form-item-val {
        color: var(--ant-color-text-tertiary);
        font-size: var(--ant-font-size-lg);
        line-height: var(--ant-line-height-lg);
      }
      .edit-name {
        gap: var(--ant-margin);
        width: 456px;
        .anticon {
          font-size: var(--ant-font-size-lg);
        }
        .error {
          flex-shrink: 0;
          .anticon {
            color: var(--ant-color-error);
          }
        }
        .btn {
          flex-shrink: 0;
          .anticon {
            color: var(--ant-color-text);
          }
        }
      }
    }
  }
}
