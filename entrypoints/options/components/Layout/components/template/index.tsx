/** 对话记录*/
import React, { useEffect, useState } from "react";
import {
  LoadingOutlined,
  SearchOutlined,
  DragOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
  PlusOutlined,
  InfoCircleFilled,
  HolderOutlined,
  UploadOutlined,
  EditOutlined,
  DeleteOutlined,
  PaperClipOutlined,
  CloseOutlined,
} from "@ant-design/icons";
import {
  Card,
  Flex,
  message,
  Spin,
  Tag,
  Input,
  Tooltip,
  theme,
  Button,
  Upload,
  Modal,
  Form,
  Divider,
  Popconfirm,
  Checkbox,
  Segmented,
} from "antd";
import type { CheckboxOptionType } from "antd";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import { formatList } from "@/config/options/ai.ts";
import { knowdgeSVGIcon } from "@/config/menu/knowdge";
import "../../index.less";
import "./index.less";
import { addTemplate, updateTemplate, templateList, delTemplate, batchUpdateTemplate } from "@/api/template";
import { getDicts } from "@/api/common.ts";
import { uploadFileToOSS } from "@/api/file.ts";
import EmptyData from "@/components/EmptyData";
import { Link } from "react-router-dom";
const { TextArea } = Input;

// 列表查询的初始化参数
const searchParamsInit = {
  templateName: "",
  agentType: "",
};
const getBase64 = (file) =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
const { useToken } = theme;
// Tab页签数据配置
const SetupComponent: React.FC = () => {
  const { token } = useToken();
  // 查询数据的请求参数
  const [searchParams, setSearchParams] = useState(searchParamsInit);
  const [insShowList, setInsShowList] = useState([]); // 显示list
  const [insHiddenList, setInsHiddenList] = useState([]); // 隐藏list
  const [visible, setVisible] = useState(false);
  // 模板数据
  const [insData, setInsData] = useState([{ templateId: 1, name: 1 }]);
  const [loading, setLoading] = useState<boolean>(true);
  const [queryContent, setQueryContent] = useState("");
  const [fileList, setFileList] = useState([]); // 文件信息
  const [templateId, setTemplateId] = useState(""); // 模板id
  const [addLoading, setAddLoading] = useState<boolean>(false); // 提交按钮loading
  const [currentData, setCurrentData] = useState<any>({}); // 选中的当前数据
  const [templateOption, setTemplateOption] = useState<CheckboxOptionType[]>([]); // 模板选项
  useEffect(() => {
    getAcctInsList();
  }, [searchParams]);

  // 处理拖拽逻辑
  const onDragEnd = (result) => {
    if (!result.destination) return; // 如果拖拽没有落到有效位置，则不做任何处理
    const newList = Array.from(insShowList);
    const [movedItem] = newList.splice(result.source.index, 1); // 移除拖拽的元素
    newList.splice(result.destination.index, 0, movedItem); // 插入到目标位置
    setInsShowList(newList); // 更新状态

    // 调用 setInsRank 时传递更新后的 showList 和 hiddenList
    setInsRank(newList, insHiddenList);
  };

  const [alignValue, setAlignValue] = useState("");
  const [optionValue, setOptionValue] = useState<any[]>([]);
  /** 模板列表 */
  const getTemplateList = () => {
    getDicts("aiTemplateType")
      .then((res: any) => {
        if (res.code === 200) {
          const arr: any[] = [];
          res.data.forEach((item: any) => {
            let obj: any = {
              ...item,
              label: item.title,
            };
            arr.push(obj);
          });
          setTemplateOption(arr);
          let tempArr = [
            {
              label: "全部",
              value: "",
            },
            ...arr,
          ];
          setOptionValue(tempArr);
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      })
      .catch(() => {
        setTemplateOption([]);
        setOptionValue([]);
      })
      .finally(() => {
        // setTemplateOption([])
      });
  };

  useEffect(() => {
    getTemplateList();
  }, []);

  /** 模板列表 */
  const getAcctInsList = () => {
    setLoading(true);
    templateList(searchParams)
      .then((res: any) => {
        if (res.code == "200") {
          setLoading(false);
          let arrHide = [];
          let arrShow = [];
          setInsData(res.data);
          res.data.forEach((item) => {
            if (item.showFlag == 0) {
              arrHide.push(item);
            } else if (item.showFlag == 1 || item.showFlag == "" || item.showFlag == null) {
              arrShow.push(item);
            }
          });
          setInsHiddenList(arrHide);
          setInsShowList(arrShow);
        } else {
          setInsData([]);
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      })
      .catch(() => {
        setLoading(false);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 关键词搜索
  const getQuery = (e) => {
    setSearchParams({ ...searchParams, templateName: e.target.value.trim() });
  };

  // 提示词隐藏显示
  const setInsIsShow = (item, type) => {
    setInsShowList((prevShowList) => {
      const newShowList =
        type === 1 ? prevShowList.filter((i) => i.templateId !== item.templateId) : [item, ...prevShowList];

      setInsHiddenList((prevHiddenList) => {
        const newHiddenList =
          type === 1 ? [item, ...prevHiddenList] : prevHiddenList.filter((i) => i.templateId !== item.templateId);

        // 在这里调用 setInsRank，确保在 state 更新后执行
        setInsRank(newShowList, newHiddenList);

        return newHiddenList;
      });

      return newShowList;
    });
  };

  // 排序根隐藏显示
  const setInsRank = (newShowList, newHiddenList) => {
    let dataList = [...newShowList, ...newHiddenList];
    let arr = [];
    dataList.forEach((item, index) => {
      if (index < newShowList.length) {
        arr.push({
          templateId: item.templateId,
          templateArrange: index + 1,
          showFlag: 1,
        });
      } else {
        arr.push({
          templateId: item.templateId,
          templateArrange: index + 1,
          showFlag: 0,
        });
      }
    });
    batchUpdateTemplate(arr).then((res) => {
      if (res.code == "200") {
        getAcctInsList();
      } else {
        message.open({
          type: "error",
          content: res.msg,
        });
      }
    });
  };
  const fileExtensionHandler = (item) => {
    let type = item.split(".")[item.split(".").length - 1];
    if (type === "pdf") {
      return <span className="extend-icon">{knowdgeSVGIcon.pdf}</span>;
    } else if (type === "docx") {
      return <span className="extend-icon">{knowdgeSVGIcon.word}</span>;
    } else if (type === "xls" || type === "xlsx" || type === "csv") {
      return <span className="extend-icon">{knowdgeSVGIcon.excel}</span>;
    } else if (type === "txt") {
      return <span className="extend-icon">{knowdgeSVGIcon.txt}</span>;
    } else if (type === "pptx") {
      return <span className="extend-icon">{knowdgeSVGIcon.ppt}</span>;
    } else if (type === "json") {
      return <span className="extend-icon">{knowdgeSVGIcon.json}</span>;
    }
  };
  const [form] = Form.useForm();

  const uploadProps = {
    accept: ".docx,.pptx,.xls,.xlsx,.pdf,.json",
    multiple: false,
    fileList,
    showUploadList: false,
    beforeUpload: (file) => {
      const suffix = file.name.split(".").pop().toLowerCase();
      const fileFormat = ["docx", "pptx", "xls", "xlsx", "pdf", "json"];
      if (!fileFormat.includes(suffix)) {
        message.error("文件格式不正确!");
        return Upload.LIST_IGNORE;
      }
      const isLt5M = file.size / 1024 / 1024 < 5;
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (suffix == "json") {
        if (!isLt20M) {
          message.error("文件大小不能超过20MB");
          return Upload.LIST_IGNORE;
        }
      } else {
        if (!isLt5M) {
          message.error("文件大小不能超过5MB");
          return Upload.LIST_IGNORE;
        }
      }
      return true;
    },
    customRequest: async (options) => {
      const { file, onSuccess, onError, onProgress } = options;
      console.log("shshsjh", file);
      try {
        const res = await uploadFileToOSS({ file });
        if (res.code === 200) {
          setFileList([res.data]);
          onSuccess(res, file);
        } else {
          message.error("上传失败");
          onError();
        }
      } catch (e) {
        message.error("上传异常");
        onError(e);
      }
    },
    onRemove: () => {
      setFileList([]);
    },
  };

  // 表单提交
  const submit = () => {
    form.validateFields().then((values) => {
      setAddLoading(true);
      const suffix = fileList[0].originalName.split(".").pop().toLowerCase();
      let type = "";
      if (suffix == "docx" || suffix == "doc") {
        type = "WORD";
      } else if (suffix == "xls" || suffix == "xlsx") {
        type = "EXCEL";
      } else if (suffix == "json") {
        type = "JSON";
      } else {
        type = "PDF";
      }
      const formData = {
        templateId: templateId,
        templateName: values.templateName,
        templateDesc: values.templateDesc,
        fileUrl: fileList[0].absoluteUrl,
        fileName: fileList[0].originalName,
        fileType: type,
        templateArrange: currentData?.templateArrange || "",
        showFlag: currentData?.showFlag || 1,
        agentType: values.agentType.join(","),
        tmplateType: "0",
        imageUrl: imageUrl,
      };
      if (templateId) {
        updateTemplate(formData).then((res) => {
          if (res.code == 200) {
            message.open({
              type: "success",
              content: "修改成功！",
            });
            setVisible(false);
            setAddLoading(false);
            setFileList([]);
            setImageList([]);
            setAgentCheckValue([]);
            setImageUrl("");
            form.resetFields();
            getAcctInsList();
          } else {
            message.open({
              type: "error",
              content: res.msg,
            });
            setAddLoading(false);
          }
        });
      } else {
        addTemplate(formData).then((res) => {
          if (res.code == 200) {
            message.open({
              type: "success",
              content: "新增成功！",
            });
            setVisible(false);
            setAddLoading(false);
            setFileList([]);
            setImageList([]);
            setAgentCheckValue([]);
            setImageUrl("");
            form.resetFields();
            getAcctInsList();
          } else {
            message.open({
              type: "error",
              content: res.msg,
            });
            setAddLoading(false);
          }
        });
      }
    });
  };
  // 修改事件
  const editInfo = (item) => {
    console.log("jsjsjsj", item);
    // getTemplateList();
    setCurrentData(item);
    setTemplateId(item.templateId);
    form.setFieldsValue({
      templateName: item.templateName,
      templateDesc: item.templateDesc,
      templateFile: [
        {
          originalName: item.fileName,
          absoluteUrl: item.fileUrl,
        },
      ],
      agentType: item.agentType.split(","),
      templateImage: [
        {
          originalName: "",
          absoluteUrl: item.imageUrl,
        },
      ],
    });
    setVisible(true);
    setFileList([
      {
        originalName: item.fileName,
        absoluteUrl: item.fileUrl,
      },
    ]);
    setAgentCheckValue(item.agentType.split(","));
    setImageList([
      {
        originalName: "",
        absoluteUrl: item.imageUrl,
      },
    ]);
    setImageUrl(item.imageUrl);
  };
  // 删除
  const handleDelete = (item) => {
    delTemplate([item.templateId]).then((res) => {
      if (res.code == 200) {
        message.open({
          type: "success",
          content: "删除成功！",
        });
        getAcctInsList();
      } else {
        message.open({
          type: "error",
          content: res.msg,
        });
      }
    });
  };
  const [hovered, setHovered] = useState(false);

  const changeAlignValue = (value: any) => {
    console.log("-----value", value);
    setAlignValue(value);
    setSearchParams({ ...searchParams, agentType: value });
    // getAcctInsList()
  };

  // 封面
  const [imageList, setImageList] = useState([]); // 封面信息
  const [imageUrl, setImageUrl] = useState<string>();
  const [agentCheckValue, setAgentCheckValue] = useState([]);
  const uploadButton = (
    <div>
      {/* 上传按钮 */}
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
    </div>
  );
  const imageBeforeUpload = (file) => {
    const suffix = file.name.split(".").pop().toLowerCase();
    const fileFormat = ["jpg", "jpeg", "png"];
    if (!fileFormat.includes(suffix)) {
      message.error("文件格式不正确!");
      return Upload.LIST_IGNORE;
    }
    const isLt1M = file.size / 1024 / 1024 < 1;
    if (!isLt1M) {
      message.error("文件大小不能超过1MB");
      return Upload.LIST_IGNORE;
    }
    return true;
  };
  const imageCustomRequest = async (options) => {
    const { file, onSuccess, onError, onProgress } = options;
    try {
      const res = await uploadFileToOSS({ file });
      if (res.code === 200) {
        setImageList([res.data]);
        setImageUrl(res.data.absoluteUrl);
        onSuccess(res, file);
      } else {
        message.error("上传失败");
        onError();
      }
    } catch (e) {
      message.error("上传异常");
      onError(e);
    }
  };
  const agentChange = (checkedValues: any[]) => {
    setAgentCheckValue(checkedValues);
  };

  return (
    <>
      <Flex className="setup-content-right" vertical>
        {/* header区 */}
        <Flex className="setup-content-header" justify="space-between">
          <h1 className="setup-content-title">模板管理</h1>
          <Input
            size="large"
            value={queryContent}
            onChange={(e) => {
              setQueryContent(e.target.value.trim());
            }}
            onBlur={getQuery}
            allowClear
            placeholder="搜索模板"
            prefix={<SearchOutlined />}
            style={{ width: "310px" }}
          />
        </Flex>
        {/* table数据区 */}
        <Flex className="template-record-main">
          <Flex className="ins-tips" justify="space-between" align="center">
            <Flex align="center" style={{ paddingLeft: token.paddingSM }}>
              <Segmented
                value={alignValue}
                style={{ marginBottom: 8 }}
                onChange={changeAlignValue}
                options={optionValue}
              />
            </Flex>
            <Button
              icon={<PlusOutlined />}
              type="primary"
              onClick={() => {
                // getTemplateList();
                setTemplateId("");
                setVisible(true);
              }}
            >
              新建模板
            </Button>
          </Flex>
          <Flex className="ins-tips" justify="space-between" align="center">
            <Flex align="center" style={{ paddingLeft: token.paddingSM }}>
              <InfoCircleFilled style={{ marginRight: token.marginXS, color: token.colorPrimary }} />
              当前模板仅适用于AI写作、知识问答，仅支持doc ，excel2种格式的模板，大小不超过5MB
              <a style={{ marginLeft: token.margin }}>模板使用指南</a>
            </Flex>
          </Flex>
          <Spin spinning={loading} indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />}>
            {insData && insData.length > 0 ? (
              <Flex className="ins-con" gap={token.marginXL} style={{ marginLeft: "88px" }}>
                <Flex className="ins-con-show" vertical>
                  <Flex className="ins-con-tit" align="center">
                    在列表中显示
                  </Flex>
                  {insShowList && insShowList.length > 0 && (
                    <DragDropContext onDragEnd={onDragEnd}>
                      <Droppable droppableId="droppable-list">
                        {(provided) => (
                          <Flex {...provided.droppableProps} ref={provided.innerRef} vertical>
                            {insShowList.map((item, index) => (
                              <Draggable key={item.templateId} draggableId={item.templateId} index={index}>
                                {(provided) => (
                                  <Flex
                                    ref={provided.innerRef}
                                    {...provided.draggableProps}
                                    {...provided.dragHandleProps}
                                    className="ins-con-sign"
                                  >
                                    <Card className="ins-card">
                                      <Flex justify="space-between">
                                        <Flex>
                                          <Flex align="center">
                                            <HolderOutlined style={{ cursor: "grab" }} className="ins-icon-scale" />
                                            {fileExtensionHandler(item.fileName)}
                                          </Flex>
                                          <Flex className="ins-name" vertical>
                                            <Flex
                                              style={{
                                                color: token.colorTextBase,
                                                fontSize: token.fontSizeLG,
                                                fontWeight: "bold",
                                                lineHeight: token.lineHeightLG,
                                              }}
                                            >
                                              {item.templateName}
                                            </Flex>
                                            <Flex
                                              style={{
                                                color: token.colorTextTertiary,
                                                fontSize: token.fontSizeSM,
                                                lineHeight: token.lineHeightSM,
                                              }}
                                            >
                                              {item.templateDesc}
                                            </Flex>
                                          </Flex>
                                        </Flex>
                                        {item.tmplateType == "0" && (
                                          <Flex gap={token.marginXS} className="operate">
                                            <Tooltip placement="top" title="修改">
                                              <EditOutlined onClick={() => editInfo(item)} />
                                            </Tooltip>
                                            <Popconfirm
                                              trigger="click"
                                              placement="bottomLeft"
                                              title="您确定要删除这条吗?"
                                              onConfirm={() => handleDelete(item)}
                                              okText="删除"
                                              cancelText="取消"
                                            >
                                              <Tooltip placement="top" title="删除">
                                                <DeleteOutlined />
                                              </Tooltip>
                                            </Popconfirm>

                                            <Tooltip placement="top" title="隐藏">
                                              <EyeInvisibleOutlined onClick={() => setInsIsShow(item, 1)} />
                                            </Tooltip>
                                          </Flex>
                                        )}
                                      </Flex>
                                    </Card>
                                  </Flex>
                                )}
                              </Draggable>
                            ))}
                            {provided.placeholder}
                          </Flex>
                        )}
                      </Droppable>
                    </DragDropContext>
                  )}
                </Flex>
                <Flex className="ins-con-show" vertical>
                  <Flex className="ins-con-tit" align="center">
                    从列表中隐藏
                  </Flex>
                  {insHiddenList && insHiddenList.length > 0 && (
                    <Flex vertical>
                      {insHiddenList.map((item, index: number) => {
                        return (
                          <Flex className="ins-con-sign" key={item.templateId}>
                            <Card className="ins-card ins-card-hidden">
                              <Flex justify="space-between">
                                <Flex>
                                  <Flex align="center">
                                    <HolderOutlined className="ins-icon-scale" />
                                    {fileExtensionHandler(item.fileName)}
                                  </Flex>
                                  <Flex className="ins-name" vertical>
                                    <Flex
                                      style={{
                                        color: token.colorTextBase,
                                        fontSize: token.fontSizeLG,
                                        fontWeight: "bold",
                                        lineHeight: token.lineHeightLG,
                                      }}
                                    >
                                      {item.templateName}
                                    </Flex>
                                    <Flex
                                      style={{
                                        color: token.colorTextTertiary,
                                        fontSize: token.fontSizeSM,
                                        lineHeight: token.lineHeightSM,
                                      }}
                                    >
                                      {item.templateDesc}
                                    </Flex>
                                  </Flex>
                                </Flex>
                                {item.tmplateType == "0" && (
                                  <Flex gap={token.marginXS} className="operate">
                                    <Tooltip placement="top" title="修改">
                                      <EditOutlined onClick={() => editInfo(item)} />
                                    </Tooltip>
                                    <Popconfirm
                                      trigger="click"
                                      placement="bottomLeft"
                                      title="您确定要删除这条吗?"
                                      onConfirm={() => handleDelete(item)}
                                      okText="删除"
                                      cancelText="取消"
                                    >
                                      <Tooltip placement="top" title="删除">
                                        <DeleteOutlined />
                                      </Tooltip>
                                    </Popconfirm>
                                    <Tooltip placement="top" title="显示">
                                      <EyeOutlined onClick={() => setInsIsShow(item, 2)} />
                                    </Tooltip>
                                  </Flex>
                                )}
                              </Flex>
                            </Card>
                          </Flex>
                        );
                      })}
                    </Flex>
                  )}
                </Flex>
              </Flex>
            ) : (
              <Flex className="setup-content-table empty">
                <EmptyData description={"暂无模板信息"} />
              </Flex>
            )}
          </Spin>
        </Flex>
      </Flex>
      <Modal
        title={templateId ? "编辑模板" : "新增模板"}
        open={visible}
        onCancel={() => {
          setVisible(false);
          setFileList([]);
          setImageList([]);
          setAgentCheckValue([]);
          setImageUrl("");
          form.resetFields();
        }}
        className="template-set-modal"
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              setVisible(false);
              setFileList([]);
              setImageList([]);
              setAgentCheckValue([]);
              setImageUrl("");
              form.resetFields();
            }}
          >
            取消
          </Button>,
          <Button
            key="save"
            type="primary"
            loading={addLoading}
            onClick={() => {
              form.submit();
            }}
          >
            保存为AI模板
          </Button>,
        ]}
        width={400}
        centered
      >
        <Form form={form} layout="vertical" onFinish={submit}>
          <Form.Item
            label="模板上传"
            name="templateFile"
            valuePropName="fileList"
            getValueFromEvent={(e) => (Array.isArray(e) ? e : e?.fileList)}
            rules={[{ required: true, message: "请上传模板文件" }]}
          >
            <Upload.Dragger {...uploadProps}>
              <p className="ant-upload-drag-icon">
                <UploadOutlined />
              </p>
              <p className="ant-upload-text">点击上传或拖拽文件到此处</p>
              <p className="ant-upload-hint">
                支持.docx，.xlsx，.pdf，.json格式文件
                <br />
                文件大小不超过5MB，.json文件不超过20M
              </p>
            </Upload.Dragger>
            {fileList && fileList.length > 0 && (
              <Flex
                gap={token.marginXS}
                align="center"
                onMouseEnter={() => setHovered(true)}
                onMouseLeave={() => setHovered(false)}
                style={{ marginTop: token.marginXS }}
              >
                <PaperClipOutlined style={{ fontSize: 16, color: "#999" }} />
                <a
                  href={fileList[0].absoluteUrl}
                  target="_blank"
                  style={{ width: "300px", overflow: "hidden", textOverflow: "ellipsis", whiteSpace: "nowrap" }}
                  rel="noreferrer"
                >
                  {fileList[0].originalName}
                </a>
                {hovered && (
                  <CloseOutlined
                    onClick={() => {
                      setFileList([]);
                      form.setFieldsValue({ templateFile: [] });
                    }}
                    style={{
                      cursor: "pointer",
                      color: "#999",
                      marginLeft: 4,
                    }}
                  />
                )}
              </Flex>
            )}
          </Form.Item>

          {/* 模板名称 */}
          <Form.Item
            label="模板名称"
            name="templateName"
            rules={[
              { required: true, message: "请输入模板名称" },
              {
                validator: (_, value) => {
                  if (!value) return Promise.resolve(); // 空值由 required 处理
                  if (formatList.includes(value)) {
                    return Promise.reject(new Error("模板名称已存在，请重新输入"));
                  }
                  return Promise.resolve();
                },
              },
            ]}
            className="tips"
          >
            <Input placeholder="请输入模板名称" maxLength={10} showCount />
          </Form.Item>
          <Flex className="tips-con" vertical>
            <Flex>该名称用以展示在调用的位置，比如写作功能</Flex>
            <Flex>该名称不可与默认模板名称重复：{formatList.join("、")}</Flex>
          </Flex>

          {/* 封面 */}
          {agentCheckValue && agentCheckValue.includes("ppt") && (
            <>
              <Form.Item
                label="封面"
                name="templateImage"
                rules={[{ required: true, message: "请上传封面" }]}
                className="tips"
              >
                <Upload
                  name="cover"
                  listType="picture-card"
                  className="avatar-uploader"
                  showUploadList={false}
                  accept=".jpg,.jpeg,.png"
                  beforeUpload={imageBeforeUpload}
                  customRequest={imageCustomRequest}
                >
                  {imageUrl ? <img src={imageUrl} alt="avatar" style={{ width: "100%" }} /> : uploadButton}
                </Upload>
              </Form.Item>
              <Flex className="tips-con" vertical>
                <Flex>封面尺寸为960*540</Flex>
                <Flex>支持.jpg，.jpeg，.png格式文件，文件大小不超过1M</Flex>
              </Flex>
            </>
          )}
          {/* 描述 */}
          <Form.Item label="描述" name="templateDesc" className="tips">
            <TextArea rows={4} placeholder="请输入模板描述..." maxLength={100} showCount />
          </Form.Item>
          <Flex className="tips-con">说明模板内容及用法以辅助用户调用决策</Flex>

          {/* 分类 */}
          <Form.Item label="分类" name="agentType" rules={[{ required: true, message: "请选择分类" }]} className="tips">
            <Checkbox.Group options={templateOption} onChange={agentChange} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default SetupComponent;
