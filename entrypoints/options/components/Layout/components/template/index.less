@import "@/assets/styles/variables";
.template-record-main {
  padding-top: var(--ant-margin);
  height: calc(100% - 40px);
  overflow-y: auto;
  width: 100%;
  cursor: default;
  display: block !important;
  position: relative;
  z-index: 2;
  .session-record {
    gap: var(--ant-margin);
    color: var(--ant-color-text);
    margin-bottom: var(--ant-margin-lg);
    flex-wrap: wrap;
  }
  .session-record-operate {
    margin-bottom: var(--ant-margin);
  }
  .clear-btn {
    border: none !important;
  }
  .ins-card{
    width: 320px !important;
    background: var(--ant-color-fill-quaternary);
    border-radius: var(--ant-border-radius) !important;
    border: 1px solid var(--ant-color-border) !important;
    margin-bottom: var(--ant-margin-sm);
    .extend-icon{
      height: 24px;
      margin-bottom: 0px;
    }
    .ant-card-body{
      padding: var(--ant-padding-sm) !important;
    }
    .operate{
      opacity: 0;
    }
    &:hover{
      background: var(--ant-control-item-bg-active);
      .operate{
        opacity: 1;
      }
    }
  }
  .ins-card-hidden{
    opacity: 0.5
  }
  .ins-tips{
    // font-size: var(--ant-font-size-lg);
    color: var(--ant-color-text-description);
    margin-bottom: 32px;
    padding: var(--ant-padding-xxs) 0px;
  }
  .ins-con{
    .ins-con-show{
      width: 368px;
      background: var(--ant-color-fill-quaternary);
      padding: var(--ant-padding-lg);
    }
    .ins-con-tit{
      font-size: var(--ant-font-size-xl);
      color: var(--ant-color-text);
      font-weight: 600;
      margin-bottom: var(--ant-margin);
    }
    .ins-name {
      width:160px;
      color: var(--ant-color-text);
      margin-left: var(--ant-margin-xs);
      >div{
        width: 160px;
        overflow:hidden;
        text-overflow:ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
      span{
        width: 100%;
        display:inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .ins-con-sign{
      width:100%;
    }
    .ins-icon-scale{
      color: var(--ant-color-text-tertiary);
      margin-right: var(--ant-margin-xs);
      font-size: 18px;
    }
  }
}
.template-set-modal{
  .tips{
    margin-bottom: 0px !important;
  }
  .tips-con{
    color: var(--ant-color-text-tertiary); 
    font-size: var(--ant-font-size-sm);
    margin-top: var(--ant-margin-xxs);
    line-height: var(--ant-line-heihgt-sm);
    margin-bottom: var(--ant-margin-lg);
  }
}
