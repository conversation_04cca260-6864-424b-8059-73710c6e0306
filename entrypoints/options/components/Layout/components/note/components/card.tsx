import React, { useState } from "react";
import { Button, Card, Flex, Form, Input, message, Modal, Popconfirm, Tooltip, theme, MenuProps, Dropdown } from "antd";
import "./card.less";
import { Prompt } from "@/types/prompt";
import { saveDetailJumpInfo } from "@/utils/notes";
import { formatDate } from "@/utils/dateFormat.ts";
import { CommentOutlined, DeleteOutlined, EditOutlined, MinusOutlined, TeamOutlined } from "@ant-design/icons";
import classnames from "classnames";
import { editNote, noteRelationRemove } from "@/api/note.ts";

/** 提示词卡片组件属性 */

export type PromptCardProps = {
  /**
   * 卡片类型：
   * public：公共卡片，
   * personal：我的卡片，
   * collection：收藏卡片
   */
  type?: "public" | "personal" | "collection";
  /** 提示词数据 */
  prompt: any;
  /** 是否允许编辑操作：type为personal时生效 */
  editable?: boolean;
  /** 是否允许删除操作：type为personal时生效 */
  deletable?: boolean;
  /** 是否允许共享操作：type为personal时生效 */
  releasable?: boolean;
  /** 编辑操作时的回调：type为personal时生效 */
  onEdit?: (prompt: Prompt) => void;
  isShow?: boolean;
  groupId?: any; // 组id
  /** 卡片操作成功后的回调 */
  onSubmitSuccess?: () => void;
  onNoteReadItem: (NoteId: string) => void;
};
export type PromptFormFieldType = {
  title: string;
  id: string;
};
let noteItems = {};
const { useToken } = theme;
/** 提示词卡片组件 */
const PromptCard: React.FC<PromptCardProps> = ({ prompt, isShow, groupId, onSubmitSuccess, onNoteReadItem }) => {
  const { token } = useToken();
  const [form] = Form.useForm();
  // 由于收藏操作完成后不能刷新页面，所以组件自己控制提示词的收藏状态
  const fetchRequest = useFetchRequest();
  const [tipOpen, setTipOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState(false);

  /** 处理删除提示词 */
  const handleNoteDelete = (note) => {
    fetchRequest({
      api: "delNote",
      params: { id: note.id },
      callback: (res) => {
        if (res.code == "200") {
          message.open({
            type: "success",
            content: "删除成功！",
          });
          onSubmitSuccess?.();
          window.postMessage({ type: "delNoteNotice", note }, "*");
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };
  const noteReadHandle = (event, noteId) => {
    // 获取点击的元素
    const clickedElement = event?.target;
    // 检查是否有 data-data 属性
    if (event && clickedElement.hasAttribute("data-data")) {
      // 获取 data-data 属性的值
      const dataValue = clickedElement.getAttribute("data-data");
      // 将值解析为对象（假设它是一个 JSON 字符串）
      try {
        const obj = JSON.parse(dataValue);
        if (obj.type === "note_rel_note") {
          const params = { id: obj.objId };
          fetchRequest({
            api: "queryNote",
            params,
            callback: (res) => {
              if (res.code === 200) {
                saveDetailJumpInfo(res.data);
              } else {
                message.open({
                  type: "error",
                  content: res.msg,
                });
              }
            },
          });
        }
      } catch (e) {
        console.error("无法解析 data-data 的值:", e);
      }
    } else {
      saveDetailJumpInfo(noteId);
      if (noteId.readFlag === "read_flag_true") return;
      event.stopPropagation();
      onNoteReadItem(noteId);
    }
  };
  const handleNoteSave = (items) => {
    let obj = {
      ...noteItems,
      title: items.title,
    };
    editNote(obj).then((res) => {
      if (res.code == "200") {
        message.open({
          type: "success",
          content: "修改成功！",
        });
        onSubmitSuccess?.();
      } else {
        message.open({
          type: "error",
          content: res.msg,
        });
      }
    });
  };
  const handleTipClick = (items) => {
    noteItems = items;
    form.setFieldsValue({
      title: items.title,
    });
    setTipOpen(true);
  };
  const handleTipCancel = () => {
    setTipOpen(false);
  };
  const handleAddModelCancel = () => {
    setTipOpen(false);
    form.setFieldsValue({
      content: "",
      title: "",
    });
  };

  //移除该便签
  const noteRelationRemoveId = (note) => {
    noteRelationRemove({
      noteIds: [note.id],
      groupId: groupId[0],
    }).then((res) => {
      if (res.code == "200") {
        message.open({
          type: "success",
          content: "剔除成功！",
        });
        onSubmitSuccess?.();
      } else {
        message.open({
          type: "error",
          content: res.msg,
        });
      }
    });
  };
  const items: MenuProps["items"] = [
    {
      key: "1",
      icon: <EditOutlined />,
      label: "编辑",
    },
    {
      key: "2",
      icon: <MinusOutlined />,
      label: "剔除该便签",
    },
    {
      key: "3",
      icon: <DeleteOutlined />,
      label: "删除",
    },
  ];
  // 右键菜单点击
  const menuClick = (e, prompt) => {
    if (e.key == "1") {
      handleTipClick(prompt);
    } else if (e.key == "3") {
      handleNoteDelete(prompt);
    } else if (e.key == "2") {
      noteRelationRemoveId(prompt);
    }
  };
  return (
    <>
      {isShow ? (
        <Dropdown
          menu={{
            items,
            onClick: (e) => {
              menuClick(e, prompt);
            },
          }}
          trigger={["contextMenu"]}
          key={prompt.id}
        >
          <div
            style={{ width: "100%" }}
            onContextMenu={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
          >
            <Flex
              className="setup-list-sign"
              align="center"
              justify="space-between"
              onClick={(e) => noteReadHandle(e, prompt)}
            >
              <Flex align="center">
                <span className={classnames(prompt.readFlag != "read_flag_true" ? "title" : "title-red")}></span>
                <Flex className="title-con">{prompt.title}</Flex>
              </Flex>
              <Flex align="center" justify="flex-end" gap={token.marginXS} style={{ width: "185px", height: "100%" }}>
                <Flex>
                  {/* cooperation_type_other 他人创建并@我 */}
                  {/* cooperation_type_null  我创建  没有@他人 */}
                  {/* cooperation_type_me  我创建并@他人 */}
                  {prompt.cooperationType === "cooperation_type_other" && (
                    <>
                      <TeamOutlined className="team-icon" />
                      {prompt.belongUserName}
                    </>
                  )}
                  {prompt.cooperationType === "cooperation_type_me" && (
                    <>
                      <TeamOutlined className="team-icon" />
                    </>
                  )}
                </Flex>
                {prompt.cooperationType !== "cooperation_type_other" && (
                  <Flex className="operte-icon" gap={token.marginXS}>
                    <Flex align="center">
                      <Tooltip
                        placement="top"
                        title="评论"
                        getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                      >
                        <Button icon={<CommentOutlined />} size="small" type="text"></Button>
                      </Tooltip>
                      <Flex align="center" style={{ color: token.colorTextQuaternary }}>
                        {prompt?.commentCount || 0}
                      </Flex>
                    </Flex>
                    <Popconfirm
                      onConfirm={(e) => {
                        e.stopPropagation();
                        handleNoteDelete(prompt);
                      }}
                      onCancel={(e) => e.stopPropagation()}
                      okText="确定"
                      cancelText="取消"
                      title="确定要删除该便签吗?"
                    >
                      <Tooltip
                        placement="top"
                        title="删除"
                        getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                      >
                        <Button
                          size="small"
                          icon={<DeleteOutlined />}
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                          type="text"
                        ></Button>
                      </Tooltip>
                    </Popconfirm>
                  </Flex>
                )}
              </Flex>
            </Flex>
          </div>
        </Dropdown>
      ) : (
        <Card
          onClick={(e) => noteReadHandle(e, prompt)}
          className="note-card"
          key={prompt.id}
          title={
            <>
              {!(prompt.readFlag === "read_flag_true") && <span className="note-card-circle"></span>}
              <span
                className={classnames(" common-title ", prompt.readFlag != "read_flag_true" ? "title" : "unread-title")}
              >
                {prompt.title ? prompt.title : "无标题"}
              </span>
            </>
          }
          extra={<div className="note-time">{formatDate(prompt.createTime)}</div>}
        >
          <p
            className={classnames(
              "common-content ",
              prompt.readFlag != "read_flag_true" ? "note-card-content" : "unread-content",
            )}
            dangerouslySetInnerHTML={{
              __html: prompt.content.replace(
                /chrome-extension:\/\/[a-zA-Z0-9]+\/images\/textEditor\//g,
                `chrome-extension://${browser.runtime.id}/images/textEditor/`,
              ),
            }}
          />
          <Flex className="footer-bar" justify="space-between" align="center">
            <Flex className="note-card-footer" align="center">
              <Flex>
                {/* cooperation_type_other 他人创建并@我 */}
                {/* cooperation_type_null  我创建  没有@他人 */}
                {/* cooperation_type_me  我创建并@他人 */}
                {prompt.cooperationType === "cooperation_type_other" && (
                  <>
                    <TeamOutlined className="team-icon" />
                    {prompt.belongUserName}
                  </>
                )}
                {prompt.cooperationType === "cooperation_type_me" && (
                  <>
                    <TeamOutlined className="team-icon" />
                  </>
                )}
              </Flex>
            </Flex>
            {prompt.cooperationType !== "cooperation_type_other" && (
              <Flex className="operte-icon">
                <Flex align="center">
                  <Tooltip
                    placement="top"
                    title="评论"
                    getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                  >
                    <Button icon={<CommentOutlined />} size="small" type="text"></Button>
                  </Tooltip>
                  <Flex align="center" style={{ color: token.colorTextQuaternary }}>
                    {prompt?.commentCount || 0}
                  </Flex>
                </Flex>
                <Tooltip
                  placement="top"
                  title="编辑"
                  getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                >
                  <Button
                    icon={<EditOutlined />}
                    type="text"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleTipClick(prompt);
                    }}
                  ></Button>
                </Tooltip>
                {groupId && groupId.length > 0 && (
                  <Tooltip
                    placement="top"
                    title="剔除便签"
                    getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                  >
                    <Button
                      icon={<MinusOutlined />}
                      type="text"
                      onClick={(e) => {
                        e.stopPropagation();
                        noteRelationRemoveId(prompt);
                      }}
                    ></Button>
                  </Tooltip>
                )}
                <Popconfirm
                  onConfirm={(e) => {
                    e.stopPropagation();
                    handleNoteDelete(prompt);
                  }}
                  onCancel={(e) => e.stopPropagation()}
                  okText="确定"
                  cancelText="取消"
                  title="确定要删除该便签吗?"
                >
                  <Tooltip
                    placement="top"
                    title="删除"
                    getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                  >
                    <Button
                      icon={<DeleteOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                      }}
                      type="text"
                    ></Button>
                  </Tooltip>
                </Popconfirm>
              </Flex>
            )}
          </Flex>
        </Card>
      )}
      <Modal
        title="编辑"
        open={tipOpen}
        onCancel={handleTipCancel}
        footer={null}
        className="setup-model"
        centered={true}
      >
        <Form
          name="basic"
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
          style={{ maxWidth: 700 }}
          onFinish={handleNoteSave}
          autoComplete="off"
          form={form}
        >
          <Form.Item<PromptFormFieldType>
            label="标题"
            name="title"
            rules={[
              { required: true, message: "必填项" },
              { max: 15, message: "标题不能超过15个字" },
            ]}
          >
            <Input placeholder="请输入便签标题" showCount maxLength={15} />
          </Form.Item>

          <Flex justify="flex-end" className="footer">
            <Button htmlType="button" onClick={handleAddModelCancel} className="setup-model-btn-cancel">
              取消
            </Button>
            <Button type="primary" htmlType="submit" loading={loading} className="setup-model-btn-submit">
              保存
            </Button>
          </Flex>
        </Form>
      </Modal>
    </>
  );
};

export default React.memo(PromptCard);
