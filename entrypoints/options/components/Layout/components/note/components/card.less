@import "@/assets/styles/variables";
.note-card {
  position: relative;
  cursor: pointer;
  .note-card-circle {
    width: 6px;
    height: 6px;
    display: block;
    border-radius: 50%;
    background-color: var(--ant-color-error);
    position: absolute;
    top: 14px;
    left: 10px;
  }
  /* 提示词卡片 */
  // box-shadow: rgba(0, 0, 0, 0.1) 0 0 10px 1px;
  min-width: 200px;
  width: 100%;
  background: var(--ant-color-bg-base);
  border-radius: var(--ant-border-radius) !important;
  border: 1px solid var(--ant-color-border) !important;
  padding: var(--ant-padding-sm) !important;
  padding-bottom: var(--ant-padding-xs) !important;
  .ant-card-head {
    /* 提示词卡片头部 */
    // background-color: #F0F5FD;
    min-height: 32px;
    height: 32px;

    border-bottom: none;
    .ant-card-head-wrapper {
      justify-content: space-between;
      .ant-card-head-title {
        /* 提示词标题 */
        margin-right: 8px;
        // .title,p {
        //   font-family: @side-panel-font-family-bold;
        //   font-weight: var(--ant-font-weight-strong);
        //   overflow: hidden;
        //   text-overflow: ellipsis;
        //   white-space: nowrap;
        //   font-size: var(--ant-font-size-lg);
        //   color: var(--ant-color-text-base);
        // }
      }

      .ant-card-extra {
        /* 操作区 */
        margin: 0;
        display: flex;
        gap: 5px;
        /* 卡片头部的操作按钮 */
        align-items: center;
        justify-content: center;
        flex-wrap: nowrap;
      }
    }
  }
  .common-title,
  .common-title p {
    max-width: 86%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-family: @side-panel-font-family-bold;
  }
  .title,
  .title p {
    font-size: var(--ant-font-size);
    color: var(--ant-color-text-base);
    font-weight: var(--ant-font-weight-strong);
  }
  .unread-title,
  .unread-title p {
    font-size: var(--ant-font-size);
    color: var(--ant-color-text-tertiary);
    font-weight: var(--ant-font-weight-strong);
  }

  .common-content {
    max-width: 100%;
    height: 24px;
    overflow: hidden; /* 隐藏溢出的内容 */
    white-space: nowrap; /* 禁止换行 */
    text-overflow: ellipsis; /* 超出部分显示省略号 */
    font-family: @side-panel-font-family;
    margin: 0;
    display: inline-flex;
    align-items: center;
  }
  .common-content p {
    white-space: nowrap;
    text-overflow: ellipsis;
    margin: 0;
    display: inline-flex;
    align-items: center;
  }
  .content,
  .content p {
    font-size: var(--ant-font-size);
    color: var(--ant-color-text);
  }
  .unread-content p,
  .unread-content {
    font-size: var(--ant-font-size);
    color: var(--ant-color-text-tertiary);
  }
  .note-time {
    color: var(--ant-color-text-quaternary);
    font-size: var(--ant-font-size-sm);
    font-family: @side-panel-font-family;
  }
  .ant-card-body {
    /* 提示词卡片主体 */
    padding: 0px;
    .title {
      vertical-align: text-bottom;
      font-size: var(--ant-font-size-lg);
      color: var(--ant-color-text-base);
      margin-left: var(--ant-margin-xs);
    }
    .note-card-content {
      /* 提示词内容，超出省略 */
      margin-top: var(--ant-margin-xxs);
      font-size: var(--ant-font-size);
      color: var(--ant-color-text-tertiary);
      line-height: var(--ant-line-height);
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      word-break: break-all;
      width: 90%;
      height: 20px;
    }
  }
  .note-card-footer {
    height: 32px;
    .footer-bar {
      font-size: var(--ant-font-size-sm);
      color: var(--ant-color-text-tertiary);
    }
    .team-icon {
      margin-right: var(--ant-margin-xxs);
    }
  }
  .operte-icon {
    opacity: 0;
    gap: var(--ant-margin-xxs);
    .anticon {
      font-size: var(--ant-font-size);
      color: var(--ant-color-text);
    }
  }
  &:hover {
    .operte-icon {
      opacity: 1;
    }
  }
  .setup-model-btn {
    display: flex;
    justify-content: end;
    // width: 100%;

    .setup-model-btn-cancel {
      margin-right: 20px !important;
    }
  }

  .footer {
    gap: var(--ant-margin-xs);
  }

  .note-card-footer:not(:empty) ~ .operte-icon {
    margin-left: auto;
  }

  a[data-data] {
    font-size: var(--ant-font-size);
    color: var(--ant-color-primary);
    display: inline-flex;
    align-items: center;
    margin-right: 2px;
  }

}
.setup-list-sign{
  width:100%;
  border-radius: var(--ant-border-radius);
  border: 1px solid var(--ant-color-border);
  padding: var(--ant-padding-xs) var(--ant-padding-sm);
  color: var(--ant-color-text-tertiary);
  font-size: var(--ant-font-size);
  margin-bottom: var(--ant-margin);
  height: 40px;
  cursor: pointer;
  .title-con{
    width: 420px;
  }
  .operte-icon{
    display:none;
  }
  .title-red, title{
    width:6px;
    height: 6px;
    border-radius: 50%;
    display: inline-block;
    margin-right: var(--ant-margin-xxs);
  }
  .title-red{
    background: var(--ant-color-error);
  }
  .un-read{
    color: var(--ant-color-text-base);
  }
  &:hover{
    .operte-icon{
      display:flex;
    }
  }
}
