@import "@/assets/styles/variables";
// 便签
.note-content-right {
  width: 1008px;
  height: 100%;
  padding: var(--ant-padding-xl);
  background-color: #fff;
  border-radius: var(--ant-border-radius-lg);
  .setup-content-header {
    /* 右侧设置内容header */
    align-items: center;
    border-bottom: 1px solid var(--ant-color-split);
    justify-content: space-between;
    padding-bottom: var(--ant-margin);

    .setup-content-title {
      height: 40px;
      line-height: 40px;
      font-size: var(--ant-font-size-heading-2);
      background: linear-gradient(101deg, #1888ff 14%, #2f54eb 89%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-weight: bold;
    }
  }
  .setup-content-tab {
    margin: var(--ant-margin) 0;
  }
  .ant-tree {
    background: none !important;
  }
  .setup-content-box, .setup-group-box {
    width: 100%;
    cursor: default;
    margin-top: var(--ant-margin);
    .setup-content-table {
      /* 提示词管理表格 */
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      grid-template-rows: repeat(auto-fit, 108px);
      justify-items: flex-start;
      gap: var(--ant-margin);
      height: calc(100vh - 310px);
      overflow: auto;

      .ant-card-bordered {
        .ant-card-head {
          padding: 0px;
        }
        .ant-card-head-title {
          padding: 0;
        }
        .ant-card-extra {
          padding: 0;
        }
      }
    }
    .setup-content-list{
      display: block;
      flex-direction: column;
      grid-template-columns:none;
      grid-template-rows:none;
    }

    .ant-pagination {
      /* 分页组件 */
      display: flex;
      justify-content: flex-end;
      margin-top: var(--ant-margin-sm);
    }
    .empty {
      height: calc(100vh - 310px);
    }
  }
  .setup-group-box{
    .setup-content-table {
      height: calc(100vh - 340px);
    }
  }
  .note-title-tab{
    margin-top: var(--ant-margin-xxs);
    // margin-bottom: var(--ant-margin-md);
    .item-tags{
      color: var(--ant-color-text);
      font-size: var(--ant-font-size-sm);
      padding: 1px var(--ant-padding-xs);
      cursor: pointer;
      line-height: var(--ant-line-height-sm);
    }
    .selected{
      background: var(--ant-color-primary);
      border-radius: var(--ant-border-radius-sm);
      color: #fff;
    }
  }
  .group-class{
    height: calc(100vh - 280px);
    overflow-y: auto;
    .ant-spin-nested-loading{
      height:100%;
      .ant-spin-container{
        height:100%;
      }
    }
  }
}
