/** 个人设置 */
import React, { useEffect, useRef, useState } from "react";
import {
  Button,
  Dropdown,
  Flex,
  Form,
  Input,
  Menu,
  message,
  Pagination,
  Segmented,
  Spin,
  Switch,
  Tag,
  theme,
  Tooltip,
  Tree,
} from "antd";
import PromptCard from "./components/card";
import NoteGroupModal from "@/components/NoteGroupModal";
import { Prompt } from "@/types/prompt";
import "./index.less";
import {
  markNoteAllRead,
  markNoteRead,
  pageNote,
  notesGroupTree,
  notesGroupPage,
  notesGroupDissolve,
} from "@/api/note";
import { formatDate } from "@/utils/dateFormat.ts";
import EmptyData from "@/components/EmptyData";
import classnames from "classnames";
import {
  ExclamationCircleOutlined,
  FolderOpenOutlined,
  LoadingOutlined,
  PlusOutlined,
  SearchOutlined,
  SortDescendingOutlined,
  SortAscendingOutlined,
  EditOutlined,
  DeleteOutlined,
  UngroupOutlined,
} from "@ant-design/icons";
const { useToken } = theme;
// 列表查询的初始化参数
const searchParamsInit = {
  pageNum: 1,
  pageSize: 10,
  orders: [{ column: "create_time", asc: false }],
  entity: {
    query: "",
    url: "",
    type: "note_type_all",
    channelType: "",
  },
};
// 列表数据的初始化数据
const pageDataInit: { count: number; page: PageAPIResponse<Prompt> } = {
  count: 0,
  page: {
    current: 1,
    size: 10,
    total: 0,
    pages: 0,
    records: [],
    count: "",
  },
};

// 便签组
const searchGroupParamsInit = {
  pageNum: 1,
  pageSize: 10,
  orders: [{ column: "create_time", asc: false }],
  entity: {
    keyword: "",
    groupId: "",
  },
};

// Tab页签数据配置
const SetupComponent: React.FC = () => {
  const { token } = useToken();
  // 查询数据的请求参数
  const [searchParams, setSearchParams] = useState(searchParamsInit);
  // 当前显示的便签/便签组分页数据
  const [promptPageData, setPromptPageData] = useState<{ count: number; page: PageAPIResponse<Prompt> }>(pageDataInit);
  // 查询便签组数据的请求参数
  const [searchGroupParams, setSearchGroupParams] = useState(searchGroupParamsInit);
  // 当前选中的便签
  const [currentPrompt, setCurrentPrompt] = useState<Prompt>(null);
  const [tabIndex, setTabIndex] = useState<string>("0");
  const [loading, setLoading] = useState<boolean>(true);
  const [queryContent, setQueryContent] = useState("");
  const [selectedItem, setSelectedItem] = useState<string>("note_type_all");
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [treeData, setTreeData] = useState([]);
  const [treeLoading, setTreeLoading] = useState(false); // 树形数据加载状态
  const [groupNum, setGroupNum] = useState(1); // 目前1是便签，2是便签组
  const [currentGroupId, setCurrentGroupId] = useState([]); // 当前选中的便签组id
  const [currentData, setCurrentData] = useState<any>({}); // 当前选中的便签组数据
  const [isSwitchList, setIsSwitchList] = useState(false); // 是否是列表模式
  const [add, setAdd] = useState(false);
  const [sort, setSort] = useState(false);
  const fetchRequest = useFetchRequest();
  const tagList = [
    { key: "note_type_all", label: "全部" },
    { key: "channel_type_created", label: "我创建的" },
    { key: "channel_type_involved", label: "协同" },
    // { key: "note_type_current", label: "当前页" },
    { key: "note_type_unread", label: "未读" },
  ];
  // 页面首次加载或请求参数变化时，获取用户信息并获取便签数据
  useEffect(() => {
    handlePromptList();
  }, [searchParams, searchGroupParams]);

  /** 分页查询便签 */
  const handlePromptList = () => {
    // 获取便签
    if (groupNum == 1) {
      setLoading(true);
      pageNote(searchParams).then((res) => {
        setLoading(false);
        if (res.code === 200) {
          setPromptPageData(res.data);
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      });
    } else {
      setLoading(true);
      notesGroupPage(searchGroupParams).then((res) => {
        setLoading(false);
        if (res.code === 200) {
          setPromptPageData(res.data);
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      });
    }
  };

  // 单个已读
  const handleNoteRead = (noteId) => {
    markNoteRead({ id: noteId.id }).then((res) => {
      if (res.code == "200") {
        handlePromptList();
      } else {
        message.open({
          type: "error",
          content: res.msg,
        });
      }
    });
  };

  // 全部已读
  const handleAllRead = (noteId) => {
    markNoteAllRead().then((res) => {
      if (res.code == "200") {
        handlePromptList();
      } else {
        message.open({
          type: "error",
          content: res.msg,
        });
      }
    });
  };
  const PromptTable: React.FC = () => {
    return (
      <>
        {promptPageData && promptPageData.page.total != 0 ? (
          <>
            <div className={`setup-content-table ${isSwitchList && groupNum === 2 ? "setup-content-list" : ""}`}>
              {
                <>
                  {promptPageData.page.records.map((item: Prompt, index: number) => {
                    return (
                      <PromptCard
                        key={item.id}
                        isShow={isSwitchList && groupNum === 2 ? true : false}
                        prompt={item}
                        deletable={true}
                        groupId={groupNum == 2 ? currentGroupId : ""}
                        editable={true}
                        releasable={true}
                        onNoteReadItem={handleNoteRead}
                        onSubmitSuccess={handlePromptList}
                      ></PromptCard>
                    );
                  })}
                </>
              }
            </div>
            <Pagination
              showSizeChanger
              onChange={(page, pageSize) => {
                setSearchParams({ ...searchParams, pageNum: page, pageSize });
              }}
              current={promptPageData.page.current}
              pageSize={promptPageData.page.size}
              total={promptPageData.page.total}
              showTotal={(total) => `共 ${total} 条`}
            />
          </>
        ) : (
          <Flex className="empty" justify="center" align="center">
            <EmptyData description={"没有找到便签"} />
          </Flex>
        )}
      </>
    );
  };
  // tab 选中
  const tabSelect = (item) => {
    setSelectedItem(item.key);
    let tabType = "";
    let channelType = "";
    if (item.key == "channel_type_created" || item.key == "channel_type_involved") {
      tabType = "note_type_all";
      channelType = item.key;
    } else {
      tabType = item.key;
      channelType = "";
    }
    setSearchParams({
      ...searchParams,
      ...{
        pageNum: 1,
        pageSize: 10,
      },
      entity: {
        ...searchParams.entity,
        ...{ type: tabType, channelType: channelType },
      },
    });
  };
  const handleTabChange = (targetKey: string) => {
    setPromptPageData(pageDataInit);
    if (targetKey === "便签") {
      setGroupNum(1);
    } else {
      setGroupNum(2);
    }
    if (targetKey == "便签") {
      setSearchParams({
        ...searchParams,
        ...{
          pageNum: 1,
          pageSize: 10,
        },
        entity: {
          ...searchParams.entity,
          ...{ query: queryContent },
        },
        ...{ orders: [{ column: "create_time", asc: sort }] },
      });
    } else {
      const newGroupId = currentData.id;
      setCurrentGroupId([newGroupId]);
      if (newGroupId) {
        setSearchGroupParams({
          ...searchGroupParams,
          pageNum: 1,
          pageSize: 10,
          orders: [{ column: "create_time", asc: sort }],
          entity: {
            ...searchGroupParams.entity,
            ...{ keyword: queryContent },
            groupId: newGroupId,
          },
        });
      }
    }
  };
  const getQuery = (e) => {
    setQueryContent(e.target.value.trim());
  };
  const getData = (e) => {
    if (groupNum == 1) {
      setSearchParams({
        ...searchParams,
        pageNum: 1,
        entity: { ...searchParams.entity, query: e.target.value.trim() },
      });
    } else {
      setSearchGroupParams({
        ...searchGroupParams,
        pageNum: 1,
        entity: { ...searchGroupParams.entity, keyword: e.target.value.trim() },
      });
    }
  };
  // 获取树结构
  const getCommentPage = () => {
    setTreeLoading(true);
    notesGroupTree({}).then((res) => {
      if (res.code == 200) {
        setTreeLoading(false);
        let arr = transformTreeData(res.data);
        setTreeData(arr);
        if (res.data[0]?.id) {
          setCurrentData(res.data[0]);
        }
      }
    });
  };
  useEffect(() => {
    getCommentPage();
  }, []);

  const transformTreeData = (data) => {
    return data.map((item) => ({
      title: item.name,
      key: item.id,
      icon: <FolderOpenOutlined />, // 默认图标
      children: item.children ? transformTreeData(item.children) : undefined,
    }));
  };
  // 开关点击
  const onSwitchChange = (checked: boolean) => {
    setIsSwitchList(checked);
  };
  // 树结构右键
  const renderTreeNodes = (data) =>
    data.map((item) => {
      const menu = (
        <Menu
          onClick={({ key }) => {
            if (key === "edit") {
              setAdd(false);
              setVisible(true);
            } else if (key === "delete") {
              setAdd(false);
              fetchRequest({
                api: "delNotesGroup",
                params: [item.key],
                callback: (res) => {
                  setLoading(false);
                  if (res.code === 200) {
                    message.open({
                      type: "success",
                      content: "删除成功",
                    });
                    getCommentPage();
                  } else {
                    if (res.code !== 401) {
                      message.open({
                        type: "error",
                        content: res.msg,
                      });
                    }
                  }
                },
              });
            } else if (key === "disband") {
              setAdd(false);
              notesGroupDissolve({ id: item.key }).then((res) => {
                if (res.code === 200) {
                  message.open({
                    type: "success",
                    content: "解散成功",
                  });
                  getCommentPage();
                } else {
                  message.open({
                    type: "error",
                    content: res.msg,
                  });
                }
              });
            }
          }}
          items={[
            { label: "编辑", icon: <EditOutlined />, key: "edit" },
            { label: "解散该组", icon: <UngroupOutlined />, key: "disband" },
            { label: "删除", icon: <DeleteOutlined />, key: "delete" },
          ]}
        />
      );

      return {
        ...item,
        title: (
          <Dropdown overlay={menu} trigger={["contextMenu"]}>
            <span onContextMenu={(e) => e.preventDefault()}>{item.title}</span>
          </Dropdown>
        ),
        children: item.children ? renderTreeNodes(item.children) : undefined,
      };
    });
  return (
    <>
      <div className="note-content-right">
        {/* header区 */}
        <Flex className="setup-content-header">
          <h1 className="setup-content-title">便签清单</h1>
          <Input
            size="large"
            value={queryContent}
            onChange={getQuery}
            onBlur={getData}
            allowClear
            placeholder="搜索便签/便签组"
            prefix={<SearchOutlined />}
            style={{ width: "310px" }}
          />
        </Flex>
        <div style={{ position: "relative", zIndex: 2 }}>
          <Spin spinning={loading} indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />}>
            <Segmented<string> className="setup-content-tab" options={["便签", "便签组"]} onChange={handleTabChange} />
            {groupNum === 1 && (
              <Flex vertical>
                <Flex className="note-title-tab" justify="space-between">
                  <Flex gap={token.marginSM} align="center">
                    {tagList.map((item) => {
                      return (
                        <Flex
                          key={item.key}
                          onClick={() => {
                            tabSelect(item);
                          }}
                          className={`item-tags ${selectedItem === item.key ? "selected" : ""}`}
                        >
                          {item.label}
                        </Flex>
                      );
                    })}
                  </Flex>
                  <Flex gap={token.paddingXS}>
                    <Tag
                      style={{
                        padding: `${token.paddingXXS} ${token.paddingXS}`,
                        marginRight: 0,
                        alignItems: "center",
                        display: "flex",
                      }}
                      icon={<ExclamationCircleOutlined />}
                      color="warning"
                      bordered={false}
                    >
                      未读便签
                      <span style={{ marginLeft: token.marginXXS }}>{promptPageData.count}</span>
                    </Tag>
                    <Button type="link" onClick={handleAllRead} disabled={promptPageData.count < 1}>
                      全部已读
                    </Button>
                    <Button
                      icon={
                        sort ? (
                          <SortDescendingOutlined style={{ fontSize: token.fontSizeXL }} />
                        ) : (
                          <SortAscendingOutlined style={{ fontSize: token.fontSizeXL }} />
                        )
                      }
                      type="text"
                      onClick={() => {
                        setSort((prevSort) => {
                          const newSort = !prevSort;
                          setSearchParams({
                            ...searchParams,
                            pageNum: 1,
                            orders: [{ column: "create_time", asc: newSort }], // 这里使用新值
                            entity: { ...searchParams.entity },
                          });
                          return newSort; // 确保 `sort` 正确更新
                        });
                      }}
                    ></Button>
                  </Flex>
                </Flex>
                <Flex vertical className="setup-group-box">
                  <PromptTable></PromptTable>
                </Flex>
              </Flex>
            )}
            {groupNum === 2 && (
              <Flex>
                <Flex
                  vertical
                  style={{
                    width: "256px",
                    padding: token.padding,
                    borderRadius: token.borderRadiusLG,
                    background: "rgba(0, 0, 0, 0.02)",
                    gap: token.paddingXS,
                  }}
                >
                  <Button
                    style={{ width: "100%" }}
                    icon={<PlusOutlined />}
                    size="large"
                    onClick={() => {
                      setVisible(true);
                      setAdd(true);
                    }}
                  >
                    新增组
                  </Button>
                  <div className="group-class">
                    {/* onSelect={onSelect} */}
                    <Spin spinning={treeLoading}>
                      <Tree
                        treeData={renderTreeNodes(treeData)}
                        defaultSelectedKeys={currentGroupId}
                        showLine={true}
                        style={{ background: "none" }}
                        onSelect={(keys, info) => {
                          setCurrentData(info.node);
                          setCurrentGroupId([info.node.key]); // 更新选中状态
                          setSearchGroupParams({
                            ...searchGroupParams,
                            ...{
                              pageNum: 1,
                              pageSize: 10,
                            },
                            entity: {
                              ...searchGroupParams.entity,
                              ...{ groupId: info.node.key },
                            },
                          });
                        }}
                      />
                    </Spin>
                  </div>
                </Flex>
                <Flex vertical style={{ padding: token.padding, flex: 1, paddingTop: 0, marginLeft: token.margin }}>
                  <Flex
                    style={{
                      color: token.colorText,
                      fontSize: token.fontSizeXL,
                      fontWeight: "600",
                      lineHeight: token.lineHeight,
                    }}
                  >
                    {currentData.name || currentData.title}
                  </Flex>
                  <Flex justify="space-between" align="center">
                    <Flex gap={token.marginXXS} align="center">
                      列表渲染
                      <Switch size="small" value={isSwitchList} onChange={onSwitchChange} />
                    </Flex>
                    <Button
                      icon={
                        sort ? (
                          <SortDescendingOutlined style={{ fontSize: token.fontSizeXL }} />
                        ) : (
                          <SortAscendingOutlined style={{ fontSize: token.fontSizeXL }} />
                        )
                      }
                      type="text"
                      onClick={() => {
                        setSort((prevSort) => {
                          const newSort = !prevSort; // 计算新值
                          setSearchGroupParams((prevParams) => ({
                            ...prevParams,
                            pageNum: 1,
                            orders: [{ column: "create_time", asc: newSort }], // 确保使用新值
                            entity: { ...prevParams.entity },
                          }));

                          return newSort; // 确保 `sort` 正确更新
                        });
                      }}
                    ></Button>
                  </Flex>
                  <Flex vertical className="setup-group-box">
                    <PromptTable></PromptTable>
                  </Flex>
                </Flex>
              </Flex>
            )}
          </Spin>
        </div>
      </div>
      <NoteGroupModal
        visible={visible}
        id={add ? "" : currentGroupId[0]}
        onClose={() => setVisible(false)}
        onConfirm={() => {
          setVisible(false);
          getCommentPage();
        }}
      />
    </>
  );
};

export default SetupComponent;
