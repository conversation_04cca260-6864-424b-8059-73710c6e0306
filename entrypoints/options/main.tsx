/** 设置页面入口文件 */
import React from "react";
import ReactDOM from "react-dom/client";
import { ConfigProvider } from "antd";
import { RouterProvider } from "react-router-dom";
import optionsRouters from "./router";
import themeToken from "@/themeOption.json";
import zhCN from "antd/es/locale/zh_CN";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
dayjs.locale("zh-cn");
// 引入全局样式
import "@/assets/styles/index.less";
ReactDOM.createRoot(document.getElementById("options")!).render(
  // <React.StrictMode>
  <ConfigProvider locale={zhCN} theme={themeToken}>
    <RouterProvider router={optionsRouters} />
  </ConfigProvider>,
  // </React.StrictMode>,
);
