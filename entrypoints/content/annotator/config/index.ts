import { settingsFrom } from "./settings";
import type { SettingsGetters } from "./settings";

type ValueGetter = (settings: SettingsGetters, name: string) => any;

type ConfigDefinition = {
  /** 获取传入源中的值的方法 */
  getValue: ValueGetter;

  /**
   * 允许在浏览器扩展中读取此设置。如果为 false
   * 并且浏览器扩展上下文为 true，则使用 `defaultValue`（如果提供）
   * 否则忽略配置键
   */
  allowInBrowserExt: boolean;

  /** 如果 `getValue` 返回 undefined，则设置默认值 */
  defaultValue?: any;
  /** 转换值的类型、值或两者 */
  coerce?: (value: any) => any;
};

type ConfigDefinitionMap = Record<string, ConfigDefinition>;

/**
 * 在特定上下文中与 Hypothesis 客户端配置相关的命名子集。
 */
type Context = "sidebar" | "annotator" | "all";

/**
 * 返回与特定上下文相关的配置键。
 */
function configurationKeys(context: Context): string[] {
  const contexts: Record<Exclude<Context, "all">, string[]> = {
    annotator: ["contentReady"],
    sidebar: [
      "annotations",
      "branding",
      "focus",
      "group",
      "query",
      "requestConfigFromFrame",
      "services",
      "showHighlights",
      "theme",
    ],
  };

  if (context === "all") {
    // 用于测试的完整配置键列表。
    return Object.values(contexts).flat();
  }

  return contexts[context];
}

const getHostPageSetting: ValueGetter = (settings, name) => settings.hostPageSetting(name);

/**
 * 配置键的定义
 */
const configDefinitions: ConfigDefinitionMap = {
  annotations: {
    allowInBrowserExt: true,
    defaultValue: null,
    getValue: (settings) => settings.annotations,
  },
  branding: {
    defaultValue: null,
    allowInBrowserExt: false,
    getValue: getHostPageSetting,
  },
  contentReady: {
    allowInBrowserExt: true,
    defaultValue: null,
    getValue: getHostPageSetting,
  },
  group: {
    allowInBrowserExt: true,
    defaultValue: null,
    getValue: (settings) => settings.group,
  },
  focus: {
    allowInBrowserExt: false,
    defaultValue: null,
    getValue: getHostPageSetting,
  },
  theme: {
    allowInBrowserExt: false,
    defaultValue: null,
    getValue: getHostPageSetting,
  },
  query: {
    allowInBrowserExt: true,
    defaultValue: null,
    getValue: (settings) => settings.query,
  },
  requestConfigFromFrame: {
    allowInBrowserExt: false,
    defaultValue: null,
    getValue: getHostPageSetting,
  },
  services: {
    allowInBrowserExt: false,
    defaultValue: null,
    getValue: getHostPageSetting,
  },
  showHighlights: {
    allowInBrowserExt: false,
    defaultValue: "always",
    getValue: (settings) => settings.showHighlights,
  },
};
/**
 * 返回与特定上下文相关的 Hypothesis 客户端配置子集。
 *
 * 有关所有可用配置及其在页面上包含的不同方式的详细信息，请参阅
 * https://h.readthedocs.io/projects/client/en/latest/publishers/config/。
 * 除了嵌入者提供的配置外，引导脚本还会将一些额外的配置传递给注释器，
 * 例如各种子应用程序和引导脚本本身的 URL。
 */
export function getConfig(context: Context, window_: Window = window) {
  const settings = settingsFrom(window_);
  const config: Record<string, unknown> = {};

  // 根据应用程序上下文过滤配置，因为某些配置值可能不适合或对某些应用程序有误。
  for (const key of configurationKeys(context)) {
    const configDef = configDefinitions[key];
    const hasDefault = configDef.defaultValue !== undefined; // 默认值可以为 null

    // 仅允许在浏览器扩展上下文中使用某些值
    if (!configDef.allowInBrowserExt) {
      // 如果此处不允许该值，则设置为提供的默认值，否则忽略键值对
      if (hasDefault) {
        config[key] = configDef.defaultValue;
      }
      continue;
    }

    // 从配置源获取值
    const value = configDef.getValue(settings, key);
    if (value === undefined) {
      // 如果没有值（例如 undefined），则设置为提供的默认值，否则忽略配置键值对
      if (hasDefault) {
        config[key] = configDef.defaultValue;
      }
      continue;
    }

    // 最后，通过可选的强制方法处理值
    config[key] = configDef.coerce ? configDef.coerce(value) : value;
  }

  return config;
}
