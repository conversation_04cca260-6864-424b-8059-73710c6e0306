import { hasOwn } from "../../shared/has-own";

export type SettingsGetters = {
  annotations: string | null;
  query: string | null;
  group: string | null;
  showHighlights: string;
  hostPageSetting: (name: string) => unknown;
};
/**
 * 如果设置不是字符串，则丢弃它。
 */
function checkIfString(value: unknown): string | null {
  return typeof value === "string" ? value : null;
}

export function settingsFrom(window_: Window): SettingsGetters {
  // 优先使用 `window.hypothesisConfig` 函数而不是 JSON 格式
  // Via 使用 `window.hypothesisConfig` 并使其不可配置和不可写。
  // 此外，Via 设置 `ignoreOtherConfiguration` 选项以防止配置合并。
  const configFuncSettings = {};
  const jsonConfigs: Record<string, unknown> = {};

  /**
   * 从给定 URL 的片段中返回 `#annotations:*` ID。
   *
   * 如果 URL 包含 `#annotations:<ANNOTATION_ID>` 片段，则返回
   * 从片段中提取的注释 ID。否则，返回 `null`。
   *
   * @return 提取的 ID，或 null。
   */
  function annotations(): string | null {
    /** 从 URL 返回注释，或 null。 */
    function annotationsFromURL() {
      // 注释 ID 是 url-safe-base64 标识符
      // 参见 https://tools.ietf.org/html/rfc4648#page-7
      const annotFragmentMatch = window_.location.href.match(/#annotations:([A-Za-z0-9_-]+)$/);
      if (annotFragmentMatch) {
        return annotFragmentMatch[1];
      }
      return null;
    }

    return checkIfString(jsonConfigs.annotations) || annotationsFromURL();
  }

  /**
   * 从给定 URL 的片段中返回 `#annotations:group:*` ID。
   *
   * 如果 URL 包含 `#annotations:group:<GROUP_ID>` 片段，则返回
   * 从片段中提取的组 ID。否则返回 `null`。
   *
   * @return 提取的 ID，或 null。
   */
  function group(): string | null {
    function groupFromURL() {
      const groupFragmentMatch = window_.location.href.match(/#annotations:group:([A-Za-z0-9_-]+)$/);
      if (groupFragmentMatch) {
        return groupFragmentMatch[1];
      }
      return null;
    }

    return checkIfString(jsonConfigs.group) || groupFromURL();
  }

  function showHighlights() {
    const value = hostPageSetting("showHighlights");

    switch (value) {
      case "always":
      case "never":
      case "whenSidebarOpen":
        return value;
      case true:
        return "always";
      case false:
        return "never";
      default:
        return "always";
    }
  }
  /**
   * 返回来自主机页面或 URL 的 config.query 设置。
   *
   * 如果主机页面包含一个包含 query 设置的 js-hypothesis-config 脚本，则返回该设置。
   *
   * 否则，如果主机页面的 URL 具有 `#annotations:query:*`（或 `#annotations:q:*`）片段，则返回该片段中的查询值。
   *
   * 否则，返回 null。
   *
   * @return config.query 设置，或 null。
   */
  function query(): string | null {
    /** 返回 URL 中的查询，或 null。 */
    function queryFromURL() {
      const queryFragmentMatch = window_.location.href.match(/#annotations:(query|q):(.+)$/i);
      if (queryFragmentMatch) {
        try {
          return decodeURIComponent(queryFragmentMatch[2]);
        } catch (err) {
          // URI Error 应该返回未过滤的页面。
        }
      }
      return null;
    }

    return checkIfString(jsonConfigs.query) || queryFromURL();
  }

  /**
   * 从各自的来源返回找到的第一个设置值，按顺序。
   *
   *  1. window.hypothesisConfig()
   *  2. <script class="js-hypothesis-config">
   *
   * 如果在任一来源中未找到设置，则返回 undefined。
   *
   * @param name - 设置的唯一名称
   */
  function hostPageSetting(name: string) {
    if (hasOwn(configFuncSettings, name)) {
      return configFuncSettings[name];
    }

    if (hasOwn(jsonConfigs, name)) {
      return jsonConfigs[name];
    }

    return undefined;
  }

  return {
    get annotations() {
      return annotations();
    },
    get group() {
      return group();
    },
    get showHighlights() {
      return showHighlights();
    },
    get query() {
      return query();
    },
    hostPageSetting,
  };
}
