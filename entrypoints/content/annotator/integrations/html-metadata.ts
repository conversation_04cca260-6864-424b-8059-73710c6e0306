/*
 ** 改编自:
 ** https://github.com/openannotation/annotator/blob/v1.2.x/src/plugin/document.coffee
 **
 ** Annotator v1.2.10
 ** https://github.com/openannotation/annotator
 **
 ** 版权所有 2015, Annotator 项目贡献者.
 ** 根据 MIT 和 GPLv3 许可证双重许可.
 ** https://github.com/openannotation/annotator/blob/master/LICENSE
 */
import { normalizeURI } from "../util/url";

type Link = {
  href: string;
  rel?: string;
  type?: string;
};

/**
 * 扩展 `Metadata` 类型，包含非可选字段 `dc`, `eprints` 等。
 */
type HTMLDocumentMetadata = {
  title: string;
  link: Link[];
  dc: Record<string, string[]>;
  eprints: Record<string, string[]>;
  facebook: Record<string, string[]>;
  highwire: Record<string, string[]>;
  prism: Record<string, string[]>;
  twitter: Record<string, string[]>;
  favicon?: string;
  documentFingerprint?: string;
};

/**
 * HTMLMetadata 从当前 HTML 文档中读取元数据/链接。
 */
export class HTMLMetadata {
  document: Document;

  constructor(options: { document?: Document } = {}) {
    this.document = options.document || document;
  }
  /**
   * 返回正在注释的文档的主要 URI
   */
  uri(): string {
    let uri = this._getDocumentHref(); // 获取未解码的 URI

    // 尝试解码 URI，如果 URI 格式错误则处理异常
    try {
      uri = decodeURIComponent(uri);
    } catch (error) {
      // 记录错误以便调试。在此之后我们回退到原始 URI
      console.error("解码 URI 时出错:", error);
    }

    // 如果存在 `link[rel=canonical]` 元素的 href，则使用它作为 URI。
    const links = this._getLinks();
    for (const link of links) {
      if (link.rel === "canonical") {
        uri = link.href; // 假设规范 href 是正确编码的
      }
    }
    return uri;
  }

  /**
   * 返回当前页面的元数据。
   */
  getDocumentMetadata(): HTMLDocumentMetadata {
    const metadata: HTMLDocumentMetadata = {
      title: document.title,
      link: [],

      dc: this._getMetaTags("name", "dc."),
      eprints: this._getMetaTags("name", "eprints."),
      facebook: this._getMetaTags("property", "og:"),
      highwire: this._getMetaTags("name", "citation_"),
      prism: this._getMetaTags("name", "prism."),
      twitter: this._getMetaTags("name", "twitter:"),
    };

    const favicon = this._getFavicon();
    if (favicon) {
      metadata.favicon = favicon;
    }

    metadata.title = this._getTitle(metadata);
    metadata.link = this._getLinks(metadata);

    const dcLink = metadata.link.find((link) => link.href.startsWith("urn:x-dc"));
    if (dcLink) {
      metadata.documentFingerprint = dcLink.href;
    }

    return metadata;
  }

  /**
   * 返回页面上 `<meta>` 标签的所有 `content` 值的数组
   * 其中属性值以 `<prefix>` 开头。
   *
   * @param prefix - 它被解释为正则表达式
   */
  private _getMetaTags(attribute: string, prefix: string): Record<string, string[]> {
    const tags: Record<string, string[]> = {};
    for (const meta of Array.from(this.document.querySelectorAll("meta"))) {
      const name = meta.getAttribute(attribute);
      const { content } = meta;
      if (name && content) {
        const match = name.match(RegExp(`^${prefix}(.+)$`, "i"));
        if (match) {
          const key = match[1].toLowerCase();
          if (tags[key]) {
            tags[key].push(content);
          } else {
            tags[key] = [content];
          }
        }
      }
    }
    return tags;
  }

  private _getTitle(metadata: HTMLDocumentMetadata): string {
    if (metadata.highwire.title) {
      return metadata.highwire.title[0];
    } else if (metadata.eprints.title) {
      return metadata.eprints.title[0];
    } else if (metadata.prism.title) {
      return metadata.prism.title[0];
    } else if (metadata.facebook.title) {
      return metadata.facebook.title[0];
    } else if (metadata.twitter.title) {
      return metadata.twitter.title[0];
    } else if (metadata.dc.title) {
      return metadata.dc.title[0];
    } else {
      return this.document.title;
    }
  }

  /**
   * 从页面上的 `<link>` 和 `<meta>` 元素获取文档 URI。
   *
   * @param [metadata] - 从 `<meta>` 标签解析的 Dublin Core 和 Highwire 元数据。
   */
  private _getLinks(
    metadata: Pick<HTMLDocumentMetadata, "highwire" | "dc"> = {
      dc: {},
      highwire: {},
    },
  ): Link[] {
    const links: Link[] = [{ href: this._getDocumentHref() }];

    // 从具有特定 `rel` 值的 `<link>` 标签中提取链接。
    const linkElements = Array.from(this.document.querySelectorAll("link"));
    for (const link of linkElements) {
      if (!["alternate", "canonical", "bookmark", "shortlink"].includes(link.rel)) {
        continue;
      }

      if (link.rel === "alternate") {
        // 忽略 RSS feed 链接。
        if (link.type && link.type.match(/^application\/(rss|atom)\+xml/)) {
          continue;
        }
        // 忽略备用语言。
        if (link.hreflang) {
          continue;
        }
      }

      try {
        const href = this._absoluteUrl(link.href);
        links.push({ href, rel: link.rel, type: link.type });
      } catch (e) {
        // 忽略无法解析的 URI。
      }
    }

    // 在学术元数据中查找链接
    for (const name of Object.keys(metadata.highwire)) {
      const values = metadata.highwire[name];
      if (name === "pdf_url") {
        for (const url of values) {
          try {
            links.push({
              href: this._absoluteUrl(url),
              type: "application/pdf",
            });
          } catch (e) {
            // 忽略无法解析的 URI。
          }
        }
      }

      // 一种将 DOI 标识符表示为链接的简便方法，但这是一个方便的地方，以便稍后查找它们，并且由于它们没有类型，因此有点合理。
      if (name === "doi") {
        for (let doi of values) {
          if (doi.slice(0, 4) !== "doi:") {
            doi = `doi:${doi}`;
          }
          links.push({ href: doi });
        }
      }
    }

    // 在 Dublin Core 数据中查找链接
    for (const name of Object.keys(metadata.dc)) {
      const values = metadata.dc[name];
      if (name === "identifier") {
        for (const id of values) {
          if (id.slice(0, 4) === "doi:") {
            links.push({ href: id });
          }
        }
      }
    }

    // 在 Dublin Core 元数据中查找标识资源的链接
    const dcRelationValues = metadata.dc["relation.ispartof"];
    const dcIdentifierValues = metadata.dc.identifier;
    if (dcRelationValues && dcIdentifierValues) {
      const dcUrnRelationComponent = dcRelationValues[dcRelationValues.length - 1];
      const dcUrnIdentifierComponent = dcIdentifierValues[dcIdentifierValues.length - 1];
      const dcUrn =
        "urn:x-dc:" + encodeURIComponent(dcUrnRelationComponent) + "/" + encodeURIComponent(dcUrnIdentifierComponent);
      links.push({ href: dcUrn });
    }

    return links;
  }

  private _getFavicon(): string | null {
    let favicon = null;
    for (const link of Array.from(this.document.querySelectorAll("link"))) {
      if (["shortcut icon", "icon"].includes(link.rel)) {
        try {
          favicon = this._absoluteUrl(link.href);
        } catch (e) {
          // 忽略无法解析的 URI。
        }
      }
    }
    return favicon;
  }

  /**
   * 将可能的相对 URI 转换为绝对 URI。如果无法解析 URL，将抛出异常。
   */
  private _absoluteUrl(url: string): string {
    return normalizeURI(url, this.document.baseURI);
  }

  /**
   * 获取通过不同协议屏蔽的真实 URI 记录。
   * 当 href 使用 'blob:' 协议设置但文档可以通过 <base> 标签设置不同的 URI 时，会发生这种情况。
   */
  private _getDocumentHref(): string {
    const { href } = this.document.location;
    const allowedSchemes = ["http:", "https:", "file:"];

    // 如果当前文档位置具有已识别的方案，则使用它。
    const scheme = new URL(href).protocol;
    if (allowedSchemes.includes(scheme)) {
      return href;
    }

    // 否则，尝试使用 <base> 元素指定的位置。
    if (this.document.baseURI && allowedSchemes.includes(new URL(this.document.baseURI).protocol)) {
      return this.document.baseURI;
    }

    // 最后返回文档 URI，即使方案不在允许列表中。
    return href;
  }
}
