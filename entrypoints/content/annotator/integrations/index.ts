import { TinyEmitter } from "tiny-emitter";

import type { Anchor, Integration } from "../../types/annotator";
import type { Selector } from "../../types/api";
import { anchor, describe } from "../anchoring/html";
import { TextRange } from "../anchoring/text-range";
import { NavigationObserver } from "../util/navigation-observer";
import { scrollElementIntoView } from "../util/scroll";
import { HTMLMetadata } from "./html-metadata";
/**
 * 普通网页的文档类型集成。
 *
 * 此集成用于未由更具体的集成处理的网页和应用程序（例如 PDF）。
 */
export class HTMLIntegration extends TinyEmitter implements Integration {
  container: HTMLElement;

  private _htmlMeta: HTMLMetadata;
  private _prevURI: string;

  private _navObserver: NavigationObserver;
  private _metaObserver: MutationObserver;

  constructor({ container = document.body }: { container?: HTMLElement }) {
    super();

    this.container = container;

    this._htmlMeta = new HTMLMetadata();
    this._prevURI = this._htmlMeta.uri();

    // 监视 `location.href` 的更改。
    this._navObserver = new NavigationObserver(() => this._checkForURIChange());

    // 监视 `<head>` 中位置信息的潜在更改，例如 `<link rel=canonical>`。
    this._metaObserver = new MutationObserver(() => this._checkForURIChange());
    this._metaObserver.observe(document.head, {
      childList: true,
      subtree: true,
      attributes: true,

      attributeFilter: [
        // <link> 元素的键和值
        "rel",
        "href",

        // <meta> 元素的键和值
        "name",
        "content",
      ],
    });
  }

  anchor(root: Element, selectors: Selector[]): Promise<Range> {
    return anchor(root, selectors);
  }

  describe(root: Element, range: Range): Selector[] {
    return describe(root, range);
  }

  _checkForURIChange() {
    const currentURI = this._htmlMeta.uri();
    if (currentURI !== this._prevURI) {
      this._prevURI = currentURI;
      this.emit("uriChanged", currentURI);
    }
  }

  /**
   * 返回修剪以删除任何前导或尾随空格的范围，
   * 如果无法从 `range` 创建有效的修剪范围，则返回 `null`
   */
  getAnnotatableRange(range: Range) {
    try {
      return TextRange.trimmedRange(range);
    } catch (err) {
      if (err instanceof RangeError) {
        return null;
      }
      throw err;
    }
  }

  destroy() {
    this._navObserver.disconnect();
    this._metaObserver.disconnect();
  }

  contentContainer() {
    return this.container;
  }

  async getMetadata() {
    return this._htmlMeta.getDocumentMetadata();
  }

  async uri() {
    return this._htmlMeta.uri();
  }

  async scrollToAnchor(anchor: Anchor) {
    const highlight = anchor.highlights?.[0];
    if (!highlight) {
      return;
    }
    await scrollElementIntoView(highlight);
  }
}
