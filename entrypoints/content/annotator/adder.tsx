import type { Destroyable } from "../types/annotator";
import type { Command } from "./components/AdderToolbar";
import AdderToolbar from "./components/AdderToolbar";
import { ConfigProvider } from "antd";
import { PreactContainer } from "./util/preact-container";
import { StyleProvider } from "@ant-design/cssinjs";
import themeToken from "@/theme.json";
import NoteItem from "@/components/ContextMenus/NoteItem/index";
import { setNote, showNoteItem } from "@/utils/notes";
import { AgentInfo } from "@/types/chat";
import React from "react";
type Target = {
  top: number;
  /** 距离视口左边缘的偏移量 */
  left: number;
  /** 距离视口顶部的偏移量 */
};

function toPx(pixels: number) {
  return pixels.toString() + "px";
}

const ARROW_HEIGHT = 10;

// 文本选择结束与添加器箭头位置之间的首选间隙。
const ARROW_H_MARGIN = 20;

/**
 * 返回 `el` 的最近的已定位祖先。
 * 如果没有祖先被定位，则返回根元素。
 */
function nearestPositionedAncestor(el: Element): Element {
  let parentEl = el.parentElement!;
  if (!parentEl) {
    console.error("依赖的组件被销毁了,查找一下原因为什么被销毁了");
    return el;
  }
  while (parentEl.parentElement) {
    if (getComputedStyle(parentEl).position !== "static") {
      break;
    }
    parentEl = parentEl.parentElement;
  }
  return parentEl;
}

type AdderOptions = {
  /** 点击“注释”按钮时调用的回调 */
  onAnnotate: (type?: string) => void;
  /** 点击“高亮”按钮时调用的回调 */
  onHighlight: (type?: string) => void;
  /** 点击“显示”按钮时调用的回调 */
  onShowAnnotations: (tags: string[]) => void;
  // 点击提问
  onQuiz: (type?: string) => void;
};

/**
 * 'adder' 工具栏的容器，提供用户注释和高亮选中文本的控件。
 *
 * 工具栏的实现分为这个类，它是工具栏的容器，在页面上定位它并使用 shadow DOM 将其与页面的样式隔离，以及实际渲染工具栏的 `AdderToolbar` Preact 组件。
 */
export class Adder implements Destroyable {
  private _container: PreactContainer;
  private _view: Window;
  private _isVisible: boolean;
  private _isExceedHalfViewport: boolean;
  private _onAnnotate: (type?: string, agent?: AgentInfo) => void;
  private _onHighlight: (type?: string) => void;
  private _onShowAnnotations: (tags: string[]) => void;
  /** 与当前选择关联的注释标签。 */
  private _annotationsForSelection: string[];
  private _notelist: CopilotNote[];
  private _tags: string[];
  private _style: React.CSSProperties;
  private _width: number;
  private _height: number;

  /**
   * 创建工具栏的容器并隐藏它。
   *
   * 添加器最初是隐藏的。
   *
   * @param element - 将创建添加器的 DOM 元素
   * @param options - 指定 `onAnnotate` 和 `onHighlight` 事件处理程序的选项对象。
   */
  constructor(element: HTMLElement, options: AdderOptions) {
    this._view = element.ownerDocument.defaultView!;
    this._isVisible = false;
    this._annotationsForSelection = [];
    this._notelist = [];
    this._tags = [];
    this._style = {};
    this._width = 244;
    this._height = 36;
    this._isExceedHalfViewport = false;

    this._onAnnotate = options.onAnnotate;
    this._onHighlight = options.onHighlight;
    this._onShowAnnotations = options.onShowAnnotations;

    this._container = new PreactContainer("note", () => this._render(), { zIndex: 2147483647 });
    element.parentNode.appendChild(this._container.element);

    // 初始时将位置从布局流中移除
    Object.assign(this._style, {
      position: "absolute",
      top: 0,
      left: 0,
    });
    this._container.render();
  }

  get annotationsForSelection() {
    return this._annotationsForSelection;
  }

  /**
   * 设置与当前选择关联的注释 ID。
   *
   * 将此设置为非空列表会导致工具栏中出现“显示”按钮。点击“显示”按钮会触发传递给构造函数的 `onShowAnnotations` 回调。
   */
  set annotationsForSelection(ids) {
    this._annotationsForSelection = ids;
    this._container.render();
  }

  async initNotelist(noteList: CopilotNote[]) {
    this._notelist = noteList;
    setNote(this._notelist);
    this._container.render();
  }

  // 添加note
  // 定义一个异步方法 setNotelist，用于更新笔记列表
  async setNotelist(note: CopilotNote) {
    const index = this._notelist.findIndex((item) => item.id === note.id);
    note.insertType = "add";
    if (index === -1) {
      this._notelist.push(note);
    } else {
      this._notelist[index] = note;
    }
    setNote(this._notelist);
    this._container.render();
  }

  // 展示隐藏全部的note
  async setNoteShowClose(type: boolean) {
    let setList = [];
    if (type) {
      setList = this._notelist.filter((x) => x.displayFlag === 0);
    }
    this._notelist.map((x) => {
      x.displayFlag = type ? 1 : 0;
    });
    if (!type) {
      setList = this._notelist;
    }
    setNote(this._notelist);
    this._container.render();
    setTimeout(() => {
      showNoteItem(setList, type);
    }, 50);
  }

  // 展示隐藏单个的note
  async setNotelistByTag(tag: string, flag: boolean = true) {
    let target = this._notelist.find((item) => item.id === tag);
    if (target) {
      target.displayFlag = flag ? 1 : 0;
    }
    setNote(this._notelist);
    this._container.render();
    setTimeout(() => {
      showNoteItem(
        this._notelist.filter((x) => x.id === tag),
        flag,
      );
    }, 50);
  }

  // 删除单个note
  async delNotelistByTag(tag: string) {
    const index = this._notelist.findIndex((item) => item.id === tag);
    if (index !== -1) {
      this._notelist.splice(index, 1);
    }
    setNote(this._notelist);
    this._container.render();
  }

  // 删除所有note
  delAllNotelist() {
    this._notelist = [];
    setNote(this._notelist);
    this._container.render();
  }

  // 便签hover效果
  setNoteHover(tags: string[]) {
    this._tags = tags;
    this._container.render();
  }
  /** 隐藏添加器 */
  hide() {
    this._isVisible = false;
    this._container.render();

    // 重新定位容器，因为它会影响主页面的响应性
    Object.assign(this._style, {
      top: 0,
      left: 0,
    });
  }

  destroy() {
    this._container.destroy();
  }

  /**
   * 在最佳位置显示添加器，以便定位 `selectionRect` 中选定的文本。
   *
   * @param selectionRect - 要定位的文本的矩形，以视口坐标表示。
   * @param isRTLselection - 如果选择是从右到左进行的，则为 true，
   *        这样焦点点最有可能位于 `targetRect` 的左上角。
   */
  show(selectionRect: DOMRect, isRTLselection: boolean) {
    const { left, top } = this._calculateTarget(selectionRect, isRTLselection);
    this._showAt(left, top);

    this._isVisible = true;
    this._container.render();
  }

  /**
   * 确定添加器及其指针箭头的最佳位置。
   * - 将指针箭头定位在选择的末端附近（用户的光标/输入最有可能在那里）
   * - 将添加器水平居中于指针箭头
   * - 对于 LTR 选择，将添加器定位在选择下方（箭头向上），对于 RTL 选择，将其定位在选择上方（箭头向下）
   *
   * @param selectionRect - 要定位的文本的矩形，以视口坐标表示。
   * @param isRTLselection - 如果选择是从右到左进行的，则为 true，
   *        这样焦点点最有可能位于 `targetRect` 的左上角。
   */
  private _calculateTarget(selectionRect: DOMRect, isRTLselection: boolean): Target {
    let top;
    let left;

    // 将添加器定位在选择的上方或下方，并靠近末端。
    const hMargin = Math.min(ARROW_H_MARGIN, selectionRect.width);
    const adderWidth = this._width;
    const adderHeight = this._height;
    if (isRTLselection) {
      left = selectionRect.left - adderWidth / 2 + hMargin;
    } else {
      left = selectionRect.left + selectionRect.width - adderWidth / 2 - hMargin;
    }

    top = selectionRect.top - adderHeight - ARROW_HEIGHT;

    // 将添加器限制在视口内。
    left = Math.max(left, 0);
    left = Math.min(left, this._view.innerWidth - adderWidth);

    top = Math.max(top, 0);
    top = Math.min(top, this._view.innerHeight - adderHeight);

    return { top, left };
  }

  /**
   * 在给定位置显示添加器，并使箭头指向该位置
   *
   * @param left - 距离视口左边缘的水平偏移量。
   * @param top - 距离视口顶部的垂直偏移量。
   */
  private _showAt(left: number, top: number) {
    // 将 (left, top) 视口坐标转换为相对于添加器最近的已定位祖先（NPA）的位置。
    // 通常，添加器是 `<body>` 的子元素，NPA 是根 `<html>` 元素。然而，页面样式可能会使 `<body>` 被定位。
    const positionedAncestor = nearestPositionedAncestor(this._container.element);
    const parentRect = positionedAncestor.getBoundingClientRect();

    let maxLeft = window.innerWidth - 313;
    let rLeft = Math.min(left - parentRect.left, maxLeft);
    let rTop = top - parentRect.top;

    Object.assign(this._style, {
      left: toPx(rLeft),
      top: toPx(rTop),
      zIndex: 2147483647,
    });

    // 获取页面视口的高度
    const viewportHeight = window.innerHeight;
    // 判断元素的 Y 坐标是否超过了屏幕视口的一半
    this._isExceedHalfViewport = rLeft >= viewportHeight / 2;
  }

  calculationStyle = (item: CopilotNote) => {
    let style: React.CSSProperties = {};
    style.position = item.noteStyle.noteType as "static" | "relative" | "absolute" | "sticky" | "fixed";
    let tag = {
      target: [],
      coordinates: {
        left: 0,
        top: 0,
      },
    };
    try {
      tag = JSON.parse((item as any).tag) || {};
    } catch (error) {
      console.error("获取或解析 tag 时发生错误:", error);
    }

    if (item.noteStyle.noteType === NOTE_MODE) {
      style.left = `${tag.coordinates?.left || 0}px`;
      style.top = `${tag.coordinates?.top || 0}px`;
    } else {
      style.top = "0px";
      style.left = "0px";
    }
    style.transform = `translate(${item.noteStyle.noteLeft}px, ${item.noteStyle.noteTop}px)`;
    return style;
  };
  public executeCommand(command: Command, agent?: AgentInfo) {
    switch (command) {
      case "quiz":
        this._onAnnotate("QUIZ", agent);
        this.hide();
        break;
      case "annotate":
        this._onAnnotate("AI", agent);
        this.hide();
        break;
      case "highlight":
        this._onHighlight("QUOTE");
        this.hide();
        break;
      case "show":
        this._onShowAnnotations(this.annotationsForSelection);
        break;
      case "hide":
        this.hide();
        break;
    }
  }

  private _render() {
    return (
      <ConfigProvider theme={themeToken}>
        <StyleProvider hashPriority="high" container={this._container._shadowRoot}>
          <div style={{ position: "absolute", ...this._style }}>
            {this._isVisible && (
              <AdderToolbar
                isExceedHalfViewport={this._isExceedHalfViewport}
                onCommand={(command, agent) => this.executeCommand(command, agent)}
              />
            )}
          </div>
          {this._notelist.map((item) => (
            <NoteItem
              key={item.id}
              note={item}
              isHover={this._tags.includes(item.id)}
              style={this.calculationStyle(item)}
            ></NoteItem>
          ))}
        </StyleProvider>
      </ConfigProvider>
    );
  }
}
