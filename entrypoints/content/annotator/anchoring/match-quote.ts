import approxSearch from "approx-string-match";
import type { Match as StringMatch } from "approx-string-match";

type Match = {
  /** 匹配文本的起始偏移量 */
  start: number;
  /** 匹配文本的结束偏移量 */
  end: number;

  /**
   * 匹配的评分，范围在 0 到 1.0 之间，其中 1.0 表示引用和上下文的完美匹配。
   */
  score: number;
};

/**
 * 在 `text` 中查找 `str` 的最佳近似匹配，允许最多 `maxErrors` 个错误。
 */
function search(text: string, str: string, maxErrors: number): StringMatch[] {
  // 进行快速搜索以查找精确匹配。`approx-string-match` 库目前没有实现这个优化。
  let matchPos = 0;
  const exactMatches: StringMatch[] = [];
  while (matchPos !== -1) {
    matchPos = text.indexOf(str, matchPos);
    if (matchPos !== -1) {
      exactMatches.push({
        start: matchPos,
        end: matchPos + str.length,
        errors: 0,
      });
      matchPos += 1;
    }
  }
  if (exactMatches.length > 0) {
    return exactMatches;
  }

  // 如果没有精确匹配，则进行更昂贵的搜索以查找带有错误的匹配。
  return approxSearch(text, str, maxErrors);
}

/**
 * 计算 `text` 和 `str` 之间相似度的评分，范围在 0 到 1.0 之间。
 */
function textMatchScore(text: string, str: string) {
  // 如果文本或模式为空，`search` 将不会返回任何匹配项，否则如果最大允许错误数至少为 `str.length`，则将返回至少一个匹配项。
  if (str.length === 0 || text.length === 0) {
    return 0.0;
  }

  const matches = search(text, str, str.length);

  // prettier-ignore
  return 1 - (matches[0].errors / str.length);
}

type Context = {
  /** 引用前的预期文本 */
  prefix?: string;
  /** 引用后的预期文本 */
  suffix?: string;
  /** 匹配在文本中的预期偏移量 */
  hint?: number;
};

/**
 * 在 `text` 中查找 `quote` 的最佳近似匹配。
 *
 * @param text - 要搜索的文档文本
 * @param quote - 要在 `text` 中查找的字符串
 * @param context - 引用最初出现的上下文。这用于选择最佳匹配。
 * @return 如果没有找到超过最低质量阈值的匹配，则返回 `null`。
 */
export function matchQuote(text: string, quote: string, context: Context = {}): Match | null {
  if (quote.length === 0) {
    return null;
  }

  // 选择允许的最大错误数进行初始搜索。
  // 这个选择涉及以下权衡：
  //
  //  - 召回率（找到的“好”匹配的比例）
  //  - 精度（找到的匹配中“好”匹配的比例）
  //  - 初始搜索和处理候选匹配的成本 [1]
  //
  // [1] 具体来说，初始搜索的预期时间复杂度为 `O((maxErrors / 32) * text.length)`。参见 `approx-string-match` 文档。
  const maxErrors = Math.min(256, quote.length / 2);

  // 基于编辑距离查找 `quote` 在 `text` 中的最接近匹配项。
  const matches = search(text, quote, maxErrors);

  if (matches.length === 0) {
    return null;
  }

  /**
   * 计算匹配候选项的评分，范围在 0 到 1.0 之间。
   */
  const scoreMatch = (match: StringMatch) => {
    const quoteWeight = 50; // 匹配文本与引用的相似度。
    const prefixWeight = 20; // 匹配文本之前的文本与 `context.prefix` 的相似度。
    const suffixWeight = 20; // 匹配文本之后的文本与 `context.suffix` 的相似度。
    const posWeight = 2; // 与预期位置的接近程度。用作平局决胜因素。

    const quoteScore = 1 - match.errors / quote.length;

    const prefixScore = context.prefix
      ? textMatchScore(text.slice(Math.max(0, match.start - context.prefix.length), match.start), context.prefix)
      : 1.0;
    const suffixScore = context.suffix
      ? textMatchScore(text.slice(match.end, match.end + context.suffix.length), context.suffix)
      : 1.0;

    let posScore = 1.0;
    if (typeof context.hint === "number") {
      const offset = Math.abs(match.start - context.hint);
      posScore = 1.0 - offset / text.length;
    }

    const rawScore =
      quoteWeight * quoteScore + prefixWeight * prefixScore + suffixWeight * suffixScore + posWeight * posScore;
    const maxScore = quoteWeight + prefixWeight + suffixWeight + posWeight;
    const normalizedScore = rawScore / maxScore;

    return normalizedScore;
  };

  // 根据实际和预期的周围文本相似度以及在文档文本中的实际/预期偏移量对匹配项进行排序。
  const scoredMatches = matches.map((m) => ({
    start: m.start,
    end: m.end,
    score: scoreMatch(m),
  }));

  // 选择评分最高的匹配项。
  scoredMatches.sort((a, b) => b.score - a.score);
  return scoredMatches[0];
}
