/**
 * 将匹配页面/图块容器中的占位符的 CSS 选择器。
 */
const placeholderSelector = ".annotator-placeholder";

/**
 * 如果页面/图块容器有占位符，则返回 true。
 */
export function hasPlaceholder(container: HTMLElement): boolean {
  return container.querySelector(placeholderSelector) !== null;
}

/**
 * 移除 `container` 中的占位符元素（如果存在）。
 */
export function removePlaceholder(container: HTMLElement) {
  container.querySelector(placeholderSelector)?.remove();
}

/**
 * 这通常用于测试与锚点关联的高亮显示元素是否在占位符内。
 */
export function isInPlaceholder(node: Node): boolean {
  if (!node.parentElement) {
    return false;
  }
  return node.parentElement.closest(placeholderSelector) !== null;
}
