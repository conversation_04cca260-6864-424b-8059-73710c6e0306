import { trimRange } from "./trim-range";

/**
 * 返回 `node` 中包含的文本节点的总长度。
 */
function nodeTextLength(node: Node): number {
  switch (node.nodeType) {
    case Node.ELEMENT_NODE:
    case Node.TEXT_NODE:
      // 注意：当调用父元素时，`textContent` 排除了注释和处理指令中的文本，
      // 因此我们不需要在这里减去它。
      return node.textContent?.length ?? 0;
    default:
      return 0;
  }
}

/**
 * 返回 `node` 的所有前一个兄弟节点的文本总长度。
 */
function previousSiblingsTextLength(node: Node): number {
  let sibling = node.previousSibling;
  let length = 0;
  while (sibling) {
    length += nodeTextLength(sibling);
    sibling = sibling.previousSibling;
  }
  return length;
}

/**
 * 将元素中的一个或多个字符偏移量解析为（文本节点，位置）对。
 *
 * @param element - 元素
 * @param offsets - 偏移量，必须按升序排序
 * @throws {RangeError}
 */
function resolveOffsets(element: Element, ...offsets: number[]): Array<{ node: Text; offset: number }> {
  let nextOffset = offsets.shift();
  const nodeIter = element.ownerDocument.createNodeIterator(element, NodeFilter.SHOW_TEXT);
  const results = [];

  let currentNode = nodeIter.nextNode() as Text | null;
  let textNode;
  let length = 0;

  // 查找包含从 `element` 开始的 `nextOffset` 个字符的文本节点。
  while (nextOffset !== undefined && currentNode) {
    textNode = currentNode;
    if (length + textNode.data.length > nextOffset) {
      results.push({ node: textNode, offset: nextOffset - length });
      nextOffset = offsets.shift();
    } else {
      currentNode = nodeIter.nextNode() as Text | null;
      length += textNode.data.length;
    }
  }

  // 边界情况。
  while (nextOffset !== undefined && textNode && length === nextOffset) {
    results.push({ node: textNode, offset: textNode.data.length });
    nextOffset = offsets.shift();
  }

  if (nextOffset !== undefined) {
    throw new RangeError("偏移量超过文本长度");
  }

  return results;
}

/**
 * 在解析 TextPosition 时，指定如果 `offset` 为 `0` 且元素没有文本，则搜索最近文本节点的方向。
 */
export enum ResolveDirection {
  FORWARDS = 1,
  BACKWARDS,
}

/**
 * 表示元素文本内容中的偏移量。
 *
 * 该位置可以使用 `resolve` 方法解析为当前 DOM 子树中的特定子节点。
 */
export class TextPosition {
  public element: Element;
  public offset: number;

  constructor(element: Element, offset: number) {
    if (offset < 0) {
      throw new Error("偏移量无效");
    }

    /** 偏移量相对于的元素。 */
    this.element = element;

    /** 从元素 `textContent` 开始的字符偏移量。 */
    this.offset = offset;
  }

  /**
   * 返回相对于给定祖先元素的偏移量的此位置的副本。
   *
   * @param parent - `this.element` 的祖先
   */
  relativeTo(parent: Element): TextPosition {
    if (!parent.contains(this.element)) {
      throw new Error("父元素不是当前元素的祖先");
    }

    let el = this.element;
    let offset = this.offset;
    while (el !== parent) {
      offset += previousSiblingsTextLength(el);
      el = el.parentElement!;
    }

    return new TextPosition(el, offset);
  }

  /**
   * 将位置解析为特定的文本节点和该节点内的偏移量。
   *
   * 如果 `this.offset` 超过元素文本的长度，则抛出异常。
   * 如果元素没有文本且 `this.offset` 为 0，则 `direction` 选项决定会发生什么。
   *
   * 在两个节点之间的边界处的偏移量解析为在边界开始的节点的开始。
   *
   * @param options.direction - 指定如果 `this.offset` 为 `0` 且 `this.element` 没有文本时搜索最近文本节点的方向。
   *                            如果未指定，则抛出错误。
   *
   * @throws {RangeError}
   */
  resolve(options: { direction?: ResolveDirection } = {}): {
    node: Text;
    offset: number;
  } {
    try {
      return resolveOffsets(this.element, this.offset)[0];
    } catch (err) {
      if (this.offset === 0 && options.direction !== undefined) {
        const tw = document.createTreeWalker(this.element.getRootNode(), NodeFilter.SHOW_TEXT);
        tw.currentNode = this.element;
        const forwards = options.direction === ResolveDirection.FORWARDS;
        const text = forwards ? (tw.nextNode() as Text | null) : (tw.previousNode() as Text | null);
        if (!text) {
          throw err;
        }
        return { node: text, offset: forwards ? 0 : text.data.length };
      } else {
        throw err;
      }
    }
  }

  /**
   * 构造一个 `TextPosition`，表示 `node` 中的 `offset` 个字符。
   */
  static fromCharOffset(node: Node, offset: number): TextPosition {
    switch (node.nodeType) {
      case Node.TEXT_NODE:
        return TextPosition.fromPoint(node, offset);
      case Node.ELEMENT_NODE:
        return new TextPosition(node as Element, offset);
      default:
        throw new Error("节点不是元素或文本节点");
    }
  }

  /**
   * 构造一个表示范围起点或终点（节点，偏移量）的 `TextPosition`。
   *
   * @param node - 节点
   * @param offset - 节点内的偏移量
   */
  static fromPoint(node: Node, offset: number): TextPosition {
    switch (node.nodeType) {
      case Node.TEXT_NODE: {
        if (offset < 0 || offset > (node as Text).data.length) {
          throw new Error("文本节点偏移量超出范围");
        }

        if (!node.parentElement) {
          throw new Error("文本节点没有父元素");
        }

        // 获取从父元素开始的偏移量。
        const textOffset = previousSiblingsTextLength(node) + offset;

        return new TextPosition(node.parentElement, textOffset);
      }
      case Node.ELEMENT_NODE: {
        if (offset < 0 || offset > node.childNodes.length) {
          throw new Error("子节点偏移量超出范围");
        }

        // 获取元素的 `offset` 个子节点之前的文本长度。
        let textOffset = 0;
        for (let i = 0; i < offset; i++) {
          textOffset += nodeTextLength(node.childNodes[i]);
        }

        return new TextPosition(node as Element, textOffset);
      }
      default:
        throw new Error("点不在元素或文本节点中");
    }
  }
}

/**
 * 表示文档的一个区域，作为一对（开始，结束）的 `TextPosition` 点。
 *
 * 以这种方式表示范围允许在范围的 DOM 内容发生变化时，不影响范围本身的文本内容。
 */
export class TextRange {
  public start: TextPosition;
  public end: TextPosition;

  constructor(start: TextPosition, end: TextPosition) {
    this.start = start;
    this.end = end;
  }

  /**
   * 创建一个新的 TextRange，其 `start` 和 `end` 相对于 `element` 计算。
   * `element` 必须是 `start.element` 和 `end.element` 的祖先。
   */
  relativeTo(element: Element): TextRange {
    return new TextRange(this.start.relativeTo(element), this.end.relativeTo(element));
  }

  /**
   * 将此 TextRange 解析为一个（DOM）Range。
   *
   * 生成的 DOM Range 将始终在 `Text` 节点中开始和结束。
   * 因此可以使用 `TextRange.fromRange(range).toRange()` 来“收缩”范围到它包含的文本。
   *
   * 如果 `start` 或 `end` 位置无法解析为范围，则可能会抛出异常。
   */
  toRange(): Range {
    let start;
    let end;

    if (this.start.element === this.end.element && this.start.offset <= this.end.offset) {
      // 快速路径，起点和终点在同一元素中。
      [start, end] = resolveOffsets(this.start.element, this.start.offset, this.end.offset);
    } else {
      start = this.start.resolve({
        direction: ResolveDirection.FORWARDS,
      });
      end = this.end.resolve({ direction: ResolveDirection.BACKWARDS });
    }

    const range = new Range();
    range.setStart(start.node, start.offset);
    range.setEnd(end.node, end.offset);
    return range;
  }

  /**
   * 从一个（DOM）Range 创建一个 TextRange
   */
  static fromRange(range: Range): TextRange {
    const start = TextPosition.fromPoint(range.startContainer, range.startOffset);
    const end = TextPosition.fromPoint(range.endContainer, range.endOffset);
    return new TextRange(start, end);
  }

  /**
   * 创建一个表示 `root` 中从 `start` 到 `end` 个字符的 TextRange
   */
  static fromOffsets(root: Element, start: number, end: number): TextRange {
    return new TextRange(new TextPosition(root, start), new TextPosition(root, end));
  }

  /**
   * 返回一个表示 `range` 去除任何前导或尾随空白的新范围
   */
  static trimmedRange(range: Range): Range {
    return trimRange(TextRange.fromRange(range).toRange());
  }
}
