/**
 * 该模块导出一组类，用于在 DOM `Range` 对象和不同类型的选择器之间进行转换。
 * 它主要是围绕一组锚定库的一个薄包装。它有两个主要目的：
 *
 *  1. 提供不同类型锚点的一致接口。
 *  2. 使其余代码与底层锚定库的 API 更改隔离开来。
 */
import type { MediaTimeSelector, RangeSelector, TextPositionSelector, TextQuoteSelector } from "../../types/api";
import { matchQuote } from "./match-quote";
import { TextRange, TextPosition } from "./text-range";
import { nodeFromXPath, xpathFromNode } from "./xpath";

/**
 * 在 `RangeSelector` 选择器和 `Range` 对象之间进行转换。
 */
export class RangeAnchor {
  root: Node;
  range: Range;

  /**
   * @param root - 锚定的根元素。
   * @param range - 描述锚点的范围。
   */
  constructor(root: Node, range: Range) {
    this.root = root;
    this.range = range;
  }

  /**
   * @param root - 锚定的根元素。
   * @param range - 描述锚点的范围。
   */
  static fromRange(root: Node, range: Range): RangeAnchor {
    return new RangeAnchor(root, range);
  }

  /**
   * 从序列化的 `RangeSelector` 选择器创建锚点。
   *
   * @param root - 锚定的根元素。
   */
  static fromSelector(root: Element, selector: RangeSelector): RangeAnchor {
    const startContainer = nodeFromXPath(selector.startContainer, root);
    if (!startContainer) {
      throw new Error("无法解析 startContainer XPath");
    }

    const endContainer = nodeFromXPath(selector.endContainer, root);
    if (!endContainer) {
      throw new Error("无法解析 endContainer XPath");
    }

    const startPos = TextPosition.fromCharOffset(startContainer, selector.startOffset);
    const endPos = TextPosition.fromCharOffset(endContainer, selector.endOffset);

    const range = new TextRange(startPos, endPos).toRange();
    return new RangeAnchor(root, range);
  }

  toRange(): Range {
    return this.range;
  }

  toSelector(): RangeSelector {
    // "收缩"范围，使其紧密包裹其文本。这确保了给定文本选择的输出更可预测。
    const normalizedRange = TextRange.fromRange(this.range).toRange();

    const textRange = TextRange.fromRange(normalizedRange);
    const startContainer = xpathFromNode(textRange.start.element, this.root);
    const endContainer = xpathFromNode(textRange.end.element, this.root);

    return {
      type: "RangeSelector",
      startContainer,
      startOffset: textRange.start.offset,
      endContainer,
      endOffset: textRange.end.offset,
    };
  }
}

/**
 * 在 `TextPositionSelector` 选择器和 `Range` 对象之间进行转换。
 */
export class TextPositionAnchor {
  root: Element;
  start: number;
  end: number;

  constructor(root: Element, start: number, end: number) {
    this.root = root;
    this.start = start;
    this.end = end;
  }

  static fromRange(root: Element, range: Range): TextPositionAnchor {
    const textRange = TextRange.fromRange(range).relativeTo(root);
    return new TextPositionAnchor(root, textRange.start.offset, textRange.end.offset);
  }

  static fromSelector(root: Element, selector: TextPositionSelector): TextPositionAnchor {
    return new TextPositionAnchor(root, selector.start, selector.end);
  }

  toSelector(): TextPositionSelector {
    return {
      type: "TextPositionSelector",
      start: this.start,
      end: this.end,
    };
  }

  toRange(): Range {
    return TextRange.fromOffsets(this.root, this.start, this.end).toRange();
  }
}

type QuoteMatchOptions = {
  /** 匹配在文本中的预期偏移量。参见 `matchQuote`。 */
  hint?: number;
};

export type TextQuoteAnchorContext = {
  prefix?: string;
  suffix?: string;
};

/**
 * 在 `TextQuoteSelector` 选择器和 `Range` 对象之间进行转换。
 */
export class TextQuoteAnchor {
  root: Element;
  exact: string;
  context: TextQuoteAnchorContext;

  /**
   * @param root - 锚定的根元素。
   */
  constructor(root: Element, exact: string, context: TextQuoteAnchorContext = {}) {
    this.root = root;
    this.exact = exact;
    this.context = context;
  }

  /**
   * 从范围创建 `TextQuoteAnchor`。
   *
   * 如果 `range` 不包含任何文本节点，将抛出错误。
   */
  static fromRange(root: Element, range: Range): TextQuoteAnchor {
    const text = root.textContent!;
    const textRange = TextRange.fromRange(range).relativeTo(root);

    const start = textRange.start.offset;
    const end = textRange.end.offset;

    // 捕获引用周围的上下文字符数。我们目前总是使用固定数量，但如果此代码能够识别文档中的逻辑边界（段落、文章等），以避免捕获与引用无关的文本，那就更好了。
    //
    // 在常规散文中，理想的内容通常是周围的句子。这是一个自然的意义单位，即使在文档不可用时也能在上下文中显示引用。我们可以在可用时使用 `Intl.Segmenter`。
    const contextLen = 32;

    return new TextQuoteAnchor(root, text.slice(start, end), {
      prefix: text.slice(Math.max(0, start - contextLen), start),
      suffix: text.slice(end, Math.min(text.length, end + contextLen)),
    });
  }

  static fromSelector(root: Element, selector: TextQuoteSelector): TextQuoteAnchor {
    const { prefix, suffix } = selector;
    return new TextQuoteAnchor(root, selector.exact, { prefix, suffix });
  }

  toSelector(): TextQuoteSelector {
    return {
      type: "TextQuoteSelector",
      exact: this.exact,
      prefix: this.context.prefix,
      suffix: this.context.suffix,
    };
  }

  toRange(options: QuoteMatchOptions = {}): Range {
    return this.toPositionAnchor(options).toRange();
  }

  toPositionAnchor(options: QuoteMatchOptions = {}): TextPositionAnchor {
    const text = this.root.textContent!;
    const match = matchQuote(text, this.exact, {
      ...this.context,
      hint: options.hint,
    });
    if (!match) {
      throw new Error("引用未找到");
    }
    return new TextPositionAnchor(this.root, match.start, match.end);
  }
}

/**
 * 解析包含自媒体开始以来的时间偏移量（以秒为单位）的字符串为浮点数。
 */
function parseMediaTime(timeStr: string): number | null {
  const val = parseFloat(timeStr);
  if (!Number.isFinite(val) || val < 0) {
    return null;
  }
  return val;
}

/** {@link Array.prototype.findLastIndex} 的实现 */
function findLastIndex<T>(ary: T[], pred: (val: T) => boolean): number {
  for (let i = ary.length - 1; i >= 0; i--) {
    if (pred(ary[i])) {
      return i;
    }
  }
  return -1;
}

function closestElement(node: Node) {
  return node instanceof Element ? node : node.parentElement;
}

/**
 * 从元素或元素对中获取与 `data-time-{start, end}` 属性相关联的媒体时间范围。
 */
function getMediaTimeRange(
  start: Element | undefined | null,
  end: Element | undefined | null = start,
): [number, number] | null {
  const startTime = parseMediaTime(start?.getAttribute("data-time-start") ?? "");
  const endTime = parseMediaTime(end?.getAttribute("data-time-end") ?? "");
  if (typeof startTime !== "number" || typeof endTime !== "number" || endTime < startTime) {
    return null;
  }
  return [startTime, endTime];
}

export class MediaTimeAnchor {
  root: Element;

  /** 自媒体开始以来的偏移量（以秒为单位）。 */
  start: number;
  /** 自媒体结束以来的偏移量（以秒为单位）。 */
  end: number;

  constructor(root: Element, start: number, end: number) {
    this.root = root;
    this.start = start;
    this.end = end;
  }

  /**
   * 返回表示范围的 {@link MediaTimeAnchor}，如果范围内的元素没有时间范围信息，则返回 `null`。
   */
  static fromRange(root: Element, range: Range): MediaTimeAnchor | null {
    const start = closestElement(range.startContainer)?.closest("[data-time-start]");
    const end = closestElement(range.endContainer)?.closest("[data-time-end]");
    const timeRange = getMediaTimeRange(start, end);
    if (!timeRange) {
      return null;
    }
    const [startTime, endTime] = timeRange;
    return new MediaTimeAnchor(root, startTime, endTime);
  }

  /**
   * 将此锚点转换为 DOM 范围。
   *
   * 返回的范围将从包含 `start` 的元素的开头开始，并继续到包含 `end` 的元素的末尾。
   */
  toRange(): Range {
    // 查找跨越此锚点的开始和结束时间的段。这是低效的，因为我们为每个锚定的注释重新查找所有段。更改此内容将涉及修订锚定 API。
    type Segment = { element: Element; start: number; end: number };
    const segments = [...this.root.querySelectorAll("[data-time-start]")]
      .map((element) => {
        const timeRange = getMediaTimeRange(element);
        if (!timeRange) {
          return null;
        }
        const [start, end] = timeRange;
        return { element, start, end };
      })
      .filter((s) => s !== null) as Segment[];
    segments.sort((a, b) => a.start - b.start);

    const startIdx = findLastIndex(segments, (s) => s.start <= this.start && s.end >= this.start);
    if (startIdx === -1) {
      throw new Error("Start segment not found");
    }
    const endIdx = startIdx + segments.slice(startIdx).findIndex((s) => s.start <= this.end && s.end >= this.end);
    if (endIdx === -1) {
      throw new Error("End segment not found");
    }

    const range = new Range();
    range.setStart(segments[startIdx].element, 0);

    const endEl = segments[endIdx].element;
    range.setEnd(endEl, endEl.childNodes.length);

    return range;
  }

  static fromSelector(root: Element, selector: MediaTimeSelector): MediaTimeAnchor {
    const { start, end } = selector;
    return new MediaTimeAnchor(root, start, end);
  }

  toSelector(): MediaTimeSelector {
    return {
      type: "MediaTimeSelector",
      start: this.start,
      end: this.end,
    };
  }
}
