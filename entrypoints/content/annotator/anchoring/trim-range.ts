/**
 * 从哪个方向评估字符串或节点：从字符串的开头或范围开始向前查找，或从末尾向后查找。
 */
enum TrimDirection {
  Forwards = 1,
  Backwards,
}

/**
 * 表示范围位置的对象（例如，用于 Range.setStart 或 Range.setEnd）
 */
type RangePosition = {
  offset: number;
  node: Node;
};

/**
 * 返回字符串 `text` 中最接近 `baseOffset` 的非空白字符的偏移量，
 * 按照指定的 `direction` 查找。如果在 `baseOffset`（包括）和字符串的终点（根据 `direction`）之间没有非空白字符，则返回 -1。
 */
function closestNonSpaceInString(text: string, baseOffset: number, direction: TrimDirection): number {
  const nextChar = direction === TrimDirection.Forwards ? baseOffset : baseOffset - 1;
  if (text.charAt(nextChar).trim() !== "") {
    // baseOffset 已经有效：它指向一个非空白字符
    return baseOffset;
  }

  let availableChars: string;
  let availableNonWhitespaceChars: string;

  if (direction === TrimDirection.Backwards) {
    availableChars = text.substring(0, baseOffset);
    availableNonWhitespaceChars = availableChars.trimEnd();
  } else {
    availableChars = text.substring(baseOffset);
    availableNonWhitespaceChars = availableChars.trimStart();
  }

  if (!availableNonWhitespaceChars.length) {
    return -1;
  }

  const offsetDelta = availableChars.length - availableNonWhitespaceChars.length;

  return direction === TrimDirection.Backwards ? baseOffset - offsetDelta : baseOffset + offsetDelta;
}

/**
 * 计算 `range` 的新范围起始位置（TrimDirection.Forwards）或结束位置（Backwards），
 * 该位置表示最接近的非空白字符，从相关的初始边界节点向终止边界节点移动。
 *
 * @throws {RangeError} 如果在范围内未找到包含非空白字符的文本节点
 */
function closestNonSpaceInRange(range: Range, direction: TrimDirection): RangePosition {
  const nodeIter = range.commonAncestorContainer.ownerDocument!.createNodeIterator(
    range.commonAncestorContainer,
    NodeFilter.SHOW_TEXT,
  );

  const initialBoundaryNode = direction === TrimDirection.Forwards ? range.startContainer : range.endContainer;

  const terminalBoundaryNode = direction === TrimDirection.Forwards ? range.endContainer : range.startContainer;

  let currentNode = nodeIter.nextNode();

  // 将 NodeIterator 前进到 `initialBoundaryNode`
  while (currentNode && currentNode !== initialBoundaryNode) {
    currentNode = nodeIter.nextNode();
  }

  if (direction === TrimDirection.Backwards) {
    // 反转 NodeIterator 方向。这将返回与之前 `nextNode()` 调用相同的节点（初始边界节点）。
    currentNode = nodeIter.previousNode();
  }

  let trimmedOffset = -1;

  const advance = () => {
    currentNode = direction === TrimDirection.Forwards ? nodeIter.nextNode() : nodeIter.previousNode();

    if (currentNode) {
      const nodeText = currentNode.textContent!;
      const baseOffset = direction === TrimDirection.Forwards ? 0 : nodeText.length;
      trimmedOffset = closestNonSpaceInString(nodeText, baseOffset, direction);
    }
  };

  while (currentNode && trimmedOffset === -1 && currentNode !== terminalBoundaryNode) {
    advance();
  }

  if (currentNode && trimmedOffset >= 0) {
    return { node: currentNode, offset: trimmedOffset };
  }
  /* istanbul ignore next */
  throw new RangeError("在范围内未找到包含非空白文本的文本节点");
}

/**
 * 返回一个新的 DOM Range，调整 `range` 的起始和结束位置，使其满足以下条件：
 *
 * - `startContainer` 和 `endContainer` 文本节点都包含至少一个非空白字符
 * - `startOffset` 和 `endOffset` 都引用非空白字符，`startOffset` 紧靠第一个非空白字符之前，`endOffset` 紧靠最后一个非空白字符之后
 *
 * 空白字符是那些被 `String.prototype.trim()` 移除的字符
 *
 * @param range - 一个 DOM Range，其 `startContainer` 和 `endContainer` 都是文本节点，并且包含至少一个非空白字符。
 * @throws {RangeError}
 */
export function trimRange(range: Range): Range {
  if (!range.toString().trim().length) {
    throw new RangeError("范围内不包含非空白文本");
  }
  if (range.startContainer.nodeType !== Node.TEXT_NODE) {
    throw new RangeError("范围的 startContainer 不是文本节点");
  }
  if (range.endContainer.nodeType !== Node.TEXT_NODE) {
    throw new RangeError("范围的 endContainer 不是文本节点");
  }

  const trimmedRange = range.cloneRange();

  let startTrimmed = false;
  let endTrimmed = false;

  const trimmedOffsets = {
    start: closestNonSpaceInString(range.startContainer.textContent!, range.startOffset, TrimDirection.Forwards),
    end: closestNonSpaceInString(range.endContainer.textContent!, range.endOffset, TrimDirection.Backwards),
  };

  if (trimmedOffsets.start >= 0) {
    trimmedRange.setStart(range.startContainer, trimmedOffsets.start);
    startTrimmed = true;
  }

  // 注意：偏移量为 0 对于结束偏移量无效，因为节点中没有文本会包含在范围内。
  if (trimmedOffsets.end > 0) {
    trimmedRange.setEnd(range.endContainer, trimmedOffsets.end);
    endTrimmed = true;
  }

  if (startTrimmed && endTrimmed) {
    return trimmedRange;
  }

  if (!startTrimmed) {
    // 在 `startOffset` 和 `startContainer` 节点末尾之间没有（非空白）字符。
    const { node, offset } = closestNonSpaceInRange(trimmedRange, TrimDirection.Forwards);

    if (node && offset >= 0) {
      trimmedRange.setStart(node, offset);
    }
  }

  if (!endTrimmed) {
    // 在范围的 `endContainer` 文本内容的开头和 `endOffset` 之间没有（非空白）字符。
    const { node, offset } = closestNonSpaceInRange(trimmedRange, TrimDirection.Backwards);

    if (node && offset > 0) {
      trimmedRange.setEnd(node, offset);
    }
  }

  return trimmedRange;
}
