/**
 * 获取用于生成 xpath 表达式的节点名称。
 */
function getNodeName(node: Node): string {
  const nodeName = node.nodeName.toLowerCase();
  return nodeName === "#text" ? "text()" : nodeName;
}

/**
 * 获取节点在其父节点的子列表中的索引
 */
function getNodePosition(node: Node): number {
  let pos = 0;
  let tmp: Node | null = node;
  while (tmp) {
    if (tmp.nodeName === node.nodeName) {
      pos += 1;
    }
    tmp = tmp.previousSibling;
  }
  return pos;
}

function getPathSegment(node: Node): string {
  const name = getNodeName(node);
  const pos = getNodePosition(node);
  return `${name}[${pos}]`;
}

/**
 * 一个简单的 XPath 生成器，可以生成形式为
 * /tag[index]/tag[index] 的 XPath。
 *
 * @param node - 要生成路径的节点
 * @param root - 返回路径相对于的根节点
 */
export function xpathFromNode(node: Node, root: Node) {
  let xpath = "";

  let elem: Node | null = node;
  while (elem !== root) {
    if (!elem) {
      throw new Error("节点不是根节点的后代");
    }
    xpath = getPathSegment(elem) + "/" + xpath;
    elem = elem.parentNode;
  }
  xpath = "/" + xpath;
  xpath = xpath.replace(/\/$/, ""); // 移除末尾的斜杠

  return xpath;
}

/**
 * 返回 `element` 的第 `index` 个立即子节点，其标签名为
 * `nodeName`（不区分大小写）。
 */
function nthChildOfType(element: Element, nodeName: string, index: number): Element | null {
  nodeName = nodeName.toUpperCase();

  let matchIndex = -1;
  for (let i = 0; i < element.children.length; i++) {
    const child = element.children[i];
    if (child.nodeName.toUpperCase() === nodeName) {
      ++matchIndex;
      if (matchIndex === index) {
        return child;
      }
    }
  }

  return null;
}

/**
 * 相对于 `root` 元素评估一个 _简单 XPath_ 并返回匹配的元素。
 *
 * 一个 _简单 XPath_ 是一个或多个 `/tagName[index]` 字符串的序列。
 *
 * 与 `document.evaluate` 不同，此函数：
 *
 *  - 仅支持简单的 XPath
 *  - 不受文档 _类型_（HTML 或 XML/XHTML）的影响
 *  - 在匹配 XPath 中的元素名称与 DOM 树中的元素时忽略元素命名空间
 *  - 对所有元素不区分大小写，而不仅仅是 HTML 元素
 *
 * 返回匹配的元素，如果没有找到这样的元素，则返回 `null`。
 * 如果 `xpath` 不是简单的 XPath，则抛出错误。
 */
function evaluateSimpleXPath(xpath: string, root: Element): Element | null {
  const isSimpleXPath = xpath.match(/^(\/[A-Za-z0-9-]+(\[[0-9]+\])?)+$/) !== null;
  if (!isSimpleXPath) {
    throw new Error("表达式不是简单的 XPath");
  }

  const segments = xpath.split("/");
  let element = root;

  // 移除前导的空段。上面的正则表达式验证了 XPath
  // 至少有两个段，第一个是空的，其他是非空的。
  segments.shift();

  for (const segment of segments) {
    let elementName;
    let elementIndex;

    const separatorPos = segment.indexOf("[");
    if (separatorPos !== -1) {
      elementName = segment.slice(0, separatorPos);

      const indexStr = segment.slice(separatorPos + 1, segment.indexOf("]"));
      elementIndex = parseInt(indexStr) - 1;
      if (elementIndex < 0) {
        return null;
      }
    } else {
      elementName = segment;
      elementIndex = 0;
    }

    const child = nthChildOfType(element, elementName, elementIndex);
    if (!child) {
      return null;
    }

    element = child;
  }

  return element;
}

/**
 * 使用相对于 `root` 的 XPath 查找元素节点
 *
 * 示例：
 *   node = nodeFromXPath('/main/article[1]/p[3]', document.body)
 */
export function nodeFromXPath(
  xpath: string,
  /* istanbul ignore next */
  root: Element = document.body,
): Node | null {
  try {
    return evaluateSimpleXPath(xpath, root);
  } catch (err) {
    return document.evaluate(
      "." + xpath,
      root,

      // 注意：`namespaceResolver` 和 `result` 参数在规范中是可选的
      // 但在 Edge Legacy 中是必需的。
      null /* namespaceResolver */,
      XPathResult.FIRST_ORDERED_NODE_TYPE,
      null /* result */,
    ).singleNodeValue;
  }
}
