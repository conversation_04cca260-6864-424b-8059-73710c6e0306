import type {
  MediaTimeSelector,
  RangeSelector,
  Selector,
  TextPositionSelector,
  TextQuoteSelector,
} from "../../types/api";
import { MediaTimeAnchor, RangeAnchor, TextPositionAnchor, TextQuoteAnchor } from "./types";

type Options = {
  hint?: number;
};

async function querySelector(
  anchor: MediaTimeAnchor | RangeAnchor | TextPositionAnchor | TextQuoteAnchor,
  options: Options,
) {
  return anchor.toRange(options);
}
/**
 * 锚定一组选择器。
 *
 * 此函数将一组选择器转换为文档范围。
 * 它封装了核心锚定算法，使用选择器单独或组合来在文档中建立最佳锚点。
 *
 * @param root - 锚定上下文的根元素
 * @param selectors - 要尝试的选择器
 */
export function anchor(root: Element, selectors: Selector[], options: Options = {}) {
  let mediaTime: MediaTimeSelector | null = null;
  let position: TextPositionSelector | null = null;
  let quote: TextQuoteSelector | null = null;
  let range: RangeSelector | null = null;

  // 收集所有选择器
  for (const selector of selectors) {
    switch (selector.type) {
      case "TextPositionSelector":
        position = selector;
        options.hint = position.start; // TextQuoteAnchor 提示
        break;
      case "TextQuoteSelector":
        quote = selector;
        break;
      case "RangeSelector":
        range = selector;
        break;
      case "MediaTimeSelector":
        mediaTime = selector;
        break;
    }
  }

  /**
   * 如果适用，断言引用与存储的引用匹配
   */
  const maybeAssertQuote = (range: Range) => {
    if (quote?.exact && range.toString() !== quote.exact) {
      throw new Error("引用不匹配");
    } else {
      return range;
    }
  };

  // 从失败的默认值开始，我们建立 catch 子句以按顺序尝试选择器，从简单到复杂。
  let promise: Promise<Range> = Promise.reject("无法锚定");

  if (range) {
    // 常量绑定确保 TS 在回调运行时不会重新分配它。
    const range_ = range;
    promise = promise.catch(() => {
      const anchor = RangeAnchor.fromSelector(root, range_);
      return querySelector(anchor, options).then(maybeAssertQuote);
    });
  }

  if (position) {
    const position_ = position;
    promise = promise.catch(() => {
      const anchor = TextPositionAnchor.fromSelector(root, position_);
      return querySelector(anchor, options).then(maybeAssertQuote);
    });
  }

  if (quote) {
    const quote_ = quote;
    promise = promise.catch(() => {
      const anchor = TextQuoteAnchor.fromSelector(root, quote_);
      return querySelector(anchor, options);
    });
  }

  if (mediaTime) {
    const mediaTime_ = mediaTime;
    promise = promise.catch(() => MediaTimeAnchor.fromSelector(root, mediaTime_).toRange());
  }

  return promise;
}

export function describe(root: Element, range: Range) {
  const types = [MediaTimeAnchor, RangeAnchor, TextPositionAnchor, TextQuoteAnchor];
  const result = [];
  for (const type of types) {
    try {
      const anchor = type.fromRange(root, range);
      if (anchor) {
        result.push(anchor.toSelector());
      }
    } catch (error) {
      // 如果解析某些锚点失败，我们只想静默跳过它
    }
  }
  return result;
}
