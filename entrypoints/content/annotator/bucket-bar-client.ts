import { ListenerCollection } from "../shared/listener-collection";
import type { PortRPC } from "../shared/messaging";
import type { Anchor, Destroyable } from "../types/annotator";
import type { HostToGuestEvent, GuestToHostEvent } from "../types/port-rpc-events";
import { computeAnchorPositions } from "./util/buckets";

type HostRPC = PortRPC<HostToGuestEvent, GuestToHostEvent>;

export type BucketBarClientOptions = {
  /**
   * 文档内容的可滚动容器元素。bucket bar 的桶指向的所有高亮都应该包含在这个元素内。
   */
  contentContainer: Element;

  hostRPC: HostRPC;
};

/**
 * 向主框架传达以下信息：
 *
 * 1. 锚点集合已更改（由于注释被添加或删除）
 * 2. 锚点相对于 guest 视口的位置已更改
 */
export class BucketBarClient implements Destroyable {
  private _hostRPC: HostRPC;
  private _updatePending: boolean;
  private _anchors: Anchor[];
  private _listeners: ListenerCollection;

  constructor({ contentContainer, hostRPC }: BucketBarClientOptions) {
    this._hostRPC = hostRPC;
    this._updatePending = false;
    this._anchors = [];
    this._listeners = new ListenerCollection();

    this._listeners.add(window, "resize", () => this.update());
    this._listeners.add(window, "scroll", () => this.update());

    // 当容器或可滚动的子元素滚动时更新 bucket 位置。
    this._listeners.add(contentContainer, "scroll", () => this.update(), {
      // "scroll" 事件不会冒泡，因此使用捕获监听器来观察子元素中的事件。
      capture: true,
    });
  }

  destroy() {
    this._listeners.removeAll();
  }

  /**
   * 在以下情况下通知主框架中的 BucketBar：
   * 1. 锚点集合已更改（由于注释被添加或删除）
   * 2. 锚点相对于 guest 视口的位置已更改
   *
   * 更新是防抖动的，以减少跨框架收集和发送锚点位置数据的开销。
   *
   * @param anchors - 当锚点被添加或删除时传递此选项
   */
  update(anchors?: Anchor[]) {
    if (anchors) {
      this._anchors = anchors;
    }

    if (this._updatePending) {
      return;
    }

    this._updatePending = true;
    requestAnimationFrame(() => {
      const positions = computeAnchorPositions(this._anchors);
      this._hostRPC.call("anchorsChanged", positions);
      this._updatePending = false;
    });
  }
}
