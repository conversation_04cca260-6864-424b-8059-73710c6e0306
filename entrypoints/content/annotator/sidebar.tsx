import classnames from "classnames";
import { sendErrorsTo } from "../shared/frame-error-capture";
import { ListenerCollection } from "../shared/listener-collection";
import { PortRPC } from "../shared/messaging";
import type { AnchorPosition, Destroyable } from "../types/annotator";
import type { GuestToHostEvent, HostToGuestEvent } from "../types/port-rpc-events";
import { BucketBar } from "./bucket-bar";

// iframeContainer 可以调整大小的最小宽度。
export const MIN_RESIZE = 280;

/**
 * `Sidebar` 类创建（1）侧边栏应用程序 iframe，（2）其容器，以及（3）相邻的控件。
 */
export class Sidebar implements Destroyable {
  private _listeners: ListenerCollection;
  private _hypothesisSidebar: HTMLElement | undefined;
  private _root: HTMLElement;

  /**
   * 跟踪哪个 `Guest` 有文本选择。`null` 表示默认选择第一个连接的 guest 框架。
   */
  private _guestWithSelection: PortRPC<GuestToHostEvent, HostToGuestEvent> | null;

  /** 主客通信通道。 */
  private _guestRPC: PortRPC<GuestToHostEvent, HostToGuestEvent>[];

  bucketBar: BucketBar | null;
  externalFrame: Element | undefined;
  iframeContainer: HTMLDivElement | undefined;

  constructor(element: HTMLElement) {
    this._guestWithSelection = null;
    this._guestRPC = [];
    this._root = element;
    this.bucketBar = null;

    this.iframeContainer = document.createElement("div");
    this.iframeContainer.className = "sino-sidebar-container";
    this.iframeContainer.id = "sino-sidebar-container";

    // 创建 bucket bar 和工具栏的背景。这也作为 bucket bar 的默认容器。
    const sidebarEdge = document.createElement("div");
    sidebarEdge.setAttribute("data-testid", "sidebar-edge");
    sidebarEdge.className = classnames("absolute");
    this.iframeContainer.append(sidebarEdge);

    this.bucketBar = new BucketBar(sidebarEdge, {
      setNoteNotice: (note, target, info) => {
        this._guestRPC.forEach((rpc) => rpc.call("setNoteNotice", note, target, info));
      },
      delNoteNotice: (note) => {
        this._guestRPC.forEach((rpc) => rpc.call("delNoteNotice", note));
      },
      onFocusAnnotations: (tags) => this._guestRPC.forEach((rpc) => rpc.call("hoverAnnotations", tags)),
      onScrollToAnnotation: (tag) => this._guestRPC.forEach((rpc) => rpc.call("scrollToAnnotation", tag)),
      onSelectAnnotations: (tags) => this._guestRPC.forEach((rpc) => rpc.call("selectAnnotations", tags)),
    });

    // 将 'iframeContainer' 元素包装到 shadow DOM 中，以便不受主 CSS 样式的影响
    this._hypothesisSidebar = document.createElement("hypothesis-sidebar");
    this._hypothesisSidebar.appendChild(this.iframeContainer);
    this._root.appendChild(this._hypothesisSidebar);

    this._listeners = new ListenerCollection();
  }

  destroy() {
    this._guestRPC.forEach((rpc) => rpc.destroy());
    this.bucketBar?.destroy();
    this._listeners.removeAll();
    if (this._hypothesisSidebar) {
      // 显式卸载 "messages" 元素，以确保清理效果
      this._hypothesisSidebar.remove();
    }

    // 注销侧边栏 iframe 作为此框架中错误的处理程序。
    sendErrorsTo(null);
  }

  /**
   * 设置与已连接到主机的框架的通信。
   */
  onFrameConnected(source: "guest", port: MessagePort) {
    switch (source) {
      case "guest":
        this._connectGuest(port);
        break;
    }
  }

  _connectGuest(port: MessagePort) {
    const guestRPC = new PortRPC<GuestToHostEvent, HostToGuestEvent>();
    guestRPC.on("textSelected", () => {
      this._guestWithSelection = guestRPC;
      this._guestRPC.filter((port) => port !== guestRPC).forEach((rpc) => rpc.call("clearSelection"));
    });

    guestRPC.on("textUnselected", () => {
      this._guestWithSelection = null;
      this._guestRPC.filter((port) => port !== guestRPC).forEach((rpc) => rpc.call("clearSelection"));
    });

    guestRPC.on("setNoteShowClose", (key, type) => {
      this.bucketBar.updateKey(`${key}-${type}`);
    });

    // 如果侧边栏没有 bucket bar（干净主题），监听器将不执行任何操作
    const bucketBar = this.bucketBar;
    // 目前，我们忽略除第一个连接的 guest 之外的所有 guest 的 `anchorsChanged`。
    if (bucketBar) {
      guestRPC.on("anchorsChanged", (positions: AnchorPosition[]) => {
        if (this._guestRPC.indexOf(guestRPC) === 0) {
          bucketBar.update(positions);
        }
      });
    }

    guestRPC.on("close", () => {
      guestRPC.destroy();
      if (guestRPC === this._guestWithSelection) {
        this._guestWithSelection = null;
      }
      this._guestRPC = this._guestRPC.filter((rpc) => rpc !== guestRPC);
    });

    guestRPC.connect(port);
    this._guestRPC.push(guestRPC);
  }
}
