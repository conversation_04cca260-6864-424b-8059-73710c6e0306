import React, { useEffect, useState } from "react";
import NoteItem from "@/components/ContextMenus/NoteItem/index";
import { getLocationNotes } from "@/utils/notes";

const TipNote: React.FC<{ item: CopilotNote }> = ({ item }) => {
  const [note, setNote] = useState(null); // 初始状态为 null
  const getNoteBytag = async (tag: string) => {
    let noteList = await getLocationNotes();
    return noteList.find((x) => x.id === tag);
  };
  console.log(note, 44444);
  useEffect(() => {
    const fetchNote = async () => {
      const note = await getNoteBytag(item.tag);
      setNote(note); // 更新状态
    };

    fetchNote();
  }, [item]); // 依赖于 item.tag

  return note ? <NoteItem note={note} isThumbnail={true} isHover={false} /> : null;
};

export default TipNote;
