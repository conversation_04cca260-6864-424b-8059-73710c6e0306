.setup-item{
  .ant-dropdown-menu-title-content{
    text-align: center;
  }
}
.menu-list-adder-t{
  width:100%;
  .ant-list-items{
    padding: var(--ant-padding-xxs) !important;
  }
  .ant-list-item{
    width: 100%;
    padding: var(--ant-padding-xxs) var(--ant-padding-sm) !important;
    &:hover{
      background: var(--ant-control-item-bg-hover);
      border-radius:var(--ant-border-radius-xs);
    }

  }
  .ant-list-items>li:last-child{
    justify-content: center;
    margin-top: var(--ant-margin-xxs);
  }
}
.iframe-modal-wrap-ins{
  .ant-modal-wrap{
    z-index: 2147483650 !important;
  }
}