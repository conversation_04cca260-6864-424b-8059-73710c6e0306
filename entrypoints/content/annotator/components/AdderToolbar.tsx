import React, { useEffect, useRef, useState } from "react";
import type { MenuProps } from "antd";
import { Button, Card, Divider, Dropdown, Flex, Image, List, Menu, message, Modal, theme, Tooltip } from "antd";
import { NoteSvgIcon, SettingSvgIcon, TipSvgIcon } from "@/config/menu/note";
import { CloseCircleFilled } from "@ant-design/icons";
import { useShortcut } from "../../shared/shortcut";
import { AgentInfo } from "@/types/chat";
import { getToken, getTenantId } from "@/utils/auth.ts";
import { getUserInfo } from "@/utils/auth";
import ItemLabel from "./ItemLabel";
import { getDefaultPrompts } from "@/config/agent";
import "./AdderToolbar.less";

const { useToken } = theme;

export type Command = "annotate" | "highlight" | "quiz" | "show" | "hide";
type AdderToolbarProps = {
  isExceedHalfViewport: Boolean;
  /** 当工具栏按钮被点击时调用 */
  onCommand: (c: Command, agent?: AgentInfo) => void; // onCommand("show") 在侧边栏展示
};

/**
 * 显示在文档中选定文本上方或下方的工具栏，
 * 提供创建注释或高亮的选项。
 *
 * @param {AdderToolbarProps} props
 * 工具栏具有微妙的悬停样式。组件结构为：
 *
 * 行为：当 div.group 被悬停时，所有后代按钮及其内容变暗，
 * 除了直接悬停的按钮的内容，它们会变暗。这旨在使悬停的按钮突出，
 * 而非悬停的按钮则退后。
 */
const AdderToolbar: React.FC<AdderToolbarProps> = ({ onCommand, isExceedHalfViewport }) => {
  const { token } = useToken();
  const hideShortcut = "Escape";
  // 添加一个快捷键来关闭添加器。注意，没有与此快捷键关联的按钮，因为任何外部点击也会隐藏添加器。
  // useShortcut(hideShortcut, () => onCommand("hide"));
  const [show, setShow] = useState(false);
  const [open, setOpen] = useState(false);
  const [exceedHalfViewport, setExceedHalfViewport] = useState(isExceedHalfViewport);
  const [items, setItems] = useState<MenuProps["items"]>([]);
  // const [menuItems, setMenuItems] = useState([]);
  const [visible, setVisible] = useState(false);
  const [currentItem, setCurrentItem] = useState<any>({});
  const iframeRef = useRef(null);
  const [sceneUrl, setSceneUrl] = useState<any>(""); // iframe url
  const records = useRef([]);
  /** 处理普通Web请求的hook */
  const fetchRequest = useFetchRequest();
  const [closeModal, setCloseModal] = useState(false);
  // 隐藏到下一次访问
  const hiddenNext = () => {
    const url = window.location.href.split("?")[0];
    const arr = sessionStorage.getItem("temporaryList") ? JSON.parse(sessionStorage.getItem("temporaryList")) : [];
    arr.includes(url) ? arr : arr.push(url);
    sessionStorage.setItem("temporaryList", JSON.stringify(arr));
    setOpen(false);
    onCommand("hide");
  };
  // 本页面禁用
  const currentDisable = async () => {
    const url = window.location.href.split("?")[0];
    await browser.storage.local.get(["smartMenu"]).then((result) => {
      result.smartMenu.websiteList.push(url);

      browser.storage.local.set({
        smartMenu: result.smartMenu,
      });
      // fetchRequest({
      //   api: "addWord",
      //   params: {
      //     configType: "DOMAIN",
      //     enabled: false,
      //     domainUrl: url,
      //   },
      //   callback: (res) => {},
      // });
      onCommand("hide");
    });
  };

  // 全局禁用
  const overallDisable = async () => {
    await browser.storage.local.get(["smartMenu"]).then((result) => {
      result.smartMenu.isShowSmartMenu = false;
      browser.storage.local.set({
        smartMenu: result.smartMenu,
      });
      // fetchRequest({
      //   api: "editWord",
      //   params: { id: result?.smartMenu.userSwitch.id, configType: "GLOBAL", enabled: false },
      //   callback: (res) => {},
      // });
      onCommand("hide");
    });
  };
  const menuItems = [
    {
      key: "1",
      label: <span className="childMenuDropdown">隐藏直到下一次访问</span>,
      onClick: hiddenNext,
    },
    {
      key: "2",
      label: <span className="childMenuDropdown">本页面禁用</span>,
      onClick: currentDisable,
    },
    {
      key: "3",
      label: <span className="childMenuDropdown">全局禁用</span>,
      onClick: overallDisable,
    },
    {
      key: "4",
      label: (
        <span
          style={{ color: token.colorTextTertiary, fontSize: token.fontSizeSM, textAlign: "center" }}
          className="childMenuDropdown"
        >
          您可以在
          <span
            style={{ color: token.colorInfoText }}
            onClick={(e) => {
              e.stopPropagation(); // 防止事件冒泡
              // e.preventDefault(); // 阻止默认行为
              window.open(browser.runtime.getURL("/options.html?message=9"), "_blank");
              setOpen(false);
            }}
          >
            设置
          </span>
          中访问
        </span>
      ),
      className: "setup-item",
    },
  ];

  useEffect(() => {
    setExceedHalfViewport(isExceedHalfViewport);
  }, [isExceedHalfViewport]);

  useEffect(() => {
    GetUserInfo();
    const handleNoteListChanged = (changes) => {
      if (changes["userInfo"]) {
        // 登录
        if (changes["userInfo"].newValue) {
          setShow(true);
        }
        // 登出
        if (changes["userInfo"].oldValue) {
          setShow(false);
        }
      }
    };
    browser.storage.local.onChanged.addListener(handleNoteListChanged);
    return () => {
      browser.storage.local.onChanged.removeListener(handleNoteListChanged);
    };
  }, []);

  const GetUserInfo = () => {
    getUserInfo().then((res) => {
      if (res) {
        setShow(true);
      }
    });
  };

  /** 提示词搜索 */
  const handlePromptSearch = async () => {
    const url = new URL(window.location.href);
    fetchRequest({
      api: "getAcctIns",
      params: {
        queryContent: "",
        insShowType: "1",
      },
      async callback(res) {
        if (res.code === 200) {
          let arrShow = [];
          res.data.forEach((item) => {
            if (item.accountShowFlag !== 0) {
              item.title = item.name;
              // eoss 合同列表 跟合同详情
              if (
                url.hostname !== "eoss.sino-bridge.com" &&
                !url.pathname.startsWith("/eoss/") &&
                (item.id == "1939862797095415810" || item.id == "1939862260430024706")
              ) {
                return;
              }
              arrShow.push(item);
            }
          });
          const defaultPrompts = await getDefaultPrompts();
          records.current = defaultPrompts.concat(arrShow);
          let newItems = records.current.map((item: Prompt) => ({
            key: `${item.id}`,
            label: <ItemLabel item={item} exceedHalfViewport={exceedHalfViewport} handleClickItem={handleClickItem} />,
          }));
          setItems(newItems);
          setOpen(true);
        }
      },
    });
  };
  // 获取cookie
  const getCookie = (name: string) => {
    const match = document.cookie.match(new RegExp("(^| )" + name + "=([^;]+)"));
    return match ? decodeURIComponent(match[2]) : null;
  };
  const handleClickItem = async (key, lang?: string) => {
    const token = await getToken();
    const tenantId = await getTenantId();
    let obj = JSON.parse(JSON.stringify(records.current.find((x) => `${x.id}` === `${key}`)));
    if (obj.insCfgType == "3") {
      // 说明是指令场景
      setSceneUrl(obj.sceneUrl);
      setCurrentItem(obj);
      browser.storage.local.get(["selectedText"]).then(async (result) => {
        let url = "";
        let eossToken = "";
        // eoss 合同列表
        if (obj.id === "1939862260430024706") {
          // 通过 cookie API 获取 Admin-Token
          eossToken = (await getCookie("Admin-Token")) || "";
        }
        url =
          obj.sceneUrl +
          `?query=${encodeURIComponent(result?.selectedText || "")}` +
          `&token=${token}` +
          `&tenantid=${tenantId}` +
          `&tenantId=${tenantId}` +
          `&agentId=${obj?.agentId}` +
          (eossToken ? `&eossToken=${eossToken}` : "");

        setSceneUrl(url);
        setVisible(true);
      });
    } else {
      if (key === "100180" && lang) {
        obj.content = obj.content.replace("${lang}", lang);
      }
      onCommand("annotate", obj);
    }
  };

  const dropdownRef = useRef(null);

  const handleClickOutside = (event) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
      setOpen(false); // 点击外部时关闭下拉菜单
    }
  };
  // 监听点击事件
  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  const shadowDom = document.getElementById("shadow-dom-note").shadowRoot;
  // 获取所有带有 class="childDropdown" 的元素
  const menuOut = (event) => {
    const childDropdownElements = shadowDom.querySelectorAll(".childMenuDropdown");
    // 检查点击是否发生在当前组件内部，并且不在任何 ".childDropdown" 元素内部
    const isClickInsideDropdown = dropdownRef.current && dropdownRef.current.contains(event.target as Node);
    const isClickInsideChildDropdown = Array.from(childDropdownElements).some((childElement) =>
      childElement.contains(event.target as Node),
    );

    if (isClickInsideDropdown || isClickInsideChildDropdown) {
      event.preventDefault();
      event.stopPropagation();
    }
  };
  useEffect(() => {
    shadowDom.addEventListener("mousedown", menuOut);
    return () => {
      shadowDom.removeEventListener("mousedown", menuOut);
    };
  }, []);
  // useEffect(() => {
  //   if (visible && iframeRef.current) {
  //     // 等待 iframe 加载完毕后再发送数据
  //     iframeRef.current.onload = async () => {
  //       const token = await getToken();
  //       const tenantId = await getTenantId();
  //       await browser.storage.local.get(["selectedText"]).then((result) => {
  //         const obj = {
  //           tenantid: tenantId,
  //           token: token,
  //           inputs: currentItem.inputs,
  //           content: result?.selectedText || "",
  //         };
  //         iframeRef.current.contentWindow.postMessage(obj, "*");
  //       });
  //     };
  //   }
  // }, [visible]);
  return (
    <>
      {show && (
        <Flex
          data-component="AdderToolbar"
          align="center"
          style={{
            height: "36px",
            padding: "2px 4px 2px 6px",
            background: "#fff",
            boxSizing: "border-box",
            borderRadius: token.borderRadiusLG,
            border: `1px solid ${token.colorBorderSecondary}`,
            boxShadow: token.boxShadowSecondary,
          }}
        >
          <Tooltip
            placement="top"
            arrow={false}
            title="提问"
            getPopupContainer={(trigger) => trigger.parentNode as any}
          >
            <Image
              preview={false}
              width={24}
              onClick={() => {
                onCommand("quiz");
              }}
              style={{ userSelect: "none", cursor: "pointer" }}
              src={browser.runtime.getURL("/images/note/question.png")}
            />
          </Tooltip>
          <Flex style={{ padding: "0 0 0 4px", height: "32px", borderRadius: token.borderRadius }} ref={dropdownRef}>
            <Tooltip
              placement="top"
              arrow={false}
              title="便签"
              getPopupContainer={(trigger) => trigger.parentNode as any}
            >
              <Button
                icon={<NoteSvgIcon />}
                style={{ padding: token.paddingXS }}
                type="text"
                onClick={() => {
                  onCommand("highlight");
                  setOpen(false);
                }}
              >
                {/* 便签 */}
              </Button>
            </Tooltip>
            <Dropdown
              menu={{ items, style: { maxHeight: "323px", overflowY: "auto" } }}
              placement="bottomLeft"
              trigger={["click"]}
              open={open}
              getPopupContainer={(trigger) => trigger.parentNode as any}
            >
              <Tooltip
                placement="top"
                arrow={false}
                title="指令"
                getPopupContainer={(trigger) => trigger.parentNode as any}
              >
                <Button
                  icon={<TipSvgIcon />}
                  style={{ padding: token.paddingXS }}
                  onClick={(e) => {
                    setOpen(false);
                    handlePromptSearch();
                    e.stopPropagation(); // 防止事件冒泡
                    // e.preventDefault(); // 阻止默认行为
                  }}
                  type="text"
                >
                  {/* 指令 */}
                </Button>
              </Tooltip>
            </Dropdown>
            {/* <Button
              icon={<AiSvgIcon />}
              style={{ padding: token.paddingXS }}
              type="text"
              onClick={() => {
                setOpen(false);
              }}
            >
              会话
            </Button> */}

            <Tooltip
              placement="top"
              arrow={false}
              title="设置"
              getPopupContainer={(trigger) => trigger.parentNode as any}
            >
              <Button
                icon={<SettingSvgIcon />}
                style={{ padding: token.paddingXS }}
                type="text"
                onClick={(e) => {
                  window.open(browser.runtime.getURL("/options.html"), "_blank");
                  setOpen(false);
                  // e.stopPropagation(); // 防止事件冒泡
                  // e.preventDefault(); // 阻止默认行为
                }}
              >
                {/* 设置 */}
              </Button>
            </Tooltip>
            <Dropdown
              menu={{ items: menuItems, style: { width: "160px" } }}
              trigger={["click"]}
              placement="bottomLeft"
              getPopupContainer={(trigger) => trigger.parentNode as any}
            >
              <Button
                icon={
                  <CloseCircleFilled
                    style={{
                      color: token.colorTextQuaternary,
                    }}
                  />
                }
                style={{ padding: token.paddingXS, borderLeft: `1px dashed ${token.colorBorder}` }}
                type="text"
              ></Button>
            </Dropdown>
          </Flex>
        </Flex>
      )}
      <Modal
        title={currentItem.name}
        open={visible}
        onCancel={() => setVisible(false)}
        rootClassName="iframe-modal-wrap-ins"
        footer={null}
        width={800}
        styles={{
          // 专用于修改 .ant-modal-wrap
          mask: {
            background: "none",
          },
        }}
        className="custom-modal"
        getContainer={() => {
          const shadowDom = document.getElementById("shadow-dom-note").shadowRoot;
          return shadowDom;
        }}
      >
        <iframe
          ref={iframeRef}
          src={sceneUrl} // 替换成你的实际 iframe 页面路径
          width="100%"
          height="600px"
          style={{ border: "none" }}
        />
      </Modal>
    </>
  );
};

export default AdderToolbar;
