import { useEffect, useRef, useState } from "react";
import { Dropdown, Flex, Tooltip, theme } from "antd";
import { langList } from "@/config/options/ai";
import IconFont from "@/components/IconFont";
import { InfoCircleOutlined, SwapOutlined } from "@ant-design/icons";
const { useToken } = theme;
const ItemLabel: React.FC<{
  item: Prompt;
  exceedHalfViewport: Boolean;
  handleClickItem: Function;
}> = ({ item, exceedHalfViewport, handleClickItem }) => {
  const { token } = useToken();
  const dropdownRef = useRef<HTMLDivElement>(null); // 用来引用当前组件的 DOM 元素
  const [openDropdown, setOpenDropdown] = useState(false);
  const [open, setOpen] = useState(false);
  const [hoverId, setHoverId] = useState("");
  // 监听点击事件
  useEffect(() => {
    const shadowDom = document.getElementById("shadow-dom-note").shadowRoot;
    const handleClickShadow = (e) => {
      // 获取所有带有 class="childDropdown" 的元素
      const childDropdownElements = shadowDom.querySelectorAll(".childDropdown");

      // 检查点击是否发生在当前组件内部，并且不在任何 ".childDropdown" 元素内部
      const isClickInsideDropdown = dropdownRef.current && dropdownRef.current.contains(e.target as Node);
      const isClickInsideChildDropdown = Array.from(childDropdownElements).some((childElement) =>
        childElement.contains(e.target as Node),
      );

      if (!isClickInsideDropdown && !isClickInsideChildDropdown) {
        setOpenDropdown(false); // 如果点击发生在外部，关闭 Dropdown
      }

      e.preventDefault();
      e.stopPropagation();
    };
    shadowDom.addEventListener("mousedown", handleClickShadow);
    return () => {
      shadowDom.removeEventListener("mousedown", handleClickShadow);
    };
  }, []);

  return (
    <Flex ref={dropdownRef} justify="space-between" style={{ width: "200px", textAlign: "left", position: "relative" }}>
      <Tooltip
        placement="top"
        title={item.title}
        overlayStyle={{ width: 170 }}
        open={open}
        getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
      >
        <Flex
          justify="space-between"
          style={{
            width: "100%",
          }}
        >
          <Flex
            onClick={() => {
              item.id === "100180" ? setOpenDropdown(true) : handleClickItem(item.id);
            }}
            onMouseEnter={() => {
              setHoverId(item.id);
              item.title.length > 10 && setOpen(true);
            }}
            onMouseLeave={() => {
              setHoverId("");
              setOpen(false);
            }}
            style={{ flex: 1, overflow: "hidden", textOverflow: "ellipsis", whiteSpace: "nowrap" }}
          >
            {!item.default && (
              <Flex
                align="center"
                style={{
                  color: token.colorTextTertiary,
                  marginRight: token.marginXXS,
                }}
              >
                <IconFont type="PromptOutlined" style={{ fill: token.colorTextTertiary }} />
              </Flex>
            )}
            {item.title.length > 10 ? item.title.slice(0, 10) + "..." : item.title}
          </Flex>
          {item.id == hoverId && !item.default && (
            <Tooltip
              placement="right"
              title={item.tmplContent}
              getPopupContainer={(trigger) =>
                trigger.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode as any
              }
            >
              <InfoCircleOutlined
                style={{ color: token.colorTextTertiary }}
                onMouseEnter={() => {
                  setHoverId(item.id);
                }}
                onMouseLeave={() => {
                  setHoverId("");
                }}
              />
            </Tooltip>
          )}

          {/* {!item.default && (
            <Typography.Text
              type="secondary"
              style={{
                maxWidth: "120px",
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
              }}
            >
              {" "}
              @{item.agentName}
            </Typography.Text>
          )} */}
          <Flex>
            {/* 指令请求一次消耗积分显示 */}
            {!!item.pointsCost && item.pointsCost > 0 && (
              <Flex align="center">
                <span style={{ color: token.blue, marginLeft: '5px' }}>{item.pointsCost}&nbsp;</span>
                <IconFont type="PointsCost" className="icon" />
              </Flex>
            )}
          </Flex>
        </Flex>
      </Tooltip>
      {item.id === "100180" && (
        <SwapOutlined
          onClick={() => {
            setOpenDropdown(true);
          }}
        />
      )}
      <Dropdown
        menu={{
          items: langList.map((x) => ({
            key: `${x.key}`,
            label: (
              <Flex style={{ width: "160px" }} className="childDropdown">
                <div
                  onClick={() => {
                    handleClickItem(item.id, x.label);
                  }}
                  style={{ flex: 1 }}
                >
                  {x.label}
                </div>
              </Flex>
            ),
          })),
          style: { maxHeight: "323px", overflowY: "auto" },
        }}
        placement={exceedHalfViewport ? "bottomLeft" : "bottomRight"}
        open={openDropdown}
        trigger={["hover"]}
        getPopupContainer={(trigger) =>
          trigger.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode as any
        }
      >
        <div
          style={{
            position: "absolute",
            opacity: 0,
            height: 1,
            width: 1,
            userSelect: "none",
            top: "50%",
            ...(exceedHalfViewport ? { left: "-210px" } : { left: "calc(100% + 16px + 190px)" }),
          }}
        />
      </Dropdown>
    </Flex>
  );
};

export default ItemLabel;
