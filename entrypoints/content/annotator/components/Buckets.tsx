import React, { useEffect } from "react";
import { useGetState } from "ahooks";
import { getLocationNotes } from "@/utils/notes";
import type { Bucket } from "../util/buckets";
import { Flex, theme, Tooltip } from "antd";
import { PinIcon } from "@/config/menu";
import TipNote from "./TipNote";
const { useToken } = theme;

export type BucketsProps = {
  bucketsKey: string;
  /**
   * 包含在 bucket bar 上方的锚点的桶。如果非空，将渲染一个“向上”桶。
   */
  above: Bucket;

  /**
   * 包含在 bucket bar 下方的锚点的桶。如果非空，将渲染一个“向下”桶。
   */
  below: Bucket;

  /**
   * 屏幕上可见的桶列表。每个桶将渲染一个左指向箭头。
   */
  buckets: Bucket[];
  onFocusAnnotations: ($tags: string[]) => void;
  onScrollToAnnotation: ($tag: string) => void;
  onSelectAnnotations: ($tags: string[]) => void;
  setNoteNotice: (note: any, target: any, info: any) => void;
  delNoteNotice: (noteId: string) => void;
};

const Buckets: React.FC<BucketsProps> = ({
  bucketsKey,
  above,
  below,
  buckets,
  onFocusAnnotations,
  onScrollToAnnotation,
  onSelectAnnotations,
  setNoteNotice,
  delNoteNotice,
}) => {
  const { token } = useToken();
  const [selectedIds, setSelectedIds, getSelectedIds] = useGetState([]);
  const showUpNavigation = above.anchors.length > 0;
  const showDownNavigation = below.anchors.length > 0;

  const receiveMessage = (event: { data: { type: string; note: any; target?: string; info?: any } }) => {
    switch (event.data.type) {
      case "setNoteNotice":
        setNoteNotice(event.data.note, event.data.target, event.data.info);
        handleButtonClick(event.data.note.id);
        break;
      case "delNoteNotice":
        delNoteNotice(event.data.note);
        break;

      default:
        break;
    }
  };
  useEffect(() => {
    window.addEventListener("message", receiveMessage);
    return () => {
      window.removeEventListener("message", receiveMessage);
    };
  }, []);

  const handleButtonClick = (id) => {
    // 检查 ID 是否已在数组中
    if (!selectedIds.includes(id)) {
      setSelectedIds([...getSelectedIds(), id]);
    }
  };

  useEffect(() => {
    let str = bucketsKey.substring(13);
    const lastIndex = str.lastIndexOf("-");
    const firstPart = str.substring(0, lastIndex);
    const secondPart = str.substring(lastIndex + 1);
    if (firstPart === "0") return;
    if (firstPart === "1") {
      let noteList = [];
      getLocationNotes().then((res) => {
        noteList = res;
        setSelectedIds(noteList.map((x) => x.id));
      });
    } else if (firstPart === "2") {
      setSelectedIds([]);
    } else {
      if (secondPart === "true") {
        handleButtonClick(firstPart);
      } else {
        if (selectedIds.includes(firstPart)) {
          setSelectedIds(getSelectedIds().filter((id) => id !== firstPart));
        }
      }
    }
  }, [bucketsKey]);

  const liStyle: React.CSSProperties = {
    position: "absolute",
    left: "-29px",
    listStyle: "none",
    borderRadius: "4px 0 0 4px",
    boxShadow:
      "0px 2px 4px 0px rgba(0, 0, 0, 0.02),0px 1px 6px -1px rgba(0, 0, 0, 0.02),0px 1px 2px 0px rgba(0, 0, 0, 0.03)",
    background: token.colorBgBase,
    fontSize: token.fontSizeSM,
    cursor: "pointer",
  };
  const Icon = () => (
    <Flex
      style={{
        transform: "scale(0.608)",
        position: "absolute",
        height: "24px",
        top: "-12px",
      }}
    >
      {PinIcon}
    </Flex>
  );

  const Top = () => (
    <Flex
      style={{
        ...liStyle,
        top: above.position,
      }}
    >
      <Icon />
      <Flex
        align="center"
        justify="center"
        data-testid="up-navigation-button"
        className="sino-bookmark"
        onClick={() => {
          const anchors = [...above.anchors].sort((a, b) => a.bottom - b.bottom);
          const bottomAnchor = anchors[anchors.length - 1];
          onScrollToAnnotation(bottomAnchor.tag);
        }}
        title={`转到上一个便签组 (${above.anchors.length})`}
      >
        {Array.from(new Map(above.anchors.map((item) => [item.tag, item])).values()).length}
      </Flex>
    </Flex>
  );

  const Center = (bucket, item, index) => (
    <Tooltip
      title={getSelectedIds().includes(item.tag) ? "" : <TipNote item={item} />}
      placement="left"
      key={index}
      overlayClassName="sino-custom-tooltip"
      color="#fff"
      overlayStyle={{ padding: 0 }}
      getPopupContainer={() => {
        return document.getElementById("sino-assistant-crx-web-app");
      }}
    >
      <Flex
        style={{
          ...liStyle,
          top: bucket.position + index * 28,
          marginTop: -bucket.anchors.length * 14,
        }}
        key={index}
        id={`tag-${item.tag}`}
        onMouseEnter={() => onFocusAnnotations([item.tag])}
        onMouseLeave={() => onFocusAnnotations([])}
      >
        <Icon />
        <Flex
          align="center"
          justify="center"
          className="sino-bookmark"
          style={{
            color: "transparent",
          }}
          onClick={(event) => {
            if (getSelectedIds().includes(item.tag)) return;
            onSelectAnnotations([item.tag]);
          }}
        >
          1
        </Flex>
      </Flex>
    </Tooltip>
  );
  const Bottom = () => (
    <Flex
      style={{
        ...liStyle,
        top: below.position,
      }}
    >
      <Icon />
      <Flex
        align="center"
        justify="center"
        className="sino-bookmark"
        onClick={() => {
          const anchors = [...below.anchors].sort((a, b) => a.top - b.top);
          const topAnchor = anchors[0];
          onScrollToAnnotation(topAnchor.tag);
        }}
        title={`转到下一个便签组 (${below.anchors.length})`}
      >
        {Array.from(new Map(below.anchors.map((item) => [item.tag, item])).values()).length}
      </Flex>
    </Flex>
  );
  return (
    <ul style={{ position: "relative", margin: 0, pointerEvents: "auto" }}>
      {showUpNavigation && <Top />}
      {buckets.map((bucket) => bucket.anchors.map((item, index) => Center(bucket, item, index)))}
      {showDownNavigation && <Bottom />}
    </ul>
  );
};

export default Buckets;
