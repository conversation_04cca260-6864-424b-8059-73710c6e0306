import { nodeIsText } from "./util/node";

/**
 * 返回一个范围，该范围从 a 或 b 的起点到文档顺序中 a 或 b 的终点。
 */
function unionRanges(a: Range, b: Range): Range {
  const result = new Range();

  if (a.compareBoundaryPoints(Range.START_TO_START, b) <= 0) {
    result.setStart(a.startContainer, a.startOffset);
  } else {
    result.setStart(b.startContainer, b.startOffset);
  }

  if (a.compareBoundaryPoints(Range.END_TO_END, b) >= 0) {
    result.setEnd(a.endContainer, a.endOffset);
  } else {
    result.setEnd(b.endContainer, b.endOffset);
  }

  return result;
}

/**
 * 返回当前选定的 {@link Range} 或者如果没有选择则返回 `null`。
 */
export function selectedRange(selection: Selection | null = document.getSelection()): Range | null {
  if (!selection || selection.rangeCount === 0) {
    return null;
  }

  let range = selection.getRangeAt(0);

  // 解决 Firefox 问题 [1]，即选择可以有多个范围，与 Selection API [2] 规范相矛盾。
  // 解决方法是将范围合并以生成与其他浏览器相同的单个范围。
  //
  // [1] https://bugzilla.mozilla.org/show_bug.cgi?id=1773065
  // [2] https://w3c.github.io/selection-api/#dom-selection-rangecount
  for (let i = 1; i < selection.rangeCount; i++) {
    range = unionRanges(range, selection.getRangeAt(i));
  }

  if (range.collapsed) {
    return null;
  }
  return range;
}

/**
 * 如果选择的起点在终点之后（文档顺序中），则返回 true。
 */
export function isSelectionBackwards(selection: Selection) {
  if (selection.focusNode === selection.anchorNode) {
    return selection.focusOffset < selection.anchorOffset;
  }

  const range = selectedRange(selection)!;

  // 在 iOS 上选择节点时无法正确工作。
  // https://bugs.webkit.org/show_bug.cgi?id=220523
  return range.startContainer === selection.focusNode;
}

/**
 * 如果 `node` 的任何部分位于 `range` 内，则返回 true。
 */
export function isNodeInRange(range: Range, node: Node) {
  try {
    const length = node.nodeValue?.length ?? node.childNodes.length;
    return (
      // 检查节点的起点是否在范围的终点之前。
      range.comparePoint(node, 0) <= 0 &&
      // 检查节点的终点是否在范围的起点之后。
      range.comparePoint(node, length) >= 0
    );
  } catch (e) {
    // 如果 `range` 和 `node` 没有共同的祖先，或者 `node` 是文档类型，则 `comparePoint` 可能会失败。
    return false;
  }
}

/**
 * 迭代所有与 `range` 重叠的节点，并按文档顺序调用 `callback`。
 */
export function forEachNodeInRange(range: Range, callback: (n: Node) => void) {
  const root = range.commonAncestorContainer;
  const nodeIter: NodeIterator = root.ownerDocument!.createNodeIterator(root, NodeFilter.SHOW_ALL);

  let currentNode;
  while ((currentNode = nodeIter.nextNode())) {
    if (isNodeInRange(range, currentNode)) {
      callback(currentNode);
    }
  }
}

function textNodeContainsText(textNode: Text): boolean {
  const whitespaceOnly = /^\s*$/;
  return !textNode.textContent!.match(whitespaceOnly);
}

/**
 * 返回 `range` 内非空白文本节点的边界矩形。
 *
 * @return 视口坐标中的边界矩形数组。
 */
export function getTextBoundingBoxes(range: Range): DOMRect[] {
  const textNodes: Text[] = [];
  forEachNodeInRange(range, (node) => {
    if (nodeIsText(node) && textNodeContainsText(node)) {
      textNodes.push(node);
    }
  });

  return textNodes.flatMap((node) => {
    const nodeRange = node.ownerDocument.createRange();
    nodeRange.selectNodeContents(node);
    if (node === range.startContainer) {
      nodeRange.setStart(node, range.startOffset);
    }
    if (node === range.endContainer) {
      nodeRange.setEnd(node, range.endOffset);
    }
    if (nodeRange.collapsed) {
      // 如果范围在此文本节点的起点结束或在此节点的终点开始，则不包括它。
      return [];
    }

    // 测量范围并从视口坐标转换为文档坐标
    const viewportRects = Array.from(nodeRange.getClientRects());
    nodeRange.detach();
    return viewportRects;
  });
}

/**
 * 返回包含选择焦点点的文本行的矩形（视口坐标）。
 *
 * 如果选择为空，则返回 null。
 */
export function selectionFocusRect(selection: Selection): DOMRect | null {
  const range = selectedRange(selection);
  if (!range) {
    return null;
  }
  const textBoxes = getTextBoundingBoxes(range);
  if (textBoxes.length === 0) {
    return null;
  }

  if (isSelectionBackwards(selection)) {
    return textBoxes[0];
  } else {
    return textBoxes[textBoxes.length - 1];
  }
}

/**
 * 检索与给定范围内的节点关联的一组项目。
 *
 * `item` 可以是调用者希望从节点计算或关联的任何数据。仅返回唯一项目（由 `Object.is` 确定）。
 *
 * @param itemForNode - 返回节点的项目的回调
 */
export function itemsForRange<T>(
  range: Range,
  itemForNode: (n: Node) => NonNullable<T> | null | undefined,
): NonNullable<T>[] {
  const checkedNodes = new Set<Node>();
  const items = new Set<NonNullable<T>>();

  forEachNodeInRange(range, (current: Node | null) => {
    while (current) {
      if (checkedNodes.has(current)) {
        break;
      }
      checkedNodes.add(current);

      const item = itemForNode(current);
      if (item !== null && item !== undefined) {
        items.add(item);
      }

      current = current.parentNode;
    }
  });

  return [...items];
}
