import { Guest } from "./guest";
import { Sidebar } from "./sidebar";
import { PortProvider } from "../shared/messaging";
import { Note } from "@/types/note";
import { initNotelist, jumpScrollToNotePos } from "@/utils/notes";

// 全局Guest实例缓存
let globalGuestInstance: any = null;

export function createStrokeService(noteList: Note[]) {
  // 如果已经有全局实例，直接更新笔记列表并返回
  if (globalGuestInstance) {
    initNotelist(globalGuestInstance, noteList);
    jumpScrollToNotePos(globalGuestInstance);
    return globalGuestInstance;
  }

  // 添加右侧滚动便签数量
  const portProvider = new PortProvider();
  const sidePanelElement = document.getElementById("shadow-side-panel");
  if (sidePanelElement) {
    const shadowRoot = sidePanelElement.shadowRoot;
    const el = shadowRoot?.getElementById("annotator-stroke");
    if (el) {
      const sidebar = new Sidebar(el);
      portProvider.on("frameConnected", (source, port) => {
        return sidebar.onFrameConnected(source, port);
      });
    }
  }

  // 划词服务
  let _guest = new Guest(document.body);
  initNotelist(_guest, noteList);
  jumpScrollToNotePos(_guest);

  // 缓存全局实例
  globalGuestInstance = _guest;

  // 设置全局Guest实例引用，供iframe消息使用
  if (window.top === window && (window as any).setGuestInstance) {
    (window as any).setGuestInstance(_guest);
  }

  return _guest;
}
