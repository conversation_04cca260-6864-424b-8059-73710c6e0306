import type { AnchorPosition, Destroyable } from "../types/annotator";
import Buckets from "./components/Buckets";
import { computeBuckets } from "./util/buckets";
import { PreactContainer } from "./util/preact-no-shadow-dom";
import { ConfigProvider } from "antd";
import { StyleProvider } from "@ant-design/cssinjs";
import themeToken from "@/theme.json";

export type BucketBarOptions = {
  setNoteNotice: (note, target, info) => void;
  delNoteNotice: (note) => void;
  onFocusAnnotations: (tags: string[]) => void;
  onScrollToAnnotation: (tag: string) => void;
  onSelectAnnotations: (tags: string[]) => void;
};

/**
 * 控制“bucket bar”，显示文档中注释的位置。
 *
 * 这通常位于侧边栏的边缘，但对于某些内容查看器，可以在其他地方渲染。
 */
export class BucketBar implements Destroyable {
  private bucketsKey: string;
  private _container: PreactContainer;
  private _positions: AnchorPosition[];
  private _onFocusAnnotations: BucketBarOptions["onFocusAnnotations"];
  private _onScrollToAnnotation: BucketBarOptions["onScrollToAnnotation"];
  private _onSelectAnnotations: BucketBarOptions["onSelectAnnotations"];
  private _setNoteNotice: BucketBarOptions["setNoteNotice"];
  private _delNoteNotice: BucketBarOptions["delNoteNotice"];

  constructor(
    container: HTMLElement,
    { onFocusAnnotations, onScrollToAnnotation, onSelectAnnotations, setNoteNotice, delNoteNotice }: BucketBarOptions,
  ) {
    this.bucketsKey = "17319185201831-true";
    this._positions = [];
    this._container = new PreactContainer("bucket-bar", () => this._render());
    Object.assign(this._container.element.style, {
      display: "block",
      flexGrow: "1",
      // bucket bar 使用绝对定位来放置桶，并且当前没有固有宽度。
      // 这应该重新考虑，以便使用自定义 bucket bar 容器的主页面不需要硬编码其宽度的假设。
      width: "100%",
      height: "calc(100vh - 50px)",
      marginTop: "25px",
    });

    container.appendChild(this._container.element);
    this._onFocusAnnotations = onFocusAnnotations;
    this._onScrollToAnnotation = onScrollToAnnotation;
    this._onSelectAnnotations = onSelectAnnotations;
    this._setNoteNotice = setNoteNotice;
    this._delNoteNotice = delNoteNotice;

    this._container.render();
  }

  destroy() {
    this._container.destroy();
  }

  updateKey(key: string) {
    this.bucketsKey = new Date().getTime() + key;
    this._container.render();
  }

  /** Update the set of anchors from which buckets are generated. */
  update(positions: AnchorPosition[]) {
    this._positions = positions;
    this._container.render();
  }

  private _render() {
    const buckets = computeBuckets(this._positions, this._container.element);
    return (
      <ConfigProvider theme={themeToken}>
        <StyleProvider hashPriority="high" container={this._container._shadowRoot}>
          <Buckets
            bucketsKey={this.bucketsKey}
            above={buckets.above}
            below={buckets.below}
            buckets={buckets.buckets}
            onFocusAnnotations={(tags) => this._onFocusAnnotations(tags)}
            onScrollToAnnotation={(tag) => this._onScrollToAnnotation(tag)}
            onSelectAnnotations={(tags) => this._onSelectAnnotations(tags)}
            setNoteNotice={(note, target, info) => this._setNoteNotice(note, target, info)}
            delNoteNotice={(note) => this._delNoteNotice(note)}
          />
        </StyleProvider>
      </ConfigProvider>
    );
  }
}
