import scrollIntoView from "scroll-into-view";

/**
 * 返回一个在下一个动画帧上解析的 Promise。
 */
function nextAnimationFrame(): Promise<number> {
  return new Promise((resolve) => {
    requestAnimationFrame(resolve);
  });
}

/**
 * 在两个值之间线性插值。
 *
 * @param fraction - [0, 1] 之间的值
 */
function interpolate(a: number, b: number, fraction: number): number {
  return a + fraction * (b - a);
}

export type DurationOptions = { maxDuration?: number };
/**
 * 平滑地滚动一个元素进入视图。
 */
export async function scrollElementIntoView(
  element: HTMLElement,
  /* istanbul ignore next - defaults are overridden in tests */
  { maxDuration = 500 }: DurationOptions = {},
): Promise<void> {
  // 使 body 的 `tagName` 在 XHTML 文档中返回一个大写字符串，就像在 HTML 文档中一样。
  // 这是 `scrollIntoView` 检测 <body> 元素的一个变通方法。参见
  // https://github.com/KoryNunn/scroll-into-view/issues/101。
  const body = element.closest("body");
  if (body && body.tagName !== "BODY") {
    Object.defineProperty(body, "tagName", {
      value: "BODY",
      configurable: true,
    });
  }

  // 确保在滚动之前打开 details 标签，以防注释在 details 标签内。
  // 这保证了用户可以立即在屏幕上查看内容。
  const details = element.closest("details");
  if (details && !details.hasAttribute("open")) {
    details.open = true;
  }

  await new Promise((resolve) => scrollIntoView(element, { time: maxDuration }, resolve));
}
