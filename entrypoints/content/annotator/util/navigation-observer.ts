/* global Navigation */
import { ListenerCollection } from "../../shared/listener-collection";

/**
 * Monkey-patch 一个对象以观察对方法的调用。
 *
 * 如果观察到的方法抛出异常，则不会调用 `handler`。
 *
 * @param handler - 在监控的方法被调用后调用的处理程序。
 * @return 回调函数，用于移除观察者并恢复 `object[method]`。
 */
function observeCalls<T>(object: T, method: keyof T, handler: (...args: unknown[]) => void): () => void {
  const origHandler = object[method];

  /* istanbul ignore next */
  if (typeof origHandler !== "function") {
    throw new Error("只能拦截函数");
  }

  const wrapper = (...args: unknown[]) => {
    const result = origHandler.call(object, ...args);
    handler(...args);
    return result;
  };
  // @ts-expect-error 已经在上面检查了类型是函数
  object[method] = wrapper;

  return () => {
    object[method] = origHandler;
  };
}

function stripFragment(url: string): string {
  return url.replace(/#.*/, "");
}

/**
 * 返回当前窗口的 Navigation API 入口点。
 *
 * 这是 `window.navigation` 的一个包装器，它检查对象是否存在并具有预期的类型。另见
 * https://github.com/hypothesis/client/issues/5324。
 */
export function getNavigation(): EventTarget | null {
  const navigation = (window as any).navigation;
  if (
    // @ts-expect-error - Navigation API 在 TS 中缺失
    typeof Navigation === "function" &&
    // @ts-expect-error: 由于类型不匹配，此行预计会产生错误
    navigation instanceof Navigation
  ) {
    return navigation;
  }
  return null;
}

/**
 * 用于检测 HTML 文档的客户端导航的实用程序。
 *
 * 如果可用，这将使用 Navigation API [1]，否则回退到
 * monkey-patching History API [2]。
 *
 * 仅报告更改路径或查询参数的导航。假定仅更改哈希片段的 URL 更新是导航到
 * 同一逻辑文档的不同部分。Hypothesis 通常在比较 URL 时忽略哈希片段。
 *
 * [1] https://wicg.github.io/navigation-api/
 * [2] https://developer.mozilla.org/en-US/docs/Web/API/History
 */
export class NavigationObserver {
  private _listeners: ListenerCollection;
  private _unpatchHistory: (() => void) | undefined;

  /**
   * 开始观察导航更改。
   *
   * @param onNavigate - 导航发生时调用的回调
   *   导航完成并且新 URL 反映在 `location.href` 中后触发回调。
   * @param getLocation - 返回当前 URL 的测试接口
   */
  constructor(
    onNavigate: (url: string) => void,
    /* istanbul ignore next - 默认值在测试中被覆盖 */
    getLocation = () => location.href,
  ) {
    this._listeners = new ListenerCollection();

    let lastURL = getLocation();
    const checkForURLChange = (newURL = getLocation()) => {
      if (stripFragment(lastURL) !== stripFragment(newURL)) {
        lastURL = newURL;
        onNavigate(newURL);
      }
    };

    const navigation = getNavigation();
    if (navigation) {
      this._listeners.add(navigation, "navigatesuccess", () => checkForURLChange());
    } else {
      const unpatchers = [
        observeCalls(window.history, "pushState", () => checkForURLChange()),
        observeCalls(window.history, "replaceState", () => checkForURLChange()),
      ];
      this._unpatchHistory = () => unpatchers.forEach((cleanup) => cleanup());
      this._listeners.add(window, "popstate", () => checkForURLChange());
    }
  }

  /** 停止观察导航更改。 */
  disconnect() {
    this._unpatchHistory?.();
    this._listeners.removeAll();
  }
}
