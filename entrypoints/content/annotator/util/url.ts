/**
 * 返回标准化的 URI。
 *
 * 这会使其变为绝对路径并去除片段标识符。
 *
 * @param uri - 相对或绝对 URL
 * @param base - 用于解析相对 URL 的基准 URL。默认为文档的基准 URL。
 */
export function normalizeURI(uri: string, base: string = document.baseURI): string {
  const absUrl = new URL(uri, base).href;

  // 移除片段标识符。
  // 这是在序列化的 URL 上完成的，而不是修改 `url.hash`，因为 Safari 中存在一个 bug。
  // 参见 https://github.com/hypothesis/h/issues/3471#issuecomment-226713750
  return absUrl.toString().replace(/#.*/, "");
}
