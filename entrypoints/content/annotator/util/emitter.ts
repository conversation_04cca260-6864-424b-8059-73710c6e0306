/* eslint-disable @typescript-eslint/ban-types */

/*
 * 禁用 @typescript-eslint/ban-types 对整个文件的影响，因为将事件的回调类型从 `Function` 更改为其他类型具有多个影响，应单独处理。
 */
import { TinyEmitter } from "tiny-emitter";

import type { Destroyable } from "../../types/annotator";

/**
 * Emitter 是一个实现发布/订阅模式的通信类。它允许通过共享的 EventBus 发送和监听事件。
 * 应用程序的不同元素可以相互通信，而不需要紧密耦合。
 */
export class Emitter implements Destroyable {
  private _emitter: TinyEmitter;
  private _subscriptions: [event: string, callback: Function][];

  constructor(emitter: TinyEmitter) {
    this._emitter = emitter;
    this._subscriptions = [];
  }

  /**
   * 触发一个事件。
   */
  publish(event: string, ...args: unknown[]) {
    this._emitter.emit(event, ...args);
  }

  /**
   * 注册一个事件监听器。
   */
  subscribe(event: string, callback: Function) {
    this._emitter.on(event, callback);
    this._subscriptions.push([event, callback]);
  }

  /**
   * 移除一个事件监听器。
   */
  unsubscribe(event: string, callback: Function) {
    this._emitter.off(event, callback);
    this._subscriptions = this._subscriptions.filter(
      ([subEvent, subCallback]) => subEvent !== event || subCallback !== callback,
    );
  }

  /**
   * 移除所有事件监听器。
   */
  destroy() {
    for (const [event, callback] of this._subscriptions) {
      this._emitter.off(event, callback);
    }
    this._subscriptions = [];
  }
}

export class EventBus {
  private _emitter: TinyEmitter;

  constructor() {
    this._emitter = new TinyEmitter();
  }

  createEmitter() {
    return new Emitter(this._emitter);
  }
}
