import type { Anchor, AnchorPosition } from "../../types/annotator";
import { getBoundingClientRect } from "../highlighter";
export type Bucket = {
  /** 此桶中的锚点。 */
  anchors: AnchorPosition[];
  /** 此桶应出现在 bucket bar 中的垂直像素偏移量 */
  position: number;
};

export type BucketSet = {
  /**
   * 包含所有注释标签的单个桶，其锚点在屏幕上方不可见
   */
  above: Bucket;

  /**
   * 包含所有注释标签的单个桶，其锚点在屏幕下方不可见
   */
  below: Bucket;

  /** 屏幕上的桶 */
  buckets: Bucket[];
};

export type WorkingBucket = {
  /** 此桶中的锚点。 */
  anchors: AnchorPosition[];

  /**
   * 此桶的计算位置（偏移量），基于当前锚点。
   * 这是在 `top` 和 `bottom` 之间居中的位置
   */
  position: number;

  /**
   * 此桶中锚点的最上（最低）垂直偏移量 —
   * 最低的 `top` 位置值，类似于围绕此桶中所有锚点高亮显示的理论框的顶部偏移量
   */
  top: number;

  /**
   * 此桶中锚点的最下（最高）垂直偏移量 —
   * 最高的 `top` 位置值，类似于围绕此桶中所有锚点高亮显示的理论框的底部偏移量
   */
  bottom: number;
};

// 生成的注释锚点高亮显示的桶应至少间隔此数量的像素
const BUCKET_GAP_SIZE = 30;

/**
 * 计算一组锚点高亮显示的顶部和底部位置，按垂直顺序从上到下排序。
 */
export function computeAnchorPositions(anchors: Anchor[]): AnchorPosition[] {
  const positions: AnchorPosition[] = [];

  anchors.forEach(({ annotation, highlights }) => {
    if (!highlights?.length) {
      return;
    }

    const { top, bottom } = getBoundingClientRect(highlights);

    if (top >= bottom) {
      // 空矩形。高亮显示可能与文档断开连接或隐藏。
      return;
    }

    positions.push({
      tag: annotation.$tag,
      top,
      bottom,
    });
  });

  // 垂直排序锚点从上到下
  positions.sort((anchor1, anchor2) => anchor1.top - anchor2.top);

  return positions;
}

/**
 * 桶的顶部/底部与容器的顶部/底部之间的间隙。
 */
export const BUCKET_BAR_VERTICAL_MARGIN = 0;

/**
 * 将锚点分组到桶中，并确定它们在 {@link container} 中的合适垂直位置。
 *
 * @param anchorPositions - 相对于视口的锚点位置
 * @param container - 将在其中渲染桶的容器
 */
export function computeBuckets(anchorPositions: AnchorPosition[], container: Element): BucketSet {
  const aboveAnchors = [] as AnchorPosition[];
  const belowAnchors = [] as AnchorPosition[];
  const buckets: Bucket[] = [];

  // 在构建每个桶时保持当前工作锚点和位置
  let currentBucket: WorkingBucket | null = null;

  /**
   * 基于提供的 `AnchorPosition` 创建一个新的工作桶
   */
  const tagSet = new Set<string>(); // 用于跟踪唯一的 tag

  function newBucket(anchor: AnchorPosition): WorkingBucket {
    const { bottom, top } = anchor;
    const anchorHeight = bottom - top;
    const bucketPosition = top + anchorHeight / 2;
    return {
      bottom,
      position: bucketPosition,
      anchors: [anchor],
      top,
    };
  }

  const containerRect = container.getBoundingClientRect();
  const vMargin = BUCKET_BAR_VERTICAL_MARGIN;

  // 计算相对于 bucket bar 而不是视口的桶位置。
  const relativePositions = anchorPositions.map((aPos) => ({
    tag: aPos.tag,
    top: aPos.top - containerRect.top,
    bottom: aPos.bottom - containerRect.top,
  }));

  // 从位置信息构建桶
  for (const aPos of relativePositions) {
    const center = (aPos.top + aPos.bottom) / 2;

    if (center < vMargin) {
      aboveAnchors.push(aPos);
      continue;
    } else if (center > containerRect.height - vMargin) {
      belowAnchors.push(aPos);
      continue;
    }

    // 检查 tag 是否已存在
    if (!tagSet.has(aPos.tag)) {
      tagSet.add(aPos.tag); // 添加到集合中

      if (!currentBucket) {
        // 我们遇到了第一个屏幕上的锚点位置：
        // 我们需要一个桶！
        currentBucket = newBucket(aPos);
        continue;
      }
      // 我们希望在共享桶内包含重叠的高亮显示和彼此接近的高亮显示
      const isContainedWithin = aPos.top > currentBucket.top && aPos.bottom < currentBucket.bottom;

      // 新锚点的位置足够低于当前桶的底部，以至于可以开始一个新桶
      const isLargeGap = aPos.top - currentBucket.bottom > BUCKET_GAP_SIZE;

      if (isLargeGap && !isContainedWithin) {
        // 我们需要开始一个新桶；推送工作桶并创建一个新桶
        buckets.push(currentBucket);
        currentBucket = newBucket(aPos);
      } else {
        // 我们将此锚点添加到当前工作桶中并相应地更新偏移属性。
        // 我们可以确信 `aPos.top` >= `currentBucket.top`，因为锚点位置按其 `top` 偏移量排序 — 这意味着 `currentBucket.top` 仍然准确表示此桶中所有锚点的虚拟矩形的 `top` 偏移量。但让我们检查一下底部是否更大/更低：
        const updatedBottom = aPos.bottom > currentBucket.bottom ? aPos.bottom : currentBucket.bottom;
        const updatedHeight = updatedBottom - currentBucket.top;
        currentBucket.anchors.push(aPos);
        currentBucket.bottom = updatedBottom;
        currentBucket.position = currentBucket.top + updatedHeight / 2;
      }
    }
  }

  if (currentBucket) {
    buckets.push(currentBucket);
  }

  // 添加一个包含屏幕上方不可见锚点的上部“导航”桶
  const above: Bucket = {
    anchors: aboveAnchors,
    position: vMargin,
  };

  // 添加一个包含屏幕下方不可见锚点的下部“导航”桶
  const below: Bucket = {
    anchors: belowAnchors,
    position: containerRect.height - vMargin,
  };
  return {
    above,
    below,
    buckets,
  };
}
