import ReactDOM from "react-dom/client";

import type { Destroyable } from "../../types/annotator";
import { createShadowRoot } from "./shadow-root";
import { ReactNode } from "react";
/**
 * 管理顶级 Hypothesis UI 元素的根 `<hypothesis-*>` 容器。
 *
 * 这实现了这些元素的通用功能，例如：
 *
 *  - 创建带有 shadow root 的 `<hypothesis-{name}>` 元素，并将样式表加载到其中。
 *  - 当调用 {@link PreactContainer.render} 时重新渲染 Preact 组件树。
 *  - 当调用 {@link PreactContainer.destroy} 时卸载组件并移除容器元素。
 */
export class PreactContainer implements Destroyable {
  private _element: HTMLElement;
  public _shadowRoot: ShadowRoot;
  private _render: () => ReactNode;
  private _root: ReactDOM.Root;

  /**
   * 创建一个新的 `<hypothesis-{name}>` 容器元素。
   *
   * 调用以执行初始渲染。
   *
   * @param name - 元素的后缀
   * @param render - 渲染此容器的根 JSX 元素的回调
   */
  constructor(name: string, render: () => ReactNode, styles?: React.CSSProperties) {
    const tag = `shadow-dom-${name}`;
    this._element = document.createElement(tag);
    this._element.id = tag;
    if (styles) {
      for (const [key, value] of Object.entries(styles)) {
        this._element.style[key as any] = value;
      }
    }
    this._shadowRoot = createShadowRoot(this._element);
    this._render = render;
    const style = document.createElement("style");
    // @ts-expect-error: 引用路径
    fetch(browser.runtime.getURL("/content-scripts/content.css")).then((response) => {
      if (response.ok) {
        response.text().then((data) => {
          style.textContent = data;
        });
        this._shadowRoot.appendChild(style);
      }
    });
    this._root = ReactDOM.createRoot(this._shadowRoot);
  }

  /** 卸载 Preact 组件并从 DOM 中移除容器元素。 */
  destroy() {
    this._root.render(null);
    this._element.remove();
  }

  /** 返回对容器元素的引用。 */
  get element(): HTMLElement {
    return this._element;
  }

  /** 重新渲染根 Preact 组件。 */
  render() {
    this._root.render(this._render());
  }
}
