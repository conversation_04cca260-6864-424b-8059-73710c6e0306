/**
 * 将注释器 UI 组件的样式表加载到 shadow DOM 根中。
 */
function loadStyles(shadowRoot: ShadowRoot) {
  // 查找由引导脚本添加的预加载样式表。
  const url = (
    document.querySelector('link[rel="preload"][href*="/build/styles/annotator.css"]') as HTMLLinkElement | undefined
  )?.href;

  if (!url) {
    return;
  }

  const linkEl = document.createElement("link");
  linkEl.rel = "stylesheet";
  linkEl.href = url;
  shadowRoot.appendChild(linkEl);
}

/**
 * 为注释器 UI 组件创建 shadow root 并将注释器 CSS 样式加载到其中。
 *
 * @param container - 用于渲染 UI 的容器元素
 */
export function createShadowRoot(container: HTMLElement): ShadowRoot {
  const shadowRoot = container.attachShadow({ mode: "open" });
  loadStyles(shadowRoot);

  // @ts-expect-error The window doesn't know about the polyfill
  // applyFocusVisiblePolyfill comes from the `focus-visible` package.
  const applyFocusVisible = window.applyFocusVisiblePolyfill;
  if (applyFocusVisible) {
    applyFocusVisible(shadowRoot);
  }

  stopEventPropagation(shadowRoot);
  return shadowRoot;
}

/**
 * 阻止多个事件的冒泡。
 *
 * 这使得主页面对注释器活动的感知减少了一些。
 * 主页面仍然可以在捕获阶段操作事件。
 *
 * 另一个好处是点击和触摸开始通常会导致侧边栏关闭。
 * 通过阻止这些事件的冒泡，我们不必单独停止传播。
 */
function stopEventPropagation(element: HTMLElement | ShadowRoot) {
  element.addEventListener("mouseup", (event) => event.stopPropagation());
  element.addEventListener("mousedown", (event) => event.stopPropagation());
  element.addEventListener("touchstart", (event) => event.stopPropagation(), {
    passive: true,
  });
}
