import type { TinyEmitter } from "tiny-emitter";

import type { APIAnnotationData, Selector, Target } from "./api";
import type { ClientAnnotationData } from "./shared";

/**
 * 表示文档中注释锚定区域的对象。
 *
 * 这种锚定范围的表示允许在锚定注释和稍后使用锚定范围之间进行某些文档变更，
 * 例如插入其他锚点的高亮。与序列化选择器的初始锚定相比，解析这些范围应该是
 * 一种廉价的操作。
 */
export type AbstractRange = {
  /**
   * 将抽象范围解析为具体的实时 `Range`。实现可能每次返回的 `Range` 不同。
   */
  toRange(): Range;
};

/**
 * 从文档中的 `<link>` 元素或等效的相关 URL 信息源收集的元数据。
 */
export type Link = {
  rel?: string;
  type?: string;
  href: string;
};

export type DocumentMetadata = {
  title: string;
  link: Link[];

  // 仅 HTML
  dc?: Record<string, string[]>;
  eprints?: Record<string, string[]>;
  facebook?: Record<string, string[]>;
  highwire?: Record<string, string[]>;
  prism?: Record<string, string[]>;
  twitter?: Record<string, string[]>;
  favicon?: string;

  // HTML + PDF
  documentFingerprint?: string;
};

/**
 * 标识文档的可加载块或段。
 *
 * 一些文档查看器不会一次加载整个文档。例如，EPUB 阅读器将一次加载出版物中的一个内容文档。
 */
export type SegmentInfo = {
  /** EPUB 内容文档的规范片段标识符 */
  cfi?: string;

  /** 此段中的页码范围。 */
  pages?: {
    start: string;
    end: string;
  };

  /** 段的相对或绝对 URL。 */
  url?: string;
};

/**
 * 注释数据的子集，允许在文档中表示注释。
 */
export type AnnotationData = ClientAnnotationData &
  Pick<APIAnnotationData, "target" | "uri"> & {
    document?: DocumentMetadata;
  };

/**
 * 表示注释在文档中关联位置的对象。
 */
export type Anchor = {
  annotation: AnnotationData;
  /** 创建此注释高亮的 HTML 元素。 */
  highlights?: HTMLElement[];
  /** 此注释选择器解析到的文档区域。 */
  range?: AbstractRange;
  target: Target;
};

/**
 * 由与锚点关联的高亮元素的并集创建的边界框的顶部和底部位置。顶部和底部位置基于视口。
 * 值为零对应于视口的顶部。隐藏在视口上方的元素具有负值。
 */
export type AnchorPosition = {
  /** 关联注释的 `tag`。 */
  tag: string;
  /** 以像素为单位的顶部坐标。 */
  top: number;
  /** 以像素为单位的底部坐标。 */
  bottom: number;
};

/**
 * 暴露给集成的 `Guest` 类的子集。
 */
export type Annotator = {
  anchors: Anchor[];
  anchor(ann: AnnotationData): Promise<Anchor[]>;
};

/**
 * 关于侧边栏当前布局状态的详细信息。
 *
 * 这用于通知侧边栏布局更改，注释器的其他部分会对其做出反应。
 */
export type SidebarLayout = {
  /** 侧边栏是打开还是关闭 */
  expanded: boolean;
  /** 侧边栏的当前宽度（以像素为单位） */
  width: number;
  /** 侧边栏的当前高度（以像素为单位） */
  height: number;
  /** 侧边栏边缘控件（工具栏、桶栏）的宽度 */
  toolbarWidth: number;
};

/**
 * 处理支持特定文档类型（网页、PDF、电子书等）的所有细节的文档类型/查看器集成接口。
 */
export type IntegrationBase = {
  /**
   * 尝试将一组序列化选择器解析为当前文档中的相应内容。
   */
  anchor(root: HTMLElement, selectors: Selector[]): Promise<Range>;

  /** 生成表示 `range` 中内容的可序列化选择器列表。 */
  describe(root: HTMLElement, range: Range): Selector[] | Promise<Selector[]>;
  /**
   * 返回包含文档内容的主要元素。这用于控件（如桶栏）了解内容何时可能已滚动。
   */
  contentContainer(): HTMLElement;

  /**
   * 返回表示 `range` 中可注释内容范围的 DOM 范围，如果 `range` 不包含任何可注释内容，则返回 `null`。
   * 例如，`range` 可能会被修剪掉前导或尾随空白。如果已经有效，则可以返回未修改的 `range`。
   */
  getAnnotatableRange(range: Range): Range | null;

  /** 返回当前加载文档的元数据，例如标题、PDF 指纹等。 */
  getMetadata(): Promise<DocumentMetadata>;

  /**
   * 返回有关当前加载文档的哪个部分的信息。
   *
   * 这用于内容（例如 EPUB），其中通常一次加载一个内容文档（通常是一个章节）。
   */
  segmentInfo?(): Promise<SegmentInfo>;

  /**
   * 返回当前加载文档的 URL。
   *
   * 例如，这可能与当前 URL (`location.href`) 不同。
   */
  uri(): Promise<string>;

  /**
   * 滚动到锚点。
   *
   * 只有在锚点至少有一个高亮时才会调用此方法（即 `anchor.highlights` 是一个非空数组）。
   */
  scrollToAnchor(a: Anchor): Promise<void>;

  /**
   * 客户端在向侧边栏报告文档信息时是否应设置 {@link DocumentInfo.persistent} 标志。
   */
  persistFrame?(): boolean;
};

export type Integration = Destroyable & TinyEmitter & IntegrationBase;

/**
 * 可销毁类实现 `destroy` 方法以正确删除所有事件处理程序和其他资源。
 */
export type Destroyable = {
  destroy(): void;
};

/**
 * 加载在访客框架中的文档的详细信息。
 */
export type DocumentInfo = {
  /**
   * 文档的主要 URI。这是与文档上创建的注释关联的主要 URI。
   */
  uri: string;

  /** 有关文档的其他 URI 和其他元数据。 */
  metadata: DocumentMetadata;

  /**
   * 有关多段文档的哪个段（页面、章节等）加载在访客框架中的信息。
   *
   * 例如，这用于 EPUB。
   */
  segmentInfo?: SegmentInfo;

  /**
   * 一个提示，表明该框架可能会被另一个访客框架替换，后者显示未来同一文档的不同段。
   *
   * 此标志用于促进书籍章节之间的更无缝过渡。
   */
  persistent: boolean;
};
