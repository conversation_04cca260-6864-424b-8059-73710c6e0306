import type { ClientAnnotationData } from "./shared";

/**
 * 指示注释引用的视频或音频文件中的时间范围的选择器。
 */
export type MediaTimeSelector = {
  type: "MediaTimeSelector";

  /** 从媒体开始的偏移量（以秒为单位）。 */
  start: number;
  /** 从媒体开始的偏移量（以秒为单位）。 */
  end: number;
};

/**
 * 使用选定文本加上周围上下文来标识文档区域的选择器。
 */
export type TextQuoteSelector = {
  type: "TextQuoteSelector";
  exact: string;
  prefix?: string;
  suffix?: string;
};

/**
 * 使用文档主体 `textContent` 中的 UTF-16 字符偏移量来标识文档区域的选择器。
 */
export type TextPositionSelector = {
  type: "TextPositionSelector";
  start: number;
  end: number;
};

/**
 * 使用 XPath 和字符偏移量来标识文档区域的选择器。
 */
export type RangeSelector = {
  type: "RangeSelector";
  startContainer: string;
  endContainer: string;
  startOffset: number;
  endOffset: number;
};

/**
 * 注释所涉及的文档区域的序列化表示。
 */
export type Selector = TextQuoteSelector | TextPositionSelector | RangeSelector | MediaTimeSelector;

/**
 * 注释的 `target` 字段中的条目，用于标识注释所引用的文档和文档区域。
 */
export type Target = {
  /** 文档的 URI */
  source: string;
  /** 文档的区域 */
  selector?: Selector[];
  baseId?: string;
};
export type Coordinates = {
  top: string;
  left: string;
  width: string;
  height: string;
};

/**
 * 表示由 h API 返回的注释。
 * API 文档：https://h.readthedocs.io/en/latest/api-reference/#tag/annotations
 */
export type APIAnnotationData = {
  /**
   * 注释的服务器分配 ID。只有在注释保存到后端后才会设置。
   */
  id?: string;
  uri: string;
  type: string;
  target: Target[];
  coordinates: Coordinates;
  noteStyle: CopilotNote["noteStyle"];
  item: CopilotNote;
};

/**
 * 增强的注释，包括 `h` API 返回的内容和客户端内部属性。
 */
export type Annotation = ClientAnnotationData & APIAnnotationData;
