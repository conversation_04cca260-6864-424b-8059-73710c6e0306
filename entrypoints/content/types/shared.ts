/**
 * 聚类为客户端应用程序提供了一种分类注释的机制，以便在注释文档中绘制的锚点高亮可以有不同的样式。
 * 一个注释只能属于一个聚类。
 */
export type HighlightCluster =
  | "other-content" // 默认聚类：不属于当前用户的内容
  | "user-annotations" // 属于当前用户的注释
  | "user-highlights"; // 高亮（高亮是私有的，总是属于当前用户）

/**
 * 注释属性不在 API 对象中存在，但由客户端添加
 */
export type ClientAnnotationData = {
  $cluster?: HighlightCluster;

  /**
   * 客户端标识符：即使注释没有服务器提供的 `id`（即未保存），也会设置
   */
  $tag: string;

  $id: string;

  /**
   * 表示等待注释锚定超时的标志
   */
  $anchorTimeout?: boolean;

  /**
   * 表示此注释是使用“高亮”按钮创建的，而不是“注释”按钮创建的标志。
   */
  $highlight?: boolean;

  /**
   * 表示在文档中未找到此注释的标志。
   * 在锚定进行时最初为 `undefined`，如果锚定失败则设置为 `true`，如果成功则设置为 `false`。
   */
  $orphan?: boolean;
  noteStyle?: CopilotNote["noteStyle"];
  item: CopilotNote;
};
