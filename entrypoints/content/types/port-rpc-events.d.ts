/**
 * 此模块定义了在客户端中具有不同角色的框架之间发送的消息
 * （访客、主机、侧边栏）。一些消息是源框架中的事件
 * （例如，活动功能标志已更改，文本已选择），
 * 其他是目标框架的命令（例如“关闭侧边栏”）。
 */

/**
 * 访客发送给主机的事件。
 */
export type GuestToHostEvent =
  /** 文本在访客框架中已取消选择。 */
  | "textUnselected"

  /** 文本在访客框架中已选择。 */
  | "textSelected"

  /** 锚点在访客框架中已更改。 */
  | "anchorsChanged"
  | "setNoteShowClose";

/**
 * 访客发送给侧边栏的事件。
 */
export type GuestToSidebarEvent =
  /**
   * 通过注释/高亮控件在访客框架中创建了一个新注释。
   */
  | "createAnnotation"

  /**
   * 在侧边栏中指示哪些注释卡片对应于访客中的悬停高亮。
   */
  | "hoverAnnotations"

  /** 访客框架中的文档 URI 或元数据已更改。 */
  | "documentInfoChanged"
  | "setNoteNotice"
  | "delNoteNotice";

/**
 * 主机发送给访客的事件。
 */
export type HostToGuestEvent =
  /**
   * 主机请求访客框架创建一个新注释。
   *
   * 这用于工具栏按钮，该按钮根据是否有选择来为主访客框架创建新注释或页面注释。
   */
  | "createAnnotation"

  /** 清除访客框架中的选择。 */
  | "clearSelection"

  /**
   * 在访客中指示哪些高亮对应于桶栏中的悬停桶。
   */
  | "hoverAnnotations"

  /**
   * 选择访客框架中的注释。
   *
   * 这用于在单击桶栏中的相应项目时选择注释。
   */
  | "selectAnnotations"

  /** 滚动到视图中的高亮。 */
  | "scrollToAnnotation"
  | "setNoteNotice"
  | "delNoteNotice";

/**
 * 侧边栏发送给访客的事件。
 */
export type SidebarToGuestEvent =
  /** 从访客框架中删除注释。 */
  | "deleteAnnotation"

  /**
   * 在访客中指示哪些高亮对应于侧边栏中的悬停注释。
   */
  | "hoverAnnotations"

  /** 将新注释加载到访客框架中。 */
  | "loadAnnotations"

  /** 滚动到视图中的注释。 */
  | "scrollToAnnotation"
  | "setNoteNotice"
  | "delNoteNotice";
