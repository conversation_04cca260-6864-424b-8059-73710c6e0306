import React from "react";

interface SuggestionCardProps {
  position: { top: number; left: number };
  suggestions: any[];
  selectedIndex?: number;
  onSelect: (suggestion: any) => void;
  onClose: () => void;
  isLoading?: boolean;
  cardWidth?: number; // 新增：卡片宽度
}

const SuggestionCard: React.FC<SuggestionCardProps> = ({
  position,
  suggestions,
  selectedIndex = 0,
  onSelect,
  onClose,
  isLoading = false,
  cardWidth = 300, // 默认宽度
}) => {
  return (
    <div id="suggestion-container" style={{ top: `${position.top}px`, left: `${position.left}px`, width: cardWidth }}>
      <div className="suggestion-card">
        {/* <div className="card-header">
          <span>推荐内容111111122222</span>
          <button className="close-button" onClick={onClose}>
            ×
          </button>
        </div> */}
        <div className="suggestions-list">
          {isLoading ? (
            <div className="loading-item">
              <div className="suggestion-content">正在加载...</div>
            </div>
          ) : suggestions.length > 0 ? (
            suggestions.map((suggestion: any, index: number) => (
              <div
                key={index}
                className={`suggestion-item ${index === selectedIndex ? "selected-suggestion" : ""}`}
                onClick={() => {
                  console.log("Usage button clicked with suggestion:", suggestion);
                  onSelect(suggestion);
                }}
              >
                <div className="suggestion-content">
                  {typeof suggestion === "boolean" ? (suggestion ? "是/选中" : "否/未选中") : String(suggestion)}
                </div>
                {/* <button
                  className="use-button"
                  onClick={() => {
                    console.log("Usage button clicked with suggestion:", suggestion);
                    onSelect(suggestion);
                  }}
                >
                  使用
                </button> */}
              </div>
            ))
          ) : (
            <div className="suggestion-item">
              <div className="suggestion-content">没有可用推荐</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SuggestionCard;
