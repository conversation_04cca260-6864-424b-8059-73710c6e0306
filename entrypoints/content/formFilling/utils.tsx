// 获取所有页面上的可输入元素
export const getAllInputElements = (): HTMLElement[] => {
  const elements: HTMLElement[] = [];

  // 获取所有常规input元素
  const inputs = document.querySelectorAll<HTMLElement>('input:not([type="hidden"]):not([disabled]):not([readonly])');

  // 获取所有textarea元素
  const textareas = document.querySelectorAll<HTMLElement>("textarea:not([disabled]):not([readonly])");

  // 获取所有select元素
  const selects = document.querySelectorAll<HTMLElement>("select:not([disabled])");

  // 获取所有可编辑div
  const editableDivs = document.querySelectorAll<HTMLElement>('[contenteditable="true"]');

  // 过滤掉不可见元素
  const visibleFilter = (el: HTMLElement): boolean => {
    // 检查元素是否可见
    const style = window.getComputedStyle(el);
    return (
      style.display !== "none" &&
      style.visibility !== "hidden" &&
      style.opacity !== "0" &&
      el.offsetWidth > 0 &&
      el.offsetHeight > 0
    );
  };

  Array.from(inputs)
    .filter(visibleFilter)
    .forEach((el) => elements.push(el));
  Array.from(textareas)
    .filter(visibleFilter)
    .forEach((el) => elements.push(el));
  Array.from(selects)
    .filter(visibleFilter)
    .forEach((el) => elements.push(el));
  Array.from(editableDivs)
    .filter(visibleFilter)
    .forEach((el) => elements.push(el));

  return elements;
};

// 确定输入元素的类型
export const determineInputType = (element: HTMLElement | null): string => {
  let type = "text";

  if (!element) return type;

  if (element.tagName === "INPUT") {
    type = (element as HTMLInputElement).type || "text";
  } else if (element.tagName === "TEXTAREA") {
    type = "textarea";
  } else if (element.tagName === "SELECT") {
    type = "select";
  } else if (element.getAttribute("contenteditable") === "true") {
    type = "contenteditable";
  }

  return type;
};
