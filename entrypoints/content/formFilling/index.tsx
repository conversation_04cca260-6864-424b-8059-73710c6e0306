import React, { useCallback, useEffect, useRef, useState } from "react";
import SuggestionCard from "./components/SuggestionCard";
import { determineInputType, getAllInputElements } from "./utils";

// 定义一个类型安全的、带清理功能的防抖 hook
function useDebouncedCallback<T extends any[]>(callback: (...args: T) => void, delay: number) {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 在组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return (...args: T) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  };
}

const FormFillingWrapper = () => {
  const [focusedElement, setFocusedElement] = useState<HTMLElement | null>(null);
  const [cardPosition, setCardPosition] = useState({ top: 0, left: 0 });
  // 新增卡片宽度 state
  const [cardWidth, setCardWidth] = useState<number>(300);
  const [suggestions, setSuggestions] = useState<any[]>([]);
  const [isVisible, setIsVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);

  const activeElementRef = useRef<HTMLElement | null>(null);
  const latestRequestIdRef = useRef(0); // 新增 Ref 用于跟踪最新请求

  /**
   * 分析输入框上下文，为AI生成友好的描述
   * @param element - The input element to analyze.
   * @returns A string describing the element's context.
   */
  const analyzeFieldContext = (element: HTMLElement): string => {
    const fieldType = determineInputType(element as HTMLInputElement);
    const id = element.id;
    const name = (element as HTMLInputElement).name;
    const placeholder = (element as HTMLInputElement).placeholder;

    let labelText = "";
    // 1. 通过 for 属性查找 label
    if (id) {
      const label = document.querySelector(`label[for="${id}"]`);
      if (label) {
        labelText = label.textContent?.trim() || "";
      }
    }

    // 2. 如果没有，查找父级 <label>
    if (!labelText && element.parentElement?.tagName === "LABEL") {
      labelText = element.parentElement.textContent?.trim() || "";
    }

    // 3. 查找 aria-label
    const ariaLabel = element.getAttribute("aria-label");
    if (ariaLabel) {
      labelText = ariaLabel;
    }

    // 4. 作为最后的手段，查找相邻的文本节点或元素
    if (!labelText) {
      let sibling = element.previousElementSibling;
      if (sibling && sibling.tagName !== "SCRIPT" && sibling.tagName !== "STYLE") {
        labelText = sibling.textContent?.trim() || "";
      }
    }

    let context = `这是一个类型为 '${fieldType}' 的输入框。`;
    if (labelText) context += ` 它的标签是 '${labelText}'。`;
    if (placeholder) context += ` 它的提示文本是 '${placeholder}'。`;
    if (name) context += ` 它的 name 属性是 '${name}'。`;
    if (id) context += ` 它的 id 属性是 '${id}'。`;

    return context.trim();
  };

  /**
   * 从背景脚本获取 Dify 建议
   */
  const fetchSuggestions = async (element: HTMLElement) => {
    if (!element) return;

    const requestId = ++latestRequestIdRef.current; // 为当前请求分配唯一ID

    setIsLoading(true);
    setSuggestions([]); // 清空旧建议
    updateCardPosition(element);
    setIsVisible(true);

    const fieldContext = analyzeFieldContext(element);
    const fieldType = determineInputType(element as HTMLInputElement);
    const pageContext = document.title;

    try {
      const response = await browser.runtime.sendMessage({
        type: "getDifySuggestions",
        payload: {
          fieldContext,
          fieldType,
          pageContext,
        },
      });

      // 在更新状态前，检查此请求是否仍然是最新的
      if (requestId !== latestRequestIdRef.current) {
        return; // 如果不是最新请求，则忽略其结果
      }

      if (response.status === "success") {
        setSuggestions(response.suggestions);
      } else {
        console.error("Failed to get suggestions:", response.message);
        setSuggestions([]);
      }
    } catch (error) {
      console.error("Error sending message to background script:", error);
      setSuggestions([]);
    } finally {
      // 同样，在更新状态前，检查此请求是否仍然是最新的
      if (requestId === latestRequestIdRef.current) {
        setIsLoading(false);
      }
    }
  };

  const debouncedFetchSuggestions = useDebouncedCallback(fetchSuggestions, 300);

  // 更新卡片位置
  const updateCardPosition = useCallback((element: HTMLElement) => {
    const rect = element.getBoundingClientRect();
    let top = rect.bottom + window.scrollY;
    let left = rect.left + window.scrollX;
    setCardPosition({ top, left });
    setCardWidth(rect.width); // 新增：设置宽度
  }, []);

  // 新增：检查元素是否适合触发建议
  const isEligibleForSuggestions = (element: HTMLElement): boolean => {
    // 1. 必须是输入框或文本域
    if (!["INPUT", "TEXTAREA"].includes(element.tagName)) {
      return false;
    }

    const inputElement = element as HTMLInputElement;

    // 2. 如果是只读的，则不触发
    if (inputElement.readOnly) {
      return false;
    }

    // 3. 检查父元素中是否有表明其为特殊组件的线索 (如 element-ui 的 el-select)
    // 这是一个比较通用的检查，可以扩展以适应更多库
    let parent = element.parentElement;
    for (let i = 0; i < 5 && parent; i++) {
      // 向上查找5层
      const className = parent.className || "";
      if (
        typeof className === "string" &&
        (className.includes("select") || className.includes("dropdown") || className.includes("picker")) // 例如 date-picker
      ) {
        return false;
      }
      parent = parent.parentElement;
    }

    return true;
  };

  // 处理交互事件
  const handleElementInteraction = useCallback(
    (event: Event) => {
      const element = event.target as HTMLElement;
      if (element.closest("#input-helper-extension-root")) return;

      // 在触发逻辑前，先检查元素是否合格
      if (isEligibleForSuggestions(element)) {
        activeElementRef.current = element;
        setFocusedElement(element);
        debouncedFetchSuggestions(element);
      }
    },
    [debouncedFetchSuggestions],
  );

  // 插入内容到输入元素
  const insertContent = useCallback((content: any) => {
    console.log("insertContent", content);
    // 使用 ref 来确保我们操作的是最后一次交互的元素，而不是依赖可能已经改变的 state
    const elementToFill = activeElementRef.current;
    if (!elementToFill) return;

    const input = elementToFill as HTMLInputElement;
    input.value = content;
    // 触发一个输入事件，让React等框架能够感知到变化
    input.dispatchEvent(new Event("input", { bubbles: true }));

    setIsVisible(false);
    setSuggestions([]);
  }, []); // 移除对 focusedElement 的依赖

  // 处理键盘事件
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (!isVisible) return;

      if (event.key === "ArrowDown") {
        event.preventDefault();
        setSelectedIndex((prev) => (prev + 1) % suggestions.length);
      } else if (event.key === "ArrowUp") {
        event.preventDefault();
        setSelectedIndex((prev) => (prev - 1 + suggestions.length) % suggestions.length);
      } else if (event.key === "Enter") {
        event.preventDefault();
        if (suggestions[selectedIndex]) {
          insertContent(suggestions[selectedIndex]);
        }
      } else if (event.key === "Escape") {
        setIsVisible(false);
      }
    },
    [isVisible, suggestions, selectedIndex, insertContent],
  );

  // 点击外部关闭卡片
  const handleClickOutside = useCallback((event: MouseEvent) => {
    // 使用 event.composedPath() 来正确处理 Shadow DOM 中的点击事件
    const path = event.composedPath();
    const clickedElement = path[0] as HTMLElement;

    // 如果点击路径中包含 suggestion-card，或者点击的是当前激活的输入框，则不关闭
    const isInsideCard = path.some((el) => el instanceof HTMLElement && el.classList.contains("suggestion-card"));
    const isInsideActiveInput = clickedElement.closest("[data-smartfill-active]");

    if (!isInsideCard && !isInsideActiveInput) {
      setIsVisible(false);
    }
  }, []);

  useEffect(() => {
    // ---- START: MutationObserver Logic ----
    const handleMutation = (mutations: MutationRecord[]) => {
      mutations.forEach((mutation) => {
        if (mutation.type === "childList" && mutation.addedNodes.length > 0) {
          mutation.addedNodes.forEach((node) => {
            if (node instanceof HTMLElement) {
              // 检查新增节点本身是不是输入框
              if (["INPUT", "TEXTAREA", "SELECT"].includes(node.tagName)) {
                node.addEventListener("focus", handleElementInteraction);
              }
              // 检查新增节点内部是否包含输入框
              node.querySelectorAll("input, textarea, select").forEach((input) => {
                input.addEventListener("focus", handleElementInteraction);
              });
            }
          });
        }
      });
    };

    const observer = new MutationObserver(handleMutation);
    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });
    // ---- END: MutationObserver Logic ----

    // 初始扫描
    const inputs = getAllInputElements();
    inputs.forEach((input) => {
      input.addEventListener("focus", handleElementInteraction);
    });

    document.addEventListener("keydown", handleKeyDown);
    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      observer.disconnect(); // 清理 observer
      inputs.forEach((input) => {
        input.removeEventListener("focus", handleElementInteraction);
      });
      document.removeEventListener("keydown", handleKeyDown);
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [handleElementInteraction, handleKeyDown, handleClickOutside]);

  // 当 focusedElement 变化时，给它添加一个特殊属性
  useEffect(() => {
    document.querySelectorAll("[data-smartfill-active]").forEach((el) => {
      el.removeAttribute("data-smartfill-active");
    });
    if (focusedElement && isVisible) {
      focusedElement.setAttribute("data-smartfill-active", "true");
    }
  }, [focusedElement, isVisible]);

  if (!isVisible) return null;

  return (
    <SuggestionCard
      position={cardPosition}
      suggestions={suggestions}
      selectedIndex={selectedIndex}
      onSelect={insertContent}
      onClose={() => setIsVisible(false)}
      isLoading={isLoading}
      cardWidth={cardWidth} // 新增
    />
  );
};

export default FormFillingWrapper;
