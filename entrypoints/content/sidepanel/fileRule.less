.agent-output-container-file {
  .file-rule-modal {
    .report-section {
      margin-bottom: 24px;
    }

    .report-section h3 {
      font-size: 16px;
      font-weight: 600;
      color: #1890ff;
      border-left: 3px solid #1890ff;
      padding-left: 8px;
    }
    .ant-modal-body {
      max-height: 1000px;
      overflow-y: auto;
      .file-rule-info {
        margin-bottom: 20px;
        border: none;
        background: none !important;
        border-radius: 0px;
        .ant-collapse-content {
          border-top: 1px solid #ebeef5;
        }
        .ant-collapse-content-box {
          padding: 0px !important;
          p {
            margin: 0px;
            padding: 0px;
            min-height: 24px;
            line-height: 24px;
            padding: 20px 20px 20px 20px;
          }
        }
        .ant-collapse-item {
          background: #f5f7fa;
          margin-bottom: 20px;
          border-radius: 4px;
          border: 1px solid #ebeef5;
        }
      }
      .file-rule-table {
        .ant-collapse-item:last-child {
          border-bottom: 0px;
        }
      }
    }
    table {
      width: 100%;
      border: none;
      border-spacing: 0px;
      margin-top: 12px;
      thead {
        th {
          background: #f5f7fa;
          font-weight: 500;
          padding: 10px;
          border-bottom: 1px solid #ebeef5;
        }
        th:nth-child(1) {
          color: #409eff;
        }
      }
      tbody {
        td {
          padding: 10px;
          color: #606266;
          border-bottom: 1px solid #ebeef5;
        }
        td:nth-child(1) {
          color: #409eff;
        }
      }
    }
    /* 商务合同校验样式 */
    .business-check-panel {
      padding: 8px;
      background-color: #fff;
      border-radius: 6px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
      margin-bottom: 12px;

      /* 部分标题样式 - 更紧凑 */
      .section {
        margin-bottom: 16px;
      }

      .section-title {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        color: #1890ff;
        font-size: 14px;
        border-bottom: 1px solid #f0f0f0;
        padding-bottom: 6px;
      }

      .section-title .icon {
        margin-right: 6px;
        font-size: 16px;
      }

      /* Markdown内容样式 - 更紧凑 */
      .markdown-content {
        padding: 8px;
        background-color: #f9f9f9;
        border-radius: 4px;
        border-left: 3px solid #1890ff;
        font-size: 13px;
        line-height: 1.5;
      }

      /* 表格样式 - 更紧凑 */
      .table-content {
        overflow-x: auto;
      }

      .styled-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 12px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      .styled-th,
      .styled-td {
        padding: 8px 10px;
        text-align: left;
        border: 1px solid #e8e8e8;
        font-size: 13px;
      }

      .styled-th {
        background-color: #f5f5f5;
        font-weight: 600;
      }

      /* 修复1: 增加判定列的宽度 */
      .styled-table td:nth-child(4) {
        width: 90px; /* 增加判定列的宽度 */
        min-width: 90px;
        max-width: 120px;
      }

      /* 状态图标样式 */
      .status-icon {
        margin-right: 6px;
        font-size: 14px;
      }

      .status-icon.success {
        color: #52c41a;
      }

      .status-icon.error {
        color: #ff4d4f;
      }

      .status-icon.warning {
        color: #faad14;
      }

      /* 状态单元格样式 */
      .styled-td.success {
        background-color: #f6ffed;
        border-left: 3px solid #52c41a;
      }

      .styled-td.error {
        background-color: #fff2f0;
        border-left: 3px solid #ff4d4f;
      }

      .styled-td.warning {
        background-color: #fffbe6;
        border-left: 3px solid #faad14;
      }

      /* 面板头部样式 - 更紧凑 */
      .panel-header {
        display: flex;
        align-items: center;
      }

      .file-icon {
        margin-right: 6px;
        color: #1890ff;
        font-size: 14px;
      }

      .file-name {
        font-weight: 500;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 200px;
        font-size: 13px;
      }

      /* 修复2: 确保风险与整改建议内容完整显示 */
      .markdown-content {
        white-space: pre-wrap; /* 保留换行符 */
        word-break: break-word; /* 长单词换行 */
      }
    }
  }
  // 合同校验表格样式
  .subTitle {
    color: #2c3e50;
    font-size: 14px;
    margin: 0 0 10px 10px;
    display: inline-block;
  }
  .contract-check-container {
    .markdown-content {
      .contract-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 30px;
        font-size: 14px;

        .contract-thead {
          background-color: #f0f5ff;

          .contract-th {
            text-align: left;
            font-weight: 700;
            color: #2c3e50;
            border: 1px solid #dcdfe6;
            font-size: 16px;

            &:not(:last-child)::after {
              content: "";
              position: absolute;
              right: 0;
              top: 50%;
              transform: translateY(-50%);
              height: 16px;
              width: 1px;
              background-color: #d9d9d9;
            }
          }
          .contract-th:first-child {
            width: 15%;
          }
          .contract-th:last-child {
            width: 15%;
          }
        }

        .contract-tr {
          border-bottom: 1px solid #e8e8e8;

          &:nth-child(even) {
            background-color: #f7fafc;
          }

          // &:hover {
          //   background-color: #f0f6ff;
          // }

          .contract-td {
            padding: 16px 12px;
            color: #333333;
            line-height: 1.6;
            border: 1px solid #e8e8e8;
          }
          .contract-td:first-child {
            span {
              font-weight: 700;
              color: #2c3e50;
              font-size: 16px;
            }
          }
          .contract-td:last-child {
            &.inconsistent {
              color: #f5222d;
              font-weight: 500;
              position: relative;
              padding-left: 35px;

              &::before {
                content: "×";
                position: absolute;
                left: 12px;
                top: 50%;
                transform: translateY(-50%);
                width: 16px;
                height: 16px;
                background-color: #f5222d;
                color: #ffffff;
                border-radius: 2px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                font-weight: bold;
              }
            }
            &.consistent {
              color: green;
              font-weight: 500;
              position: relative;
              padding-left: 35px;

              &::before {
                content: "✓";
                position: absolute;
                left: 12px;
                top: 50%;
                transform: translateY(-50%);
                width: 16px;
                height: 16px;
                background-color: green;
                color: #ffffff;
                border-radius: 2px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                font-weight: bold;
              }
            }
            &.undetermined {
              color: #faad14; // 警告色
              font-weight: 500;
              position: relative;
              padding-left: 35px;

              &::before {
                content: "?";
                position: absolute;
                left: 12px;
                top: 50%;
                transform: translateY(-50%);
                width: 16px;
                height: 16px;
                background-color: #faad14;
                color: #ffffff;
                border-radius: 2px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                font-weight: bold;
              }
            }
          }
        }
      }
    }

    .final-result {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      background-color: #fff0f0;
      border: 1px solid #ffccc7;
      border-radius: 4px;
      display: flex;
      align-items: center;

      .final-result-label {
        color: #f5222d;
        margin-right: 8px;
        font-size: 16px;
        font-weight: 700;
      }

      .final-result-value {
        font-size: 14px;
        font-weight: 700;
        padding: 4px 8px;
        background-color: #f5222d;
        color: #ffffff;
        border-radius: 5px;
        position: relative;
        padding-left: 24px;
        margin-right: 28px;

        &::before {
          content: "×";
          position: absolute;
          left: 8px;
          top: 50%;
          transform: translateY(-50%);
          width: 14px;
          height: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          font-weight: bold;
        }
      }
    }
    .final-result2 {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      background-color: #e0f2e0;
      border: 1px solid green;
      border-radius: 4px;
      display: flex;
      align-items: center;

      .final-result-label2 {
        color: green;
        margin-right: 8px;
        font-size: 16px;
        font-weight: 700;
      }

      .final-result-value2 {
        font-size: 14px;
        font-weight: 700;
        padding: 4px 8px;
        background-color: green;
        color: #ffffff;
        border-radius: 5px;
        position: relative;
        padding-left: 24px;
        margin-right: 28px;

        &::before {
          content: "√";
          position: absolute;
          left: 8px;
          top: 50%;
          transform: translateY(-50%);
          width: 14px;
          height: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          font-weight: bold;
        }
      }
    }
    .final-result3 {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      background-color: #fceed2;
      border: 1px solid #fcf2dd;
      border-radius: 4px;
      display: flex;
      align-items: center;

      .final-result-label3 {
        color: #faad14;
        margin-right: 8px;
        font-size: 16px;
        font-weight: 700;
      }

      .final-result-value3 {
        font-size: 14px;
        font-weight: 700;
        padding: 4px 8px;
        background-color: #faad14;
        color: #ffffff;
        border-radius: 5px;
        position: relative;
        padding-left: 24px;
        margin-right: 28px;

        &::before {
          content: "?";
          position: absolute;
          left: 8px;
          top: 50%;
          transform: translateY(-50%);
          width: 14px;
          height: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          font-weight: bold;
        }
      }
    }
  }
}
