import ReactDOM from "react-dom/client";
import SidePanelWrapper from "@/entrypoints/content/sidepanel/wrapper";
import styles from "./index.module.less";
import CreateNote from "@/components/ContextMenus/CreateNote/index";
import { ConfigProvider } from "antd";
import { StyleProvider } from "@ant-design/cssinjs";
import themeToken from "@/theme.json";
// 引入全局样式
import "@/assets/styles/index.less";

export const SIDE_PANEL_WRAPPER_ID = "sino-assistant-crx-web-app";
export const SHADOW_SIDE_PANEL = "shadow-side-panel";

const getStyleContent = () => {
  // @ts-expect-error: 引用路径
  return fetch(browser.runtime.getURL("/content-scripts/content.css"))
    .then((response) => {
      if (response.ok) {
        return response.text();
      }
      throw new Error("无法加载样式文件");
    })
    .catch((error) => {
      console.error(error);
      return Promise.resolve(""); // 返回空样式或错误处理
    });
};

/** 将侧边栏显然到主页面 */
const createSidePanelWrapper = async () => {
  const style = document.createElement("style");
  await getStyleContent().then((data) => {
    style.textContent = data;
  });
  const shadowDom = document.createElement("shadow-dom");
  shadowDom.id = SHADOW_SIDE_PANEL;
  shadowDom.style.position = "relative";
  shadowDom.style.zIndex = (2 ** 32 - 1).toString();
  const shadowRoot = shadowDom.attachShadow({ mode: "open" });
  // shadowRoot.appendChild(antdStyle);
  shadowRoot.appendChild(style);
  const wrapper = document.createElement("div");
  wrapper.id = SIDE_PANEL_WRAPPER_ID;
  wrapper.className = styles["sino-assistant-web-app"];
  shadowRoot.appendChild(wrapper);
  ReactDOM.createRoot(wrapper).render(
    <StyleProvider hashPriority="high" container={shadowRoot}>
      <ConfigProvider theme={themeToken}>
        <SidePanelWrapper container={wrapper} />
        <CreateNote />
      </ConfigProvider>
    </StyleProvider>,
  );
  document.body.parentNode.appendChild(shadowDom);
};

export default createSidePanelWrapper;
