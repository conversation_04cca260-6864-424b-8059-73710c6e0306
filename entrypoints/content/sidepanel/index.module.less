@import "@/assets/styles/variables";

.sino-assistant-web-app {
  /* 注入DOM根容器 */
  width: 400px;
  height: 100vh;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 10000 !important;
  box-shadow: 0 10px 30px 0 rgba(0, 0, 0, 0.2);
  border-radius: 5px 0 0 5px;
  transform: translateX(400px);
  transition: transform 0.2s;
  background: #fff;

  &.expand {
    transform: translateX(0);
  }

  .toggle-btn-wrapper {
    /** 侧边栏伸出按钮容器 */
    position: absolute;
    user-select: none;
    z-index: 10;
    .toggle-btn,
    .toggle-btn-expand {
      background: var(--ant-color-bg-container);
      width: 49px;
      transform: translateX(-49px);
      /** 侧边栏切换按钮 */
      cursor: pointer;
      box-sizing: border-box;
      border: none;
      // padding: 10px 10px 10px 14px;
      position: absolute;
      top: 50px;
      z-index: 10000 !important;
      border-radius: var(--ant-border-radius-lg) 0px 0px var(--ant-border-radius-lg);
      display: flex;
      justify-content: center;
      align-items: center;
      height: 40px;
      box-shadow:
        0px 2px 4px -1px rgba(0, 0, 0, 0.12),
        0px 4px 5px 0px rgba(0, 0, 0, 0.08),
        0px 1px 10px 0px rgba(0, 0, 0, 0.05);
      &:hover {
        background: @gradientPrimary;
      }
      .btn-wrapper-img {
        position: absolute;
        bottom: 0;
        width: 36px;
        height: 38px;
        margin-left: 5px;
      }
      &.dragging {
        border-radius: 50%;
        opacity: 0.8;
      }

      &.expand {
        /* 侧边栏展开时，拖拽按钮的样式 */
        background-color: #fff;
      }
    }
    .toggle-btn-expand {
      background: #f7f7f7 !important;
      cursor: pointer;
      box-shadow: -7px 0px 8px -3px rgba(0, 0, 0, 0.12);
      svg path {
        fill: var(--ant-color-text);
      }
      &:hover {
        cursor: pointer;
        box-shadow: -7px 0px 8px -3px rgba(0, 0, 0, 0.12);
      }
    }
  }
}

