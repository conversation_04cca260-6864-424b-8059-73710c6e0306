import React from "react";
import { Modal, Button } from "antd";
import "./ContractInfoModal.less";

interface ContractInfoModalProps {
  visible: boolean;
  onClose: () => void;
  onReupload: () => void;
}

const ContractInfoModal: React.FC<ContractInfoModalProps> = ({ visible, onClose, onReupload }) => {
  console.log("ContractInfoModal 渲染，visible:", visible);
  
  React.useEffect(() => {
    console.log("ContractInfoModal useEffect - visible 变化:", visible);
    if (visible) {
      // 检查DOM中是否真的有弹窗元素
      setTimeout(() => {
        const modalElements = document.querySelectorAll('.ant-modal, .contract-info-modal');
        console.log('DOM中找到的Modal元素数量:', modalElements.length);
        modalElements.forEach((el, index) => {
          console.log(`Modal元素${index}:`, el, '可见性:', getComputedStyle(el).display, getComputedStyle(el).visibility);
        });
      }, 100);
    }
  }, [visible]);
  
  // 如果visible为true，同时渲染一个简单的div作为测试
  if (visible) {
    console.log('准备渲染ContractInfoModal，visible=true');
  }
  
  // 备用的简单弹窗实现，以防Antd Modal在插件环境下不工作
  if (visible) {
    return (
      <>
        {/* Antd Modal */}
        <Modal
          title="智能填充信息"
          open={visible}
          onCancel={onClose}
          footer={null}
          width={600}
          className="contract-info-modal"
          maskClosable={false}
          getContainer={() => document.body}
          destroyOnClose={true}
          zIndex={10000}
        >
          <div className="contract-info-content">
            <h3>合同关键信息列表</h3>

            <div className="info-list">
              <div className="info-item">
                <span className="label">甲方:</span>
                <span className="value">华鑫国际信托有限公司</span>
              </div>

              <div className="info-item">
                <span className="label">乙方:</span>
                <span className="value">北京同天科技有限公司</span>
              </div>

              <div className="info-item">
                <span className="label">合同金额:</span>
                <span className="value">人民币200,000元（贰拾万元整）</span>
              </div>

              <div className="info-item">
                <span className="label">付款条件:</span>
                <div className="payment-terms">
                  <div className="term-item">
                    <span className="term-label">首付款:</span>
                    <span className="term-value">
                      合同签订后，收到乙方发票后的五个工作日内支付50%（即人民币100,000元）。
                    </span>
                  </div>
                  <div className="term-item">
                    <span className="term-label">尾款:</span>
                    <span className="term-value">
                      项目上线并验收通过后，收到乙方发票后的五个工作日内支付剩余50%（即人民币100,000元）。
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="modal-actions">
              <Button onClick={onClose} style={{ marginRight: 8 }}>
                取消
              </Button>
              <Button type="primary" onClick={onReupload} style={{ marginRight: 8 }}>
                重新上传
              </Button>
              <Button type="primary">填充</Button>
              <Button type="link" style={{ padding: 0, marginLeft: 8 }}>
                收起
              </Button>
            </div>
          </div>
        </Modal>
        
        {/* 备用简单弹窗 */}
        <div 
          style={{
            position: 'fixed',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: '600px',
            maxHeight: '80vh',
            backgroundColor: 'white',
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            boxShadow: '0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
            zIndex: 10001,
            overflow: 'auto'
          }}
          className="backup-contract-modal"
        >
          <div style={{ padding: '16px 24px', borderBottom: '1px solid #f0f0f0', fontSize: '16px', fontWeight: 600 }}>
            智能填充信息 (备用弹窗)
            <button 
              onClick={onClose}
              style={{
                float: 'right',
                border: 'none',
                background: 'none',
                fontSize: '16px',
                cursor: 'pointer',
                color: '#999'
              }}
            >
              ×
            </button>
          </div>
           <div style={{ padding: '24px' }}>
             <h3 style={{ margin: '0 0 20px 0', fontSize: '16px', fontWeight: 600, color: '#262626' }}>合同关键信息列表</h3>

             <div>
               <div style={{ marginBottom: '16px', display: 'flex', alignItems: 'flex-start' }}>
                 <span style={{ minWidth: '80px', fontWeight: 500, color: '#595959', marginRight: '12px' }}>甲方:</span>
                 <span style={{ flex: 1, color: '#262626' }}>华鑫国际信托有限公司</span>
               </div>

               <div style={{ marginBottom: '16px', display: 'flex', alignItems: 'flex-start' }}>
                 <span style={{ minWidth: '80px', fontWeight: 500, color: '#595959', marginRight: '12px' }}>乙方:</span>
                 <span style={{ flex: 1, color: '#262626' }}>北京同天科技有限公司</span>
               </div>

               <div style={{ marginBottom: '16px', display: 'flex', alignItems: 'flex-start' }}>
                 <span style={{ minWidth: '80px', fontWeight: 500, color: '#595959', marginRight: '12px' }}>合同金额:</span>
                 <span style={{ flex: 1, color: '#262626' }}>人民币200,000元（贰拾万元整）</span>
               </div>

               <div style={{ marginBottom: '16px', display: 'flex', alignItems: 'flex-start' }}>
                 <span style={{ minWidth: '80px', fontWeight: 500, color: '#595959', marginRight: '12px' }}>付款条件:</span>
                 <div style={{ flex: 1 }}>
                   <div style={{ marginBottom: '8px', display: 'flex', alignItems: 'flex-start' }}>
                     <span style={{ minWidth: '60px', fontWeight: 500, color: '#8c8c8c', marginRight: '8px', fontSize: '14px' }}>首付款:</span>
                     <span style={{ flex: 1, color: '#595959', fontSize: '14px', lineHeight: 1.5 }}>
                       合同签订后，收到乙方发票后的五个工作日内支付50%（即人民币100,000元）。
                     </span>
                   </div>
                   <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                     <span style={{ minWidth: '60px', fontWeight: 500, color: '#8c8c8c', marginRight: '8px', fontSize: '14px' }}>尾款:</span>
                     <span style={{ flex: 1, color: '#595959', fontSize: '14px', lineHeight: 1.5 }}>
                       项目上线并验收通过后，收到乙方发票后的五个工作日内支付剩余50%（即人民币100,000元）。
                     </span>
                   </div>
                 </div>
               </div>
             </div>

             <div style={{ marginTop: '32px', paddingTop: '20px', borderTop: '1px solid #f0f0f0', display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
               <button onClick={onClose} style={{ marginRight: '8px', padding: '4px 15px', border: '1px solid #d9d9d9', borderRadius: '6px', background: 'white', cursor: 'pointer' }}>
                 取消
               </button>
               <button onClick={onReupload} style={{ marginRight: '8px', padding: '4px 15px', border: '1px solid #1890ff', borderRadius: '6px', background: '#1890ff', color: 'white', cursor: 'pointer' }}>
                 重新上传
               </button>
               <button style={{ marginRight: '8px', padding: '4px 15px', border: '1px solid #1890ff', borderRadius: '6px', background: '#1890ff', color: 'white', cursor: 'pointer' }}>填充</button>
               <button style={{ padding: 0, marginLeft: '8px', border: 'none', background: 'none', color: '#1890ff', cursor: 'pointer' }}>收起</button>
             </div>
           </div>
         </div>
       </>
     );
   }
   
   return null;
};

export default ContractInfoModal;
