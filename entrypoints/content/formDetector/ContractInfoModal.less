.contract-info-modal {
  .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 24px;
  }

  .ant-modal-body {
    padding: 24px;
  }

  .contract-info-content {
    h3 {
      margin: 0 0 20px 0;
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }

    .info-list {
      .info-item {
        margin-bottom: 16px;
        display: flex;
        align-items: flex-start;

        .label {
          min-width: 80px;
          font-weight: 500;
          color: #595959;
          margin-right: 12px;
        }

        .value {
          flex: 1;
          color: #262626;
        }

        .payment-terms {
          flex: 1;

          .term-item {
            margin-bottom: 8px;
            display: flex;
            align-items: flex-start;

            &:last-child {
              margin-bottom: 0;
            }

            .term-label {
              min-width: 60px;
              font-weight: 500;
              color: #8c8c8c;
              margin-right: 8px;
              font-size: 14px;
            }

            .term-value {
              flex: 1;
              color: #595959;
              font-size: 14px;
              line-height: 1.5;
            }
          }
        }
      }
    }

    .modal-actions {
      margin-top: 32px;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }
}

// 确保弹窗在最顶层
.contract-info-modal.ant-modal {
  z-index: 10000;
}

.contract-info-modal .ant-modal-mask {
  z-index: 9999;
}