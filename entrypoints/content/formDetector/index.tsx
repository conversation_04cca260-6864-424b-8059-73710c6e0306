import React, { useCallback, useEffect, useState, useRef } from "react";
import { Button, message, ConfigProvider } from "antd";
import { useFetchRequest } from "@/hooks/useFetchRequest";
import MentionsComponent, { MentionsComponentRef } from "@/components/FormDetectorMentions";
import { useGetState } from "ahooks";
import { getUserInfo, UserInfo, getToken, getTenantId } from "@/utils/auth.ts";
import { PermissionProvider } from "@/entrypoints/sidepanel/components/PermissionProvider";
import { cacheGet, cacheSet } from "@/utils/browserStorage";
import useSSEChat, { GenerationProgress } from "@/hooks/useSSEChat.ts";
import { extractFormData as extractFormDataUtil } from "@/utils/formDataExtractor";
import { getFormElementLabel } from "@/utils/frameworkFormDetector";
import ContractInfoModal from "./ContractInfoModal";
import "./index.less";

interface FormDetectorProps {
  onClose?: () => void;
  shouldExtractData?: boolean;
  onDataExtracted?: () => void;
  onResetStatus?: () => void; // 新增：重置状态的回调
}

interface FormField {
  name: string;
  type: string;
  value: string;
  placeholder?: string;
  required?: boolean;
}

interface ExtractedFormData {
  fields: FormField[];
  formAction?: string;
  formMethod?: string;
}

type StateType = {
  selectedTab: string;
  content: string | undefined;
  bearer: string;
};
const xinqiaohetongkey = "app-tnAp2ScwikjBhlTT6A4shV2h";
const agentxinqiaoid = "aa139b2b-3563-4e64-94c2-204c336fc881";
const FormDetector: React.FC<FormDetectorProps> = ({ onClose, shouldExtractData, onDataExtracted, onResetStatus }) => {
  const [formData, setFormData] = useState<any>(null);
  const [isExtracting, setIsExtracting] = useState<boolean>(false);
  const [isFilling, setIsFilling] = useState<boolean>(false);
  const [userInfo, setUserInfo] = useState<UserInfo>({});
  const [bearerList, setBearerList] = useState<any[]>([]);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const mentionsRef = useRef<MentionsComponentRef>(null);
  const panelRef = useRef<HTMLDivElement>(null);
  const [selectedfrom, setSelectedfrom] = useState<string>("0"); //默认是知识库
  const [localFileUploadResData, setLocalFileUploadResData] = useState<any>(null); //本地文件上传返回数据
  const [contractInfoVisible, setContractInfoVisible] = useState<boolean>(false); //合同信息弹窗显示状态
  const fetchRequest = useFetchRequest();

  // 监听合同信息弹窗状态变化
  useEffect(() => {
    console.log("contractInfoVisible 状态变化:", contractInfoVisible);
  }, [contractInfoVisible]);
  // SSE会话Hook
  const sseChat = useSSEChat();

  // 监听SSE状态变化，处理错误情况和完成状态
  useEffect(() => {
    if (isFilling) {
      if (sseChat.progress === GenerationProgress.ERROR) {
        console.error("SSE连接发生错误");
        setIsFilling(false);
        message.error("智能填充处理失败，请重试！");
      } else if (sseChat.progress === GenerationProgress.USER_CANCELED) {
        console.log("用户取消了填充操作");
        setIsFilling(false);
        message.info("已取消智能填充操作");
      } else if (sseChat.progress === GenerationProgress.RENDER_FINISHED) {
        console.log("SSE渲染完成，确保状态恢复");
        // 延迟一点时间确保所有处理都完成
        setTimeout(() => {
          setIsFilling(false);
        }, 200);
      }
    }
  }, [sseChat.progress, isFilling]);

  // 额外的安全机制：监听SSE的isRendering状态
  useEffect(() => {
    if (isFilling && !sseChat.isRendering && sseChat.progress === GenerationProgress.RENDER_FINISHED) {
      // 如果填充状态为true，但SSE已经不在渲染且已完成，强制恢复状态
      console.log("检测到SSE已完成但填充状态未恢复，强制恢复状态");
      setTimeout(() => {
        setIsFilling(false);
      }, 300);
    }
  }, [isFilling, sseChat.isRendering, sseChat.progress]);
  const [state, setState] = useState<StateType>({
    selectedTab: "1",
    content: "",
    bearer: "gpt-4o",
  });

  const [getState, setGetState] = useGetState(state);

  // 处理状态变化
  const handleChange = useCallback(
    (value: any, key: string) => {
      const newState = { ...state, [key]: value };
      setState(newState);
      setGetState(newState);
    },
    [state, setGetState],
  );

  // 处理MentionsComponent的查询变化
  const handleQueryChange = useCallback(
    (val: string) => {
      handleChange(val, "content");
    },
    [handleChange],
  );

  // 获取MentionsComponent的数据
  const getMentionsData = useCallback(() => {
    return (
      mentionsRef.current?.getMentionsData() || {
        query: [],
      }
    );
  }, []);

  // 清空MentionsComponent的数据
  const clearMentionsData = useCallback(() => {
    mentionsRef.current?.setMentionsData({
      query: [],
      localFile: [],
      selectKnowledgeArr: [],
      selectedKnowledgeItem: null,
    });
  }, []);

  // 拖拽处理函数
  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      if (e.target === e.currentTarget || (e.target as HTMLElement).classList.contains("drag-handle")) {
        setIsDragging(true);
        setDragStart({
          x: e.clientX - position.x,
          y: e.clientY - position.y,
        });
        e.preventDefault();
      }
    },
    [position],
  );

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (isDragging) {
        const newX = e.clientX - dragStart.x;
        const newY = e.clientY - dragStart.y;

        // 限制面板不超出屏幕边界
        const maxX = window.innerWidth - 400; // 面板宽度
        const maxY = window.innerHeight - 100; // 留一些边距

        setPosition({
          x: Math.max(0, Math.min(newX, maxX)),
          y: Math.max(0, Math.min(newY, maxY)),
        });
      }
    },
    [isDragging, dragStart],
  );

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // 初始化面板位置到屏幕中间
  useEffect(() => {
    const centerPanel = () => {
      const screenWidth = window.innerWidth;
      const screenHeight = window.innerHeight;
      const panelWidth = 400; // 面板宽度
      const panelHeight = 600; // 估计面板高度

      setPosition({
        x: (screenWidth - panelWidth) / 2,
        y: (screenHeight - panelHeight) / 2,
      });
    };

    centerPanel();
    window.addEventListener("resize", centerPanel);
    return () => window.removeEventListener("resize", centerPanel);
  }, []);
  // 获取用户信息
  useEffect(() => {
    getUserInfo().then((res) => {
      setUserInfo(res);
    });

    // 获取 bearer 列表
    const initBearerList = async () => {
      try {
        const token = await getToken();
        const tenantId = await getTenantId();
        if (token && tenantId) {
          fetchRequest({
            api: "getAgentList",
            params: {
              path: "/api/v1/agent/list",
            },
            callback: (res: any) => {
              if (res?.data) {
                setBearerList(res.data);
                if (res.data.length > 0) {
                  setState((prev) => ({ ...prev, bearer: res.data[0].id }));
                }
              }
            },
          });
        }
      } catch (error) {
        console.error("Failed to fetch bearer list:", error);
      }
    };

    initBearerList();
  }, []);

  // 提取表单数据的核心函数
  const extractFormData = useCallback(() => {
    // 使用统一的表单数据提取器
    const allFormData = extractFormDataUtil("formDetector");

    console.log("📋 表单数据提取完成", {
      totalForms: allFormData.length,
      formTypes: allFormData.map((form) => form.formType),
      totalFields: allFormData.reduce((sum, form) => sum + form.fields.length, 0),
    });

    return {
      allFormData,
    };
  }, []);

  // 智能填充表单数据
  const handleFillFormData = useCallback(async (): Promise<void> => {
    // 获取MentionsComponent的数据
    const mentionsData = getMentionsData();
    //拿到选的那个知识id
    const selectedKnowledgeItemId = mentionsData.selectedKnowledgeItem?.baseFileId || null;

    //暂时去掉
    if (state.content == "" && !localFileUploadResData && !selectedKnowledgeItemId) {
      message.warning("请先输入问题或上传文件或选择知识！");
      return;
    }
    if (isFilling) return;

    setIsFilling(true);
    message.info("正在智能填充表单数据，请稍候...");

    // 重置已填充字段记录，确保流式填充能够正常工作
    filledFieldsRef.current.clear();
    lastProcessedLengthRef.current = 0;
    lastFieldEndPositionRef.current = 0;
    console.log("🔄 开始智能填充，已重置填充状态");

    try {
      // 从缓存获取数据
      // const cachedData = await cacheGet("formJsonData");
      // if (!cachedData) {
      //   message.warning("未找到缓存的表单数据，请先提取表单数据！");
      //   setIsFilling(false);
      //   return;
      // }

      // const formData = cachedData;
      // console.log("从缓存获取的表单数据：", formData);

      const token = await getToken();
      const tenantId = await getTenantId();
      //提取当前的页面表单
      const extractedData = extractFormData();
      console.log("填充：--- 当前页面的表单数据", extractedData);
      //将当前的表单数据转成json
      const extractedDataJson = JSON.stringify(extractedData, null, 2);
      console.log("填充：--- 当前页面的表单数据json", extractedDataJson);
      // 构建文件数据

      console.log("mentionsData", mentionsData);
      const key = "app-d5xgC6KSBwgHELd608dTUfyb";
      const agentid = "f66c4735-5abd-4a1d-abd7-2e269098a8bb";

      // 使用SSE流式处理
      sseChat.start({
        url: "/dify/broker/agent/stream",
        headers: {
          "Content-Type": "application/json",
          Token: token || "",
          // Token: "06e7ef55-783b-4c67-aebe-9aa1ffa44375",
        },
        body: {
          insId: "1",
          bizType: "app:agent",
          bizId: agentxinqiaoid, // 使用state中的bearer作为bizId
          agentId: agentxinqiaoid, // 使用state中的bearer作为agentId
          path: "/chat-messages",
          difyJson: {
            conversation_id: "",

            files: localFileUploadResData ? [localFileUploadResData] : null,
            inputs: {
              kbId: selectedKnowledgeItemId,
              json: extractedDataJson, //填充的json缓存里的
              token: token || "",
              // Token: "06e7ef55-783b-4c67-aebe-9aa1ffa44375",
              tenantid: tenantId || "",
              appKey: xinqiaohetongkey, //agent
              textContent: state.content || "",
              // dataSource: selectedfrom,

              // outputTemplate: {
              //   // 模板信息
              //   type: "document",
              //   transfer_method: "local_file",
              //   upload_file_id: localFileUploadResData.upload_file_id,
              // },

              // extractJson: formData, //提取当前页面的json
            },
            response_mode: "streaming",
            user: userInfo.id || "anonymous",
            query: "1",
            noConversationId: "true",
          },
        },
        query: {},

        onFinished: (result: string) => {
          console.log("SSE流式处理完成，结果：", result);

          // // 更严格的错误检测逻辑 - 只检测明确的错误标识
          // const isError =
          //   result &&
          //   (result.includes("❌") ||
          //     result.includes("发生错误") ||
          //     result.includes("请求失败") ||
          //     result.includes("网络错误") ||
          //     result.includes("服务器错误") ||
          //     result.includes("认证失败") ||
          //     result.includes("权限不足") ||
          //     result.startsWith("错误：") ||
          //     result.startsWith("Error:"));

          // if (isError) {
          //   console.error("智能填充处理失败：", result);

          //   // 根据错误内容提供更具体的错误信息
          //   let errorMessage = "智能填充处理失败！";
          //   if (result.includes("网络") || result.includes("连接")) {
          //     errorMessage = "网络连接失败，请检查网络后重试";
          //   } else if (result.includes("超时")) {
          //     errorMessage = "请求超时，请稍后重试";
          //   } else if (result.includes("认证") || result.includes("登录")) {
          //     errorMessage = "身份验证失败，请重新登录";
          //   } else if (result.includes("权限")) {
          //     errorMessage = "权限不足，请联系管理员";
          //   } else if (result.includes("服务器")) {
          //     errorMessage = "服务器内部错误，请稍后重试";
          //   } else {
          //     errorMessage = `智能填充失败：${result}`;
          //   }

          //   message.error(errorMessage);
          //   setIsFilling(false);

          //   // 清理相关状态
          //   clearMentionsData();
          //   setLocalFileUploadResData(null);
          // } else {
          //   // 成功情况
          //   console.log("智能填充成功，开始处理结果...");
          //   message.success("智能填充完成！");

          //   // 重置填充状态，恢复按钮初始状态
          //   setIsFilling(false);
          //   clearMentionsData();
          //   setLocalFileUploadResData(null);

          //   // 这里可以添加处理成功结果的逻辑
          //   // 例如：解析结果并填充到表单中
          //   try {
          //     // 如果结果是JSON格式，可以解析并填充表单
          //     if (result.trim().startsWith("{") && result.trim().endsWith("}")) {
          //       const formData = JSON.parse(result);
          //       console.log("解析到的表单数据：", formData);
          //       // 这里可以添加自动填充表单的逻辑
          //     }
          //   } catch (parseError) {
          //     console.log("结果不是JSON格式，作为文本处理：", result);
          //   }
          // }
        },
      });
    } catch (error) {
      console.error("智能填充初始化失败：", error);

      // 根据错误类型提供更具体的错误信息
      let errorMessage = "智能填充初始化失败！";
      if (error instanceof Error) {
        if (error.message.includes("token")) {
          errorMessage = "获取认证信息失败，请重新登录";
        } else if (error.message.includes("tenant")) {
          errorMessage = "获取租户信息失败，请联系管理员";
        } else if (error.message.includes("form")) {
          errorMessage = "表单数据提取失败，请刷新页面重试";
        } else {
          errorMessage = `初始化失败：${error.message}`;
        }
      }

      message.error(errorMessage);
      setIsFilling(false);

      // 清理相关状态
      clearMentionsData();
      setLocalFileUploadResData(null);
    }
  }, [isFilling, cacheGet, getMentionsData, state, sseChat, userInfo]);

  // 包装函数，用于安全调用智能填充
  const handleFillFormDataWithResult = useCallback(async () => {
    try {
      console.log("开始智能填充...");
      await handleFillFormData();
      console.log("智能填充调用完成");
    } catch (error) {
      console.error("调用智能填充时发生未预期的错误：", error);
      message.error("调用智能填充时发生未预期的错误");

      // 确保在出错时重置状态
      setIsFilling(false);
      clearMentionsData();
      setLocalFileUploadResData(null);
    }
  }, [handleFillFormData]);

  // 停止SSE流式处理
  const handleStopFilling = useCallback(async () => {
    sseChat.stop(agentxinqiaoid, xinqiaohetongkey, userInfo?.id || "anonymous");
    setIsFilling(false);
    // 完全重置SSE状态
    await sseChat.reset();
    message.info("已停止智能填充处理");
  }, [sseChat, state.bearer, userInfo]);

  // 处理合同按钮点击事件
  const handleProcessContract = useCallback(() => {
    console.log("处理按钮被点击，准备显示合同信息弹窗");
    setContractInfoVisible(true);
    console.log("contractInfoVisible 已设置为 true");
    // 注意：不调用 onClose()，避免关闭整个填充面板
    // 合同信息弹窗应该与主面板共存
  }, []);

  // 关闭合同信息弹窗
  const handleCloseContractInfo = useCallback(() => {
    setContractInfoVisible(false);
  }, []);

  // 重新上传处理
  const handleReupload = useCallback(() => {
    setContractInfoVisible(false);
    // 重新打开智能填充信息弹窗
    // 这里可以添加重新打开主弹窗的逻辑
    message.info("请重新上传文件");
  }, []);

  // 监听SSE流式输出变化，实现动态填充
  // 用于跟踪已填充的字段，避免重复填充
  const filledFieldsRef = useRef<Set<string>>(new Set());
  // 用于存储上次处理的文本长度，实现增量解析
  const lastProcessedLengthRef = useRef<number>(0);
  // 用于记录上次成功解析字段的结束位置，实现顺序填充
  const lastFieldEndPositionRef = useRef<number>(0);

  // 添加防抖处理的时间戳
  const lastProcessTimeRef = useRef<number>(0);
  // 添加数据类型完成状态跟踪
  const dataCompletionStatusRef = useRef<{
    formFields: boolean;
    tableRows: boolean;
    tableMetadata: boolean;
  }>({
    formFields: false,
    tableRows: false,
    tableMetadata: false,
  });

  // 重置完成状态的函数
  const resetCompletionStatus = () => {
    dataCompletionStatusRef.current = {
      formFields: false,
      tableRows: false,
      tableMetadata: false,
    };
    // 同时重置字段解析位置，确保新会话从头开始解析
    lastFieldEndPositionRef.current = 0;
    console.log("🔄 重置数据完成状态和字段解析位置");
  };

  // 检查是否需要重置状态（检测到新会话开始的标记）
  const checkForSessionReset = (text: string) => {
    const sessionResetMarkers = [
      /\[\s*SESSION_START\s*\]|会话开始|session\s*start/i,
      /\[\s*NEW_REQUEST\s*\]|新请求|new\s*request/i,
      /\[\s*RESET\s*\]|重置|reset/i,
    ];

    if (sessionResetMarkers.some((marker) => marker.test(text))) {
      resetCompletionStatus();
      return true;
    }
    return false;
  };
  // 存储已识别的表格元数据和状态
  const tableStatesRef = useRef<
    Map<
      string,
      {
        metadata: any;
        isHighlighted: boolean;
        targetElement: HTMLElement | null;
        processedRows: Set<string>;
      }
    >
  >(new Map());

  // 统一的tableKey生成函数
  const generateTableKey = (metadata: any): string => {
    // 优先使用表头文本组合作为key（与存储时保持一致）
    if (metadata.structuralFingerprint?.headerTexts?.length > 0) {
      return metadata.structuralFingerprint.headerTexts.join("_");
    }

    // 其次使用表格选择器
    if (metadata.tableSelector) {
      return metadata.tableSelector;
    }

    // 再次使用表格ID或名称
    if (metadata.tableId) {
      return metadata.tableId;
    }

    if (metadata.tableName) {
      return metadata.tableName;
    }

    // 最后使用类名
    if (metadata.tableClass) {
      return metadata.tableClass;
    }

    // 默认值
    return "unknown_table";
  };

  // 流式解析表格元数据的函数
  const parseTableMetadataFromStream = (text: string): number => {
    // console.log("🔍 开始解析表格元数据");
    let processedCount = 0;

    // 1. 匹配表格元数据模式
    const metadataPatterns = [
      // 匹配完整的tableMetadata对象 - 更宽松的模式
      /"tableMetadata"\s*:\s*\{[\s\S]*?\}/g,
      // 匹配基本表格标识信息
      /"tableName"\s*:\s*"([^"]+)"/g,
      /"tableId"\s*:\s*"([^"]+)"/g,
      /"tableClass"\s*:\s*"([^"]+)"/g,
    ];

    // 尝试解析完整的表格元数据
    // console.log("🔍 使用的正则表达式：", metadataPatterns[0]);
    // console.log("🔍 检查文本中是否包含 'tableMetadata'：", text.includes("tableMetadata"));

    // 使用更智能的方法来提取tableMetadata对象
    const tableMetadataIndex = text.indexOf('"tableMetadata"');
    // console.log("🔍 tableMetadata索引位置：", tableMetadataIndex);

    if (tableMetadataIndex !== -1) {
      // 找到冒号后的开始大括号
      const colonIndex = text.indexOf(":", tableMetadataIndex);
      if (colonIndex !== -1) {
        const openBraceIndex = text.indexOf("{", colonIndex);
        if (openBraceIndex !== -1) {
          // console.log("🔍 找到开始大括号，位置：", openBraceIndex);

          // 使用更智能的括号计数来找到匹配的结束大括号
          let braceCount = 0;
          let endIndex = openBraceIndex;
          let inString = false;
          let escapeNext = false;

          for (let i = openBraceIndex; i < text.length; i++) {
            const char = text[i];

            if (escapeNext) {
              escapeNext = false;
              continue;
            }

            if (char === "\\") {
              escapeNext = true;
              continue;
            }

            if (char === '"' && !escapeNext) {
              inString = !inString;
              continue;
            }

            if (!inString) {
              if (char === "{") braceCount++;
              if (char === "}") braceCount--;
              if (braceCount === 0) {
                endIndex = i;
                break;
              }
            }
          }

          if (braceCount === 0) {
            const metadataStr = text.substring(openBraceIndex, endIndex + 1);
            // console.log("🎯 提取到完整的tableMetadata对象长度：", metadataStr.length);
            // console.log("🎯 开始位置：", openBraceIndex, "结束位置：", endIndex);
            // console.log(
            //   "🎯 tableMetadata对象预览：",
            //   metadataStr.substring(0, 500) + (metadataStr.length > 500 ? "..." : ""),
            // );

            // 验证JSON格式
            const firstChar = metadataStr.charAt(0);
            const lastChar = metadataStr.charAt(metadataStr.length - 1);
            // console.log("🔍 首字符：", firstChar, "末字符：", lastChar);

            if (firstChar === "{" && lastChar === "}") {
              try {
                const metadataObj = JSON.parse(metadataStr);
                // console.log("✅ JSON解析成功，对象键：", Object.keys(metadataObj));

                // 生成表格唯一标识
                const tableKey =
                  metadataObj.structuralFingerprint?.headerTexts?.join("_") ||
                  metadataObj.tableSelector ||
                  `metadata_${processedCount}`;

                // 检查是否已经处理过这个表格的元数据
                // console.log(`🔍 检查表格键是否存在：${tableKey}，已存在：${tableStatesRef.current.has(tableKey)}`);
                if (!tableStatesRef.current.has(tableKey)) {
                  console.log(`✅ 发现新表格元数据：${tableKey}`);

                  // 立即尝试匹配并高亮表格
                  console.log(`🚀 开始调用 findAndHighlightTableByMetadata`);
                  const targetElement = findAndHighlightTableByMetadata(metadataObj);
                  console.log(`📋 findAndHighlightTableByMetadata 返回结果：`, targetElement);

                  // 存储表格状态
                  tableStatesRef.current.set(tableKey, {
                    metadata: metadataObj,
                    isHighlighted: !!targetElement,
                    targetElement,
                    processedRows: new Set(),
                  });

                  if (targetElement) {
                    console.log(`🎉 表格 ${tableKey} 匹配并高亮成功`);
                    processedCount++;
                  } else {
                    // console.warn(`❌ 表格 ${tableKey} 未找到匹配的DOM元素`);
                  }
                } else {
                  // console.log(`⏭️ 表格键 ${tableKey} 已存在，跳过处理`);
                }
              } catch (error) {
                // console.warn("⚠️ 解析表格元数据失败：", error, metadataStr.substring(0, 100));
              }
            } else {
              // console.warn("⚠️ JSON格式不正确，首字符：", firstChar, "末字符：", lastChar);
            }
          } else {
            // console.warn("⚠️ 未找到匹配的结束大括号");
          }
        } else {
          // console.warn("⚠️ 未找到开始大括号");
        }
      } else {
        // console.warn("⚠️ 未找到冒号");
      }
    }

    // 保留原有的正则表达式方法作为备用
    const metadataPattern = metadataPatterns[0];
    let metadataMatch;
    // console.log("🔄 开始正则表达式匹配循环（备用方法）");
    while ((metadataMatch = metadataPattern.exec(text)) !== null) {
      const metadataStr = metadataMatch[0];
      // console.log("🎯 找到表格元数据：", metadataStr);

      try {
        // 构造完整的JSON对象进行解析
        const fullObjectStr = `{${metadataStr}}`;
        const metadataObj = JSON.parse(fullObjectStr);

        if (metadataObj.tableMetadata) {
          const metadata = metadataObj.tableMetadata;

          // 生成表格唯一标识（使用统一的生成函数）
          const tableKey = generateTableKey(metadata);

          // 检查是否已经处理过这个表格的元数据
          if (!tableStatesRef.current.has(tableKey)) {
            // console.log(`✅ 发现新表格元数据：${tableKey}`);

            // 立即尝试匹配并高亮表格
            const targetElement = findAndHighlightTableByMetadata(metadata);

            // 存储表格状态
            tableStatesRef.current.set(tableKey, {
              metadata,
              isHighlighted: !!targetElement,
              targetElement,
              processedRows: new Set(),
            });

            if (targetElement) {
              console.log(`🎉 表格 ${tableKey} 匹配并高亮成功`);
              processedCount++;
            } else {
              // console.warn(`❌ 表格 ${tableKey} 未找到匹配的DOM元素`);
            }
          }
        }
      } catch (error) {
        // console.warn("⚠️ 解析表格元数据失败：", error, metadataStr);
      }
    }

    // console.log("🔄 正则表达式匹配循环结束，processedCount：", processedCount);

    // 如果没有找到完整元数据，尝试解析基本标识信息
    if (processedCount === 0) {
      processedCount += parseBasicTableIdentifiers(text);
    }

    return processedCount;
  };

  // 解析基本表格标识信息的函数
  const parseBasicTableIdentifiers = (text: string): number => {
    console.log("🔍 解析基本表格标识信息");
    let processedCount = 0;

    // 收集基本标识信息
    const identifiers: { [key: string]: any } = {};

    const patterns = {
      tableName: /"tableName"\s*:\s*"([^"]+)"/g,
      tableId: /"tableId"\s*:\s*"([^"]+)"/g,
      tableClass: /"tableClass"\s*:\s*"([^"]+)"/g,
    };

    // 提取所有标识信息
    Object.entries(patterns).forEach(([key, pattern]) => {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        const value = match[1];
        if (!identifiers[value]) {
          identifiers[value] = { [key]: value };
        } else {
          identifiers[value][key] = value;
        }
      }
    });

    // 为每个标识符组合尝试匹配表格
    Object.entries(identifiers).forEach(([key, identifier]) => {
      // 构造基本元数据
      const basicMetadata = {
        tableSelector: identifier.tableId
          ? `#${identifier.tableId}`
          : identifier.tableClass
            ? `.${identifier.tableClass}`
            : "",
        tableName: identifier.tableName,
        tableId: identifier.tableId,
        tableClass: identifier.tableClass,
      };

      // 使用统一的tableKey生成函数
      const tableKey = generateTableKey(basicMetadata);

      if (!tableStatesRef.current.has(tableKey)) {
        console.log(`✅ 发现基本表格标识：${tableKey}`, identifier);

        // 尝试匹配表格
        const targetElement = findAndHighlightTableByBasicInfo(basicMetadata);

        // 存储表格状态
        tableStatesRef.current.set(tableKey, {
          metadata: basicMetadata,
          isHighlighted: !!targetElement,
          targetElement,
          processedRows: new Set(),
        });

        if (targetElement) {
          console.log(`🎉 基本标识表格 ${tableKey} 匹配并高亮成功`);
          processedCount++;
        }
      }
    });

    return processedCount;
  };

  // 流式解析表格行数据的函数
  const parseTableRowDataFromStream = (text: string): number => {
    console.log("📊 开始解析表格行数据");
    let processedCount = 0;

    // 更精确的行数据匹配模式
    const rowDataPatterns = [
      // 匹配表格行数组中的单个行对象
      /"rows"\s*:\s*\[[^\]]*\{[^}]*"value"[^}]*\}[^\]]*/g,
      // 匹配单个单元格数据对象
      /\{[^{}]*"value"\s*:\s*"[^"]*"[^{}]*"columnIndex"[^{}]*\}/g,
      // 匹配键值对形式的单元格数据
      /"([^"]+)"\s*:\s*\{[^{}]*"value"\s*:\s*"([^"]*)"/g,
    ];

    // 尝试解析完整的行数据数组
    const rowArrayPattern = rowDataPatterns[0];
    let rowArrayMatch;

    while ((rowArrayMatch = rowArrayPattern.exec(text)) !== null) {
      const rowArrayStr = rowArrayMatch[0];
      console.log("🎯 找到行数据数组：", rowArrayStr);

      try {
        // 从上下文中提取表格标识
        const contextInfo = extractRowColumnContext(text, rowArrayMatch.index);
        console.log("行数据数组有contextInfo吗?", contextInfo);
        if (contextInfo.tableKey) {
          const tableState = tableStatesRef.current.get(contextInfo.tableKey);
          console.log("行数据数组有tableState?", tableState);
          if (tableState && tableState.targetElement) {
            // 解析行数据数组中的单个单元格
            const cellPattern = /\{[^{}]*"value"\s*:\s*"([^"]*)"/g;
            let cellMatch;
            let cellIndex = 0;
            console.log("行数据数组有-准备进入循环-填充单元格");
            while ((cellMatch = cellPattern.exec(rowArrayStr)) !== null) {
              const cellValue = cellMatch[1];

              if (cellValue && cellValue.trim()) {
                console.log("行数据数组有-进入循环-填充单元格", cellValue);
                const cellKey = `${contextInfo.rowIndex || 0}_${cellIndex}`;

                if (!tableState.processedRows.has(cellKey)) {
                  const actualRowIndex = contextInfo.rowIndex || 0;
                  console.log(
                    `✅ 准备填充单元格：表格${contextInfo.tableKey}, 行${actualRowIndex}, 列${cellIndex} = ${cellValue}`,
                  );
                  console.log("开始填充-fillTableCell");
                  // 立即填充单元格 - frameworkTableExtractor现在使用0-based索引
                  const fillSuccess = fillTableCell(
                    tableState.targetElement,
                    contextInfo.rowIndex || 0, // 直接使用0-based索引
                    cellIndex, // cellIndex在这里已经是0-based
                    cellValue,
                    undefined, // columnHeader
                    tableState.metadata, // tableMetadata
                  );

                  if (fillSuccess) {
                    tableState.processedRows.add(cellKey);
                    processedCount++;
                    // 填充成功后，确保移除表格空状态
                    removeTableEmptyState(tableState.targetElement);
                    console.log(`🎉 单元格填充成功：${contextInfo.tableKey}[${actualRowIndex},${cellIndex}]`);
                  }
                }
              }
              cellIndex++;
            }
          }
        }
      } catch (error) {
        console.warn("⚠️ 解析行数据数组失败：", error, rowArrayStr);
      }
    }

    // 尝试解析单个单元格数据
    const cellPattern = rowDataPatterns[1];
    let cellMatch;

    while ((cellMatch = cellPattern.exec(text)) !== null) {
      const cellDataStr = cellMatch[0];
      console.log("🎯 找到单元格数据：", cellDataStr);

      try {
        const cellData = JSON.parse(cellDataStr);

        if (cellData.value !== undefined && cellData.columnIndex !== undefined) {
          // 尝试从上下文中提取表格和行信息
          const contextInfo = extractRowColumnContext(text, cellMatch.index);
          console.log("单元格数据有contextInfo吗?", contextInfo);
          if (contextInfo.tableKey) {
            const tableState = tableStatesRef.current.get(contextInfo.tableKey);
            console.log("单元格数据有tableState?", tableState);
            if (tableState && tableState.targetElement) {
              const cellKey = `${contextInfo.rowIndex || 0}_${cellData.columnIndex || 0}`;
              console.log("单元格数据有tableState.targetElement?", tableState.targetElement);
              if (!tableState.processedRows.has(cellKey)) {
                const actualRowIndex = contextInfo.rowIndex || 0;
                const actualColumnIndex = cellData.columnIndex || 0;
                console.log(
                  `✅ 准备填充单元格：表格${contextInfo.tableKey}, 行${actualRowIndex}, 列${actualColumnIndex} = ${cellData.value}`,
                );
                console.log("单元格数据-开始填充-fillTableCell");
                // 立即填充单元格 - frameworkTableExtractor现在使用0-based索引
                const fillSuccess = fillTableCell(
                  tableState.targetElement,
                  contextInfo.rowIndex || 0, // 直接使用0-based索引
                  cellData.columnIndex || 0, // 直接使用0-based索引
                  cellData.value,
                  undefined, // columnHeader
                  tableState.metadata, // tableMetadata
                );

                if (fillSuccess) {
                  tableState.processedRows.add(cellKey);
                  processedCount++;
                  console.log(`🎉 单元格填充成功：${contextInfo.tableKey}[${actualRowIndex},${actualColumnIndex}]`);
                }
              }
            }
          }
        }
      } catch (error) {
        console.warn("⚠️ 解析单元格数据失败：", error, cellDataStr);
      }
    }

    // 尝试解析键值对形式的单元格数据
    const keyValuePattern = rowDataPatterns[2];
    let keyValueMatch;

    while ((keyValueMatch = keyValuePattern.exec(text)) !== null) {
      const columnKey = keyValueMatch[1];
      const cellValue = keyValueMatch[2];

      console.log(`🎯 找到键值对单元格数据：${columnKey} = ${cellValue}`);

      if (cellValue && cellValue.trim()) {
        // 尝试从上下文中提取表格信息
        const contextInfo = extractRowColumnContext(text, keyValueMatch.index);
        console.log("键值对形式contextInfo  ", contextInfo);
        if (contextInfo.tableKey) {
          const tableState = tableStatesRef.current.get(contextInfo.tableKey);
          console.log("键值对形式tableState?", tableState);
          if (tableState && tableState.targetElement) {
            // 尝试通过列名匹配列索引
            const columnIndex = findColumnIndexByName(tableState.targetElement, columnKey);
            console.log("键值对形式-列名匹配列索引-columnIndex?", columnIndex);
            if (columnIndex !== -1) {
              const cellKey = `${contextInfo.rowIndex || 0}_${columnIndex}`;

              if (!tableState.processedRows.has(cellKey)) {
                const actualRowIndex = contextInfo.rowIndex || 0;
                console.log(
                  `✅ 准备填充单元格：表格${contextInfo.tableKey}, 行${actualRowIndex}, 列${columnIndex}(${columnKey}) = ${cellValue}`,
                );
                console.log("键值对形式-填充单元格");
                // 立即填充单元格 - frameworkTableExtractor现在使用0-based索引
                const fillSuccess = fillTableCell(
                  tableState.targetElement,
                  contextInfo.rowIndex || 0, // 直接使用0-based索引
                  columnIndex, // findColumnIndexByName返回的已经是0-based索引
                  cellValue,
                  columnKey, // columnHeader
                  tableState.metadata, // tableMetadata
                );

                if (fillSuccess) {
                  tableState.processedRows.add(cellKey);
                  processedCount++;
                  // 填充成功后，确保移除表格空状态
                  removeTableEmptyState(tableState.targetElement);
                  console.log(`🎉 单元格填充成功：${contextInfo.tableKey}[${actualRowIndex},${columnIndex}]`);
                }
              }
            }
          }
        }
      }
    }

    return processedCount;
  };

  // 通过列名查找列索引的辅助函数
  const findColumnIndexByName = (tableElement: HTMLElement, columnName: string): number => {
    const headers = extractTableHeaders(tableElement);

    for (let i = 0; i < headers.length; i++) {
      if (
        headers[i].toLowerCase().includes(columnName.toLowerCase()) ||
        columnName.toLowerCase().includes(headers[i].toLowerCase())
      ) {
        return i;
      }
    }

    return -1;
  };

  // 从上下文中提取行列信息
  const extractRowColumnContext = (
    text: string,
    matchIndex: number,
  ): {
    tableKey?: string;
    rowIndex?: number;
    columnIndex?: number;
  } => {
    // 大幅扩大搜索范围以获取包含tableMetadata的完整上下文
    // tableMetadata通常与rows在同一层级，需要向前搜索更大范围
    const searchStart = Math.max(0, matchIndex - 5000);
    const searchEnd = Math.min(text.length, matchIndex + 2000);
    const contextText = text.substring(searchStart, searchEnd);
    const beforeText = text.substring(searchStart, matchIndex);
    const afterText = text.substring(matchIndex, searchEnd);

    console.log(`🔍 提取上下文信息，搜索范围：${searchStart}-${searchEnd}，匹配位置：${matchIndex}`);
    console.log(`🔍 上下文文本片段：`, contextText.substring(0, 500) + "...");

    let tableKey: string | undefined;

    // 首先尝试提取完整的表格元数据来生成一致的tableKey
    // 使用更灵活的方法来查找tableMetadata，支持多种可能的位置
    const metadataPatterns = [
      '"tableMetadata"',
      '"tableMetadata"', // 可能有不同的引号
      "tableMetadata", // 可能没有引号
    ];

    let metadataIndex = -1;
    for (const pattern of metadataPatterns) {
      metadataIndex = contextText.indexOf(pattern);
      if (metadataIndex !== -1) {
        console.log(`找到tableMetadata模式: ${pattern} at index ${metadataIndex}`);
        break;
      }
    }

    console.log("metadataIndex", metadataIndex);

    if (metadataIndex !== -1) {
      try {
        // 找到tableMetadata的开始位置
        const colonIndex = contextText.indexOf(":", metadataIndex);
        if (colonIndex !== -1) {
          // 从冒号后开始查找对象
          let braceCount = 0;
          let startIndex = -1;
          let endIndex = -1;

          for (let i = colonIndex + 1; i < contextText.length; i++) {
            const char = contextText[i];
            if (char === "{") {
              if (startIndex === -1) startIndex = i;
              braceCount++;
            } else if (char === "}") {
              braceCount--;
              if (braceCount === 0 && startIndex !== -1) {
                endIndex = i;
                break;
              }
            }
          }

          if (startIndex !== -1 && endIndex !== -1) {
            const metadataStr = contextText.substring(startIndex, endIndex + 1);
            console.log("提取的tableMetadata字符串:", metadataStr);
            const metadataObj = JSON.parse(metadataStr);
            tableKey = generateTableKey(metadataObj);
            console.log(`🎯 从元数据生成表格标识：${tableKey}`);
          }
        }
      } catch (error) {
        console.warn("解析元数据失败，尝试其他方式", error);
      }
    } else {
      console.log("❌ 在上下文中未找到tableMetadata");
      // 输出部分上下文用于调试
      console.log("上下文样本:", contextText.substring(0, 1000));
    }

    // 如果没有找到完整元数据，尝试提取基本标识信息
    if (!tableKey) {
      // 尝试提取表格标识（优先级从高到低）
      const tableKeyPatterns = [
        // 直接的表格标识
        /"tableName"\s*:\s*"([^"]+)"/,
        /"tableId"\s*:\s*"([^"]+)"/,
        /"table_id"\s*:\s*"([^"]+)"/,
        // 表格元数据中的标识
        /"tableMetadata"[^}]*"tableId"\s*:\s*"([^"]+)"/,
        /"tableMetadata"[^}]*"id"\s*:\s*"([^"]+)"/,
        // 通用标识
        /"id"\s*:\s*"([^"]+)"/,
        /"name"\s*:\s*"([^"]+)"/,
        // 表格选择器
        /"tableSelector"\s*:\s*"[^"]*#([^"\s.]+)/,
        /"tableSelector"\s*:\s*"[^"]*\.([^"\s#]+)/,
        // 从表格数据结构中推断
        /"tables"\s*:\s*\{\s*"([^"]+)"/,
        /"tableData"\s*:\s*\{[^}]*"([^"]+)"/,
      ];

      for (const pattern of tableKeyPatterns) {
        const match = contextText.match(pattern);
        if (match && match[1]) {
          tableKey = match[1];
          console.log(`🎯 找到表格标识：${tableKey} (模式: ${pattern.source})`);
          break;
        }
      }
    }

    // 如果仍然没有找到明确的表格标识，尝试从结构中推断
    if (!tableKey) {
      // 查找表格数据结构的开始
      const structurePatterns = [
        /"extractedTables"\s*:\s*\{\s*"([^"]+)"/,
        /"forms"\s*:\s*\[[^\]]*"formType"\s*:\s*"table"[^}]*"tableName"\s*:\s*"([^"]+)"/,
        /"formType"\s*:\s*"table"[^}]*"tableName"\s*:\s*"([^"]+)"/,
      ];

      for (const pattern of structurePatterns) {
        const match = beforeText.match(pattern);
        if (match && match[1]) {
          tableKey = match[1];
          console.log(`🔍 从结构推断表格标识：${tableKey}`);
          break;
        }
      }
    }

    // 尝试提取行索引（优先级从高到低）
    const rowIndexPatterns = [
      // 明确的行索引
      /"rowIndex"\s*:\s*(\d+)/,
      /"row_index"\s*:\s*(\d+)/,
      /"row"\s*:\s*(\d+)/,
      // 数组索引形式
      /"rows"\s*\[\s*(\d+)\s*\]/,
      /"data"\s*\[\s*(\d+)\s*\]/,
      // 从位置推断
      /\[\s*(\d+)\s*\]\s*[^\]]*"value"/,
    ];

    let rowIndex: number | undefined;

    for (const pattern of rowIndexPatterns) {
      const match = contextText.match(pattern);
      if (match && match[1] !== undefined) {
        rowIndex = parseInt(match[1], 10);
        console.log(`🎯 找到行索引：${rowIndex} (模式: ${pattern.source})`);
        break;
      }
    }

    // 尝试提取列索引（优先级从高到低）
    const columnIndexPatterns = [
      // 明确的列索引
      /"columnIndex"\s*:\s*(\d+)/,
      /"column_index"\s*:\s*(\d+)/,
      /"column"\s*:\s*(\d+)/,
      /"col"\s*:\s*(\d+)/,
      // 从单元格数据中提取
      /"cell"\s*\[\s*(\d+)\s*\]/,
      /"cells"\s*\[\s*(\d+)\s*\]/,
    ];

    let columnIndex: number | undefined;

    for (const pattern of columnIndexPatterns) {
      const match = contextText.match(pattern);
      if (match && match[1] !== undefined) {
        columnIndex = parseInt(match[1], 10);
        console.log(`🎯 找到列索引：${columnIndex} (模式: ${pattern.source})`);
        break;
      }
    }

    // 如果没有找到明确的行列索引，尝试从位置推断
    if ((rowIndex === undefined || columnIndex === undefined) && tableKey) {
      // 计算在当前表格数据中的相对位置
      const cellValueMatches = beforeText.match(/"value"\s*:/g);
      if (cellValueMatches) {
        const cellCount = cellValueMatches.length;

        // 尝试从已识别的表格中获取列数
        const tableState = tableStatesRef.current.get(tableKey);
        if (tableState && tableState.targetElement) {
          const headers = extractTableHeaders(tableState.targetElement);
          const columnCount = headers.length;

          if (columnCount > 0) {
            if (rowIndex === undefined) {
              rowIndex = Math.floor(cellCount / columnCount);
              console.log(`🔍 推断行索引：${rowIndex}`);
            }
            if (columnIndex === undefined) {
              columnIndex = cellCount % columnCount;
              console.log(`🔍 推断列索引：${columnIndex}`);
            }
          }
        }
      }
    }

    const result = {
      tableKey,
      rowIndex,
      columnIndex,
    };

    console.log(`📍 上下文提取结果：`, result);
    return result;
  };

  // 解析表单字段的函数 - 优化为顺序填充机制
  const parseFormFieldsFromStream = (text: string): number => {
    console.log("📝 开始解析表单字段数据");

    // 匹配完整的表单字段对象
    const fieldObjectPattern = /\{[^{}]*?"fieldIndex"[^{}]*?"value"\s*:\s*"[^"]*"[^{}]*?\}/g;

    // 从上次成功解析的位置开始搜索
    const startPosition = lastFieldEndPositionRef.current;
    console.log(`🔍 从位置 ${startPosition} 开始搜索新的完整字段`);

    // 设置正则表达式的起始位置
    fieldObjectPattern.lastIndex = startPosition;

    let processedCount = 0;
    let fieldMatch;

    // 只查找下一个完整的字段对象
    if ((fieldMatch = fieldObjectPattern.exec(text)) !== null) {
      const fieldObjectStr = fieldMatch[0];
      const matchStartIndex = fieldMatch.index;
      const matchEndIndex = fieldMatch.index + fieldMatch[0].length;

      console.log(`🎯 找到完整字段对象（位置 ${matchStartIndex}-${matchEndIndex}）：`, fieldObjectStr);

      try {
        const fieldData = JSON.parse(fieldObjectStr);
        console.log("📝 解析字段数据：", fieldData);

        // 验证必要字段
        if (fieldData.value && (fieldData.label || fieldData.name || fieldData.id)) {
          // 检查字段是否被禁用
          if (fieldData.isDisabled === true) {
            console.log(
              `⚠️ 跳过禁用字段：${fieldData.label || fieldData.name || fieldData.id} (isDisabled: ${fieldData.isDisabled})`,
            );
            // 跳过禁用字段，更新搜索位置
            lastFieldEndPositionRef.current = matchEndIndex;
            return processedCount;
          }

          // 使用fieldIndex生成唯一标识，确保不同表单中的相同字段不会被跳过
          const baseFieldKey = fieldData.label || fieldData.name || fieldData.id || "unknown";
          const uniqueFieldKey = fieldData.fieldIndex ? `${baseFieldKey}_${fieldData.fieldIndex}` : baseFieldKey;

          // 检查是否已经填充过这个特定的字段实例
          if (!filledFieldsRef.current.has(uniqueFieldKey)) {
            console.log(`✅ 准备填充表单字段（索引${fieldData.fieldIndex}）：${uniqueFieldKey} = ${fieldData.value}`);

            // 立即填充这个字段
            const fillSuccess = fillSingleFormField(fieldData);
            if (fillSuccess) {
              filledFieldsRef.current.add(uniqueFieldKey);
              processedCount++;
              // 更新下次搜索的起始位置为当前字段的结束位置
              lastFieldEndPositionRef.current = matchEndIndex;
              console.log(`🎉 表单字段 ${uniqueFieldKey} 填充成功，下次从位置 ${matchEndIndex} 开始搜索`);
            } else {
              console.warn(`❌ 表单字段 ${uniqueFieldKey} 填充失败`);
            }
          } else {
            console.log(`⏭️ 表单字段 ${uniqueFieldKey} 已填充，跳过`);
            // 即使跳过，也要更新搜索位置，避免重复检查同一个字段
            lastFieldEndPositionRef.current = matchEndIndex;
          }
        } else {
          console.warn("⚠️ 字段数据不完整，跳过：", fieldData);
          // 跳过不完整的字段，更新搜索位置
          lastFieldEndPositionRef.current = matchEndIndex;
        }
      } catch (error) {
        console.warn("⚠️ 解析字段对象失败：", error, fieldObjectStr);
        // 解析失败也要更新搜索位置，避免重复尝试解析同一个错误的字段
        lastFieldEndPositionRef.current = matchEndIndex;
      }
    } else {
      console.log(`🔍 从位置 ${startPosition} 开始未找到新的完整字段对象，等待更多数据`);
    }

    return processedCount;
  };

  // 基于元数据匹配并高亮表格的函数
  const findAndHighlightTableByMetadata = (metadata: any): HTMLElement | null => {
    console.log(`🔍 基于元数据查找表格`, metadata);

    try {
      // 1. 优先使用DOM选择器
      if (metadata.tableSelector) {
        const element = document.querySelector(metadata.tableSelector) as HTMLElement;
        if (element) {
          highlightElement(element);
          return element;
        }
      }

      // 2. 使用XPath
      if (metadata.tableXPath) {
        const result = document.evaluate(
          metadata.tableXPath,
          document,
          null,
          XPathResult.FIRST_ORDERED_NODE_TYPE,
          null,
        );
        if (result.singleNodeValue) {
          const element = result.singleNodeValue as HTMLElement;
          highlightElement(element);
          return element;
        }
      }

      // 3. 使用结构特征匹配
      if (metadata.structuralFingerprint) {
        const tables = document.querySelectorAll("table, .el-table, .ant-table, .v-data-table, .MuiTable-root");

        for (const table of tables) {
          const tableElement = table as HTMLElement;

          // 检查表头匹配
          if (metadata.structuralFingerprint.headerTexts) {
            const headers = extractTableHeaders(tableElement);
            const headerMatch = headers.some((header) => metadata.structuralFingerprint.headerTexts.includes(header));

            if (headerMatch) {
              highlightElement(tableElement);
              return tableElement;
            }
          }
        }
      }

      return null;
    } catch (error) {
      console.error("基于元数据匹配表格时出错:", error);
      return null;
    }
  };

  // 基于基本信息匹配并高亮表格的函数
  const findAndHighlightTableByBasicInfo = (basicInfo: any): HTMLElement | null => {
    console.log(`🔍 基于基本信息查找表格`, basicInfo);

    try {
      // 1. 使用ID选择器
      if (basicInfo.tableId) {
        const element = document.getElementById(basicInfo.tableId);
        if (element) {
          highlightElement(element);
          return element;
        }
      }

      // 2. 使用类选择器
      if (basicInfo.tableClass) {
        const element = document.querySelector(`.${basicInfo.tableClass}`) as HTMLElement;
        if (element) {
          highlightElement(element);
          return element;
        }
      }

      // 3. 使用表格名称匹配
      if (basicInfo.tableName) {
        const tables = document.querySelectorAll("table, .el-table, .ant-table, .v-data-table, .MuiTable-root");

        for (const table of tables) {
          const tableElement = table as HTMLElement;

          // 检查表格标题或caption
          const caption = tableElement.querySelector("caption");
          if (caption && caption.textContent?.includes(basicInfo.tableName)) {
            highlightElement(tableElement);
            return tableElement;
          }

          // 检查表格附近的标题
          const prevElement = tableElement.previousElementSibling;
          if (prevElement && prevElement.textContent?.includes(basicInfo.tableName)) {
            highlightElement(tableElement);
            return tableElement;
          }
        }
      }

      return null;
    } catch (error) {
      console.error("基于基本信息匹配表格时出错:", error);
      return null;
    }
  };

  // 提取表格表头的辅助函数
  const extractTableHeaders = (tableElement: HTMLElement): string[] => {
    const headers: string[] = [];

    // 查找表头单元格
    const headerCells = tableElement.querySelectorAll("th, .el-table__header th, .ant-table-thead th");
    headerCells.forEach((cell) => {
      const text = cell.textContent?.trim();
      if (text) {
        headers.push(text);
      }
    });

    return headers;
  };

  // 高亮元素的辅助函数
  const highlightElement = (element: HTMLElement) => {
    // 添加高亮样式
    const originalStyle = {
      outline: element.style.outline,
      backgroundColor: element.style.backgroundColor,
    };

    element.style.outline = "3px solid #52c41a";
    element.style.backgroundColor = "rgba(82, 196, 26, 0.1)";

    // 2秒后移除高亮
    setTimeout(() => {
      element.style.outline = originalStyle.outline;
      element.style.backgroundColor = originalStyle.backgroundColor;
    }, 2000);
  };

  // 专门处理表单和表格数据结构的流式解析函数
  const parseDataFromStream = (text: string) => {
    // console.log("🚀 开始解析数据流式内容，文本长度：", text.length);
    let totalProcessedCount = 0;

    // 检查是否需要重置会话状态
    checkForSessionReset(text);

    // 检查完成标记
    const completionMarkers = {
      formFields: /\[\s*FORM_FIELDS_COMPLETE\s*\]|表单字段填充完成|form\s*fields?\s*complete/i,
      tableRows: /\[\s*TABLE_ROWS_COMPLETE\s*\]|表格行填充完成|table\s*rows?\s*complete/i,
      tableMetadata: /\[\s*TABLE_METADATA_COMPLETE\s*\]|表格元数据完成|table\s*metadata\s*complete/i,
    };

    // 更新完成状态
    Object.keys(completionMarkers).forEach((key) => {
      if (completionMarkers[key].test(text)) {
        dataCompletionStatusRef.current[key] = true;
        console.log(`✅ 检测到${key}完成标记，设置完成状态`);
      }
    });

    // 1. 解析表格元数据（优先级最高，立即匹配和高亮）这个必须有他是为后面填充行加tableStatesRef.current.set
    if (!dataCompletionStatusRef.current.tableMetadata) {
      const metadataCount = parseTableMetadataFromStream(text);
      totalProcessedCount += metadataCount;
    }

    // 2. 解析表格行数据（在元数据匹配成功后进行流式填充）
    if (!dataCompletionStatusRef.current.tableRows) {
      const rowDataCount = parseTableRowDataFromStream(text);
      totalProcessedCount += rowDataCount;
    } else {
      console.log("⏭️ 表格行数据已完成，跳过解析");
    }

    // 3. 解析表单字段数据
    if (!dataCompletionStatusRef.current.formFields) {
      const formFieldsCount = parseFormFieldsFromStream(text);
      totalProcessedCount += formFieldsCount;
    } else {
      console.log("⏭️ 表单字段数据已完成，跳过解析");
    }

    return totalProcessedCount > 0;
  };

  const createTableRow = (tableElement: HTMLElement, rowIndex: number, tableMetadata?: any): HTMLElement | null => {
    try {
      console.log(`🔨 尝试创建表格行：行${rowIndex}`, tableMetadata);

      // 获取框架信息，优先使用提取时的元数据
      let framework = tableMetadata?.frameworkInfo?.framework || "native";
      let headerCount = tableMetadata?.structuralFingerprint?.headerCount || 0;

      // 容错机制：如果元数据中没有列数信息，尝试从DOM中获取
      if (headerCount === 0) {
        const headerCells = tableElement.querySelectorAll(
          "th, .el-table__header th, .ant-table-thead th, .v-data-table__header th, .MuiTableCell-head",
        );
        headerCount = headerCells.length;
        console.log(`📋 从DOM中检测到列数：${headerCount}`);
      }

      // 容错机制：如果仍然没有列数，使用默认值
      if (headerCount === 0) {
        headerCount = 3; // 默认3列
        console.warn(`⚠️ 无法检测列数，使用默认值：${headerCount}`);
      }

      console.log(`📋 检测到表格框架：${framework}，列数：${headerCount}`);

      let newRow: HTMLElement | null = null;
      let targetBody: HTMLElement | null = null;
      let fallbackAttempted = false;

      // 根据不同框架创建行
      switch (framework) {
        case "elementUI":
          // Element UI 表格行创建
          targetBody = tableElement.querySelector(".el-table__body-wrapper tbody");
          if (targetBody) {
            newRow = document.createElement("tr");
            newRow.className = "el-table__row";

            // 创建单元格
            for (let i = 0; i < headerCount; i++) {
              const cell = document.createElement("td");
              cell.className = "el-table__cell";

              // 创建单元格内容容器
              const cellDiv = document.createElement("div");
              cellDiv.className = "cell";
              cellDiv.textContent = "";

              cell.appendChild(cellDiv);
              newRow.appendChild(cell);
            }
          }
          break;

        case "antDesign":
          // Ant Design 表格行创建
          targetBody = tableElement.querySelector(".ant-table-tbody");
          if (targetBody) {
            newRow = document.createElement("tr");
            newRow.className = "ant-table-row";

            // 创建单元格
            for (let i = 0; i < headerCount; i++) {
              const cell = document.createElement("td");
              cell.className = "ant-table-cell";
              cell.textContent = "";
              newRow.appendChild(cell);
            }
          }
          break;

        case "vuetify":
          // Vuetify 表格行创建
          targetBody = tableElement.querySelector("tbody");
          if (targetBody) {
            newRow = document.createElement("tr");

            // 创建单元格
            for (let i = 0; i < headerCount; i++) {
              const cell = document.createElement("td");
              cell.className = "v-data-table__cell";
              cell.textContent = "";
              newRow.appendChild(cell);
            }
          }
          break;

        case "materialUI":
          // Material-UI 表格行创建
          targetBody = tableElement.querySelector(".MuiTableBody-root, tbody");
          if (targetBody) {
            newRow = document.createElement("tr");
            newRow.className = "MuiTableRow-root";

            // 创建单元格
            for (let i = 0; i < headerCount; i++) {
              const cell = document.createElement("td");
              cell.className = "MuiTableCell-root";
              cell.textContent = "";
              newRow.appendChild(cell);
            }
          }
          break;

        default:
          // 原生 HTML 表格行创建
          targetBody = tableElement.querySelector("tbody");
          if (!targetBody) {
            // 如果没有tbody，创建一个
            targetBody = document.createElement("tbody");
            tableElement.appendChild(targetBody);
          }

          if (targetBody) {
            newRow = document.createElement("tr");

            // 创建单元格
            for (let i = 0; i < headerCount; i++) {
              const cell = document.createElement("td");
              cell.textContent = "";
              newRow.appendChild(cell);
            }
          }
          break;
      }

      // 回退策略：如果特定框架的行创建失败，尝试使用原生HTML方式
      if (!newRow && !fallbackAttempted && framework !== "native") {
        console.warn(`⚠️ ${framework}框架行创建失败，尝试原生HTML回退策略`);
        fallbackAttempted = true;
        framework = "native";

        // 使用原生HTML方式创建行
        targetBody = tableElement.querySelector("tbody");
        if (!targetBody) {
          targetBody = document.createElement("tbody");
          tableElement.appendChild(targetBody);
        }

        if (targetBody) {
          newRow = document.createElement("tr");

          // 创建单元格
          for (let i = 0; i < headerCount; i++) {
            const cell = document.createElement("td");
            cell.textContent = "";
            newRow.appendChild(cell);
          }
        }
      }

      // 插入新行到指定位置
      if (newRow && targetBody) {
        const existingRows = targetBody.querySelectorAll("tr");
        console.log(`📍 准备插入行${rowIndex}，当前tbody中有${existingRows.length}行`);

        if (rowIndex >= existingRows.length) {
          // 如果索引超出现有行数，直接追加
          targetBody.appendChild(newRow);
          console.log(`📍 行${rowIndex}已追加到tbody末尾`);
        } else {
          // 插入到指定位置
          targetBody.insertBefore(newRow, existingRows[rowIndex]);
          console.log(`📍 行${rowIndex}已插入到指定位置`);
        }

        // 验证行是否成功插入
        const updatedRows = targetBody.querySelectorAll("tr");
        console.log(`📍 插入后tbody中有${updatedRows.length}行`);

        // 为新创建的行添加标识属性，便于后续识别
        newRow.setAttribute("data-dynamic-row", "true");
        newRow.setAttribute("data-row-index", rowIndex.toString());

        const strategyUsed = fallbackAttempted
          ? `${tableMetadata?.frameworkInfo?.framework || "unknown"} -> native`
          : framework;
        console.log(`✅ 成功创建表格行：行${rowIndex}，策略：${strategyUsed}`);

        // 创建行后，检查并移除表格的空状态提示
        removeTableEmptyState(tableElement);

        return newRow;
      }

      console.error(`❌ 创建表格行完全失败：行${rowIndex}，框架：${framework}，已尝试回退策略：${fallbackAttempted}`);
      return null;
    } catch (error) {
      console.error(`创建表格行时发生错误：行${rowIndex}`, error);
      return null;
    }
  };

  // 流式填充表格单元格的函数（增强版，支持动态创建行）
  const fillTableCell = (
    tableElement: HTMLElement,
    rowIndex: number,
    columnIndex: number,
    value: string,
    columnHeader?: string,
    tableMetadata?: any,
  ): boolean => {
    try {
      console.log(`🎯 尝试填充单元格：表格元素`, tableElement, `行${rowIndex}, 列${columnIndex} = ${value}`);

      // 查找目标单元格的多种策略
      let targetCell: HTMLElement | null = null;

      // 策略1: 直接通过行列索引查找（适用于标准table结构）- 只选择数据行，排除表头
      // 使用更全面的选择器，包括动态创建的行
      let bodyRows = tableElement.querySelectorAll(
        "tbody tr, .el-table__body-wrapper .el-table__row, .ant-table-tbody .ant-table-row, .v-data-table tbody tr, tr:not(thead tr):not(.el-table__header tr)",
      );

      // 如果没有找到足够的行，尝试查找所有数据行（包括动态创建的）
      let dataRowsArray: HTMLElement[] = [];
      if (bodyRows.length <= rowIndex) {
        const allRows = tableElement.querySelectorAll("tr");
        dataRowsArray = Array.from(allRows).filter((row) => {
          const isHeaderRow =
            row.closest("thead") ||
            row.classList.contains("el-table__header") ||
            row.querySelector("th:not([data-dynamic-row])");
          return !isHeaderRow;
        }) as HTMLElement[];
        console.log(`🔍 扩展查询找到${dataRowsArray.length}个数据行`);
      }

      // 使用扩展查询的结果或原始查询结果
      const effectiveRows = dataRowsArray.length > 0 ? dataRowsArray : (Array.from(bodyRows) as HTMLElement[]);
      console.log(`🔍 当前查询到${effectiveRows.length}行，目标行索引：${rowIndex}`);

      // 检查是否需要创建新行
      if (effectiveRows.length <= rowIndex) {
        console.log(`📝 目标行${rowIndex}不存在，当前只有${effectiveRows.length}行，尝试创建新行`);

        // 创建缺失的行
        let creationFailed = false;
        for (let i = effectiveRows.length; i <= rowIndex; i++) {
          const newRow = createTableRow(tableElement, i, tableMetadata);
          if (!newRow) {
            console.error(`❌ 创建行${i}失败，停止后续行创建`);
            creationFailed = true;
            break;
          }
        }

        // 如果行创建失败，尝试回退策略
        if (creationFailed) {
          console.warn(`⚠️ 动态行创建失败，尝试直接填充现有结构`);
          // 不返回false，继续尝试填充现有的单元格
        }

        // 重新查询行，使用更全面的选择器确保能找到动态创建的行
        const updatedBodyRows = tableElement.querySelectorAll(
          "tbody tr, .el-table__body-wrapper .el-table__row, .ant-table-tbody .ant-table-row, .v-data-table tbody tr, tr:not(thead tr):not(.el-table__header tr)",
        );

        console.log(`🔍 重新查询后找到${updatedBodyRows.length}行，目标行索引：${rowIndex}`);

        // 如果重新查询后仍然没有足够的行，尝试更宽泛的查询
        if (updatedBodyRows.length <= rowIndex) {
          console.log(`⚠️ 重新查询后仍然没有足够的行，尝试更宽泛的查询`);
          const allRows = tableElement.querySelectorAll("tr");
          console.log(`🔍 找到所有行${allRows.length}个`);

          // 过滤掉表头行
          const dataRows = Array.from(allRows).filter((row) => {
            const isHeaderRow =
              row.closest("thead") || row.classList.contains("el-table__header") || row.querySelector("th");
            return !isHeaderRow;
          });

          console.log(`🔍 过滤后的数据行${dataRows.length}个`);

          if (dataRows.length > rowIndex) {
            const targetRow = dataRows[rowIndex] as HTMLElement;
            const cells = targetRow.querySelectorAll("td, .el-table__cell, .ant-table-cell, .v-data-table__cell, th");
            if (cells.length > columnIndex) {
              targetCell = cells[columnIndex] as HTMLElement;
            }
          }
        } else {
          const targetRow = updatedBodyRows[rowIndex] as HTMLElement;
          const cells = targetRow.querySelectorAll("td, .el-table__cell, .ant-table-cell, .v-data-table__cell");
          if (cells.length > columnIndex) {
            targetCell = cells[columnIndex] as HTMLElement;
          }
        }
      } else {
        // 行已存在，直接获取单元格
        const targetRow = effectiveRows[rowIndex];
        const cells = targetRow.querySelectorAll("td, .el-table__cell, .ant-table-cell, .v-data-table__cell");
        if (cells.length > columnIndex) {
          targetCell = cells[columnIndex] as HTMLElement;
        }
      }

      // 策略2: 通过表头名称查找列，然后定位到对应行的单元格
      if (!targetCell && columnHeader) {
        const headerCells = tableElement.querySelectorAll(
          "th, .el-table__header .el-table__cell, .ant-table-thead th, .v-data-table__header th",
        );
        let targetColumnIndex = -1;

        for (let i = 0; i < headerCells.length; i++) {
          const headerCell = headerCells[i] as HTMLElement;
          if (headerCell.textContent?.trim() === columnHeader.trim()) {
            targetColumnIndex = i;
            break;
          }
        }

        if (targetColumnIndex >= 0) {
          const bodyRows = tableElement.querySelectorAll(
            "tbody tr, .el-table__body-wrapper .el-table__row, .ant-table-tbody .ant-table-row, .v-data-table tbody tr, tr:not(thead tr):not(.el-table__header tr)",
          );
          if (bodyRows.length > rowIndex) {
            const targetRow = bodyRows[rowIndex] as HTMLElement;
            const cells = targetRow.querySelectorAll("td, .el-table__cell, .ant-table-cell, .v-data-table__cell");
            if (cells.length > targetColumnIndex) {
              targetCell = cells[targetColumnIndex] as HTMLElement;
            }
          }
        }
      }

      if (!targetCell) {
        console.warn(`❌ 未找到目标单元格：行${rowIndex}, 列${columnIndex}, 表头"${columnHeader}"`);
        return false;
      }

      // 查找单元格内的表单元素
      const formElement = targetCell.querySelector("input, textarea, select") as
        | HTMLInputElement
        | HTMLTextAreaElement
        | HTMLSelectElement;

      if (formElement) {
        // 如果单元格包含表单元素，填充表单元素
        console.log(`📝 填充表格表单元素：行${rowIndex}, 列${columnIndex} = ${value}`);
        formElement.value = value;

        // 触发事件
        const events = ["input", "change", "blur"];
        events.forEach((eventType) => {
          const event = new Event(eventType, { bubbles: true });
          formElement.dispatchEvent(event);
        });

        // 添加视觉反馈
        const originalStyle = {
          backgroundColor: formElement.style.backgroundColor,
          border: formElement.style.border,
        };

        formElement.style.backgroundColor = "#f6ffed";
        formElement.style.border = "1px solid #52c41a";

        setTimeout(() => {
          formElement.style.backgroundColor = originalStyle.backgroundColor;
          formElement.style.border = originalStyle.border;
        }, 2000);
      } else {
        // 如果单元格不包含表单元素，直接设置文本内容
        console.log(`📄 填充表格文本内容：行${rowIndex}, 列${columnIndex} = ${value}`);
        targetCell.textContent = value;

        // 添加视觉反馈
        const originalStyle = {
          backgroundColor: targetCell.style.backgroundColor,
          color: targetCell.style.color,
        };

        targetCell.style.backgroundColor = "#f6ffed";
        targetCell.style.color = "#52c41a";

        setTimeout(() => {
          targetCell!.style.backgroundColor = originalStyle.backgroundColor;
          targetCell!.style.color = originalStyle.color;
        }, 2000);
      }

      // 填充成功后，检查并移除表格的空状态提示
      removeTableEmptyState(tableElement);

      return true;
    } catch (error) {
      console.error(`填充单元格时发生错误：行${rowIndex}, 列${columnIndex}`, error);
      return false;
    }
  };

  // 移除表格空状态提示的函数
  const removeTableEmptyState = (tableElement: HTMLElement): void => {
    try {
      console.log("🔍 检查并移除当前表格的空状态提示:", tableElement);

      // 查找各种可能的空状态提示元素
      const emptyStateSelectors = [
        ".ant-empty",
        ".el-table__empty-block",
        ".v-data-table__empty-wrapper",
        ".MuiTableBody-root .MuiTableRow-root .MuiTableCell-root[colspan]",
        "tbody tr td[colspan]",
        ".empty-data",
        ".no-data",
        ".table-empty",
      ];

      // 只在当前表格内查找空状态元素，不向上查找父容器
      emptyStateSelectors.forEach((selector) => {
        const emptyElements = tableElement.querySelectorAll(selector);
        emptyElements.forEach((element) => {
          const textContent = element.textContent?.trim() || "";
          // 检查是否包含"暂无数据"或类似的空状态文本
          if (
            textContent.includes("暂无数据") ||
            textContent.includes("无数据") ||
            textContent.includes("No data") ||
            textContent.includes("Empty") ||
            textContent.includes("没有数据")
          ) {
            console.log("🗑️ 移除当前表格的空状态提示元素:", element);
            (element as HTMLElement).style.display = "none";
            // 或者完全移除元素
            // element.remove();
          }
        });
      });

      // 特殊处理：查找包含"暂无数据"文本的单元格
      const allCells = tableElement.querySelectorAll("td, th, .el-table__cell, .ant-table-cell");
      allCells.forEach((cell) => {
        const textContent = cell.textContent?.trim() || "";
        if (textContent === "暂无数据" || textContent === "无数据" || textContent === "No data") {
          console.log("🗑️ 隐藏包含空状态文本的单元格:", cell);
          (cell as HTMLElement).style.display = "none";
        }
      });

      // 检查并移除空的行（如果整行都是空状态提示）
      const allRows = tableElement.querySelectorAll(
        "tbody tr, .el-table__body-wrapper .el-table__row, .ant-table-tbody .ant-table-row",
      );
      allRows.forEach((row) => {
        const rowText = row.textContent?.trim() || "";
        if (rowText === "暂无数据" || rowText === "无数据" || rowText === "No data") {
          console.log("🗑️ 隐藏空状态行:", row);
          (row as HTMLElement).style.display = "none";
        }
      });
    } catch (error) {
      console.error("移除表格空状态时发生错误:", error);
    }
  };

  // 智能表单元素填充函数 - 支持下拉框、日历等组件的真实数据匹配
  const fillFormElementWithSmartMatching = (
    element: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement | null,
    value: string,
    label?: string,
  ): boolean => {
    try {
      // 添加空值检查
      if (!element || !value) {
        console.warn("元素或值为空，跳过填充");
        return false;
      }

      const tagName = element.tagName.toLowerCase();
      const inputType = (element as HTMLInputElement).type?.toLowerCase();

      // 检查元素是否被禁用或只读
      if (element.disabled) {
        console.log(`⚠️ 跳过禁用的表单元素：${tagName}${inputType ? `[${inputType}]` : ""}, 标签：${label || "未知"}`);
        return false;
      }

      // if ((element as HTMLInputElement | HTMLTextAreaElement).readOnly) {
      //   console.log(`⚠️ 跳过只读的表单元素：${tagName}${inputType ? `[${inputType}]` : ""}, 标签：${label || '未知'}`);
      //   return false;
      // }

      console.log(`🎯 智能填充元素：${tagName}${inputType ? `[${inputType}]` : ""}, 值：${value}`);

      // 处理下拉框 (select)
      if (tagName === "select") {
        const selectElement = element as HTMLSelectElement;
        return fillSelectElement(selectElement, value);
      }

      // 处理日期相关输入框
      if (inputType && ["date", "datetime-local", "time", "month", "week"].includes(inputType)) {
        return fillDateElement(element as HTMLInputElement, value, label);
      }

      // 处理复选框和单选框
      if (inputType && ["checkbox", "radio"].includes(inputType)) {
        return fillCheckboxRadioElement(element as HTMLInputElement, value);
      }

      // 先尝试UI框架组件填充（适用于所有元素类型，包括div容器）
      const frameworkResult = fillUIFrameworkComponent(element, value, label);
      if (frameworkResult) {
        return true;
      }

      // 处理普通输入框和文本域
      if (tagName === "input" || tagName === "textarea") {
        // 使用普通填充方式
        console.log(`🔧 使用普通填充方式：${tagName}, 当前值: "${element.value}", 目标值: "${value}"`);
        element.value = value;

        // 触发必要的事件
        const events = ["input", "change", "blur"];
        events.forEach((eventType) => {
          const event = new Event(eventType, { bubbles: true });
          element.dispatchEvent(event);
        });

        console.log(`✅ 普通元素填充完成：${tagName}, 最终值: "${element.value}"`);
        return true;
      }

      return false;
    } catch (error) {
      console.error("智能填充失败：", error);
      return false;
    }
  };
  // 填充单个表单字段的函数
  const fillSingleFormField = (fieldData: any): boolean => {
    try {
      const { label, value, placeholder, type, fieldIndex, framework, componentType } = fieldData;

      if (!value || !value.trim()) {
        console.warn("字段值为空，跳过填充");
        return false;
      }

      console.log(`🔍 尝试填充字段：`, { label, placeholder, type, framework, componentType, fieldIndex });
      console.log(`📍 字段详细信息：`, fieldData);

      // 查找DOM元素的策略（按优先级排序）
      let domElement: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement | null = null;
      let usedSelector = "";

      // 策略1: 通过label文本查找（最可靠的方式）
      if (label && !domElement) {
        console.log(`🏷️ 通过label查找："${label}"`);

        // 查找所有输入元素，与提取时保持完全一致的选择器
        const allInputs = document.querySelectorAll(
          "input, textarea, select, .el-input, .el-select, .el-textarea, .el-checkbox, .el-radio, .el-switch, .el-date-picker, .el-time-picker, .el-cascader, .el-slider, .el-rate, .el-upload, .ant-input, .ant-select, .ant-checkbox, .ant-radio, .ant-switch, .ant-date-picker, .ant-time-picker, .ant-cascader, .ant-slider, .ant-rate, .ant-upload, .ant-input-number, .v-text-field, .v-select, .v-checkbox, .v-radio, .v-switch, .v-date-picker, .v-time-picker, .v-slider, .v-rating, .MuiTextField-root, .MuiSelect-root, .MuiCheckbox-root, .MuiRadio-root, .MuiSwitch-root, .MuiSlider-root",
        );
        const candidates: { element: HTMLElement; score: number; originalIndex: number }[] = [];

        for (let i = 0; i < allInputs.length; i++) {
          const element = allInputs[i] as HTMLElement;
          let score = 0;

          // 使用统一的标签查找函数
          const elementLabel = getFormElementLabel(element);
          if (elementLabel && elementLabel.trim() === label.trim()) {
            score += 10; // 最高优先级 - 完全匹配
          } else if (elementLabel && elementLabel.includes(label.trim())) {
            score += 5; // 部分匹配
          } else if (elementLabel && label.includes(elementLabel.trim())) {
            score += 3; // 反向部分匹配
          }

          if (score > 0) {
            candidates.push({ element, score, originalIndex: i });
          }
        }

        if (candidates.length > 0) {
          if (fieldIndex && fieldIndex > 0) {
            // 如果指定了fieldIndex，直接在所有输入元素中查找指定索引的元素（与提取时保持一致）
            const allInputs = document.querySelectorAll(
              "input, textarea, select, .el-input, .el-select, .el-textarea, .el-checkbox, .el-radio, .el-switch, .el-date-picker, .el-time-picker, .el-cascader, .el-slider, .el-rate, .el-upload, .ant-input, .ant-select, .ant-checkbox, .ant-radio, .ant-switch, .ant-date-picker, .ant-time-picker, .ant-cascader, .ant-slider, .ant-rate, .ant-upload, .ant-input-number, .v-text-field, .v-select, .v-checkbox, .v-radio, .v-switch, .v-date-picker, .v-time-picker, .v-slider, .v-rating, .MuiTextField-root, .MuiSelect-root, .MuiCheckbox-root, .MuiRadio-root, .MuiSwitch-root, .MuiSlider-root",
            );
            if (fieldIndex <= allInputs.length) {
              const targetElement = allInputs[fieldIndex - 1] as HTMLElement;
              // 检查元素是否已经有值（但允许覆盖空值或默认值）
              const currentValue = (targetElement as any).value || "";
              const isAlreadyFilled =
                currentValue.trim() !== "" &&
                currentValue.trim() !== value.trim() &&
                !currentValue.includes("请选择") &&
                !currentValue.includes("请输入");

              console.log(`🔍 fieldIndex=${fieldIndex} 元素检查：`, {
                tagName: targetElement.tagName,
                currentValue: `"${currentValue}"`,
                targetValue: `"${value}"`,
                isAlreadyFilled,
                element: targetElement,
              });

              if (!isAlreadyFilled) {
                domElement = targetElement as any;
                usedSelector = `fieldIndex[${fieldIndex}]`;
                console.log(`✅ 使用fieldIndex指定的元素（索引：${fieldIndex - 1}）`);
              } else {
                console.log(`⚠️ fieldIndex指定的元素已填充，跳过填充`);
                return false;
              }
            } else {
              console.log(`⚠️ fieldIndex ${fieldIndex} 超出范围（总共${allInputs.length}个元素）`);
              return false;
            }
          } else {
            // 按分数排序，选择最佳匹配
            candidates.sort((a, b) => b.score - a.score);
            // 查找第一个未填充的元素
            for (const candidate of candidates) {
              const elementValue = (candidate.element as any).value;
              const isAlreadyFilled =
                elementValue && elementValue.trim() !== "" && elementValue.trim() !== value.trim();
              if (!isAlreadyFilled) {
                domElement = candidate.element as any;
                usedSelector = `label="${label}"[分数：${candidate.score}]`;
                console.log(`✅ 使用最佳label匹配元素（分数：${candidate.score}）`);
                break;
              }
            }
          }
        }
      }

      // 策略2: 通过placeholder属性查找
      if (placeholder && !domElement) {
        // 首先查找UI框架日期组件
        const frameworkDateSelectors = [
          `.el-date-picker input[placeholder="${placeholder}"]`,
          `.ant-date-picker input[placeholder="${placeholder}"]`,
          `.v-date-picker input[placeholder="${placeholder}"]`,
          `.MuiDatePicker input[placeholder="${placeholder}"]`,
        ];

        let frameworkElements: NodeListOf<HTMLInputElement> | null = null;
        for (const selector of frameworkDateSelectors) {
          const elements = document.querySelectorAll(selector) as NodeListOf<HTMLInputElement>;
          if (elements.length > 0) {
            frameworkElements = elements;
            console.log(`🗓️ 通过框架日期选择器找到${elements.length}个元素：${selector}`);
            break;
          }
        }

        const elements =
          frameworkElements ||
          (document.querySelectorAll(
            `input[placeholder="${placeholder}"], textarea[placeholder="${placeholder}"]`,
          ) as NodeListOf<HTMLInputElement | HTMLTextAreaElement>);

        if (elements.length > 0) {
          console.log(`🔍 通过placeholder="${placeholder}"找到${elements.length}个元素，fieldIndex=${fieldIndex}`);

          const isDateField = type && ["date", "datetime-local", "time", "month", "week"].includes(type);
          const isDateLabel = label && /日期|时间|date|time/i.test(label);
          const isDatePlaceholder = /日期|时间|date|time/i.test(placeholder);

          if (fieldIndex && fieldIndex > 0) {
            // 如果指定了fieldIndex，直接在所有输入元素中查找指定索引的元素
            const allInputsRaw = document.querySelectorAll("input, textarea, select");
            // 过滤掉不可见的表单元素以优化性能
            const allInputs = Array.from(allInputsRaw).filter(element => {
              const style = window.getComputedStyle(element);
              const rect = element.getBoundingClientRect();
              
              // 检查元素是否可见
              const isVisible = 
                style.display !== 'none' &&
                style.visibility !== 'hidden' &&
                style.opacity !== '0' &&
                rect.width > 0 &&
                rect.height > 0;
              
              return isVisible;
            });
            if (fieldIndex <= allInputs.length) {
              const targetElement = allInputs[fieldIndex - 1] as
                | HTMLInputElement
                | HTMLTextAreaElement
                | HTMLSelectElement;
              // 检查元素是否已经有值（但允许覆盖空值或默认值）
              const currentValue = targetElement.value || "";
              const isAlreadyFilled =
                currentValue.trim() !== "" &&
                currentValue.trim() !== value.trim() &&
                !currentValue.includes("请选择") &&
                !currentValue.includes("请输入");

              if (!isAlreadyFilled) {
                domElement = targetElement;
                usedSelector = `fieldIndex[${fieldIndex}]`;
                console.log(`✅ 使用fieldIndex指定的元素（索引：${fieldIndex - 1}）`);
              } else {
                console.log(`⚠️ fieldIndex指定的元素已填充，跳过填充`);
                return false;
              }
            } else {
              console.log(`⚠️ fieldIndex ${fieldIndex} 超出范围（总共${allInputs.length}个元素）`);
              return false;
            }
          } else if (elements.length === 1) {
            // 只有一个匹配元素，直接使用
            domElement = elements[0];
            usedSelector = `placeholder="${placeholder}"`;
            console.log(`✅ 只有一个匹配元素，直接使用`);
          } else {
            // 多个匹配但没有fieldIndex，查找第一个未填充的元素
            let targetElement = null;
            let targetIndex = -1;

            for (let i = 0; i < elements.length; i++) {
              const el = elements[i];
              const isAlreadyFilled = el.value && el.value.trim() !== "" && el.value.trim() !== value.trim();

              if (isDateField || isDateLabel || isDatePlaceholder) {
                const dateContainer = el.closest(".el-date-picker, .ant-date-picker, .v-date-picker, .MuiDatePicker");
                if (dateContainer && !isAlreadyFilled) {
                  targetElement = el;
                  targetIndex = i;
                  console.log(`🗓️ 优先选择日期组件中的元素（索引：${i}）`);
                  break;
                }
              }

              if (!isAlreadyFilled) {
                targetElement = el;
                targetIndex = i;
                break;
              }
            }

            if (targetElement) {
              domElement = targetElement;
              usedSelector = `placeholder="${placeholder}"[未填充索引${targetIndex}]`;
              console.log(`✅ 使用第一个未填充的元素（索引：${targetIndex}）`);
            } else {
              // 如果所有元素都已填充，使用第一个元素
              domElement = elements[0];
              usedSelector = `placeholder="${placeholder}"[默认第一个]`;
              console.log(`⚠️ 所有元素都已填充，使用第一个元素`);
            }
          }
        }
      }

      // 策略3: 通过类型和索引位置匹配
      if (!domElement && type) {
        const selector = type === "textarea" ? "textarea" : type === "select" ? "select" : `input[type="${type}"]`;
        const elements = Array.from(document.querySelectorAll(selector)) as (
          | HTMLInputElement
          | HTMLTextAreaElement
          | HTMLSelectElement
        )[];

        if (elements.length > 0) {
          // 尝试通过fieldIndex匹配
          if (fieldIndex && fieldIndex > 0) {
            // 如果指定了fieldIndex，直接在所有输入元素中查找指定索引的元素（与提取时保持一致）
            const allInputs = document.querySelectorAll(
              "input, textarea, select, .el-input, .el-select, .el-textarea, .el-checkbox, .el-radio, .el-switch, .el-date-picker, .el-time-picker, .el-cascader, .el-slider, .el-rate, .el-upload, .ant-input, .ant-select, .ant-checkbox, .ant-radio, .ant-switch, .ant-date-picker, .ant-time-picker, .ant-cascader, .ant-slider, .ant-rate, .ant-upload, .ant-input-number, .v-text-field, .v-select, .v-checkbox, .v-radio, .v-switch, .v-date-picker, .v-time-picker, .v-slider, .v-rating, .MuiTextField-root, .MuiSelect-root, .MuiCheckbox-root, .MuiRadio-root, .MuiSwitch-root, .MuiSlider-root",
            );
            if (fieldIndex <= allInputs.length) {
              const targetElement = allInputs[fieldIndex - 1] as
                | HTMLInputElement
                | HTMLTextAreaElement
                | HTMLSelectElement;
              // 检查元素是否已经有值（但允许覆盖空值或默认值）
              const currentValue = targetElement.value || "";
              const isAlreadyFilled =
                currentValue.trim() !== "" &&
                currentValue.trim() !== value.trim() &&
                !currentValue.includes("请选择") &&
                !currentValue.includes("请输入");

              if (!isAlreadyFilled) {
                domElement = targetElement;
                usedSelector = `fieldIndex[${fieldIndex}]`;
                console.log(`✅ 使用fieldIndex指定的元素（索引：${fieldIndex - 1}）`);
              } else {
                console.log(`⚠️ fieldIndex指定的元素已填充，跳过填充`);
                return false;
              }
            } else {
              console.log(`⚠️ fieldIndex ${fieldIndex} 超出范围（总共${allInputs.length}个元素）`);
              return false;
            }
          }

          // 如果索引匹配失败，尝试通过相似度匹配
          if (!domElement) {
            for (let i = 0; i < elements.length; i++) {
              const el = elements[i];
              const elPlaceholder = el.getAttribute("placeholder") || "";
              const elementLabel = getFormElementLabel(el);

              // 计算相似度分数
              let score = 0;
              if (placeholder && elPlaceholder.includes(placeholder)) score += 3;
              if (label && elementLabel) {
                if (elementLabel.includes(label) || label.includes(elementLabel)) {
                  score += 4; // 使用统一标签匹配给更高分数
                }
              }

              if (score >= 2) {
                domElement = el;
                usedSelector = `${selector}[${i}] (相似度匹配: ${score}, 标签: "${elementLabel}")`;
                break;
              }
            }
          }
        }
      }

      // 策略6: 模糊文本匹配
      if (!domElement && (label || placeholder)) {
        const searchText = label || placeholder;
        const allInputsRaw = document.querySelectorAll("input, textarea, select");
        // 过滤掉不可见的表单元素以优化性能
        const allInputs = Array.from(allInputsRaw).filter(element => {
          const style = window.getComputedStyle(element);
          const rect = element.getBoundingClientRect();
          
          // 检查元素是否可见
          const isVisible = 
            style.display !== 'none' &&
            style.visibility !== 'hidden' &&
            style.opacity !== '0' &&
            rect.width > 0 &&
            rect.height > 0;
          
          return isVisible;
        });

        for (const input of allInputs) {
          const inputEl = input as HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;

          // 使用统一的标签查找函数进行模糊匹配
          const elementLabel = getFormElementLabel(inputEl);
          if (elementLabel && (elementLabel.includes(searchText) || searchText.includes(elementLabel))) {
            domElement = inputEl;
            usedSelector = `模糊文本匹配: "${searchText}" -> "${elementLabel}"`;
            break;
          }
        }
      }

      if (domElement) {
        console.log(`✅ 找到字段元素，使用选择器：${usedSelector}，设置值：${value}`);
        console.log(`🎯 选中的DOM元素：`, {
          tagName: domElement.tagName,
          placeholder: domElement.getAttribute("placeholder"),
          className: domElement.className,
          currentValue: domElement.value,
        });

        // 使用增强的智能填充函数
        const fillSuccess = fillFormElementWithSmartMatching(domElement, value, label);

        if (fillSuccess) {
          // 添加视觉反馈
          const originalStyle = {
            backgroundColor: domElement.style.backgroundColor,
            border: domElement.style.border,
            transition: domElement.style.transition,
          };

          domElement.style.transition = "all 0.3s ease";
          domElement.style.backgroundColor = "#e6f7ff";
          domElement.style.border = "2px solid #1890ff";

          // 2秒后恢复原样
          setTimeout(() => {
            domElement!.style.backgroundColor = originalStyle.backgroundColor;
            domElement!.style.border = originalStyle.border;
            domElement!.style.transition = originalStyle.transition;
          }, 2000);
        }

        return fillSuccess;
      } else {
        console.warn(`❌ 未找到字段：${label || placeholder}`);
        return false;
      }
    } catch (error) {
      console.error("填充单个字段失败：", error);
      return false;
    }
  };

  // 填充下拉框的函数
  const fillSelectElement = (selectElement: HTMLSelectElement, value: string): boolean => {
    try {
      // 检查下拉框是否被禁用
      if (selectElement.disabled) {
        console.log(`⚠️ 跳过禁用的下拉框元素`);
        return false;
      }
      // 策略1: 精确匹配选项文本
      for (let i = 0; i < selectElement.options.length; i++) {
        const option = selectElement.options[i];
        if (option.text.trim() === value.trim()) {
          selectElement.selectedIndex = i;
          selectElement.value = option.value;
          // 触发多个事件确保兼容性
          selectElement.dispatchEvent(new Event("input", { bubbles: true }));
          selectElement.dispatchEvent(new Event("change", { bubbles: true }));
          selectElement.dispatchEvent(new Event("blur", { bubbles: true }));
          console.log(`✅ 下拉框精确匹配：${option.text}`);
          return true;
        }
      }

      // 策略2: 模糊匹配选项文本
      for (let i = 0; i < selectElement.options.length; i++) {
        const option = selectElement.options[i];
        if (
          option.text.toLowerCase().includes(value.toLowerCase()) ||
          value.toLowerCase().includes(option.text.toLowerCase())
        ) {
          selectElement.selectedIndex = i;
          selectElement.value = option.value;
          // 触发多个事件确保兼容性
          selectElement.dispatchEvent(new Event("input", { bubbles: true }));
          selectElement.dispatchEvent(new Event("change", { bubbles: true }));
          selectElement.dispatchEvent(new Event("blur", { bubbles: true }));
          console.log(`✅ 下拉框模糊匹配：${option.text}`);
          return true;
        }
      }

      // 策略3: 匹配选项值
      for (let i = 0; i < selectElement.options.length; i++) {
        const option = selectElement.options[i];
        if (option.value === value) {
          selectElement.selectedIndex = i;
          selectElement.value = option.value;
          // 触发多个事件确保兼容性
          selectElement.dispatchEvent(new Event("input", { bubbles: true }));
          selectElement.dispatchEvent(new Event("change", { bubbles: true }));
          selectElement.dispatchEvent(new Event("blur", { bubbles: true }));
          console.log(`✅ 下拉框值匹配：${option.value}`);
          return true;
        }
      }

      console.warn(`❌ 下拉框未找到匹配选项：${value}`);
      return false;
    } catch (error) {
      console.error("填充下拉框失败：", error);
      return false;
    }
  };

  // 填充日期元素的函数 - 优化版本，支持多组件精确匹配
  const fillDateElement = (dateElement: HTMLInputElement, value: string, label?: string): boolean => {
    try {
      // 检查是否已经填充过相同的值
      if (dateElement.value && dateElement.value.trim() === value.trim()) {
        console.log(`🔄 日期元素已有相同值，跳过填充：${value}`);
        return true;
      }

      console.log(`🗓️ 开始填充日期元素，原始值：${value}`);
      console.log(`📍 日期元素信息：`, {
        type: dateElement.type,
        name: dateElement.name,
        id: dateElement.id,
        className: dateElement.className,
        placeholder: dateElement.placeholder,
        currentValue: dateElement.value,
        label: label,
      });

      // 检查日期元素是否被禁用或只读
      if (dateElement.disabled) {
        console.log(`⚠️ 跳过禁用的日期元素`);
        return false;
      }

      if (dateElement.readOnly) {
        console.log(`⚠️ 跳过只读的日期元素`);
        return false;
      }

      // 检查元素是否可见
      const computedStyle = window.getComputedStyle(dateElement);
      if (computedStyle.display === "none" || computedStyle.visibility === "hidden") {
        console.warn(`⚠️ 日期元素不可见，跳过填充`);
        return false;
      }

      // 尝试解析和格式化日期
      const inputType = dateElement.type || "date";
      const formattedDate = formatDateForInput(value, inputType);
      if (!formattedDate) {
        console.warn(`⚠️ 日期格式化失败，原始值：${value}，输入类型：${inputType}`);
        return false;
      }

      console.log(`📅 格式化后的日期：${formattedDate}，输入类型：${inputType}`);

      try {
        // 先聚焦元素（模拟用户交互）
        dateElement.focus();

        // 使用原生setter确保值被正确设置
        const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value")?.set;
        if (nativeInputValueSetter) {
          nativeInputValueSetter.call(dateElement, formattedDate);
        } else {
          dateElement.value = formattedDate;
        }

        // 触发完整的事件序列（模拟真实用户操作）
        const events = [
          { type: "focus", bubbles: true },
          { type: "input", bubbles: true },
          { type: "change", bubbles: true },
          { type: "blur", bubbles: true },
        ];

        events.forEach(({ type, bubbles }) => {
          try {
            const event = new Event(type, { bubbles, cancelable: true });
            dateElement.dispatchEvent(event);
          } catch (e) {
            console.warn(`触发${type}事件失败:`, e);
          }
        });

        // 单独处理InputEvent，因为它可能在某些环境中不可用
        try {
          if (typeof InputEvent !== "undefined") {
            const inputEvent = new InputEvent("input", {
              bubbles: true,
              cancelable: true,
              data: formattedDate,
            });
            dateElement.dispatchEvent(inputEvent);
          }
        } catch (e) {
          console.warn("触发InputEvent失败:", e);
        }

        // 触发HTML5日期输入特定事件
        if (inputType === "date" || inputType === "datetime-local" || inputType === "time") {
          try {
            const customEvent = new CustomEvent("datechange", {
              bubbles: true,
              detail: { value: formattedDate, type: inputType },
            });
            dateElement.dispatchEvent(customEvent);
          } catch (e) {
            console.warn("触发日期自定义事件失败:", e);
          }
        }

        // 验证填充结果
        setTimeout(() => {
          if (dateElement.value === formattedDate) {
            console.log(`✅ 日期元素填充成功并验证：${formattedDate}`);
          } else {
            console.warn(`⚠️ 日期元素填充后值不匹配，期望：${formattedDate}，实际：${dateElement.value}`);
          }
        }, 100);

        console.log(`✅ 日期元素填充完成：${formattedDate}`);
        return true;
      } catch (error) {
        console.error("触发日期事件失败：", error);
        return false;
      }
    } catch (error) {
      console.error("填充日期失败：", error);
      return false;
    }
  };

  // 填充复选框和单选框的函数
  const fillCheckboxRadioElement = (element: HTMLInputElement, value: string): boolean => {
    try {
      // 检查复选框/单选框是否被禁用或只读
      if (element.disabled) {
        console.log(`⚠️ 跳过禁用的${element.type}元素`);
        return false;
      }

      if (element.readOnly) {
        console.log(`⚠️ 跳过只读的${element.type}元素`);
        return false;
      }
      const shouldCheck =
        ["true", "1", "yes", "是", "checked"].includes(value.toLowerCase()) ||
        (element.value && element.value.toLowerCase() === value.toLowerCase());

      if (element.checked !== shouldCheck) {
        element.checked = shouldCheck;
        element.dispatchEvent(new Event("change", { bubbles: true }));
        console.log(`✅ ${element.type}设置为：${shouldCheck}`);
      }
      return true;
    } catch (error) {
      console.error("填充复选框/单选框失败：", error);
      return false;
    }
  };

  // 填充UI框架组件的函数
  const fillUIFrameworkComponent = (element: HTMLElement, value: string, label?: string): boolean => {
    try {
      // 检查元素是否被禁用（通过disabled属性或CSS类名）
      const inputElement = element as HTMLInputElement;
      if (inputElement.disabled) {
        console.log(`⚠️ 跳过禁用的UI框架组件`);
        return false;
      }

      // 检查是否有禁用相关的CSS类名
      if (
        element.classList.contains("disabled") ||
        element.classList.contains("is-disabled") ||
        element.classList.contains("ant-input-disabled") ||
        (element.classList.contains("el-input__inner") && element.closest(".is-disabled"))
      ) {
        console.log(`⚠️ 跳过禁用状态的UI框架组件`);
        return false;
      }
      // Element UI 组件处理 - 优先匹配日期选择器，避免被当作普通input处理
      const elDateContainer = element.closest(".el-date-picker, .el-date-editor");
      if (elDateContainer) {
        return fillElementUIComponent(elDateContainer as HTMLElement, value, label);
      }

      // 然后处理其他Element UI组件
      const elContainer = element.closest(".el-select, .el-textarea, .el-input, .el-checkbox, .el-radio");
      if (elContainer) {
        return fillElementUIComponent(elContainer as HTMLElement, value, label);
      }

      // Ant Design 组件处理
      const antContainer = element.closest(
        ".ant-select, .ant-date-picker, .ant-input, .ant-textarea, .ant-checkbox, .ant-radio",
      );
      if (antContainer) {
        return fillAntDesignComponent(antContainer as HTMLElement, value, label);
      }

      // Vuetify 组件处理
      const vuetifyContainer = element.closest(
        ".v-select, .v-text-field, .v-textarea, .v-input, .v-checkbox, .v-radio, .v-switch, .v-date-picker",
      );
      if (vuetifyContainer) {
        return fillVuetifyComponent(vuetifyContainer as HTMLElement, value, label);
      }

      // Material-UI 组件处理
      const muiContainer = element.closest(
        ".MuiTextField-root, .MuiInput-root, .MuiOutlinedInput-root, .MuiFilledInput-root, .MuiSelect-root, .MuiCheckbox-root, .MuiRadio-root, .MuiSwitch-root",
      );
      if (muiContainer) {
        return fillMaterialUIComponent(muiContainer as HTMLElement, value, label);
      }

      return false;
    } catch (error) {
      console.error("填充UI框架组件失败：", error);
      return false;
    }
  };

  // Element UI 组件填充 - 优化版本，支持多组件精确匹配
  const fillElementUIComponent = (container: HTMLElement, value: string, label?: string): boolean => {
    try {
      // Element UI Select - 直接填充输入框
      if (container.classList.contains("el-select")) {
        const input = container.querySelector(".el-input__inner") as HTMLInputElement;
        if (input && input !== null) {
          console.log(`📋 找到Element UI Select输入框，直接填充：${value}`);
          input.value = value;

          // 触发必要的事件
          const events = ["input", "change", "blur"];
          events.forEach((eventType) => {
            const event = new Event(eventType, { bubbles: true });
            input.dispatchEvent(event);
          });
          // 点击打开下拉框
          // container.click();

          // 使用更长的延迟和重试机制确保下拉选项加载完成
          // const trySelectOption = (retryCount = 0) => {
          //   setTimeout(() => {
          //     // 查找所有可见的Element UI Select下拉框
          //     const dropdowns = document.querySelectorAll('.el-select-dropdown:not(.el-select-dropdown--hidden)');
          //     let targetDropdown = null;

          //     // 如果只有一个可见下拉框，直接使用
          //     if (dropdowns.length === 1) {
          //       targetDropdown = dropdowns[0];
          //     } else if (dropdowns.length > 1) {
          //       // 如果有多个下拉框，尝试找到最近显示的那个（通常是z-index最高的）
          //       let maxZIndex = -1;
          //       for (const dropdown of dropdowns) {
          //         const style = window.getComputedStyle(dropdown as HTMLElement);
          //         const zIndex = parseInt(style.zIndex) || 0;
          //         if (zIndex > maxZIndex) {
          //           maxZIndex = zIndex;
          //           targetDropdown = dropdown;
          //         }
          //       }
          //     }

          //     // 如果没有找到下拉框，直接填充输入框
          //     if (!targetDropdown) {
          //       if (retryCount < 3) {
          //         trySelectOption(retryCount + 1);
          //         return;
          //       }
          //       console.warn('未找到Element UI Select下拉框，直接填充输入框');
          //       // 直接填充输入框
          //       input.value = value;
          //       input.focus();
          //       const events = ["input", "change", "blur"];
          //       events.forEach((eventType) => {
          //         const event = new Event(eventType, { bubbles: true });
          //         input.dispatchEvent(event);
          //       });
          //       console.log(`✅ Element UI Select直接填充输入框：${value}`);
          //       return;
          //     }

          //     // 在对应的下拉框内查找选项
          //     const options = targetDropdown.querySelectorAll(".el-select-dropdown__item");
          //     console.log(`🔍 在当前下拉框中找到${options.length}个选项（尝试${retryCount + 1}次）`);

          //     // 如果没有找到选项，直接填充输入框
          //     if (options.length === 0) {
          //       if (retryCount < 3) {
          //         trySelectOption(retryCount + 1);
          //         return;
          //       }
          //       console.warn('未找到选项，直接填充输入框');
          //       // 关闭下拉框
          //       const backdrop = document.querySelector('.el-select-dropdown') || document.querySelector('.v-popper__backdrop');
          //       if (backdrop) {
          //         (backdrop as HTMLElement).click();
          //       }
          //       // 直接填充输入框
          //       input.value = value;
          //       input.focus();
          //       const events = ["input", "change", "blur"];
          //       events.forEach((eventType) => {
          //         const event = new Event(eventType, { bubbles: true });
          //         input.dispatchEvent(event);
          //       });
          //       console.log(`✅ Element UI Select直接填充输入框：${value}`);
          //       return;
          //     }

          //     // 尝试匹配选项
          //     let optionFound = false;
          //     for (const option of options) {
          //       const optionText = option.textContent?.trim() || '';
          //       const searchValue = value.trim();
          //       if (
          //         optionText === searchValue ||
          //         optionText.toLowerCase().includes(searchValue.toLowerCase())
          //       ) {
          //         (option as HTMLElement).click();
          //         console.log(`✅ Element UI Select选择：${optionText}`);
          //         optionFound = true;
          //         return;
          //       }
          //     }

          //     // 如果没有找到匹配的选项，直接在输入框中填充值
          //     if (!optionFound) {
          //       console.warn(`⚠️ Element UI Select未找到匹配的选项：${value}，直接填充输入框`);

          //       // 关闭下拉框
          //       const backdrop = document.querySelector('.el-select-dropdown') || document.querySelector('.v-popper__backdrop');
          //       if (backdrop) {
          //         (backdrop as HTMLElement).click();
          //       }

          //       // 直接在输入框中填充值
          //       input.value = value;
          //       input.focus();

          //       // 触发必要的事件
          //       const events = ["input", "change", "blur"];
          //       events.forEach((eventType) => {
          //         const event = new Event(eventType, { bubbles: true });
          //         input.dispatchEvent(event);
          //       });

          //       console.log(`✅ Element UI Select直接填充输入框：${value}`);
          //     }
          //   }, 200 + retryCount * 100); // 递增延迟时间
          // };

          // trySelectOption();
          return true; // 返回true表示已处理
        } else {
          console.warn(`⚠️ Element UI Select中未找到.el-input__inner元素`);
        }
      }

      // Element UI Input - 普通输入框（排除日期选择器）
      if (
        container.classList.contains("el-input") &&
        !container.classList.contains("el-date-editor") &&
        !container.classList.contains("el-date-picker")
      ) {
        const input = container.querySelector(".el-input__inner") as HTMLInputElement;
        if (input && input !== null) {
          console.log(`📋 找到Element UI Input输入框，直接填充：${value}`);
          input.value = value;

          // 触发必要的事件
          const events = ["input", "change", "blur"];
          events.forEach((eventType) => {
            const event = new Event(eventType, { bubbles: true });
            input.dispatchEvent(event);
          });

          console.log(`✅ Element UI Input填充完成：${value}`);
          return true;
        } else {
          console.warn(`⚠️ Element UI Input中未找到.el-input__inner元素`);
        }
      }

      // Element UI Textarea - 文本域
      if (container.classList.contains("el-textarea")) {
        const textarea = container.querySelector(".el-textarea__inner") as HTMLTextAreaElement;
        if (textarea && textarea !== null) {
          console.log(`📋 找到Element UI Textarea，直接填充：${value}`);
          textarea.value = value;

          // 触发必要的事件
          const events = ["input", "change", "blur"];
          events.forEach((eventType) => {
            const event = new Event(eventType, { bubbles: true });
            textarea.dispatchEvent(event);
          });

          console.log(`✅ Element UI Textarea填充完成：${value}`);
          return true;
        } else {
          console.warn(`⚠️ Element UI Textarea中未找到.el-textarea__inner元素`);
        }
      }

      // Element UI DatePicker (支持 el-date-picker 和 el-date-editor) - 优化版本
      if (container.classList.contains("el-date-picker") || container.classList.contains("el-date-editor")) {
        return fillElementUIDatePicker(container, value, label);
      }

      return false;
    } catch (error) {
      console.error("Element UI组件填充失败：", error);
      return false;
    }
  };

  // Element UI DatePicker 专用填充函数 - 增强多组件支持
  const fillElementUIDatePicker = (container: HTMLElement, value: string, label?: string): boolean => {
    try {
      const input = container.querySelector("input") as HTMLInputElement;
      if (!input) {
        console.warn(`⚠️ Element UI DatePicker中未找到input元素`);
        return false;
      }

      // 检查是否已经填充过相同的值
      if (input.value && input.value.trim() === value.trim()) {
        console.log(`🔄 Element UI DatePicker已有相同值，跳过填充：${value}`);
        return true;
      }

      console.log(`🗓️ 找到Element UI DatePicker输入框，准备填充日期：${value}`);
      console.log(`📍 DatePicker容器信息：`, {
        className: container.className,
        id: container.id,
        name: input.name,
        placeholder: input.placeholder,
        currentValue: input.value,
      });

      const formattedDate = formatDateForInput(value, "date");
      if (!formattedDate) {
        console.warn(`⚠️ 日期格式化失败，原始值：${value}`);
        return false;
      }

      try {
        // 方法1: 尝试通过Vue实例更新（最佳方案）
        // const vueInstance = (input as any).__vue__ || (container as any).__vue__;
        // if (vueInstance) {
        //   console.log(`🎯 找到Vue实例，尝试通过Vue更新`);

        //   // 尝试多种Vue实例更新方式
        //   if (vueInstance.$emit) {
        //     vueInstance.$emit('input', formattedDate);
        //     vueInstance.$emit('change', formattedDate);
        //     console.log(`✅ Element UI DatePicker通过Vue $emit填充成功：${formattedDate}`);
        //     return true;
        //   }

        //   // 尝试直接设置Vue组件的value
        //   if (vueInstance.value !== undefined) {
        //     vueInstance.value = formattedDate;
        //     if (vueInstance.$forceUpdate) {
        //       vueInstance.$forceUpdate();
        //     }
        //     console.log(`✅ Element UI DatePicker通过Vue value填充成功：${formattedDate}`);
        //     return true;
        //   }
        // }

        // // 方法2: 查找父级Vue组件实例
        // let parentElement = container.parentElement;
        // let attempts = 0;
        // while (parentElement && attempts < 5) {
        //   const parentVue = (parentElement as any).__vue__;
        //   if (parentVue && parentVue.$emit) {
        //     console.log(`🎯 找到父级Vue实例，尝试更新`);
        //     parentVue.$emit('input', formattedDate);
        //     parentVue.$emit('change', formattedDate);
        //     console.log(`✅ Element UI DatePicker通过父级Vue填充成功：${formattedDate}`);
        //     return true;
        //   }
        //   parentElement = parentElement.parentElement;
        //   attempts++;
        // }
        // const vueInstance = findVueInstance(container); // 使用辅助函数

        // if (vueInstance) {
        //   // 如果找到了，就执行之前的最佳方案
        //   const dateObject = new Date(value);
        //   if (isNaN(dateObject.getTime())) {
        //     return false;
        //   }
        //   vueInstance.$emit("input", dateObject);
        //   vueInstance.$emit("change", dateObject);
        //   console.log("✅ 通过父元素的Vue实例填充成功!");
        //   return true;
        // }
        // 方法3: 备用方案 - 直接设置值并触发事件
        console.log(`🔄 Vue实例方法失败，使用备用方案`);

        const PANEL_SELECTOR = ".el-picker-panel.el-date-picker.el-popper";
        const PREV_MONTH_SELECTOR = ".el-picker-panel__icon-btn.el-date-picker__prev-btn.el-icon-arrow-left";
        const NEXT_MONTH_SELECTOR = ".el-picker-panel__icon-btn.el-date-picker__next-btn.el-icon-arrow-right";
        const HEADER_LABEL_SELECTOR = ".el-date-picker__header-label";

        // 为当前输入框生成唯一标识
        const inputId = input.id || input.name || `datepicker_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        if (!input.id && !input.dataset.pickerId) {
          input.dataset.pickerId = inputId;
        }

        // 不需要聚焦，直接填充值即可

        // 触发多种事件确保框架能够响应
        const events = ["focus", "input", "change", "blur"];

        events.forEach((eventType) => {
          try {
            const event = new Event(eventType, { bubbles: true, cancelable: true });
            input.dispatchEvent(event);
          } catch (e) {
            console.warn(`触发${eventType}事件失败:`, e);
          }
        });

        setTimeout(() => {
          console.log(`步骤 2: 查找与当前输入框关联的日历面板`);

          // 查找所有可能的日历面板
          const allPanels = document.querySelectorAll(PANEL_SELECTOR);
          let pickerPanel: Element | null = null;

          if (allPanels.length === 0) {
            console.error("❌ 失败：未找到任何日历弹窗。");
            return;
          } else if (allPanels.length === 1) {
            // 只有一个面板，直接使用
            pickerPanel = allPanels[0];
            console.log("✅ 找到唯一日历面板，直接使用");
          } else {
            // 多个面板，需要找到与当前输入框关联的面板
            console.log(`🔍 找到${allPanels.length}个日历面板，正在匹配关联面板...`);

            const inputRect = input.getBoundingClientRect();
            const inputId = input.dataset.pickerId || input.id || input.name;
            
            console.log(`📍 输入框位置: x=${inputRect.left}, y=${inputRect.top}, id=${inputId}`);

            // 方法1: 通过DOM关系查找 - 查找共同的父容器
            let bestPanel: Element | null = null;
            let bestScore = -1;

            allPanels.forEach((panel, index) => {
              const panelElement = panel as HTMLElement;
              const panelRect = panelElement.getBoundingClientRect();
              
              // 检查面板可见性
              const isVisible = 
                panelRect.width > 0 && 
                panelRect.height > 0 &&
                window.getComputedStyle(panelElement).visibility !== "hidden" &&
                window.getComputedStyle(panelElement).display !== "none";

              if (!isVisible) {
                console.log(`  面板${index}: 不可见，跳过`);
                return;
              }

              let score = 0;
              
              // 评分标准1: DOM层级关系 - 查找共同祖先
              const commonAncestor = findCommonAncestor(input, panelElement);
              if (commonAncestor) {
                const inputDepth = getElementDepth(input, commonAncestor);
                const panelDepth = getElementDepth(panelElement, commonAncestor);
                // 层级越近得分越高
                score += Math.max(0, 20 - Math.abs(inputDepth - panelDepth));
                console.log(`  面板${index}: DOM关系得分 ${Math.max(0, 20 - Math.abs(inputDepth - panelDepth))}`);
              }

              // 评分标准2: 位置关系 - 面板应该在输入框附近
              const distance = Math.sqrt(
                Math.pow(panelRect.left - inputRect.left, 2) + 
                Math.pow(panelRect.top - inputRect.bottom, 2)
              );
              const proximityScore = Math.max(0, 50 - distance / 10);
              score += proximityScore;
              console.log(`  面板${index}: 位置得分 ${proximityScore.toFixed(1)} (距离: ${distance.toFixed(1)}px)`);

              // 评分标准3: z-index - 更高的z-index表示更近期打开
              const zIndex = parseInt(window.getComputedStyle(panelElement).zIndex) || 0;
              score += Math.min(zIndex / 100, 10); // 最多10分
              console.log(`  面板${index}: z-index得分 ${Math.min(zIndex / 100, 10).toFixed(1)} (z-index: ${zIndex})`);

              // 评分标准4: 时间戳关联 - 如果有唯一标识符
              if (inputId && panelElement.dataset.relatedInput === inputId) {
                score += 100; // 直接关联得最高分
                console.log(`  面板${index}: 直接关联得分 100`);
              }

              console.log(`  面板${index}: 总得分 ${score.toFixed(1)}`);

              if (score > bestScore) {
                bestScore = score;
                bestPanel = panelElement;
              }
            });

            if (bestPanel) {
              pickerPanel = bestPanel;
              console.log(`✅ 通过综合评分找到最佳关联面板 (得分: ${bestScore.toFixed(1)})`);
              
              // 为面板添加关联标记，便于下次识别
              if (inputId) {
                (bestPanel as HTMLElement).dataset.relatedInput = inputId;
              }
            }

            // 方法2: 备用方案 - 如果评分方法失败，使用最新的可见面板
            if (!pickerPanel) {
              for (let i = allPanels.length - 1; i >= 0; i--) {
                const panel = allPanels[i] as HTMLElement;
                const rect = panel.getBoundingClientRect();
                const isVisible = rect.width > 0 && rect.height > 0;
                if (isVisible) {
                  pickerPanel = panel;
                  console.log(`⚠️ 使用备用方案：最后一个可见面板 (索引: ${i})`);
                  break;
                }
              }
            }
          }

          // 辅助函数：查找两个元素的共同祖先
          function findCommonAncestor(elem1: Element, elem2: Element): Element | null {
            const ancestors1 = [];
            let current: Element | null = elem1;
            while (current) {
              ancestors1.push(current);
              current = current.parentElement;
            }

            current = elem2;
            while (current) {
              if (ancestors1.includes(current)) {
                return current;
              }
              current = current.parentElement;
            }
            return null;
          }

          // 辅助函数：计算元素在祖先中的深度
          function getElementDepth(element: Element, ancestor: Element): number {
            let depth = 0;
            let current: Element | null = element;
            while (current && current !== ancestor) {
              depth++;
              current = current.parentElement;
            }
            return current === ancestor ? depth : -1;
          }

          if (!pickerPanel) {
            console.error("❌ 失败：无法确定要操作的日历面板。");
            return;
          }

          console.log("✅ 成功确定目标日历面板!", pickerPanel);

          // 根据你的截图定制的日期单元格选择器
          const DAY_CELL_SELECTOR = "td.available span";
          const targetDate = new Date(value);
          const targetYear = targetDate.getFullYear();
          const targetMonth = targetDate.getMonth(); // 0-11 for Jan-Dec
          const targetDay = targetDate.getDate().toString();

          // 动态计算最大尝试次数：基于目标日期与当前日期的差距
          const currentDate = new Date();
          const monthsDiff = Math.abs(
            (targetYear - currentDate.getFullYear()) * 12 + (targetMonth - currentDate.getMonth()),
          );
          const maxAttempts = Math.max(24, monthsDiff + 6); // 至少24次，或者根据月份差距+6次缓冲

          console.log(`🎯 目标日期: ${targetYear}-${targetMonth + 1}-${targetDay}`);
          console.log(
            `📅 当前日期: ${currentDate.getFullYear()}-${currentDate.getMonth() + 1}-${currentDate.getDate()}`,
          );
          console.log(`🔢 计算的最大尝试次数: ${maxAttempts} (月份差距: ${monthsDiff})`);

          // 使用一个递归函数来导航到正确的月份
          const navigateToCorrectMonth = (attempt = 0) => {
            // 安全阀：防止无限循环
            if (attempt > maxAttempts) {
              console.error(`❌ 失败：翻页次数过多 (${attempt}/${maxAttempts})，可能陷入了死循环或目标日期过远。`);
              return;
            }

            const headerLabels = pickerPanel.querySelectorAll(HEADER_LABEL_SELECTOR);
            if (headerLabels.length < 2) {
              console.error(
                `❌ 失败：找不到足够的年月显示标签 ("${HEADER_LABEL_SELECTOR}")，只找到了 ${headerLabels.length} 个`,
              );
              return;
            }

            // 从分离的元素中解析当前显示的年月
            const yearText = headerLabels[0].textContent || ""; // e.g., "2025 年"
            const monthText = headerLabels[1].textContent || ""; // e.g., "8 月"

            const yearMatch = yearText.match(/(\d{4})/);
            const monthMatch = monthText.match(/(\d{1,2})/);

            if (!yearMatch || !monthMatch) {
              console.error(`❌ 失败：无法从 "${yearText}" 和 "${monthText}" 中解析出年月`);
              return;
            }

            const currentYear = parseInt(yearMatch[1], 10);
            const currentMonth = parseInt(monthMatch[1], 10) - 1; // 转换为 0-11

            console.log(`目标: ${targetYear}-${targetMonth + 1} | 当前显示: ${currentYear}-${currentMonth + 1}`);

            // 检查年月是否匹配
            if (currentYear === targetYear && currentMonth === targetMonth) {
              console.log("✅ 年月已匹配! 准备点击日期...");
              clickDay();
              return;
            }

            // 判断是前进还是后退
            if (currentYear < targetYear || (currentYear === targetYear && currentMonth < targetMonth)) {
              console.log("  -> 点击 '下一月'");
              (pickerPanel.querySelector(NEXT_MONTH_SELECTOR) as HTMLElement)?.click();
            } else {
              console.log("  -> 点击 '上一月'");
              (pickerPanel.querySelector(PREV_MONTH_SELECTOR) as HTMLElement)?.click();
            }

            // UI需要时间渲染，再次调用自己进行下一次检查
            setTimeout(() => navigateToCorrectMonth(attempt + 1), 100);
          };

          const clickDay = () => {
            const dayCells = pickerPanel.querySelectorAll(DAY_CELL_SELECTOR);
            let targetCell: HTMLElement | null = null;
            for (const cell of dayCells) {
              if (cell.textContent?.trim() === targetDay) {
                targetCell = cell as HTMLElement;
                break;
              }
            }

            if (targetCell) {
              targetCell.click();
              console.log(`🎉 成功模拟点击，日期 ${targetDay} 已选择!`);
            } else {
              console.warn(`⚠️ 在目标月份中未找到可用日期: "${targetDay}"`);
              document.body.click();
            }
          };
          navigateToCorrectMonth();
        }, 500); // 500ms的延迟通常足够，如果网站较慢可适当增加

        return true;
        // 单独处理InputEvent，因为它可能在某些环境中不可用
        // try {
        //   if (typeof InputEvent !== "undefined") {
        //     const inputEvent = new InputEvent("input", {
        //       bubbles: true,
        //       cancelable: true,
        //       data: formattedDate,
        //     });
        //     input.dispatchEvent(inputEvent);
        //   }
        // } catch (e) {
        //   console.warn("触发InputEvent失败:", e);
        // }

        // // 额外触发Element UI特定事件
        // try {
        //   const customEvent = new CustomEvent("el.form.change", {
        //     bubbles: true,
        //     detail: { value: formattedDate },
        //   });
        //   input.dispatchEvent(customEvent);
        // } catch (e) {
        //   console.warn("触发Element UI自定义事件失败:", e);
        // }

        console.log(`✅ Element UI DatePicker填充成功：${formattedDate}`);
      } catch (error) {
        console.error(`❌ Element UI DatePicker填充失败：`, error);
        return false;
      }
    } catch (error) {
      console.error("Element UI DatePicker填充失败：", error);
      return false;
    }
  };
  // 这是一个用来查找 Vue 实例的辅助函数
  const findVueInstance = (element: HTMLElement, maxDepth = 10): any | null => {
    let currentElement: HTMLElement | null = element;
    let depth = 0;

    while (currentElement && depth < maxDepth) {
      // 检查当前元素是否有 __vue__ 实例
      if ((currentElement as any).__vue__) {
        console.log(`✅ 在第 ${depth} 层父元素上找到了Vue实例!`, currentElement);
        return (currentElement as any).__vue__;
      }
      // 如果没有，继续向上查找父元素
      currentElement = currentElement.parentElement;
      depth++;
    }

    console.warn(`向上查找 ${maxDepth} 层后，仍未找到Vue实例。`);
    return null;
  };
  // Ant Design 组件填充 - 优化版本，支持多组件精确匹配
  const fillAntDesignComponent = (container: HTMLElement, value: string, label?: string): boolean => {
    try {
      // Ant Design Select
      if (container.classList.contains("ant-select")) {
        const selector = container.querySelector(".ant-select-selector") as HTMLElement;
        if (selector && selector !== null) {
          console.log(`📋 找到Ant Design Select选择器，准备选择：${value}`);
          // 点击打开下拉框
          selector.click();

          setTimeout(() => {
            // 查找所有可见的Ant Design Select下拉框
            const dropdowns = document.querySelectorAll(".ant-select-dropdown:not(.ant-select-dropdown-hidden)");
            let targetDropdown = null;

            // 如果只有一个可见下拉框，直接使用
            if (dropdowns.length === 1) {
              targetDropdown = dropdowns[0];
            } else if (dropdowns.length > 1) {
              // 如果有多个下拉框，尝试找到最近显示的那个（通常是z-index最高的）
              let maxZIndex = -1;
              for (const dropdown of dropdowns) {
                const style = window.getComputedStyle(dropdown as HTMLElement);
                const zIndex = parseInt(style.zIndex) || 0;
                if (zIndex > maxZIndex) {
                  maxZIndex = zIndex;
                  targetDropdown = dropdown;
                }
              }
            }

            // 直接填充输入框的函数
            const fillInputDirectly = () => {
              const input = container.querySelector("input") as HTMLInputElement;
              if (input) {
                input.value = value;
                input.dispatchEvent(new Event("input", { bubbles: true }));
                input.dispatchEvent(new Event("change", { bubbles: true }));
                input.dispatchEvent(new Event("blur", { bubbles: true }));
                console.log(`✅ Ant Design Select直接填充输入框：${value}`);
                return true;
              }
              return false;
            };

            if (!targetDropdown) {
              console.warn("未找到Ant Design Select下拉框，直接填充输入框");
              return fillInputDirectly();
            }

            // 在对应的下拉框内查找选项
            const options = targetDropdown.querySelectorAll(".ant-select-item-option");
            console.log(`🔍 在当前下拉框中找到${options.length}个选项`);

            // 如果没有选项，直接填充输入框
            if (options.length === 0) {
              console.warn("未找到选项，直接填充输入框");
              // 关闭下拉框
              const backdrop = document.querySelector(".ant-select-dropdown") || document.body;
              if (backdrop) {
                (backdrop as HTMLElement).click();
              }
              return fillInputDirectly();
            }

            let optionFound = false;
            for (const option of options) {
              const optionText = option.textContent?.trim() || "";
              const searchValue = value.trim();
              if (optionText === searchValue || optionText.toLowerCase().includes(searchValue.toLowerCase())) {
                (option as HTMLElement).click();
                console.log(`✅ Ant Design Select选择：${optionText}`);
                optionFound = true;
                return true;
              }
            }

            // 如果没有找到匹配的选项，直接在输入框中填充值
            if (!optionFound) {
              console.warn(`⚠️ Ant Design Select未找到匹配的选项：${value}，直接填充输入框`);

              // 关闭下拉框
              const backdrop = document.querySelector(".ant-select-dropdown") || document.body;
              if (backdrop) {
                (backdrop as HTMLElement).click();
              }

              return fillInputDirectly();
            }
          }, 100);
        } else {
          console.warn(`⚠️ Ant Design Select中未找到.ant-select-selector元素`);
        }
      }

      // Ant Design Input - 普通输入框
      if (container.classList.contains("ant-input")) {
        const input = container.querySelector("input") as HTMLInputElement;
        if (input && input !== null) {
          console.log(`📋 找到Ant Design Input，直接填充：${value}`);
          input.value = value;

          // 触发必要的事件
          const events = ["input", "change", "blur"];
          events.forEach((eventType) => {
            const event = new Event(eventType, { bubbles: true });
            input.dispatchEvent(event);
          });

          console.log(`✅ Ant Design Input填充完成：${value}`);
          return true;
        } else {
          console.warn(`⚠️ Ant Design Input中未找到input元素`);
        }
      }

      // Ant Design Textarea - 文本域
      if (container.classList.contains("ant-textarea")) {
        const textarea = container.querySelector("textarea") as HTMLTextAreaElement;
        if (textarea && textarea !== null) {
          console.log(`📋 找到Ant Design Textarea，直接填充：${value}`);
          textarea.value = value;

          // 触发必要的事件
          const events = ["input", "change", "blur"];
          events.forEach((eventType) => {
            const event = new Event(eventType, { bubbles: true });
            textarea.dispatchEvent(event);
          });

          console.log(`✅ Ant Design Textarea填充完成：${value}`);
          return true;
        } else {
          console.warn(`⚠️ Ant Design Textarea中未找到textarea元素`);
        }
      }

      // Ant Design DatePicker - 优化版本
      if (container.classList.contains("ant-date-picker")) {
        return fillAntDesignDatePicker(container, value, label);
      }

      return false;
    } catch (error) {
      console.error("Ant Design组件填充失败：", error);
      return false;
    }
  };

  // Vuetify 组件填充
  const fillVuetifyComponent = (container: HTMLElement, value: string, label?: string): boolean => {
    try {
      // Vuetify Select
      if (container.classList.contains("v-select")) {
        const input = container.querySelector("input") as HTMLInputElement;
        if (input) {
          console.log(`📋 找到Vuetify Select输入框，准备选择：${value}`);
          // 点击打开下拉框
          container.click();

          setTimeout(() => {
            // 查找所有可见的Vuetify Select下拉框
            const dropdowns = document.querySelectorAll(
              ".v-menu__content:not(.v-menu__content--hidden), .v-overlay__content:not(.v-overlay__content--hidden)",
            );
            let targetDropdown = null;

            // 如果只有一个可见下拉框，直接使用
            if (dropdowns.length === 1) {
              targetDropdown = dropdowns[0];
            } else if (dropdowns.length > 1) {
              // 如果有多个下拉框，尝试找到最近显示的那个（通常是z-index最高的）
              let maxZIndex = -1;
              for (const dropdown of dropdowns) {
                const style = window.getComputedStyle(dropdown as HTMLElement);
                const zIndex = parseInt(style.zIndex) || 0;
                if (zIndex > maxZIndex) {
                  maxZIndex = zIndex;
                  targetDropdown = dropdown;
                }
              }
            }

            // 直接填充输入框的函数
            const fillInputDirectly = () => {
              if (input) {
                input.value = value;
                input.dispatchEvent(new Event("input", { bubbles: true }));
                input.dispatchEvent(new Event("change", { bubbles: true }));
                input.dispatchEvent(new Event("blur", { bubbles: true }));
                console.log(`✅ Vuetify Select直接填充输入框：${value}`);
                return true;
              }
              return false;
            };

            if (!targetDropdown) {
              console.warn("未找到Vuetify Select下拉框，直接填充输入框");
              return fillInputDirectly();
            }

            // 在对应的下拉框内查找选项
            const options = targetDropdown.querySelectorAll(".v-list-item");
            console.log(`🔍 在当前下拉框中找到${options.length}个选项`);

            // 如果没有选项，直接填充输入框
            if (options.length === 0) {
              console.warn("未找到选项，直接填充输入框");
              // 关闭下拉框
              const backdrop = document.querySelector(".v-overlay__scrim") || document.body;
              if (backdrop) {
                (backdrop as HTMLElement).click();
              }
              return fillInputDirectly();
            }

            let optionFound = false;
            for (const option of options) {
              const optionText = option.textContent?.trim() || "";
              const searchValue = value.trim();
              if (optionText === searchValue || optionText.toLowerCase().includes(searchValue.toLowerCase())) {
                (option as HTMLElement).click();
                console.log(`✅ Vuetify Select选择：${optionText}`);
                optionFound = true;
                return true;
              }
            }

            // 如果没有找到匹配的选项，直接在输入框中填充值
            if (!optionFound) {
              console.warn(`⚠️ Vuetify Select未找到匹配的选项：${value}，直接填充输入框`);

              // 关闭下拉框
              const backdrop = document.querySelector(".v-overlay__scrim") || document.body;
              if (backdrop) {
                (backdrop as HTMLElement).click();
              }

              return fillInputDirectly();
            }
          }, 100);
        }
      }

      // Vuetify Text Field
      if (container.classList.contains("v-text-field")) {
        const input = container.querySelector("input, textarea") as HTMLInputElement | HTMLTextAreaElement;
        if (input) {
          input.value = value;
          input.dispatchEvent(new Event("input", { bubbles: true }));
          input.dispatchEvent(new Event("change", { bubbles: true }));
          input.dispatchEvent(new Event("blur", { bubbles: true }));
          console.log(`✅ Vuetify Text Field填充：${value}`);
          return true;
        }
      }

      // Vuetify Textarea
      if (container.classList.contains("v-textarea")) {
        const textarea = container.querySelector("textarea") as HTMLTextAreaElement;
        if (textarea) {
          textarea.value = value;
          textarea.dispatchEvent(new Event("input", { bubbles: true }));
          textarea.dispatchEvent(new Event("change", { bubbles: true }));
          textarea.dispatchEvent(new Event("blur", { bubbles: true }));
          console.log(`✅ Vuetify Textarea填充：${value}`);
          return true;
        }
      }

      // Vuetify Input
      if (container.classList.contains("v-input")) {
        const input = container.querySelector("input") as HTMLInputElement;
        if (input) {
          input.value = value;
          input.dispatchEvent(new Event("input", { bubbles: true }));
          input.dispatchEvent(new Event("change", { bubbles: true }));
          input.dispatchEvent(new Event("blur", { bubbles: true }));
          console.log(`✅ Vuetify Input填充：${value}`);
          return true;
        }
      }

      return false;
    } catch (error) {
      console.error("Vuetify组件填充失败：", error);
      return false;
    }
  };

  // Material-UI 组件填充
  const fillMaterialUIComponent = (container: HTMLElement, value: string, label?: string): boolean => {
    try {
      // Material-UI Select
      if (container.classList.contains("MuiSelect-root")) {
        const selectButton = container.querySelector('[role="button"]') as HTMLElement;
        if (selectButton) {
          console.log(`📋 找到Material-UI Select按钮，准备选择：${value}`);
          // 点击打开下拉框
          selectButton.click();

          setTimeout(() => {
            // 查找所有可见的Material-UI Select下拉框
            const dropdowns = document.querySelectorAll(
              ".MuiPaper-root .MuiMenu-list, .MuiPopover-paper .MuiMenu-list",
            );
            let targetDropdown = null;

            // 如果只有一个可见下拉框，直接使用
            if (dropdowns.length === 1) {
              targetDropdown = dropdowns[0];
            } else if (dropdowns.length > 1) {
              // 如果有多个下拉框，尝试找到最近显示的那个（通常是z-index最高的）
              let maxZIndex = -1;
              for (const dropdown of dropdowns) {
                const style = window.getComputedStyle(dropdown as HTMLElement);
                const zIndex = parseInt(style.zIndex) || 0;
                if (zIndex > maxZIndex) {
                  maxZIndex = zIndex;
                  targetDropdown = dropdown;
                }
              }
            }

            // 直接填充输入框的函数
            const fillInputDirectly = () => {
              const input = container.querySelector("input") as HTMLInputElement;
              if (input) {
                input.value = value;
                input.dispatchEvent(new Event("input", { bubbles: true }));
                input.dispatchEvent(new Event("change", { bubbles: true }));
                input.dispatchEvent(new Event("blur", { bubbles: true }));
                console.log(`✅ Material-UI Select直接填充输入框：${value}`);
                return true;
              }
              return false;
            };

            if (!targetDropdown) {
              console.warn("未找到Material-UI Select下拉框，直接填充输入框");
              return fillInputDirectly();
            }

            // 在对应的下拉框内查找选项
            const options = targetDropdown.querySelectorAll('.MuiMenuItem-root, [role="option"]');
            console.log(`🔍 在当前下拉框中找到${options.length}个选项`);

            // 如果没有选项，直接填充输入框
            if (options.length === 0) {
              console.warn("未找到选项，直接填充输入框");
              // 关闭下拉框
              const backdrop = document.querySelector(".MuiBackdrop-root") || document.body;
              if (backdrop) {
                (backdrop as HTMLElement).click();
              }
              return fillInputDirectly();
            }

            let optionFound = false;
            for (const option of options) {
              const optionText = option.textContent?.trim() || "";
              const searchValue = value.trim();
              if (optionText === searchValue || optionText.toLowerCase().includes(searchValue.toLowerCase())) {
                (option as HTMLElement).click();
                console.log(`✅ Material-UI Select选择：${optionText}`);
                optionFound = true;
                return true;
              }
            }

            // 如果没有找到匹配的选项，直接在输入框中填充值
            if (!optionFound) {
              console.warn(`⚠️ Material-UI Select未找到匹配的选项：${value}，直接填充输入框`);

              // 关闭下拉框
              const backdrop = document.querySelector(".MuiBackdrop-root") || document.body;
              if (backdrop) {
                (backdrop as HTMLElement).click();
              }

              return fillInputDirectly();
            }
          }, 100);
        }
      }

      // Material-UI TextField
      if (container.classList.contains("MuiTextField-root")) {
        const input = container.querySelector("input, textarea") as HTMLInputElement | HTMLTextAreaElement;
        if (input) {
          input.value = value;
          input.dispatchEvent(new Event("input", { bubbles: true }));
          input.dispatchEvent(new Event("change", { bubbles: true }));
          input.dispatchEvent(new Event("blur", { bubbles: true }));
          console.log(`✅ Material-UI TextField填充：${value}`);
          return true;
        }
      }

      // Material-UI Input (基础输入组件)
      if (
        container.classList.contains("MuiInput-root") ||
        container.classList.contains("MuiOutlinedInput-root") ||
        container.classList.contains("MuiFilledInput-root")
      ) {
        const input = container.querySelector("input, textarea") as HTMLInputElement | HTMLTextAreaElement;
        if (input) {
          input.value = value;
          input.dispatchEvent(new Event("input", { bubbles: true }));
          input.dispatchEvent(new Event("change", { bubbles: true }));
          input.dispatchEvent(new Event("blur", { bubbles: true }));
          console.log(`✅ Material-UI Input填充：${value}`);
          return true;
        }
      }

      return false;
    } catch (error) {
      console.error("Material-UI组件填充失败：", error);
      return false;
    }
  };

  // Ant Design DatePicker 专用填充函数 - 增强多组件支持
  const fillAntDesignDatePicker = (container: HTMLElement, value: string, label?: string): boolean => {
    try {
      const input = container.querySelector("input") as HTMLInputElement;
      if (!input) {
        console.warn(`⚠️ Ant Design DatePicker中未找到input元素`);
        return false;
      }

      // 检查是否已经填充过相同的值
      if (input.value && input.value.trim() === value.trim()) {
        console.log(`🔄 Ant Design DatePicker已有相同值，跳过填充：${value}`);
        return true;
      }

      console.log(`🗓️ 找到Ant Design DatePicker输入框，准备填充日期：${value}`);
      console.log(`📍 DatePicker容器信息：`, {
        className: container.className,
        id: container.id,
        name: input.name,
        placeholder: input.placeholder,
        currentValue: input.value,
      });

      const formattedDate = formatDateForInput(value, "date");
      if (!formattedDate) {
        console.warn(`⚠️ 日期格式化失败，原始值：${value}`);
        return false;
      }

      try {
        // 方法1: 尝试通过React Fiber实例更新（最佳方案）
        const reactFiberKeys = [
          "_reactInternalFiber",
          "_reactInternalInstance",
          "__reactInternalInstance",
          "_reactInternals",
        ];
        let reactFiber = null;

        // 尝试从input和container中查找React Fiber
        for (const key of reactFiberKeys) {
          reactFiber = (input as any)[key] || (container as any)[key];
          if (reactFiber) break;
        }

        if (reactFiber) {
          console.log(`🎯 找到React Fiber实例，尝试通过React更新`);

          // 尝试通过memoizedProps.onChange
          if (reactFiber.memoizedProps && reactFiber.memoizedProps.onChange) {
            reactFiber.memoizedProps.onChange({
              target: { value: formattedDate },
              currentTarget: { value: formattedDate },
            });
            console.log(`✅ Ant Design DatePicker通过React onChange填充成功：${formattedDate}`);
            return true;
          }

          // 尝试通过pendingProps.onChange
          if (reactFiber.pendingProps && reactFiber.pendingProps.onChange) {
            reactFiber.pendingProps.onChange({
              target: { value: formattedDate },
              currentTarget: { value: formattedDate },
            });
            console.log(`✅ Ant Design DatePicker通过React pendingProps填充成功：${formattedDate}`);
            return true;
          }

          // 尝试查找父级React组件
          let parentFiber = reactFiber.return || reactFiber.parent;
          let attempts = 0;
          while (parentFiber && attempts < 5) {
            if (parentFiber.memoizedProps && parentFiber.memoizedProps.onChange) {
              console.log(`🎯 找到父级React组件，尝试更新`);
              parentFiber.memoizedProps.onChange({
                target: { value: formattedDate },
                currentTarget: { value: formattedDate },
              });
              console.log(`✅ Ant Design DatePicker通过父级React填充成功：${formattedDate}`);
              return true;
            }
            parentFiber = parentFiber.return || parentFiber.parent;
            attempts++;
          }
        }

        // 方法2: 查找父级DOM元素的React实例
        let parentElement = container.parentElement;
        let attempts = 0;
        while (parentElement && attempts < 5) {
          for (const key of reactFiberKeys) {
            const parentReact = (parentElement as any)[key];
            if (parentReact && parentReact.memoizedProps && parentReact.memoizedProps.onChange) {
              console.log(`🎯 找到父级DOM的React实例，尝试更新`);
              parentReact.memoizedProps.onChange({
                target: { value: formattedDate },
                currentTarget: { value: formattedDate },
              });
              console.log(`✅ Ant Design DatePicker通过父级DOM React填充成功：${formattedDate}`);
              return true;
            }
          }
          parentElement = parentElement.parentElement;
          attempts++;
        }

        // 方法3: 备用方案 - 直接设置值并触发事件
        console.log(`🔄 React实例方法失败，使用备用方案`);

        // 使用原生setter确保值被正确设置
        const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value")?.set;
        if (nativeInputValueSetter) {
          nativeInputValueSetter.call(input, formattedDate);
        } else {
          input.value = formattedDate;
        }

        // 不需要聚焦，直接填充值即可

        // 触发多种事件确保框架能够响应
        const events = ["focus", "input", "change", "blur"];

        events.forEach((eventType) => {
          try {
            const event = new Event(eventType, { bubbles: true, cancelable: true });
            input.dispatchEvent(event);
          } catch (e) {
            console.warn(`触发${eventType}事件失败:`, e);
          }
        });

        // 单独处理InputEvent，因为它可能在某些环境中不可用
        try {
          if (typeof InputEvent !== "undefined") {
            const inputEvent = new InputEvent("input", {
              bubbles: true,
              cancelable: true,
              data: formattedDate,
            });
            input.dispatchEvent(inputEvent);
          }
        } catch (e) {
          console.warn("触发InputEvent失败:", e);
        }

        // 额外触发Ant Design特定事件
        try {
          const customEvent = new CustomEvent("ant.form.change", {
            bubbles: true,
            detail: { value: formattedDate },
          });
          input.dispatchEvent(customEvent);
        } catch (e) {
          console.warn("触发Ant Design自定义事件失败:", e);
        }

        console.log(`✅ Ant Design DatePicker填充成功：${formattedDate}`);
        return true;
      } catch (error) {
        console.error(`❌ Ant Design DatePicker填充失败：`, error);
        return false;
      }
    } catch (error) {
      console.error("Ant Design DatePicker填充失败：", error);
      return false;
    }
  };

  // 日期格式化函数
  const formatDateForInput = (dateString: string, inputType: string): string | null => {
    try {
      // 尝试解析各种日期格式
      let date: Date | null = null;

      // 常见日期格式
      const dateFormats = [
        /^(\d{4})-(\d{1,2})-(\d{1,2})$/, // YYYY-MM-DD
        /^(\d{4})\/(\d{1,2})\/(\d{1,2})$/, // YYYY/MM/DD
        /^(\d{1,2})-(\d{1,2})-(\d{4})$/, // DD-MM-YYYY
        /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/, // DD/MM/YYYY
        /^(\d{4})年(\d{1,2})月(\d{1,2})日$/, // 中文格式
      ];

      // 尝试直接解析
      date = new Date(dateString);
      if (isNaN(date.getTime())) {
        // 如果直接解析失败，尝试正则匹配
        for (const format of dateFormats) {
          const match = dateString.match(format);
          if (match) {
            if (format.source.includes("年")) {
              // 中文格式
              date = new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]));
            } else if (format.source.startsWith("^(\\d{4})")) {
              // YYYY开头的格式
              date = new Date(parseInt(match[1]), parseInt(match[2]) - 1, parseInt(match[3]));
            } else {
              // DD开头的格式，假设是DD/MM/YYYY
              date = new Date(parseInt(match[3]), parseInt(match[2]) - 1, parseInt(match[1]));
            }
            break;
          }
        }
      }

      if (!date || isNaN(date.getTime())) {
        return null;
      }

      // 根据输入类型格式化
      switch (inputType) {
        case "date":
          return date.toISOString().split("T")[0]; // YYYY-MM-DD
        case "datetime-local":
          return date.toISOString().slice(0, 16); // YYYY-MM-DDTHH:mm
        case "time":
          return date.toTimeString().slice(0, 5); // HH:mm
        case "month":
          return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}`; // YYYY-MM
        case "week": {
          const year = date.getFullYear();
          const week = getWeekNumber(date);
          return `${year}-W${String(week).padStart(2, "0")}`; // YYYY-Www
        }
        default:
          return date.toISOString().split("T")[0];
      }
    } catch (error) {
      console.error("日期格式化失败：", error);
      return null;
    }
  };

  // 获取周数的辅助函数
  const getWeekNumber = (date: Date): number => {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil(((d.getTime() - yearStart.getTime()) / 86400000 + 1) / 7);
  };

  useEffect(() => {
    if (sseChat.displayedText && isFilling) {
      // 获取新增的文本内容（增量部分）
      const currentText = sseChat.displayedText;
      const lastProcessedLength = lastProcessedLengthRef.current;

      // 如果文本长度没有增加，说明没有新内容，直接返回
      if (currentText.length <= lastProcessedLength) {
        return;
      }

      // 性能优化：避免过于频繁的处理，但保证流式填充的及时性
      const textGrowth = currentText.length - lastProcessedLength;
      const now = Date.now();
      const timeSinceLastProcess = now - lastProcessTimeRef.current;

      // 调整条件：减少文本增长阈值，缩短时间间隔，确保更及时的流式处理
      // 如果文本增长少于20字符且距离上次处理不足100ms，则跳过
      if (textGrowth < 20 && timeSinceLastProcess < 100) {
        return;
      }

      lastProcessTimeRef.current = now;
      // console.log("📈 流式文本更新，当前长度：", currentText.length, "增量：", textGrowth);
      // console.log("📄 当前文本内容片段：", currentText.substring(Math.max(0, currentText.length - 200)));

      // 执行流式解析（包括表单字段和表格数据）
      const parseSuccess = parseDataFromStream(currentText);

      // 更新已处理的文本长度
      lastProcessedLengthRef.current = currentText.length;

      // if (parseSuccess) {
      //   console.log("✅ 成功解析并填充数据，当前已填充字段：", Array.from(filledFieldsRef.current));
      // }
    }
  }, [sseChat.displayedText, isFilling]);

  // 重置填充状态的useEffect
  useEffect(() => {
    if (!isFilling) {
      // 重置填充跟踪状态
      filledFieldsRef.current.clear();
      lastProcessedLengthRef.current = 0;
      lastFieldEndPositionRef.current = 0;
      console.log("重置填充状态");
    }
  }, [isFilling]);
  //当取消或者点击右上角x时要将mentions数据清空且重置填充状态
  const handleClose = useCallback(() => {
    onClose?.();
    clearMentionsData();
    setIsFilling(false);
    setLocalFileUploadResData(null);
    // 通知wrapper组件面板已关闭
    window.postMessage(
      {
        type: "FORM_DETECTOR_CLOSED",
      },
      "*",
    );
  }, [clearMentionsData]);

  // 暴露重置状态函数给父组件
  useEffect(() => {
    if (onResetStatus) {
      // 将重置函数绑定到window对象，供外部调用
      (window as any).resetFormDetectorStatus = () => {
        resetCompletionStatus();
        console.log("✅ 手动重置FormDetector状态完成");
      };
    }

    return () => {
      // 清理绑定
      if ((window as any).resetFormDetectorStatus) {
        delete (window as any).resetFormDetectorStatus;
      }
    };
  }, [onResetStatus]);

  // 监听 shouldExtractData prop 变化，自动提取表单数据
  // useEffect(() => {
  //   if (shouldExtractData) {
  //     console.log("收到提取数据指令，开始提取表单数据");
  //     handleExtractFormData();
  //     // 通知父组件数据提取已处理
  //     onDataExtracted?.();
  //   }
  // }, [shouldExtractData, handleExtractFormData, onDataExtracted]);

  return (
    <ConfigProvider
      getPopupContainer={(triggerNode) => {
        // 确保弹出层渲染在FormDetector面板内
        const panel = document.querySelector(".form-detector-panel");
        return (panel || triggerNode?.parentNode || document.body) as HTMLElement;
      }}
    >
      <PermissionProvider>
        <div
          ref={panelRef}
          className={`form-detector-panel ${isDragging ? "dragging" : ""}`}
          style={{
            left: `${position.x}px`,
            top: `${position.y}px`,
          }}
        >
          <div className="drag-handle" onMouseDown={handleMouseDown}>
            <h3>智能填充信息</h3>
            <button
              onClick={() => {
                handleClose();
              }}
              style={{
                background: "none",
                border: "none",
                fontSize: "18px",
                cursor: "pointer",
                color: "#999",
              }}
            >
              ×
            </button>
          </div>

          <div className="collector-note">
            注:请输入文本/选择知识库知识/上传本地文件，点击填充后， 自动将信息填充到页面表单中
          </div>
          <div style={{ padding: "16px" }}>
            {/* 智能输入助手 */}

            <div style={{ marginBottom: "12px" }}>
              <MentionsComponent
                ref={mentionsRef}
                agentId={state.bearer}
                formData={formData}
                onQueryChange={handleQueryChange}
                selectedfrom={selectedfrom}
                setSelectedfrom={setSelectedfrom}
                setLocalFileUploadResData={setLocalFileUploadResData}
              />
            </div>

            <div style={{ marginBottom: "10px", display: "flex", justifyContent: "center" }}>
              <Button onClick={handleClose} style={{ marginRight: "8px" }}>
                取消
              </Button>
              <div style={{ display: "flex", marginBottom: "10px" }}>
                <Button
                  type="primary"
                  loading={isFilling}
                  onClick={handleFillFormDataWithResult}
                  style={{ flex: 1 }}
                  disabled={isFilling}
                >
                  {isFilling ? "正在填充表单数据..." : "填充"}
                </Button>
                {isFilling && (
                  <Button
                    type="primary"
                    danger
                    onClick={handleStopFilling}
                    style={{ minWidth: "60px", marginLeft: "8px" }}
                  >
                    停止
                  </Button>
                )}
              </div>
              {/* <Button type="primary" onClick={handleProcessContract}>
                处理
              </Button> */}
            </div>
          </div>
        </div>
      </PermissionProvider>

      {/* 合同信息弹窗 */}
      {contractInfoVisible && (
        <ContractInfoModal
          visible={contractInfoVisible}
          onClose={handleCloseContractInfo}
          onReupload={handleReupload}
        />
      )}
    </ConfigProvider>
  );
};

export default FormDetector;
