// 变量定义
@primary-color: #1890ff;
@primary-hover: #4096ff;
@primary-active: #0958d9;
@primary-bg: #e6f4ff;
@text-color: #000000d9;
@text-secondary: #00000073;
@text-tertiary: #00000040;
@text-quaternary: #00000026;
@bg-base: #ffffff;
@border-color: #d9d9d9;
@border-secondary: #f0f0f0;
@fill-tertiary: #f5f5f5;
@font-size-base: 14px;
@font-size-sm: 12px;
@font-size-lg: 16px;
@line-height-base: 1.5714285714285714;
@border-radius-base: 6px;
@border-radius-sm: 4px;
@padding-xs: 8px;
@padding-xxs: 4px;
@margin-xs: 8px;
@margin-xxs: 4px;
@z-index-max: 2147483647;
@panel-width: 400px;
@panel-min-width: 350px;
@panel-min-height: 200px;
@shadow-base: 0 4px 12px rgba(0, 0, 0, 0.15);
@shadow-hover: 0 6px 16px rgba(0, 0, 0, 0.2);
@font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;

// 混合
.reset-all() {
  all: initial !important;
}

.base-font() {
  font-family: @font-family !important;
  color: @text-color !important;
  font-size: @font-size-base !important;
  line-height: @line-height-base !important;
}

.input-base() {
  .reset-all();
  display: inline-block !important;
  box-sizing: border-box !important;
  width: 100% !important;
  min-height: 32px !important;
  padding: 4px 11px !important;
  color: rgba(0, 0, 0, 0.88) !important;
  font-size: @font-size-base !important;
  line-height: @line-height-base !important;
  background: @bg-base !important;
  border: 1px solid @border-color !important;
  border-radius: @border-radius-base !important;
  transition: all 0.2s !important;
  font-family: @font-family !important;

  &:focus {
    border-color: @primary-hover !important;
    box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1) !important;
    outline: none !important;
  }
}

.button-base() {
  display: inline-block !important;
  cursor: pointer !important;
  border: none !important;
  background: none !important;
  padding: 0 !important;
  margin: 0 !important;
  font-family: inherit !important;
}

// 光标闪烁动画
@keyframes blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

// // FormDetector 主面板样式
.form-detector-panel {
  // 重新设置必要的样式
  position: absolute;
  width: 700px;
  min-width: @panel-min-width !important;
  max-width: 800px !important;
  max-height: 80vh !important;
  background: #ffffff;
  border: 1px solid @border-color;
  border-radius: 5px;
  box-shadow: @shadow-base;
  // z-index: 10000 !important;
  font-family: @font-family;
  overflow: visible;
  min-height: @panel-min-height;
  user-select: none;
  transition: box-shadow 0.2s ease;
  display: block;
  color: #000;
  font-size: @font-size-base;
  line-height: 1.5;
  text-align: left;
  direction: ltr;
  pointer-events: auto;
  // 为Modal提供定位上下文
  transform: translateZ(0); // 创建新的层叠上下文
  // 重置可能影响布局的属性，但保留事件处理
  margin: 0;
  padding: 0;
  box-sizing: border-box;

  &:hover {
    box-shadow: @shadow-hover;
  }

  // 确保面板内元素样式正确，但不重置事件处理
  * {
    box-sizing: border-box;
    font-family: @font-family;
  }
  h3 {
    margin: 0;
  }
  // CSS变量定义，确保antd组件正确显示
  --ant-color-text: @text-color;
  --ant-color-text-secondary: @text-secondary;
  --ant-color-text-tertiary: @text-tertiary;
  --ant-color-text-quaternary: @text-quaternary;
  --ant-color-primary: @primary-color;
  --ant-color-primary-hover: @primary-hover;
  --ant-color-primary-active: @primary-active;
  --ant-color-primary-bg: @primary-bg;
  --ant-color-bg-base: @bg-base;
  --ant-color-bg-container: @bg-base;
  --ant-color-fill-tertiary: @fill-tertiary;
  --ant-color-border: @border-color;
  --ant-color-border-secondary: @border-secondary;
  --ant-font-size: @font-size-base;
  --ant-font-size-sm: @font-size-sm;
  --ant-font-size-lg: @font-size-lg;
  --ant-line-height: @line-height-base;
  --ant-margin-xs: @margin-xs;
  --ant-margin-xxs: @margin-xxs;
  --ant-padding-xs: @padding-xs;
  --ant-padding-xxs: @padding-xxs;
  --ant-border-radius: @border-radius-base;
  --ant-border-radius-sm: @border-radius-sm;
  --ant-button-text-hover-bg: rgba(0, 0, 0, 0.06);

  // 拖拽手柄样式
  .drag-handle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: grab;
    padding: 12px 16px;
    background: linear-gradient(135deg, @fill-tertiary 0%, #e8e8e8 100%);
    border-bottom: 1px solid @border-color;
    user-select: none;
    font-family: @font-family;

    &:active {
      cursor: grabbing;
    }
  }

  &.dragging .drag-handle {
    cursor: grabbing !important;
  }

  .collector-note {
    margin: 12px;
    font-size: 12px;
    color: #8c8c8c;
    line-height: 1.5;
    margin-bottom: 0px;
    // background: #f6f6f6;
    padding: 12px;
    border-radius: 4px;
    // border-left: 3px solid #1890ff;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .form-detector-panel {
    width: 60vw !important;
    max-height: 90vh !important;
  }

  .formDetector-mentions-components {
    .knowledge-content-base-width {
      width: 100%;
    }
  }
}
