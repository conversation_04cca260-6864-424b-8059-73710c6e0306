let errorDestination: Window | null = null;

/**
 * 使用 {@link sendError} 将错误转发到另一个框架的错误处理程序包装回调。
 *
 * @param context - 指示错误发生位置的简短消息。
 */
export function captureErrors<Result, Args = unknown>(
  callback: (...args: Args[]) => Result,
  context: string,
): (...args: Args[]) => Result {
  return (...args) => {
    try {
      return callback(...args);
    } catch (err) {
      sendError(err, context);
      throw err;
    }
  };
}

type ErrorData = {
  message: string;
  stack?: string;
};

/**
 * 返回可克隆的错误表示。
 *
 * 这在不支持结构化克隆错误对象的浏览器中是必要的，或者如果错误由于某种原因不可克隆。
 */
function serializeError(err: Error | unknown): ErrorData {
  if (!(err instanceof Error)) {
    return { message: String(err), stack: undefined };
  }

  return {
    message: err.message,
    stack: err.stack,
  };
}

/**
 * 将由 {@link serializeError} 序列化的错误数据转换回错误。
 */
function deserializeError(data: ErrorData): Error {
  const err = new Error(data.message);
  err.stack = data.stack;
  return err;
}

/**
 * 将错误转发到使用 {@link sendErrorsTo} 注册的框架。
 *
 * 错误是尽力而为地传递的。如果没有注册错误处理框架或框架仍在加载，则不会接收到错误。
 *
 * 理想情况下，我们会使用更强大的传递系统，可以将消息排队直到它们可以被处理（例如，使用 MessagePort）。
 * 我们目前使用 `window.postMessage`，因为我们正在尝试排除在设置侧边栏 <-> 主机通信时与 MessageChannel/MessagePort 相关的问题。
 *
 * @param context - 指示错误发生位置的简短消息。
 */
export function sendError(error: unknown, context: string) {
  if (!errorDestination) {
    return;
  }

  const data = {
    type: "hypothesis-error",
    error: error instanceof Error ? error : serializeError(error),
    context,
  };

  try {
    // 尝试发送错误。如果由于浏览器不支持结构化克隆错误而失败，请使用回退。
    try {
      errorDestination.postMessage(data, "*");
    } catch (postErr) {
      if (postErr instanceof DOMException && postErr.name === "DataCloneError") {
        data.error = serializeError(data.error);
        errorDestination.postMessage(data, "*");
      } else {
        throw postErr;
      }
    }
  } catch (sendErr) {
    console.warn("无法报告 Hypothesis 错误", sendErr);
  }
}

/**
 * 注册一个处理使用 {@link sendError} 发送到当前框架的错误的处理程序
 *
 * @return 一个注销处理程序的函数
 */
export function handleErrorsInFrames(callback: (error: unknown, context: string) => void): () => void {
  const handleMessage = (event: MessageEvent) => {
    const { data } = event;
    if (data && data?.type === "hypothesis-error") {
      const { context, error } = data;
      callback(error instanceof Error ? error : deserializeError(error), context);
    }
  };
  window.addEventListener("message", handleMessage);
  return () => window.removeEventListener("message", handleMessage);
}

/**
 * 注册 {@link sendError} 应该提交错误的目标框架。
 */
export function sendErrorsTo(destination: Window | null) {
  errorDestination = destination;
}
