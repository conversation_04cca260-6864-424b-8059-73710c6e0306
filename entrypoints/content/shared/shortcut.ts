import { useEffect } from "react";

/**
 * 表示快捷键所需的修饰符或按键事件中按下的修饰符的位标志。
 */
const modifiers = {
  alt: 1,
  ctrl: 2,
  meta: 4,
  shift: 8,
} as Record<string, number>;

/**
 * 将 `shortcut` 快捷键序列与 keydown 事件进行匹配。
 *
 * 快捷键序列是由 "+" 分隔的键盘修饰符和按键组成的字符串。
 * 列表可以包含零个或多个修饰符，并且必须包含一个非修饰符键。键和修饰符名称不区分大小写。
 * 例如 "Ctrl+Enter", "shift+a"。
 *
 * 键名与 {@link KeyboardEvent.key} 匹配。请注意，对于某些键，此属性会受到修饰符的影响。
 * 例如，在美国 QWERTY 键盘上，"Shift+1" 不会匹配任何事件，因为键值将是 "!"。
 * 另请参阅 https://github.com/w3c/uievents/issues/247。
 */
export function matchShortcut(event: KeyboardEvent, shortcut: string): boolean {
  // 解决一个问题，即 Chrome 自动填充可能会调度带有非 `KeyboardEvent` 参数的 "keydown" 事件。
  //
  // 参见 https://bugs.chromium.org/p/chromium/issues/detail?id=739792。
  if (!(event instanceof KeyboardEvent)) {
    return false;
  }

  const parts = shortcut.split("+").map((p) => p.toLowerCase());

  let requiredModifiers = 0;
  let requiredKey: string | null = null;

  for (const part of parts) {
    const modifierFlag = modifiers[part];
    if (modifierFlag) {
      requiredModifiers |= modifierFlag;
    } else if (requiredKey === null) {
      requiredKey = part;
    } else {
      throw new Error("指定了多个非修饰符键");
    }
  }

  if (!requiredKey) {
    throw new Error(`无效的快捷键：${shortcut}`);
  }

  const actualModifiers =
    (event.ctrlKey ? modifiers.ctrl : 0) |
    (event.metaKey ? modifiers.meta : 0) |
    (event.altKey ? modifiers.alt : 0) |
    (event.shiftKey ? modifiers.shift : 0);

  return actualModifiers === requiredModifiers && event.key.toLowerCase() === requiredKey;
}

export type ShortcutOptions = {
  /**
   * 安装键事件监听器的元素。默认为 `document.body`。
   */
  rootElement?: HTMLElement;
};

/**
 * 在文档上安装快捷键监听器。
 *
 * 这可以直接在组件外部使用。要在 Preact 组件中使用，您可能需要 {@link useShortcut}。
 *
 * @param shortcut - 快捷键序列。参见 {@link matchShortcut}。
 * @param onPress - 匹配快捷键时调用的函数
 * @return 移除快捷键的函数
 */
export function installShortcut(
  shortcut: string,
  onPress: (e: KeyboardEvent) => void,
  {
    // 我们使用 `documentElement` 作为根元素，而不是在其他地方用作根元素的 `document.body`，
    // 因为在 Safari/Chrome 中，body 元素在 XHTML 文档中不可键盘聚焦。
    // 参见 https://github.com/hypothesis/client/issues/4364。
    //
    // 注意：TS 类型中 `documentElement` 非空，但如果根元素被显式移除，它可能为空。
    // 我们不知道这是如何发生的，但在某些 ChromeOS 设备上已观察到这种情况。
    // 参见 https://hypothesis.sentry.io/issues/3987992034。
    rootElement = (document.documentElement as HTMLElement | null) ?? undefined,
  }: ShortcutOptions = {},
) {
  const onKeydown = (event: KeyboardEvent) => {
    if (matchShortcut(event, shortcut)) {
      onPress(event);
    }
  };
  /* istanbul ignore next */
  if (!rootElement) {
    return () => {};
  }
  rootElement.addEventListener("keydown", onKeydown);
  return () => rootElement.removeEventListener("keydown", onKeydown);
}

/**
 * 一个效果钩子，使用 {@link installShortcut} 安装快捷键，并在组件卸载时移除它。
 *
 * 这提供了一种方便的方法来在组件挂载时启用文档级别的快捷键。
 * 这与将 `onKeyDown` 处理程序添加到组件的 DOM 元素之一不同，因为它不需要组件具有焦点。
 *
 * 要有条件地禁用快捷键，请将 `shortcut` 设置为 `null`。
 *
 * @param shortcut - 要匹配的快捷键序列或 `null` 以禁用。参见 {@link matchShortcut}。
 * @param onPress - 匹配快捷键时调用的函数
 */
export function useShortcut(
  shortcut: string | null,
  onPress: (e: KeyboardEvent) => void,
  { rootElement }: ShortcutOptions = {},
) {
  useEffect(() => {
    if (!shortcut) {
      return undefined;
    }
    return installShortcut(shortcut, onPress, { rootElement });
  }, [shortcut, onPress, rootElement]);
}
