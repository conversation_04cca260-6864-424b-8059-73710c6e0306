/**
 * 辅助方法，用于识别浏览器版本和操作系统类型
 */

/**
 * 当操作系统是 Mac OS 时返回 true。
 *
 * @param _userAgent - 测试参数
 */
export const isMacOS = (_userAgent: string = window.navigator.userAgent) => {
  return _userAgent.indexOf("Mac OS") >= 0;
};

/**
 * 当设备是 iOS 时返回 true。
 * https://stackoverflow.com/a/9039885/14463679
 */
export const isIOS = (
  _navigator: { platform: string; userAgent: string } = window.navigator,
  _ontouchend: boolean = "ontouchend" in document,
): boolean => {
  return (
    ["iPad Simulator", "iPhone Simulator", "iPod Simulator", "iPad", "iPhone", "iPod"].includes(_navigator.platform) ||
    // iOS 13 上的 iPad 检测
    (_navigator.userAgent.includes("Mac") && _ontouchend)
  );
};

/**
 * 当设备是触摸设备（如 Android 或 iOS）时返回 true。
 * https://developer.mozilla.org/en-US/docs/Web/CSS/@media/pointer#browser_compatibility
 */
export const isTouchDevice = (_window: Window = window): boolean => {
  return _window.matchMedia("(pointer: coarse)").matches;
};
