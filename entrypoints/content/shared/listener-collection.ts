type Listener = {
  eventTarget: EventTarget;
  eventType: string;
  listener: (event: Event) => void;
  options?: AddEventListenerOptions;
};

/**
 * 返回监听器将接收的事件类型。
 *
 * 例如 `EventType<HTMLElement, 'keydown'>` 解析为 `KeyboardEvent`。
 *
 * 事件类型是从目标的 `on${Type}` 属性中提取的（例如，这里的 `HTMLElement.onkeydown`）。
 * 如果没有这样的属性，类型默认为 `Event`。
 */
type EventType<Target extends EventTarget, Type extends string> = `on${Type}` extends keyof Target
  ? Target[`on${Type}`] extends ((...args: any[]) => void) | null
    ? Parameters<NonNullable<Target[`on${Type}`]>>[0]
    : Event
  : Event;

/**
 * 提供一种方便的方法来移除一组不再需要的 DOM 事件监听器的工具。
 */
export class ListenerCollection {
  private _listeners: Map<symbol, Listener>;

  constructor() {
    this._listeners = new Map();
  }

  /**
   * 添加一个监听器并返回一个可以用来稍后移除它的 ID
   */
  add<Type extends string, Target extends EventTarget>(
    eventTarget: Target,
    eventType: Type,
    listener: (event: EventType<Target, Type>) => void,
    options?: AddEventListenerOptions,
  ) {
    eventTarget.addEventListener(eventType, listener as EventListener, options);
    const symbol = Symbol();
    this._listeners.set(symbol, {
      eventTarget,
      eventType,
      // eslint-disable-next-line object-shorthand
      listener: listener as EventListener,
      options,
    });
    return symbol;
  }

  /**
   * 移除一个特定的监听器。
   */
  remove(listenerId: symbol) {
    const event = this._listeners.get(listenerId);
    if (event) {
      const { eventTarget, eventType, listener, options } = event;
      eventTarget.removeEventListener(eventType, listener, options);
      this._listeners.delete(listenerId);
    }
  }

  /**
   * 移除所有监听器。
   */
  removeAll() {
    this._listeners.forEach(({ eventTarget, eventType, listener, options }) => {
      eventTarget.removeEventListener(eventType, listener, options);
    });
    this._listeners.clear();
  }
}
