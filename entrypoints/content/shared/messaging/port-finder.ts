import type { Destroyable } from "../../types/annotator";
import { ListenerCollection } from "../listener-collection";
import { generateHexString } from "../random";
import { isMessage } from "./port-util";
import type { Frame } from "./port-util";

/** 等待主框架响应端口请求的超时时间。 */
export const MAX_WAIT_FOR_PORT = 1000 * 20;

/** 请求主框架端口的轮询间隔。 */
export const POLLING_INTERVAL_FOR_PORT = 250;

export type Options = {
  /** 此框架的角色。 */
  source: Exclude<Frame, "host">;

  /** 此框架的标识符。 */
  sourceId?: string;

  /** `PortProvider` 监听消息的框架。 */
  hostFrame: Window;
};

/** 当 {@link PortFinder.discover} 请求失败时抛出的错误。 */
export class PortRequestError extends Error {
  constructor(message: string) {
    super(message);
  }
}

/**
 * PortFinder 用于客户端中的非主机框架，以建立与其他框架的基于 MessagePort 的连接。
 * 它与运行在主框架中的 PortProvider 一起使用。请参阅 PortProvider 以了解概述。
 */
export class PortFinder implements Destroyable {
  private _hostFrame: Window;
  private _listeners: ListenerCollection;
  private _source: Exclude<Frame, "host">;
  private _sourceId: string | undefined;

  constructor({ hostFrame, source, sourceId }: Options) {
    this._hostFrame = hostFrame;
    this._source = source;
    this._sourceId = sourceId;
    this._listeners = new ListenerCollection();
  }

  destroy() {
    this._listeners.removeAll();
  }

  /**
   * 请求从主框架获取特定端口
   *
   * @param target - 目标框架
   */
  async discover(target: Frame): Promise<MessagePort> {
    const requestId = generateHexString(6);

    return new Promise((resolve, reject) => {
      const postRequest = () => {
        this._hostFrame.postMessage(
          {
            frame1: this._source,
            frame2: target,
            type: "request",
            requestId,
            sourceId: this._sourceId,
          },
          "*",
        );
      };

      // 因为 `guest` iframes 与 `host` 框架并行加载，我们不能假设 `host` 框架中的代码在
      // `guest` 框架中的代码之前执行。因此，我们不能假设 `PortProvider`（在 `host` 框架中）
      // 在 `PortFinder`（在非主机框架中）之前初始化。因此，对于 `PortFinder`，我们实现了轮询
      // 策略（每 N 毫秒发送一次消息），直到收到响应。
      const intervalId = setInterval(postRequest, POLLING_INTERVAL_FOR_PORT);

      // `host` 框架可能很忙，所以我们应该等待。
      const timeoutId = setTimeout(() => {
        clearInterval(intervalId);
        reject(new PortRequestError(`无法建立 ${this._source}-${target} 通信通道`));
      }, MAX_WAIT_FOR_PORT);

      const listenerId = this._listeners.add(window, "message", (event) => {
        const { data, ports } = event;

        // 忽略以下消息：
        //
        // - 与端口发现无关
        // - 不是我们上面发送的请求的响应。请注意，主框架可能与当前窗口相同，
        //   因为例如主框架也可以是 guest 框架。因此，我们还检查 `data.type` 以确保这是响应。
        if (!isMessage(data) || data.requestId !== requestId || data.type === "request") {
          return;
        }

        clearInterval(intervalId);
        clearTimeout(timeoutId);
        this._listeners.remove(listenerId);

        if (typeof data.error === "string") {
          reject(new PortRequestError(data.error));
        } else if (ports.length > 0) {
          resolve(ports[0]);
        } else {
          reject(new PortRequestError(`${this._source}-${target} 端口请求失败`));
        }
      });

      postRequest();
    });
  }
}
