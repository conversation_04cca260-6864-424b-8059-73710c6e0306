import { TinyEmitter } from "tiny-emitter";

import type { Destroyable } from "../../types/annotator";
import { captureErrors, sendError } from "../frame-error-capture";
import { ListenerCollection } from "../listener-collection";
import { isMessage, isMessageEqual, isSourceWindow } from "./port-util";
import type { Message } from "./port-util";

type Channel = "guest-host" | "guest-sidebar" | "sidebar-host";

/**
 * PortProvider 创建一个 `MessageChannel` 用于两个框架之间的通信。
 *
 * 有 4 种类型的框架：
 * - `host`：加载 Hypothesis 客户端的框架。
 * - `guest`：具有可注释内容的框架。在某些情况下，`guest` 框架可以与 `host` 框架相同，
 *    在其他情况下，它是一个 iframe，其中 (1) 注入了 hypothesis 客户端或 (2) hypothesis 客户端被配置为仅作为 `guest`（不显示侧边栏）。
 * - `sidebar`：主要的 hypothesis 前端应用。它在与主机不同的源上的 iframe 中运行，并负责与后端的通信（获取和保存注释）。
 *
 * 此布局表示框架的当前排列：
 *
 * `host` 框架
 * |-> `sidebar` iframe
 * |-> [`guest` iframes]
 *
 * 目前，我们支持以下框架对之间的通信：
 * - `guest-host`
 * - `guest-sidebar`
 * - `sidebar-host`
 *
 * `PortProvider` 仅在 `host` 框架中使用。其他框架使用配套类 `PortFinder`。`PortProvider` 创建一个 `MessageChannel`
 * 供两个框架相互通信。它还监听特定 `MessagePort` 的请求并分发相应的 `MessagePort`。
 *
 *
 *        PortFinder (非主机框架)                 |       PortProvider (主机框架)
 * -----------------------------------------------------------------------------------------------
 * 1. 通过 `window.postMessage` 请求 `MessagePort` ---> 2. 接收请求并创建端口对
 *                                                                          |
 *                                                                          V
 * 4. 接收请求的端口      <---------------------- 3. 将第一个端口发送到请求框架
 *                                                        5. 将第二个端口发送到其他框架
 *                                                           （例如通过主机和其他框架之间的 MessageChannel 连接）
 */
export class PortProvider implements Destroyable {
  private _allowedMessages: Array<Partial<Message> & { allowedOrigin: string }>;
  private _emitter: TinyEmitter;

  /**
   * 已处理的端口请求的 ID。
   *
   * 这用于避免多次响应相同的请求。
   * 特别是 guest 框架可能会发送重复请求（参见 PortFinder 中的注释）。
   */
  private _handledRequests: Set<string>;

  private _listeners: ListenerCollection;

  // 用于从 Hypothesis 应用程序连接到主机框架的消息通道。
  private _sidebarHostChannel: MessageChannel;
  private _sidebarConnected: boolean;

  /**
   * 开始监听来自其他框架的端口请求。
   *
   */
  constructor() {
    this._emitter = new TinyEmitter();

    this._handledRequests = new Set();

    // 立即创建 `sidebar-host` 通道，而其他通道按需创建
    this._sidebarHostChannel = new MessageChannel();
    this._sidebarConnected = false;

    this._listeners = new ListenerCollection();

    this._allowedMessages = [
      {
        allowedOrigin: "*",
        frame1: "guest",
        frame2: "host",
        type: "request",
      },
      {
        allowedOrigin: "*",
        frame1: "guest",
        frame2: "sidebar",
        type: "request",
      },
      {
        allowedOrigin: "*",
        frame1: "sidebar",
        frame2: "host",
        type: "request",
      },
    ];

    this._listen();
  }

  private _listen() {
    const errorContext = "处理端口请求";
    const sentErrors = new Set<string>();

    const reportError = (message: string) => {
      if (sentErrors.has(message)) {
        // PortFinder 将重复发送请求，直到收到响应或超时。
        //
        // 只发送一次错误以避免垃圾邮件 Sentry。
        return;
      }
      sentErrors.add(message);
      sendError(new Error(message), errorContext);
    };

    const handleRequest = (event: MessageEvent) => {
      const { data, origin, source } = event;

      // 忽略发送者在我们发送响应之前消失的消息。
      if (!source) {
        return;
      }

      // 忽略看起来不像端口请求的消息。
      if (!isMessage(data) || data.type !== "request") {
        return;
      }

      const { frame1, frame2, requestId, sourceId } = data;
      const channel = `${frame1}-${frame2}` as Channel;

      if (!isSourceWindow(source)) {
        reportError(`忽略来自非窗口源的 ${channel} 通道的端口请求`);
        return;
      }

      const match = this._allowedMessages.find(({ allowedOrigin, ...allowedMessage }) =>
        this._messageMatches({
          allowedMessage,
          allowedOrigin,
          data,
          origin,
        }),
      );

      if (match === undefined) {
        reportError(`忽略来自 ${origin} 的无效 ${channel} 通道端口请求`);
        return;
      }

      if (this._handledRequests.has(requestId)) {
        return;
      }
      this._handledRequests.add(requestId);

      // 如果源窗口具有不透明的来源 [1]，`event.origin` 将是字符串 "null"。
      // 这不是 `postMessage` 的 `targetOrigin` 参数的合法值，因此将其重新映射为 "*"。
      //
      // [1] https://html.spec.whatwg.org/multipage/origin.html#origin.
      //     具有不透明来源的文档包括 file:// URL 和沙盒 iframe。
      const targetOrigin = origin === "null" ? "*" : origin;

      // 为这两个框架创建通道进行通信，并将相应的端口发送给它们。
      const messageChannel = channel === "sidebar-host" ? this._sidebarHostChannel : new MessageChannel();

      // 发送到源框架希望连接到的目标框架以及请求连接的源框架的消息。
      // 每条消息都附带一个适当端口的连接端口。
      const message: Message = {
        frame1,
        frame2,
        type: "offer",
        requestId,
        sourceId,
      };

      // 侧边栏只能连接一次。如果某些原因导致 iframe 重新加载，它可能会尝试第二次连接。
      // 我们还不能从这个恢复。因此，我们只在这里记录一个警告。端口发现协议没有返回错误的方法，
      // 因此侧边栏只能在等待响应超时时了解这一点。
      if (messageChannel === this._sidebarHostChannel) {
        if (this._sidebarConnected) {
          console.warn("忽略 Hypothesis 侧边栏第二次连接到主机框架的请求");
          message.error = "收到重复的端口请求";
          source.postMessage(message, targetOrigin);
          return;
        }
        this._sidebarConnected = true;
      }

      source.postMessage(message, targetOrigin, [messageChannel.port1]);

      if (frame2 === "sidebar") {
        this._sidebarHostChannel.port2.postMessage(message, [messageChannel.port2]);
      } else if (frame2 === "host") {
        this._emitter.emit("frameConnected", frame1, messageChannel.port2);
      }
    };

    this._listeners.add(window, "message", captureErrors(handleRequest, errorContext));
  }

  private _messageMatches({
    allowedMessage,
    allowedOrigin,
    data,
    origin,
  }: {
    /** 消息必须匹配的字段。 */
    allowedMessage: Partial<Message>;

    /** 来源必须匹配此值。接受 `*` 通配符。 */
    allowedOrigin: string;

    /** 要与 `allowedMessage` 进行比较的数据 */
    data: unknown;

    /** 要与 `allowedOrigin` 进行比较的来源。 */
    origin: string;
  }) {
    if (allowedOrigin !== "*" && origin !== allowedOrigin) {
      return false;
    }

    return isMessageEqual(data, allowedMessage);
  }

  on(eventName: "frameConnected", handler: (source: "guest", port: MessagePort) => void) {
    this._emitter.on(eventName, handler);
  }

  destroy() {
    this._listeners.removeAll();
  }
}
