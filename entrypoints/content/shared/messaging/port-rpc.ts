import type { Destroyable } from "../../types/annotator";
import { ListenerCollection } from "../listener-collection";

/*
  此模块改编自 https://github.com/substack/frame-rpc 中的 `index.js`。

  本软件根据 MIT 许可证发布：

  特此免费授予任何获得本软件副本和相关文档文件（“软件”）的人
  使用本软件的权利，包括但不限于使用、复制、修改、合并、发布、分发、再许可和/或出售
  软件副本的权利，并允许软件提供者在满足以下条件的情况下这样做：

  上述版权声明和本许可声明应包含在本软件的所有副本或主要部分中。

  本软件按“原样”提供，不提供任何明示或暗示的担保，包括但不限于
  对适销性、特定用途适用性和非侵权的担保。在任何情况下，作者或版权持有人均不对任何索赔、损害或其他责任负责，
  无论是在合同诉讼、侵权行为或其他情况下，由本软件或使用本软件引起或与之相关。
 */

const VERSION = "1.0.0";
const PROTOCOL = "frame-rpc";

/**
 * 在框架之间发送的消息的格式。
 *
 * 参见 https://github.com/substack/frame-rpc#protocol
 */
type RequestMessage = {
  arguments: unknown[];
  method: string;
  protocol: typeof PROTOCOL;
  sequence: number;
  version: typeof VERSION;
};

type ResponseMessage = {
  arguments: unknown[];
  protocol: typeof PROTOCOL;
  response: number;
  version: typeof VERSION;
};

type Message = RequestMessage | ResponseMessage;

function makeRequestMessage(method: string, args: unknown[] = [], sequence = -1): RequestMessage {
  return {
    protocol: PROTOCOL,
    version: VERSION,
    arguments: args,
    method,
    sequence,
  };
}

/**
 * 发送一个 PortRPC 方法调用。
 *
 * @param [sequence] - 用于回复的序列号
 */
function sendCall(port: MessagePort, method: string, args: unknown[] = [], sequence = -1) {
  port.postMessage(makeRequestMessage(method, args, sequence));
}

/**
 * 安装一个消息监听器，以确保 Safari <= 15 中的 {@link PortRPC} 正确传递 "close" 通知。
 *
 * 这必须在拥有端口的框架的 _parent_ 框架中调用。
 * 在 Hypothesis 中，这意味着例如在主机框架中安装此解决方法，以确保从 "guest" 框架传递 "close" 通知。
 *
 * @param [userAgent] - 测试接缝
 * @return 移除监听器的回调
 */
export function installPortCloseWorkaroundForSafari(
  /* istanbul ignore next */
  userAgent = navigator.userAgent,
): () => void {
  if (!shouldUseSafariWorkaround(userAgent)) {
    return () => {};
  }

  const handler = (event: MessageEvent) => {
    if (event.data?.type === "hypothesisPortClosed" && event.ports[0]) {
      const port = event.ports[0];
      sendCall(port, "close");
      port.close();
    }
  };

  window.addEventListener("message", handler);
  return () => window.removeEventListener("message", handler);
}

/**
 * 测试此浏览器是否需要 https://bugs.webkit.org/show_bug.cgi?id=231167 的解决方法。
 */
function shouldUseSafariWorkaround(userAgent: string) {
  const webkitVersionMatch = userAgent.match(/\bAppleWebKit\/([0-9]+)\b/);
  if (!webkitVersionMatch) {
    return false;
  }
  const version = parseInt(webkitVersionMatch[1]);

  // Chrome 的用户代理包含标记 "AppleWebKit/537.36"，其中版本号被冻结。这对应于 2013 年的 Safari 版本，
  // 早于所有受支持的 Safari 版本。
  if (version <= 537) {
    return false;
  }

  return true;
}

/**
 * 用于 RPC 方法处理程序和结果回调的回调类型。
 */
type Callback = (...args: unknown[]) => void;

function isCallback(value: any): value is Callback {
  return typeof value === "function";
}

/**
 * PortRPC 提供框架或工作者之间的远程过程调用。它使用 Channel Messaging API [1] 作为传输。
 *
 * 要使用此类在两个框架之间进行通信，请在每个框架中构造一个 PortRPC 实例，并使用 {@link on} 注册方法处理程序。
 * 创建一个 {@link MessageChannel} 并将其两个端口之一发送到每个框架。然后调用 {@link connect} 使每个框架中的 PortRPC 实例使用相应的端口。
 *
 * 除了 PortRPC 处理的自定义方法外，还有几个内置事件会自动发送：
 *
 * - "connect" 在 PortRPC 连接到端口时发送。此事件可用于确认发送框架已收到端口，并在其消失时发送 "close" 事件。
 * - "close" 在 PortRPC 被销毁或拥有框架被卸载时发送。如果 guest 框架异常终止（例如由于操作系统进程崩溃），则可能不会调度此事件。
 *
 * [1] https://developer.mozilla.org/en-US/docs/Web/API/Channel_Messaging_API
 *
 * @template OnMethod - 此客户端响应的 RPC 方法的名称
 * @template CallMethod - 此客户端调用的 RPC 方法的名称
 */
export class PortRPC<OnMethod extends string, CallMethod extends string> implements Destroyable {
  /**
   * 序列号到响应回调的映射，用于从此实例发送的 RPC 调用。
   */
  private _callbacks: Map<number, Callback>;

  private _destroyed: boolean;
  private _listeners: ListenerCollection;

  /** RPC 调用此实例接收的方法名称到处理程序的映射。 */
  private _methods: Map<string, Callback>;

  /** 底层通信通道。 */
  private _port: MessagePort | null;

  /** 下一个调用的序列号。 */
  private _sequence: number;

  /**
   * 在连接端口之前进行的挂起 RPC 调用的方法和参数。
   */
  private _pendingCalls: Array<[CallMethod, unknown[]]>;

  private _receivedCloseEvent: boolean;

  constructor({
    userAgent = navigator.userAgent,
    currentWindow = window,
    forceUnloadListener = false,
  }: {
    userAgent?: string;
    currentWindow?: Window;

    // 测试接缝。即使浏览器支持 MessagePort 的 "close" 事件，也强制使用 Window "unload" 监听器。
    forceUnloadListener?: boolean;
  } = {}) {
    this._port = null;
    this._methods = new Map();
    this._sequence = 1;
    this._callbacks = new Map();

    this._listeners = new ListenerCollection();

    // 在发出 "close" 事件时，我们可以直接监听该事件以识别另一端断开连接的情况。
    // 在其他浏览器中，我们必须在窗口包含发送端口时通过消息通道发送 "close" 事件。
    if (!("onclose" in MessagePort.prototype) || forceUnloadListener) {
      this._listeners.add(currentWindow, "unload", () => {
        if (this._port) {
          // 直接发送 "close" 通知。这在 Chrome、Firefox 和 Safari >= 16 中有效。
          sendCall(this._port, "close");

          // 为了解决 Safari <= 15 中的一个错误，该错误阻止在窗口卸载时发送消息，
          // 尝试将端口传输到父框架并从那里重新发送 "close" 事件。
          if (currentWindow !== currentWindow.parent && shouldUseSafariWorkaround(userAgent)) {
            currentWindow.parent.postMessage({ type: "hypothesisPortClosed" }, "*", [this._port]);
          }
        }
      });
    }

    this._pendingCalls = [];

    this._destroyed = false;
    this._receivedCloseEvent = false;
  }

  /**
   * 为传入的 RPC 请求注册方法处理程序。
   *
   * 处理程序的参数将是传递给 {@link call} 的参数加上一个最终的回调参数，该参数可用于将结果返回给调用者。
   *
   * 这也可用于为内置的 "connect" 和 "close" 事件注册处理程序。
   *
   * 所有处理程序必须在调用 {@link connect} 之前注册。
   */
  on<Handler extends (...args: any[]) => void>(method: OnMethod | "close" | "connect", handler: Handler) {
    if (this._port) {
      throw new Error("连接端口后无法添加方法处理程序");
    }
    this._methods.set(method, handler as any);
  }

  /**
   * 连接到 MessagePort 并处理任何排队的 RPC 请求。
   */
  connect(port: MessagePort) {
    this._port = port;
    this._listeners.add(port, "message", (event) => this._handle(event));

    // 对于支持 MessagePort 的 `close` 事件的浏览器，我们使用它来识别另一端断开连接的情况。
    // 这被转换为一个消息事件，类似于我们在使用 Window 卸载处理程序的旧浏览器中收到的事件。
    this._listeners.add(port, "close", () => {
      port.dispatchEvent(
        new MessageEvent("message", {
          data: makeRequestMessage("close"),
        }),
      );
    });

    port.start();
    sendCall(port, "connect");

    for (const [method, args] of this._pendingCalls) {
      this.call(method, ...args);
    }
    this._pendingCalls = [];
  }

  /**
   * 断开 RPC 通道并关闭 MessagePort。
   */
  destroy() {
    if (this._port) {
      sendCall(this._port, "close");
      this._port.close();
    }
    this._destroyed = true;
    this._listeners.removeAll();
  }

  /**
   * 通过连接的端口发送 RPC 请求。
   *
   * 如果此客户端尚未连接到端口，则调用将排队并在调用 {@link connect} 时发送。
   *
   * 如果 `args` 的最后一个参数是函数，则将其视为回调，该回调以 (error, result) 参数的形式接收响应。
   */
  call(method: CallMethod, ...args: unknown[]) {
    if (!this._port) {
      this._pendingCalls.push([method, args]);
    }

    if (!this._port || this._destroyed) {
      return;
    }

    const seq = this._sequence++;
    const finalArg = args[args.length - 1];
    if (isCallback(finalArg)) {
      this._callbacks.set(seq, finalArg);
      args = args.slice(0, -1);
    }

    sendCall(this._port, method, args, seq);
  }

  /**
   * 验证消息
   */
  private _parseMessage({ data }: MessageEvent): Message | null {
    if (!data || typeof data !== "object") {
      return null;
    }
    if (data.protocol !== PROTOCOL) {
      return null;
    }
    if (data.version !== VERSION) {
      return null;
    }
    if (!Array.isArray(data.arguments)) {
      return null;
    }

    return data;
  }

  private _handle(event: MessageEvent) {
    const msg = this._parseMessage(event);
    const port = this._port;

    if (!msg || !port) {
      return;
    }

    if ("method" in msg) {
      // 仅允许关闭事件触发一次。
      if (msg.method === "close") {
        if (this._receivedCloseEvent) {
          return;
        } else {
          this._receivedCloseEvent = true;
        }
      }

      const handler = this._methods.get(msg.method);
      if (!handler) {
        return;
      }

      const callback = (...args: unknown[]) => {
        const message: ResponseMessage = {
          arguments: args,
          protocol: PROTOCOL,
          response: msg.sequence,
          version: VERSION,
        };

        port.postMessage(message);
      };
      handler(...msg.arguments, callback);
    } else if ("response" in msg) {
      const cb = this._callbacks.get(msg.response);
      this._callbacks.delete(msg.response);
      if (cb) {
        cb(...msg.arguments);
      }
    }
  }
}
