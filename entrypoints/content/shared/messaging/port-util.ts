export type Frame = "guest" | "host" | "sidebar";

/**
 * `PortProvider` 和 `PortFinder` 发送的消息，用于在两个框架之间建立基于 MessageChannel 的连接。
 */
export type Message = {
  /** 源框架的角色。 */
  frame1: Frame;

  /** 目标框架的角色。 */
  frame2: Frame;

  /**
   * 消息类型。"request" 消息由源框架发送到主机框架以请求连接。
   * "offer" 消息从主机框架发送回源框架并发送到目标框架，附带一个 MessagePort。
   */
  type: "offer" | "request";

  /** 如果在 `offer` 消息上设置，则表示请求被拒绝。 */
  error?: string;

  /**
   * 请求的 ID。用于将 "offer" 消息与其对应的 "request" 消息关联起来。
   */
  requestId: string;

  /**
   * 源框架的标识符。这在多个具有给定角色的源框架可能连接到同一目标框架的情况下很有用。
   */
  sourceId?: string;
};

/**
 * 如果一个对象（例如来自 `MessageEvent` 的 `data` 字段）是一个有效的 `Message`，则返回 true。
 */
export function isMessage(data: any): data is Message {
  if (data === null || typeof data !== "object") {
    return false;
  }

  for (const property of ["frame1", "frame2", "type", "requestId"]) {
    if (typeof data[property] !== "string") {
      return false;
    }
  }

  return true;
}

/**
 * 如果来自 MessageEvent 的数据有效负载与 `message` 匹配，则返回 true。
 */
export function isMessageEqual(data: any, message: Partial<Message>) {
  if (!isMessage(data)) {
    return false;
  }

  // 我们假设 `JSON.stringify` 不会抛出异常，因为 `isMessage` 验证了我们在这里序列化的所有字段都是可序列化的值。
  return JSON.stringify(data, Object.keys(message).sort()) === JSON.stringify(message, Object.keys(message).sort());
}

/**
 * 检查 source 是否为 Window 类型。
 */
export function isSourceWindow(source: MessageEventSource | null): source is Window {
  if (
    // `source` 可以是 Window、MessagePort、ServiceWorker 或 null 类型。
    // 如果 `source` 是跨域窗口，则 `source instanceof Window` 在 Chrome 中不起作用。
    source === null ||
    source instanceof MessagePort ||
    (window.ServiceWorker && source instanceof ServiceWorker)
  ) {
    return false;
  }

  return true;
}
