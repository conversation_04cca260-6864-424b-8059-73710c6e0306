.data-collector-panel {
  position: fixed;
  width: 700px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 10000;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  border: 1px solid #e8e8e8;

  &.dragging {
    user-select: none;
    cursor: grabbing;
  }

  .drag-handle {
    padding: 16px 20px;
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    border-radius: 8px 8px 0 0;
    cursor: grab;
    user-select: none;

    &:active {
      cursor: grabbing;
    }

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #262626;
    }

    button {
      &:hover {
        color: #ff4d4f !important;
      }
    }
  }

  &.dragging .drag-handle {
    cursor: grabbing !important;
  }

  .collector-content {
    padding: 20px;

    .collector-note {
      text-align: center;
      font-size: 12px;
      color: #050404;
      line-height: 1.5;
      margin-bottom: 20px;
      // background: #f6f6f6;
      padding: 12px;
      border-radius: 4px;
      // border-left: 3px solid #1890ff;
    }

    .knowledge-selector {
      margin: 0 24px;
      margin-bottom: 24px;
      display: flex;
      align-items: center;
      .selector-label {
        font-size: 14px;
        color: #262626;
        margin-right: 30px;
        font-weight: 500;
      }

      .ant-select {
        .ant-select-selector {
          border-radius: 4px;
          border: 1px solid #d9d9d9;

          &:hover {
            border-color: #40a9ff;
          }
        }

        &.ant-select-focused {
          .ant-select-selector {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }
      }
    }

    .action-buttons {
      display: flex;
      justify-content: center;
      gap: 8px;

      .ant-btn {
        border-radius: 4px;
        font-weight: 400;

        &.ant-btn-primary {
          background: #1890ff;
          border-color: #1890ff;

          &:hover {
            background: #40a9ff;
            border-color: #40a9ff;
          }

          &:disabled {
            background: #f5f5f5;
            border-color: #d9d9d9;
            color: #bfbfbf;
          }
        }

        &:not(.ant-btn-primary) {
          color: #595959;
          border-color: #d9d9d9;

          &:hover {
            color: #1890ff;
            border-color: #1890ff;
          }
        }
      }
    }
  }
}

/* 确保弹窗在最顶层 */
.data-collector-panel {
  z-index: 999999 !important;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .data-collector-panel {
    width: 90vw;
    left: 5vw !important;

    .collector-content {
      padding: 16px;

      .action-buttons {
        flex-direction: column;

        .ant-btn {
          width: 100%;
        }
      }
    }
  }
}
