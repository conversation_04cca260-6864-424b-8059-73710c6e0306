import React, { useState, useRef, useCallback, useEffect } from "react";
import { Button, Select, message, ConfigProvider } from "antd";
import { useFetchRequest } from "@/hooks/useFetchRequest";
import { FormDataExtractor } from "@/utils/formDataExtractor";
import "./index.less";

interface DataCollectorProps {
  onClose?: () => void;
  shouldCollectData?: boolean;
  onDataCollected?: () => void;
}

interface FormField {
  name: string;
  type: string;
  value: string;
  placeholder?: string;
  required?: boolean;
}

interface ExtractedFormData {
  fields: FormField[];
  formAction?: string;
  formMethod?: string;
}
const searchParamsInit = {
  pageNum: 1,
  pageSize: 3000,
  entity: {
    libName: "",
  },
};
const DataCollector: React.FC<DataCollectorProps> = ({ onClose, shouldCollectData, onDataCollected }) => {
  const [isCollecting, setIsCollecting] = useState<boolean>(false);
  const [selectedKnowledge, setSelectedKnowledge] = useState<string>("请选择知识库");
  const [position, setPosition] = useState({ x: window.innerWidth / 2 - 200, y: window.innerHeight / 2 - 150 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const panelRef = useRef<HTMLDivElement>(null);
  const [searchParams, setSearchParams] = useState(searchParamsInit);
  const [knowledLoading, setKnowledLoading] = useState<boolean>(false); // 知识库loading
  const [cardData, setCardData] = useState<any[]>([]); // 知识库数据
  const fetchRequest = useFetchRequest();

  const getAllListData = (searchParamsInit, latestSelectKnowledgeArr?) => {
    setKnowledLoading(true);
    fetchRequest({
      api: "getKnowledgeTeam",
      params: searchParamsInit,

      callback: (res) => {
        if (res.code === 200) {
          let data = res.data.records;
          const initCardData = data.map((item) => ({
            label: item.libName,
            value: item.id,
          }));

          console.log("知识库数据", data);
          setCardData(initCardData);
        }

        setKnowledLoading(false);
      },
    });
  };

  // 初始化数据
  useEffect(() => {
    getAllListData(searchParams);
  }, []);

  // 处理拖拽
  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      setIsDragging(true);
      setDragStart({
        x: e.clientX - position.x,
        y: e.clientY - position.y,
      });
    },
    [position],
  );

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (isDragging) {
        setPosition({
          x: e.clientX - dragStart.x,
          y: e.clientY - dragStart.y,
        });
      }
    },
    [isDragging, dragStart],
  );

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // 监听 shouldCollectData prop 变化，自动收集数据
  React.useEffect(() => {
    if (shouldCollectData) {
      console.log("收到收集数据指令，开始收集表单数据");
      handleCollectData();
      // 通知父组件数据收集已处理
      onDataCollected?.();
    }
  }, [shouldCollectData]);
  // 文件转base64
  function fileToBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      // 成功读取文件时的回调
      reader.onload = () => {
        resolve(reader.result); // Base64 编码的字符串
      };

      // 读取文件失败时的回调
      reader.onerror = (error) => {
        reject(error);
      };

      // 读取文件并转为 Base64
      reader.readAsDataURL(file);
    });
  }
  // 提取表单数据的核心函数
  const extractFormData = useCallback(() => {
    // 使用统一的表单数据提取器
    const extractor = new FormDataExtractor({ extractionMethod: "dataCollector" });
    const allFormData = extractor.extractFormData();

    console.log("📋 表单数据提取完成", {
      totalForms: allFormData.length,
      formTypes: allFormData.map((form) => form.formType),
      totalFields: allFormData.reduce((sum, form) => sum + form.fields.length, 0),
    });

    const totalFormsCount = allFormData.filter((form) => form.formType !== "table").length;
    const totalTablesCount = allFormData.filter((form) => form.formType === "table").length;

    console.log(
      `📊 数据提取完成 - 表单: ${totalFormsCount}个, 表格: ${totalTablesCount}个, 总计: ${allFormData.length}个数据源`,
    );

    return {
      pageUrl: window.location.href,
      pageTitle: document.title,
      extractTime: new Date().toISOString(),
      formsCount: totalFormsCount,
      tablesCount: totalTablesCount,
      totalDataSources: allFormData.length,
      forms: allFormData, // 包含表单和表格数据
    };
  }, []);

  // 收集表单数据
  const handleCollectData = async () => {
    if (selectedKnowledge === "请选择知识库") {
      message.warning("请先选择知识库");
      return;
    }

    setIsCollecting(true);
    try {
      const extractedData = extractFormData();

      console.log("收集到的表单数据:", extractedData);
      // 将收集的表单转换成json
      uploadFormData(extractedData, selectedKnowledge);
      // 这里可以调用API将数据保存到选定的知识库
      // await fetchRequest.post('/api/knowledge/save', {
      //   knowledgeBase: selectedKnowledge,
      //   formData: extractedData
      // });

      message.success(`表单数据已成功收集到 ${selectedKnowledge}`);

      // 收集完成后关闭面板
      setTimeout(() => {
        onClose?.();
      }, 1500);
    } catch (error) {
      console.error("收集表单数据失败:", error);
      message.error("收集表单数据失败，请重试");
    } finally {
      setIsCollecting(false);
    }
  };
  // 上传表单数据到后端
  const uploadFormData = useCallback(
    async (data: any, selectedKnowledgeId: string) => {
      console.log("uploadFormData", data, selectedKnowledgeId);
      try {
        const jsonString = JSON.stringify(data, null, 2);
        console.log(jsonString);
        const fileName = `form_data_${document.title.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, "_")}_${Date.now()}.txt`;
        const file = new File([jsonString], fileName, { type: "text/plain" });
        console.log("上传到知识库", file);
        const fileData = {
          libId: selectedKnowledgeId,
          fileStr: await fileToBase64(file),
          fileName: fileName,
        };

        fetchRequest({
          api: "uploadKnowledgeFile",
          params: fileData,
          file: true,
          callback: (response) => {
            console.log(response);
            console.log(response.data);
            if (response.code == "200") {
              message.success("表单数据已成功提取并上传！");
            } else {
              message.error("表单数据上传失败！");
            }
          },
        });
      } catch (error) {
        console.error("表单数据处理失败：", error);
        message.error("表单数据处理失败！");
      }
    },
    [fetchRequest, fileToBase64],
  );
  return (
    <ConfigProvider
      getPopupContainer={(triggerNode) => {
        const panel = document.querySelector(".data-collector-panel");
        return (panel || triggerNode?.parentNode || document.body) as HTMLElement;
      }}
    >
      <div
        ref={panelRef}
        className={`data-collector-panel ${isDragging ? "dragging" : ""}`}
        style={{
          left: `${position.x}px`,
          top: `${position.y}px`,
        }}
      >
        <div
          className="drag-handle"
          style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}
          onMouseDown={handleMouseDown}
        >
          <h3>智能收集信息</h3>
          <button
            onClick={() => {
              onClose?.();
              // 通知wrapper组件面板已关闭
              window.postMessage(
                {
                  type: "DATA_COLLECTOR_CLOSED",
                },
                "*",
              );
            }}
            style={{
              background: "none",
              border: "none",
              fontSize: "18px",
              cursor: "pointer",
              color: "#999",
            }}
          >
            ×
          </button>
        </div>

        <div className="collector-content">
          <div className="collector-note">
            选择知识库后，自动将页面信息收集到知识库中，可利用提取表单工具，将信息填入到其他系统面板
          </div>

          <div className="knowledge-selector">
            <div className="selector-label">知识库</div>
            <Select
              value={selectedKnowledge}
              onChange={setSelectedKnowledge}
              options={cardData}
              style={{ flex: 1 }}
              placeholder="请选择知识库"
              showSearch
              filterOption={(input, option) => (option?.label ?? "").toLowerCase().includes(input.toLowerCase())}
            />
          </div>

          <div className="action-buttons">
            <Button onClick={onClose} style={{ marginRight: "8px" }}>
              取消
            </Button>
            <Button
              type="primary"
              loading={isCollecting}
              onClick={handleCollectData}
              disabled={isCollecting || selectedKnowledge === "请选择知识库"}
            >
              {isCollecting ? "收集中..." : "收集"}
            </Button>
          </div>
        </div>
      </div>
    </ConfigProvider>
  );
};

export default DataCollector;
