import FormDetector from "../formDetector/index";
import DataCollector from "../DataCollector/index";
import { ConfigProvider } from "antd";
import { StyleProvider } from "@ant-design/cssinjs";
import themeToken from "@/theme.json";
import React from "react";
// 创建 FormDetector 包装组件来管理显示状态
const FormDetectorWrapper = ({ formDetectorShadowRoot }: { formDetectorShadowRoot: ShadowRoot }) => {
  const [showCollectorPanel, setShowCollectorPanel] = React.useState(false);
  const [shouldCollectData, setShouldCollectData] = React.useState(false);
  const [showFillPanel, setShowFillPanel] = React.useState(false);
  const [shouldFillData, setShouldFillData] = React.useState(false);

  React.useEffect(() => {
    const handleMessage = (event) => {
      console.log("FormDetectorWrapper 收到消息:", event.data);
      if (event.data.type === "TOGGLE_FORM_DETECTOR") {
        setShowCollectorPanel(event.data.show);
        // 传递收集数据的标志
        if (event.data.extractFormData && event.data.show) {
          setShouldCollectData(true);
        }
      } else if (event.data.type === "FILL_FORM_DATA") {
        setShowFillPanel(true);
        setShouldFillData(true);
      }
    };

    window.addEventListener("message", handleMessage);
    return () => window.removeEventListener("message", handleMessage);
  }, []);

  const handleCollectorClose = () => {
    setShowCollectorPanel(false);
    setShouldCollectData(false);
    // 通知 SidePanelWrapper 更新状态
    window.postMessage(
      {
        type: "DATA_COLLECTOR_CLOSED",
        show: false,
      },
      "*",
    );
  };

  const handleFillClose = () => {
    setShowFillPanel(false);
    setShouldFillData(false);
    // 通知 SidePanelWrapper 更新状态
    window.postMessage(
      {
        type: "FORM_FILL_CLOSED",
        show: false,
      },
      "*",
    );
  };

  return (
    <StyleProvider hashPriority="high" container={formDetectorShadowRoot}>
      <ConfigProvider theme={themeToken}>
        {/* 表单数据收集组件 */}
        {showCollectorPanel && (
          <DataCollector
            onClose={handleCollectorClose}
            shouldCollectData={shouldCollectData}
            onDataCollected={() => setShouldCollectData(false)}
          />
        )}
        
        {/* 表单数据填充组件 */}
        {showFillPanel && (
          <FormDetector
            onClose={handleFillClose}
            shouldExtractData={shouldFillData}
            onDataExtracted={() => setShouldFillData(false)}
            onResetStatus={() => {}} // 启用重置状态功能
          />
        )}
      </ConfigProvider>
    </StyleProvider>
  );
};

export default FormDetectorWrapper;
