import { defineContentScript } from "wxt/sandbox";

export default defineContentScript({
  matches: ["<all_urls>"],
  runAt: "document_idle",
  allFrames: true, // 让它能注入到 iframe
  main(ctx) {
    if (window.top !== window) {
      // document.addEventListener("mouseup", () => {
      //   console.log("22333333")
      //   const selection = window.getSelection();
      //   if (!selection || selection.isCollapsed) return;

      //   const range = selection.getRangeAt(0);
      //   const rect = range.getBoundingClientRect();

      //   window.top?.postMessage(
      //     {
      //       type: "iframe-selection",
      //       text: selection.toString(),
      //       rect: {
      //         top: rect.top,
      //         left: rect.left,
      //         width: rect.width,
      //         height: rect.height,
      //       },
      //     },
      //     "*",
      //   );
      // });
      // document.addEventListener("mouseup", (e) => {
      //   const target = e.target;
      //   if (target && (target.tagName === "INPUT" || target.tagName === "TEXTAREA")) {
      //     const el = target
      //     const start = el.selectionStart ?? 0;
      //     const end = el.selectionEnd ?? 0;
      //     const text = el.value.substring(start, end);
      //     console.log(text, 1231)
      //     if (text) {
      //       const rect = el.getBoundingClientRect();
      //       window.top?.postMessage(
      //         {
      //           type: "iframe-input-selection",
      //           text,
      //           rect: {
      //             top: rect.top,
      //             left: rect.left,
      //             width: rect.width,
      //             height: rect.height,
      //           },
      //         },
      //         "*",
      //       );
      //     }
      //   }
      // });

      // 计算当前iframe相对于直接父窗口的偏移（不累积）
      function getCurrentIframeOffset() {
        // 如果是顶层窗口，返回零偏移
        if (window === window.top) {
          return { top: 0, left: 0 };
        }

        try {
          // 在父窗口中找到当前窗口对应的iframe元素
          const parentDoc = window.parent.document;
          const iframes = parentDoc.querySelectorAll('iframe');

          for (let i = 0; i < iframes.length; i++) {
            if (iframes[i].contentWindow === window) {
              const rect = iframes[i].getBoundingClientRect();

              // getBoundingClientRect() 返回的就是iframe相对于父窗口视口的正确位置
              // 不需要额外添加任何样式偏移
              const currentOffset = {
                top: rect.top,
                left: rect.left,
              };

              // 调试信息：获取iframe的所有样式信息用于分析
              const iframeStyles = window.parent.getComputedStyle(iframes[i]);
              const borderTop = parseFloat(iframeStyles.borderTopWidth) || 0;
              const borderLeft = parseFloat(iframeStyles.borderLeftWidth) || 0;
              const paddingTop = parseFloat(iframeStyles.paddingTop) || 0;
              const paddingLeft = parseFloat(iframeStyles.paddingLeft) || 0;
              const marginTop = parseFloat(iframeStyles.marginTop) || 0;
              const marginLeft = parseFloat(iframeStyles.marginLeft) || 0;

              // 检查是否有滚动条影响
              const hasVerticalScrollbar = iframes[i].scrollHeight > iframes[i].clientHeight;
              const hasHorizontalScrollbar = iframes[i].scrollWidth > iframes[i].clientWidth;

              // 检查父容器是否有影响（比如父容器的padding）
              const parentElement = iframes[i].parentElement;
              let parentPaddingTop = 0;
              let parentPaddingLeft = 0;
              if (parentElement) {
                const parentStyles = window.parent.getComputedStyle(parentElement);
                parentPaddingTop = parseFloat(parentStyles.paddingTop) || 0;
                parentPaddingLeft = parseFloat(parentStyles.paddingLeft) || 0;
              }

              console.log(`📍 当前iframe偏移计算:`, {
                iframeIndex: i,
                rect: { top: rect.top, left: rect.left },
                styles: {
                  border: { top: borderTop, left: borderLeft },
                  padding: { top: paddingTop, left: paddingLeft },
                  margin: { top: marginTop, left: marginLeft }
                },
                parentPadding: {
                  top: parentPaddingTop,
                  left: parentPaddingLeft
                },
                scrollbars: {
                  vertical: hasVerticalScrollbar,
                  horizontal: hasHorizontalScrollbar
                },
                currentOffset,
                note: "修复：直接使用getBoundingClientRect()结果，不添加额外偏移"
              });

              return currentOffset;
            }
          }
        } catch (e) {
          console.debug('跨域iframe检测到，无法获取偏移');
        }

        return { top: 0, left: 0 };
      }

      // 监听来自子iframe的消息并转发
      window.addEventListener("message", (e) => {
        // 只处理来自子iframe的消息
        if (
          e.source &&
          e.source !== window &&
          (e.data.type === "iframe-selection" || e.data.type === "iframe-mousedown")
        ) {
          // 处理来自子iframe的划词消息，累积偏移信息
          if (e.data.type === "iframe-selection" && e.data.rect) {
            try {
              // 找到发送消息的子iframe
              const childIframes = document.querySelectorAll("iframe");
              for (const iframe of childIframes) {
                if (iframe.contentWindow === e.source) {
                  const iframeRect = iframe.getBoundingClientRect();

                  // 获取当前窗口的滚动偏移
                  const scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;
                  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0;

                  // getBoundingClientRect() 返回的就是iframe相对于父窗口视口的正确位置
                  // 不需要额外添加任何样式偏移，因为这些已经包含在getBoundingClientRect()中
                  const currentLayerOffsetTop = iframeRect.top;
                  const currentLayerOffsetLeft = iframeRect.left;

                  // 调试信息：获取iframe的详细样式信息用于分析
                  const iframeStyles = window.getComputedStyle(iframe);
                  const borderTop = parseFloat(iframeStyles.borderTopWidth) || 0;
                  const borderLeft = parseFloat(iframeStyles.borderLeftWidth) || 0;
                  const paddingTop = parseFloat(iframeStyles.paddingTop) || 0;
                  const paddingLeft = parseFloat(iframeStyles.paddingLeft) || 0;
                  const marginTop = parseFloat(iframeStyles.marginTop) || 0;
                  const marginLeft = parseFloat(iframeStyles.marginLeft) || 0;

                  console.log(`🔄 消息转发 - 累积偏移:`, {
                    iframeRect: { top: iframeRect.top, left: iframeRect.left },
                    styles: {
                      border: { top: borderTop, left: borderLeft },
                      padding: { top: paddingTop, left: paddingLeft },
                      margin: { top: marginTop, left: marginLeft }
                    },
                    currentLayerOffset: { top: currentLayerOffsetTop, left: currentLayerOffsetLeft },
                    existingCumulativeOffset: e.data.cumulativeOffset,
                    note: "修复：直接使用getBoundingClientRect()结果，避免重复计算偏移"
                  });

                  // 如果已经有累积偏移，则在其基础上继续累积
                  if (e.data.cumulativeOffset) {
                    e.data.cumulativeOffset.top += currentLayerOffsetTop;
                    e.data.cumulativeOffset.left += currentLayerOffsetLeft;
                  } else {
                    // 初始化累积偏移
                    e.data.cumulativeOffset = {
                      top: currentLayerOffsetTop,
                      left: currentLayerOffsetLeft,
                    };
                  }

                  console.log(`✅ 累积后的偏移:`, e.data.cumulativeOffset);

                  // 累积当前层的滚动偏移信息
                  if (!e.data.scrollOffsets) {
                    e.data.scrollOffsets = [];
                  }
                  e.data.scrollOffsets.push({
                    top: scrollTop,
                    left: scrollLeft,
                  });

                  break;
                }
              }
            } catch (error) {
              console.warn("计算iframe偏移时出错:", error);
            }
          }

          // 继续向上传递消息
          if (window.parent !== window) {
            window.parent.postMessage(e.data, "*");
          }
        }
      });

      document.addEventListener("pointerup", () => {
        setTimeout(() => {
          const selection = window.getSelection();
          if (!selection || selection.isCollapsed) return;

          const text = selection.toString().trim();
          if (!text) return;

          const range = selection.getRangeAt(0);
          const rect = range.getBoundingClientRect();

          // 计算当前iframe相对于直接父窗口的偏移
          const currentOffset = getCurrentIframeOffset();

          // 获取当前iframe的实时滚动偏移
          const currentScrollTop =
            window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;
          const currentScrollLeft =
            window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0;

          const messageData = {
            type: "iframe-selection",
            text,
            rect: {
              // 保持原始的视口坐标，不加滚动偏移
              top: rect.top,
              left: rect.left,
              width: rect.width,
              height: rect.height,
            },
            cumulativeOffset: currentOffset, // 当前iframe的偏移，会在消息传递过程中累积
            iframeScrollOffset: {
              // 添加当前iframe的实时滚动偏移信息
              top: currentScrollTop,
              left: currentScrollLeft,
            },
          };

          console.log(`🚀 iframe划词事件发起:`, {
            text: text.substring(0, 20) + (text.length > 20 ? '...' : ''),
            rect,
            currentOffset,
            scrollOffset: { top: currentScrollTop, left: currentScrollLeft }
          });

          // 向父级发送消息（会被逐层转发到顶层）
          if (window.parent !== window) {
            window.parent.postMessage(messageData, "*");
          }
        }, 10); // 微延迟确保 Range 已更新
      });

      document.addEventListener("pointerdown", (e) => {
        // 点击任何地方都通知上级
        const messageData = { type: "iframe-mousedown" };
        if (window.parent !== window) {
          window.parent.postMessage(messageData, "*");
        }
      });
    }
  },
});
