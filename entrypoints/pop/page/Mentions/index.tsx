import { ReactElement, useEffect, useState } from "react";
import { Flex } from "antd";
import TextEditor from "@/components/TextEditor/index";
import { cacheGet } from "@/utils/browserStorage";
import "@/components/TextEditor/prosemirror.less";

// 主编辑器组件
export default function Mentions(): ReactElement {
  const [noteId, setNoteId] = useState("");
  const [insertType, setInsertType] = useState("");
  const [noteContent, setNoteContent] = useState("");
  const [sinoKey, setSinoKey] = useState("");
  const [type, setType] = useState("");

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const noteId = urlParams.get("noteId");
    const insertType = urlParams.get("insertType");
    const sinoKey = urlParams.get("sinoKey");
    setInsertType(insertType);
    setSinoKey(sinoKey);
    cacheGet("noteList").then((res) => {
      let currentNote = (res || []).find((x) => x.id === noteId);
      setNoteContent(
        (currentNote?.content || "").replace(
          /chrome-extension:\/\/[a-zA-Z0-9]+\/images\/textEditor\//g,
          `chrome-extension://${browser.runtime.id}/images/textEditor/`,
        ),
      );
      setNoteId(noteId);
      setType(currentNote.type);
    });
  }, []);

  const postMessage = (type, data) => {
    // 使用 URLSearchParams 解析查询参数
    const urlParams = new URLSearchParams(window.location.search);
    // 获取 "noteId" 参数的值
    const noteId = urlParams.get("noteId");
    window.parent.postMessage({ type, data, noteId }, "*");
  };

  const updateValue = (value) => {
    postMessage("updateValue", value);
  };

  return (
    <Flex vertical style={{ height: "100%" }}>
      {noteId && (
        <TextEditor
          noteId={noteId}
          height={"100%"}
          value={noteContent}
          insertType={insertType}
          sinoKey={sinoKey}
          type={type}
          placeholder={"请输入内容"}
          updateValue={updateValue}
        />
      )}
    </Flex>
  );
}
