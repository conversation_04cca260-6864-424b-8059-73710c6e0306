/** 设置页面入口文件 */
import ReactDOM from "react-dom/client";
import { ConfigProvider, ThemeConfig } from "antd";
import { RouterProvider } from "react-router-dom";
import optionsRouters from "./router";
import themeToken from "@/themeOption.json";
import zhCN from "antd/es/locale/zh_CN";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
dayjs.locale("zh-cn");
// 引入全局样式
import "@/assets/styles/index.less";

if (typeof global === "undefined") {
  var global = window; // 在浏览器中，global 对象等价于 window 对象
}
ReactDOM.createRoot(document.getElementById("popup")!).render(
  <ConfigProvider locale={zhCN} theme={themeToken as ThemeConfig}>
    <RouterProvider router={optionsRouters} />
  </ConfigProvider>,
);
