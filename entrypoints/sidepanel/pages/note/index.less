@import "@/assets/styles/variables";

.note-container {
  width: 100%;
  .input {
    margin: var(--ant-margin) 0 var(--ant-margin-sm);
  }

  .note-list-wrapper {
    // max-height: calc(100vh - 180px);
    // padding: var(--ant-margin-sm) 0 0;
    overflow-y: auto;
    column-gap: 10px;
    width: 100%;
  }
  .tag-num {
    padding: var(--ant-padding-xxs) var(--ant-padding-xs);
    font-size: var(--ant-font-size-sm);
    display: flex;
    align-items: center;
  }
  .ant-spin-nested-loading {
    overflow-y: auto;
    .ant-spin-container{
      height: 100%;
    }
  }
  .no-data {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 84%;
  }
  .note-empty-data {
    width: 100%;
  }
  .note-title-tab{
    margin-top: var(--ant-margin);
    margin-bottom: var(--ant-margin-sm);
    .item-tags{
      color: var(--ant-color-text);
      font-size: var(--ant-font-size-sm);
      padding: 1px var(--ant-padding-xs);
      cursor: pointer;
      line-height: var(--ant-line-height-sm);
    }
    .selected{
      background: var(--ant-color-primary);
      border-radius: var(--ant-border-radius-sm);
      color: #fff;
    }
  }
}

@media (max-width: 569.98px) {
  .note-list-wrapper.side-panel {
    grid-template-columns: repeat(1, 1fr);
  }
}

@media (min-width: 570px) and (max-width: 829.98px) {
  .note-list-wrapper.side-panel {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 830px) and (max-width: 1099.98px) {
  .note-list-wrapper.side-panel {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1100px) {
  .note-list-wrapper.side-panel {
    grid-template-columns: repeat(4, 1fr);
  }
}

.comment-tooltip-content {
  .ant-tooltip-inner {
    padding: 0 !important;
  }

  .create {
    padding: 0 8px;
    height: 32px;
    line-height: 32px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);

    &.mine {
      color: @primary-color;
    }
  }

  .detail {
    padding: 8px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    line-height: 22px;
  }
}

.url-tooltip-content {
  max-width: 280px;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 20px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  /* 显示两行文本 */
  -webkit-line-clamp: 2;
  /* 超出部分使用省略号代替 */
  text-overflow: ellipsis;
  overflow: hidden;
}

.note-group-sign{
  position:relative;
  min-height: 86px;
  cursor: pointer;
  margin-bottom: var(--ant-margin-sm);
  >div{
    position: absolute;
    border: 1px solid var(--ant-color-border);
    background: var(--ant-color-bg-layout); 
    border-radius: var(--ant-border-radius);
    padding: var(--ant-padding-sm);
  }
  >div:nth-child(1){
    top: 16px;
    z-index: 3;
  }
  >div:nth-child(2){
    width: calc(100% - 8px);
    left: 4px;
    top: 8px;
    z-index: 2;
  }
  >div:nth-child(3){
    width: calc(100% - 16px);
    left: 8px;
    top: 0px;
    z-index: 1;
  }
}
.note-group{
  width:100%;
  height: 70px;
  .note-group-tit{
    width:100%;
    margin-bottom: var(--ant-margin-xxs);
    font-size: var(--ant-font-size);
    line-height: var(--ant-line-height);
    span:nth-child(1){
      max-width: 86%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: var(--ant-color-text-base);
      font-weight: bold;
      display:inline-block;
    }
    span:nth-child(2){
      color: var(--ant-color-text-quaternary);
      font-size: var(--ant-font-size-sm);
      display:inline-block;
    }
  }
  .note-group-con{
    font-size: var(--ant-font-size);
    line-height: var(--ant-line-height);
    color: var(--ant-color-text-tertiary);
    overflow:hidden;
    text-overflow:ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;

  }
}
