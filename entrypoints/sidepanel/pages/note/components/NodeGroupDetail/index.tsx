import React, { useEffect, useState } from "react";
import { Flex, message, Space, Spin, theme, Tooltip, Typography } from "antd";
import TopTitle from "../../../../components/TopTitle";
import NoteCard from "../NoteCard/index";
import Empty from "../Empty/index";
const { Text } = Typography;
const { useToken } = theme;

const NodeGroupDetail: React.FC<{
  isGroupOpen: boolean;
  groupId: string;
  groupInfo?: any;
  handleBack?: () => void;
  onNoteDetail?: (item: CopilotNote) => void;
}> = ({ handleBack, isGroupOpen, groupId, groupInfo, onNoteDetail }) => {
  const { token } = useToken();
  const fetchRequest = useFetchRequest();
  const [loading, setLoading] = useState<boolean>(true);
  const [isOpen, setIsOpen] = useState(false);
  const [groupDataNote, setGroupDataNote] = useState([]);
  const handleGroupDetail = () => {
    setLoading(true);
    fetchRequest({
      api: "notesGroupPage",
      params: {
        pageNum: 1,
        pageSize: 10000,
        orders: [{ column: "create_time", asc: false }],
        entity: {
          query: "",
          groupId: groupId,
        },
      },
      callback: (res) => {
        setLoading(false);
        if (res.code === 200) {
          setGroupDataNote(res.data.page.records);
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };
  const deleItemHandler = () => {
    handleGroupDetail();
    // handleNoteList({ pageNum: 1, entity: { query: searchText, url: currentPage, type: "note_type_all" } });
  };
  useEffect(() => {
    if (isGroupOpen) {
      handleGroupDetail();
    }
  }, [isGroupOpen]);

  const handleDetail = (item: CopilotNote) => {
    onNoteDetail(item);
  };
  return (
    <Flex vertical flex={1} style={{ width: "100%" }}>
      <TopTitle title={groupInfo.name || "返回"} titleDetail={true} handleBackPar={() => handleBack()}></TopTitle>
      <Spin spinning={loading} size="default">
        <Flex vertical>
          {groupDataNote && groupDataNote.length > 0 ? (
            <>
              {groupDataNote.map((item: CopilotNote) => {
                return (
                  <NoteCard
                    key={item.id}
                    note={item}
                    groupId={groupId}
                    unReadNote={deleItemHandler}
                    onSubmitSuccess={handleGroupDetail}
                    onView={(item) => {
                      handleDetail(item);
                    }}
                  ></NoteCard>
                );
              })}
            </>
          ) : (
            <Flex vertical style={{ height: "84%", paddingTop: "45%" }}>
              <Empty />
            </Flex>
          )}
        </Flex>
      </Spin>
    </Flex>
  );
};

export default NodeGroupDetail;
