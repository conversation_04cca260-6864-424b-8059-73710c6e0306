import React, { useEffect, useState } from "react";
import { saveDetailJumpInfo } from "@/utils/notes";
import { Note } from "@/types/note";
import { NOTE_MODIFY_STORAGE_KEY } from "@/utils/browserStorageCurrentPage.ts";
import { Avatar, Button, Empty, Flex, message, Space, theme, Tooltip, Typography } from "antd";
import Delete from "@/components/ContextMenus/ConfigPanel/Delete/index";
import { DownOutlined, LinkOutlined, UpOutlined } from "@ant-design/icons";
import DiffComment from "@/components/ContextMenus/ConfigPanel/Comment/DiffComment";
import { formatDateS } from "@/utils/dateFormat";
import TopTitle from "../../../../components/TopTitle";
import "@/components/TextEditor/prosemirror.less";
import "./index.less";
import { traverseAndCollect } from "@/utils/tree";

const { Text } = Typography;
const { useToken } = theme;

const NoteDetail: React.FC<{
  detailData: Note;
  isOpen: boolean;
  handleBack?: () => void;
}> = ({ detailData, handleBack, isOpen }) => {
  const { token } = useToken();
  const fetchRequest = useFetchRequest();
  const [avatarList, setAvatarList] = useState([]);
  const [noteInfo, setNoteInfo] = useState(detailData);
  const [isCommentOpen, setIsCommentOpen] = useState<boolean>(isOpen);
  const [loading, setLoading] = useState<boolean>(true);
  const [commentList, setCommentList] = useState<Array<CommentType>>([]);

  useEffect(() => {
    setIsCommentOpen(isOpen);
  }, [isOpen]);

  useEffect(() => {
    setNoteInfo(detailData);
    getCommentPage(detailData);
  }, [detailData]);

  // 组件注册时，便开始监听用户自身便签数据的变化，一旦变化，重新渲染列表
  useEffect(() => {
    let sinoKey = sessionStorage.getItem("sino-tap-key");
    const handleNoteListChanged = (changes) => {
      const noteChange = changes[NOTE_MODIFY_STORAGE_KEY + sinoKey];
      if (noteChange && noteChange.newValue && noteChange.newValue.id === noteInfo.id) {
        if (noteChange.newValue.updateType === "del") {
          handleBack();
        } else if (noteChange.newValue.updateType === "edit") {
          setNoteInfo(noteChange.newValue);
        }
      }
    };
    browser.storage.local.onChanged.addListener(handleNoteListChanged);
    return () => {
      browser.storage.local.onChanged.removeListener(handleNoteListChanged);
    };
  }, []);

  // 点击后打开网页并滚动到便签位置
  const noteClick = () => {
    handleBack();
    saveDetailJumpInfo(noteInfo);
  };

  // 获取评论列表
  const getCommentPage = (note?) => {
    let param = {
      busiId: note ? note.id.toString() : noteInfo.id.toString(),
    };
    fetchRequest({
      api: "pageComments",
      params: param,
      callback: (res) => {
        setLoading(false);
        if (res.code === 200) {
          setCommentList(res.data);
          let arr = [];
          arr = traverseAndCollect(res.data, 4, arr);
          setAvatarList(arr);
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };

  // 展开评论
  const openComment = () => {
    setIsCommentOpen(!isCommentOpen);
  };

  const handleNote = (event) => {
    // 获取点击的元素
    const clickedElement = event?.target;
    // 检查是否有 data-data 属性
    if (event && clickedElement.hasAttribute("data-data")) {
      // 获取 data-data 属性的值
      const dataValue = clickedElement.getAttribute("data-data");
      // 将值解析为对象（假设它是一个 JSON 字符串）
      try {
        const obj = JSON.parse(dataValue);
        if (obj.type === "note_rel_note") {
          const params = { id: obj.objId };
          fetchRequest({
            api: "queryNote",
            params,
            callback: (res) => {
              if (res.code === 200) {
                saveDetailJumpInfo(res.data);
              } else {
                message.open({
                  type: "error",
                  content: res.msg,
                });
              }
            },
          });
        }
      } catch (e) {
        console.error("无法解析 data-data 的值:", e);
      }
    }
  };

  return (
    <Flex vertical flex={1}>
      <TopTitle title="返回" titleDetail={true} handleBackPar={() => handleBack()}></TopTitle>
      <Flex className="sino-detail-back-arrow" justify="flex-end">
        <Space size={0}>
          <Tooltip placement="top" title="跳转链接" getPopupContainer={(triggerNode) => triggerNode.parentNode as any}>
            <Button
              style={{ padding: token.paddingXXS, width: "32px" }}
              icon={<LinkOutlined style={{ color: token.colorText }} />}
              type="link"
              onClick={noteClick}
            ></Button>
          </Tooltip>
          {noteInfo.delEnable === 1 ? (
            <Delete
              note={noteInfo}
              svgWidth={14}
              width={32}
              type="link"
              onSuccess={() => {
                handleBack();
              }}
            />
          ) : null}
        </Space>
      </Flex>
      <Flex className="sino-detail-module" style={{ background: noteInfo?.color }} gap={8} vertical>
        <p
          className="sino-proseMirror-static"
          style={{ fontSize: "16px" }}
          dangerouslySetInnerHTML={{ __html: noteInfo.title || "无标题" }}
        />
        <Text type="secondary">{formatDateS(noteInfo.createTime, "yyyy 年 MM 月 dd 日 hh:mm:ss")}</Text>
        {noteInfo.quoteContent && (
          <Flex className="sino-quote">
            <Text mark>
              <i
                dangerouslySetInnerHTML={{
                  __html: noteInfo.quoteContent.replace(
                    /chrome-extension:\/\/[a-zA-Z0-9]+\/images\/textEditor\//g,
                    `chrome-extension://${browser.runtime.id}/images/textEditor/`,
                  ),
                }}
              ></i>
            </Text>
          </Flex>
        )}
        <p
          className="sino-proseMirror-static note-detail-p"
          onClick={handleNote}
          dangerouslySetInnerHTML={{
            __html: noteInfo.content.replace(
              /chrome-extension:\/\/[a-zA-Z0-9]+\/images\/textEditor\//g,
              `chrome-extension://${browser.runtime.id}/images/textEditor/`,
            ),
          }}
        />
      </Flex>
      <Flex vertical className={`sino-detail-bottom ${isCommentOpen ? "expanded" : ""}`}>
        <Flex justify="space-between">
          <Typography.Title level={5} style={{ display: "flex", alignItems: "center", margin: "0", padding: "10px 0" }}>
            评论
            {isCommentOpen ? (
              "..."
            ) : (
              <i style={{ marginLeft: "8px", display: "flex", alignItems: "center" }}>
                <Avatar.Group
                  max={{
                    count: 3,
                    style: { color: "#f56a00", backgroundColor: "#fde3cf" },
                  }}
                >
                  {avatarList.map((x) => {
                    return x.cmtPersonPhoto ? (
                      <Avatar
                        size={24}
                        key={x.id}
                        style={{ flex: "24px 0 0" }}
                        src={<img src={x.cmtPersonPhoto} alt="avatar" />}
                      />
                    ) : (
                      <Avatar size={24} key={x.id} style={{ flex: "24px 0 0" }}>
                        {x?.cmtPersonName?.charAt(0)}
                      </Avatar>
                    );
                  })}
                </Avatar.Group>
              </i>
            )}
          </Typography.Title>

          {isCommentOpen ? (
            <DownOutlined onClick={() => openComment()} />
          ) : (
            <UpOutlined onClick={() => openComment()} />
          )}
        </Flex>
        {isCommentOpen && !loading && (
          <Flex vertical>
            {commentList.length > 0 ? (
              <DiffComment
                commentKey={""}
                commentList={commentList}
                getCommentPage={getCommentPage}
                noteInfo={noteInfo}
              />
            ) : (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无数据" />
            )}
          </Flex>
        )}
      </Flex>
    </Flex>
  );
};

export default NoteDetail;
