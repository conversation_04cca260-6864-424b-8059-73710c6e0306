import React, { useCallback, useEffect, useState } from "react";
import { message } from "antd";
import { debounce } from "@/utils/debounce.ts";
import { pageDataInit } from "@/entrypoints/sidepanel/pages/note/index";

import "./index.less";
import { delNoteObjRela } from "@/api/note.ts";

const Relation: React.FC<{ note: CopilotNote; handleBack?: (note: CopilotNote) => void }> = ({ note, handleBack }) => {
  const updateData = note?.noteObjs
    ? note?.noteObjs.map((item: any) => ({
        ...item,
        value: item.objId,
        name: item.objDesc ? item.objDesc : item.objName,
      }))
    : [];
  const [noteInfo, setNoteInfo] = useState(note);
  // 搜索参数
  const [searchText, setSearchText] = useState<string>("");

  const fetchRequest = useFetchRequest();

  // 查询数据的请求参数
  const [searchParams, setSearchParams] = useState<PageAPIRequest<PageNotesRequest>>({
    query: "",
  });
  // 当前显示的提示词分页数据
  const [notePageData, setNotePageData] = useState<PageAPIResponse<CopilotNote>>({
    ...pageDataInit,
    total: (note?.noteObjs ?? []).length,
    list: note?.noteObjs ?? [],
  });
  const [relationList, setRelationList] = useState(note?.noteObjs ?? []);
  let noteArr = [];
  updateData.forEach((item: any) => {
    if (item.relType == "note_rel_note") {
      noteArr.push(item);
    }
  });

  const [noteList, setNoteList] = useState([noteArr]); // 所有的关联便签

  useEffect(() => {
    setNoteInfo(note);
    // setNotePageData({
    //   ...pageDataInit,
    //   total: (note?.relatedNotes ?? []).length,
    //   list: note?.relatedNotes ?? []
    // });
    let arr = [];
    updateData.forEach((item: any) => {
      if (item.relType == "note_rel_note") {
        arr.push(item);
      }
    });
    setNoteList(arr);
    setRelationList(note?.noteObjs ?? []);
  }, [note]);

  /** 分页查询便签 */
  const handleNoteList = (paramsData) => {
    let obj = {
      type: "note_rel_note",
      query: paramsData.query,
    };
    fetchRequest({
      api: "listNoteRela",
      params: obj,
      callback: (res) => {
        if (res.code === 200) {
          let obj = {
            list: res.data || [],
            total: res.data?.length || 0,
          };
          setNotePageData(obj);
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
        // setLoading(false);
      },
    });
  };

  // useEffect(() => {
  //   handleNoteList(searchParams);
  // }, []);

  const handleNoteSearch = (value) => {
    searchParams.query = value;
    setSearchParams(searchParams);

    handleNoteList(searchParams);
  };

  const updateNoteRelationPage = (noteObjs) => {
    // noteInfo.relatedNotes = relatedNotes;
    // setNoteInfo(noteInfo);
    // setNotePageData({
    //   ...pageDataInit,
    //   total: (noteInfo?.relatedNotes ?? []).length,
    //   list: noteInfo?.relatedNotes ?? []
    // });
    handleBack({ noteObjs });
  };

  const debounceNoteSearch = useCallback(debounce(handleNoteSearch, 500), []);
  const inputChange = (e) => {
    const value = e.target.value.trim();
    setSearchText(value);

    // setLoading(true);
    if (value) {
      debounceNoteSearch(value);
    } else {
      updateNoteRelationPage(relationList);
    }
  };

  const handleSelected = (event, noteElement, selected) => {
    event.stopPropagation();
    let noteObjs = [];
    if (selected) {
      let params = { id: noteElement.id };
      fetchRequest({
        api: "delNoteObjRela",
        params,
        callback: (res) => {
          if (res.code == 200) {
            message.open({
              type: "success",
              content: "取消关联成功！",
            });
            noteObjs = relationList.filter((item) => item.objId !== noteElement.objId);
            setRelationList(noteObjs);
            let obj = {
              list: relationList || [],
              total: relationList?.length || 0,
            };
            setNotePageData(obj);
            !searchText && updateNoteRelationPage(noteObjs);
          }
        },
      });
    } else {
      // const { noteId, objId, url } = noteElement || {};
      let obj = {
        noteId: note.id,
        objId: noteElement.objId,
        objDesc: noteElement.name,
        relType: "note_rel_note",
      };
      fetchRequest({
        api: "addNoteObjRela",
        params: obj,
        callback: (res) => {
          if (res.code == 200) {
            message.open({
              type: "success",
              content: "关联成功！",
            });
            noteObjs = [
              ...relationList,
              {
                id: res.data,
                objId: noteElement.objId,
                objName: noteElement.name,
                noteId: note.id,
                relType: noteElement.type,
              },
            ];
            setRelationList(noteObjs);
            noteObjs.forEach((val: any) => {
              notePageData.list.forEach((item: any) => {
                if (item.objId == val.objId) {
                  item.id = val.id;
                }
              });
            });
            updateNoteRelationPage(noteObjs);
          }
        },
      });
    }
  };

  const renderNoteList = (item) => {
    const query = relationList.find((r) => r.objId == item.objId);

    return (
      <div
        // className={`relation-item ${query ? "selected" : ""}`}
        className="relation-item"
        onClick={(e) => handleSelected(e, item, query)}
      >
        <div dangerouslySetInnerHTML={{ __html: item.objDesc || item.name }} title={item.objDesc || item.name} />
        {/* {query && (
          <Tooltip
            placement="top"
            title={query ? "取消关联" : "关联"}
            getPopupContainer={(triggerNode) => triggerNode.parentNode}
          >
            <span className="icon" onClick={(e) => handleSelected(e, item, query)}>
              {LinkSVGIcon}
            </span>
          </Tooltip>
        )} */}
      </div>
    );
  };

  return (
    <>
      <div className="relation">
        {/* <div className="relation-input">
          <Input
            allowClear
            placeholder="搜索便签名称"
            style={{ width: "100%" }}
            value={searchText}
            onChange={inputChange}
          />
        </div>
        {searchText && (!notePageData || notePageData.total === 0) && <p className="empty">没有找到便签</p>} */}
        {/* <div className="relation-list-wrapper">
          {searchText
            ? notePageData.list.map((item: any) => {
                return <div key={item.objId}>{renderNoteList(item)}</div>;
              })
            : relationList.map((item: any) => {
                return item.relType  == "note_rel_note" ? <div key={item.objId}>{renderNoteList(item)}</div> : "";
              })}
        </div> */}
        {noteList && noteList.length > 0 ? (
          <div>
            <p className="relation-title">
              <span>关联的便签</span>
              <img src={browser.runtime.getURL("/images/textEditor/relation.png")} alt="" />
            </p>
            <div className="relation-list-wrapper">
              {noteList.map((item: any) => {
                return <div key={item.objId}>{renderNoteList(item)}</div>;
              })}
            </div>
          </div>
        ) : (
          <p className="empty">暂未关联便签</p>
        )}
      </div>
    </>
  );
};

export default Relation;
