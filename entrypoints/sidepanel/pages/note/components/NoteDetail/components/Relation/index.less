@import "@/assets/styles/variables";

.relation {
  display: flex;
  flex-direction: column;
  height: 100%;
  .relation-input {
    margin-bottom: 16px;
    .ant-input-affix-wrapper .ant-input {
      padding: 5px 12px !important;
    }
  }
  .relation-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    span {
      font-family: @side-panel-font-family-bold;
      font-size: 14px;
      color: #121212;
    }
    img {
      width: 20px;
    }
  }
  .empty {
    font-family: AlibabaPuHuiTi_2_55_Regular;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.45);
    line-height: 20px;
    margin: 30px auto;
  }
  .relation-list-wrapper {
    flex: 1;
    overflow-y: auto;
    padding-right: 4px;
    .relation-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 4px;
      height: 28px;
      margin-bottom: 8px;
      border-radius: 6px;
      box-sizing: border-box;
      cursor: pointer;
      &:hover,
      &.selected {
        background: #f5e8ff;
      }
      & > div {
        max-width: 90%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        font-size: 14px;
        color: #121212;
      }
      .icon {
        color: @primary-color;
      }
    }
  }
}
