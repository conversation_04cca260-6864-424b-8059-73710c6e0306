@import "@/assets/styles/variables";

.sino-detail-back-arrow {
  position: fixed;
  z-index: 999;
  top: 0;
  right: 0;
  width: 50%;
  height: 60px;
  line-height: 60px;
  padding: 0 20px;
  .back-width {
    // width: 30px;
    font-size: var(--ant-font-size-heading-3);
    color: var(--ant-color-text);
    cursor: pointer;
  }
}
.sino-detail-module {
  width: 100%;
  padding: var(--ant-padding) var(--ant-padding) var(--ant-padding-md);
  overflow: auto;
  border-radius: var(--ant-border-radius);
  border: 1px solid var(--ant-color-border);
  background-color: var(--ant-color-bg-container);
  margin-bottom: 60px;
  z-index: 1;
  .note-detail-p {
    font-family: @side-panel-font-family;
    font-size: 14px;
    color: #121212;
    line-height: 20px;
  }
  .sino-proseMirror-static {
    padding: 0;
    margin: 0;
    > p {
      display: flex;
      align-items: center;
      line-height: var(--ant-line-height);
      flex-wrap: wrap;
    }
    a {
      background: var(--ant-color-primary-bg;);
      border-radius: var(--ant-border-radius-xs);
      padding: 0px var(--ant-padding-xxs);
      display: inline-flex;
      align-items: center;
      line-height: var(--ant-line-height);
      font-family: side-panel-font-family;
      font-size: var(--ant-font-size-sm);
      color: var(--ant-color-primary-text);
      margin: 0px 2px 0px;
    }
    i {
      font-style: normal;
    }
  }
}
.sino-detail-bottom {
  position: fixed;
  padding: 0 16px;
  z-index: 1000;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--ant-color-bg-container);
  border-radius: 0 0 12px 12px;
  border-top: 1px solid var(--ant-color-border);
  transition: all 0.2s linear;
  overflow-y: auto;
  &.expanded {
    height: 50vh;
    padding-bottom: 10px;
  }
}
.sino-quote {
  min-height: 20px;
  max-height: 111px;
  overflow-y: auto;
  font-style: normal;
  i {
    font-style: normal;
  }
}
