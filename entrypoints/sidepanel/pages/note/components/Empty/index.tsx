import React from "react";
import "./index.less";
import { Empty, Flex, Typography } from "antd";

interface NoteEmptyProps {
  title?: string;
  description?: string;
  showImage?: boolean;
}

const NoteEmpty: React.FC<NoteEmptyProps> = ({
  title = "暂无便签",
  description = "请在页面中右键或划词添加便签",
  showImage = true,
}) => {
  return (
    <Flex className="note-empty-data" vertical align="center">
      <Empty
        image={showImage ? Empty.PRESENTED_IMAGE_SIMPLE : null}
        imageStyle={{}}
        description={
          <Flex vertical>
            <Typography.Text className="desc-title">{title}</Typography.Text>
            <Typography.Text className="desc-descr">{description}</Typography.Text>
          </Flex>
        }
      />
    </Flex>
  );
};

export default NoteEmpty;
