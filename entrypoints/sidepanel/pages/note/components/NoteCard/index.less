@import "@/assets/styles/variables";
.note-item {
  box-sizing: border-box;
  position: relative;
  margin-bottom: var(--ant-margin-sm);
  border: 1px solid var(--ant-color-border);
  cursor: pointer;
  border-radius: var(--ant-border-radius);
  padding: var(--ant-padding-sm);
  &:hover {
    .operte-icon {
      opacity: 1 !important;
    }
  }
  .note-card-circle {
    width: 6px;
    height: 6px;
    display: block;
    border-radius: 50%;
    background-color: var(--ant-color-error);
    position: absolute;
    top: 8px;
    left: 8px;
  }
  .day {
    font-size: var(--ant-font-size-sm);
    color: var(--ant-color-text-quaternary);
  }

  .note-item-top {
    box-sizing: border-box;
    height: 24px;
    margin-bottom: var(--ant-margin-xxs);
    .common-title,
    .common-title p {
      max-width: 86%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-family: @side-panel-font-family-bold;
    }
    .title,
    .title p {
      font-size: var(--ant-font-size);
      color: var(--ant-color-text-base);
      font-weight: var(--ant-font-weight-strong);
    }
    .unread-title,
    .unread-title p {
      font-size: var(--ant-font-size);
      color: var(--ant-color-text-tertiary);
      font-weight: var(--ant-font-weight-strong);
    }
  }

  .common-content {
    max-width: 100%;
    overflow: hidden; /* 隐藏溢出的内容 */
    // white-space: nowrap; /* 禁止换行 */
    text-overflow: ellipsis; /* 超出部分显示省略号 */
    font-family: @side-panel-font-family;
    margin: 0;
    display: inline-flex;
    align-items: center;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }

  .common-content p {
    white-space: nowrap;
    text-overflow: ellipsis;
    margin: 0;
    display: inline-flex;
    align-items: center;
  }
  .content,
  .content p {
    font-size: var(--ant-font-size);
    color: var(--ant-color-text);
  }
  .unread-content p,
  .unread-content {
    font-size: var(--ant-font-size);
    color: var(--ant-color-text-tertiary);
  }

  .note-card-footer {
    margin-top: var(--ant-margin-xxs);
    .footer-bar {
      font-size: var(--ant-font-size-sm);
      color: var(--ant-color-text-tertiary);
    }
    .team-icon {
      margin-right: var(--ant-margin-xxs);
    }
    .operte-icon {
      gap: var(--ant-margin-xxs);
      opacity: 0;
      .anticon {
        font-size: var(--ant-font-size);
        color: var(--ant-color-text);
      }
    }
    .footer-bar:not(:empty) ~ .operte-icon {
      margin-left: auto;
    }
  }
  .team-icon {
    margin-right: var(--ant-margin-xxs);
  }

  a[data-data] {
    font-size: var(--ant-font-size);
    color: var(--ant-color-primary);
    display: inline-flex;
    align-items: center;
    margin-right: 2px;
  }
}
