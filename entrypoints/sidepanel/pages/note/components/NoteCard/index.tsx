import React, { useEffect } from "react";
import Delete from "@/components/ContextMenus/ConfigPanel/Delete/index";
import { Button, Flex, message, Tooltip, Typography, theme } from "antd";
import { saveDetailJumpInfo } from "@/utils/notes";
import { formatDate } from "@/utils/dateFormat.ts";
import "@/components/TextEditor/prosemirror.less";
import "./index.less";
import { CommentOutlined, MinusOutlined, ProfileOutlined, TeamOutlined } from "@ant-design/icons";
import classnames from "classnames";
const { useToken } = theme;
import { ONQUIZ } from "@/utils/notes";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import remarkBreaks from "remark-breaks";
export type NoteCardProps = {
  /** 便签数据 */
  note: any;
  /** 查看操作时的回调：时生效 */
  onView?: (note: CopilotNote) => void;
  groupId?: string; // 群组id
  /** 卡片操作成功后的回调 */
  onSubmitSuccess?: () => void;
  unReadNote?: () => void;
};

const NoteCard: React.FC<NoteCardProps> = ({ note, groupId, onView, onSubmitSuccess, unReadNote }) => {
  const { token } = useToken();
  useEffect(() => {
    let arr = [];
    note.noteObjs.map((item: any) => {
      if (item.relType == "note_rel_contact") {
        arr.push(item);
      }
    });
    note.userList = arr;
  }, []);
  // 点击后打开网页并滚动到便签位置
  const noteClick = (event) => {
    // 获取点击的元素
    const clickedElement = event?.target;
    // 检查是否有 data-data 属性
    if (event && clickedElement.hasAttribute("data-data")) {
      // 获取 data-data 属性的值
      const dataValue = clickedElement.getAttribute("data-data");
      // 将值解析为对象（假设它是一个 JSON 字符串）
      try {
        const obj = JSON.parse(dataValue);
        if (obj.type === "note_rel_note") {
          const params = { id: obj.objId };
          fetchRequest({
            api: "queryNote",
            params,
            callback: (res) => {
              if (res.code === 200) {
                saveDetailJumpInfo(res.data);
              } else {
                message.open({
                  type: "error",
                  content: res.msg,
                });
              }
            },
          });
        }
      } catch (e) {
        console.error("无法解析 data-data 的值:", e);
      }
    } else {
      if (note.readFlag == "read_flag_false") {
        noteReadHandle();
      }
      saveDetailJumpInfo(note);
    }
  };
  const fetchRequest = useFetchRequest();
  const goView = (event) => {
    event.stopPropagation();
    const params = { id: note.id };
    fetchRequest({
      api: "queryNote",
      params,
      callback: (res) => {
        if (res.code === 200) {
          onView(res.data);
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };
  const noteReadHandle = () => {
    fetchRequest({
      api: "markNoteRead",
      params: { id: note.id },
      callback: () => {
        unReadNote();
      },
    });
  };
  //移除该便签
  const noteRelationRemoveId = (note) => {
    fetchRequest({
      api: "noteRelationRemove",
      params: {
        noteIds: [note.id],
        groupId: groupId,
      },
      callback: (res) => {
        if (res.code == "200") {
          message.open({
            type: "success",
            content: "剔除成功！",
          });
          unReadNote();
        }
      },
    });
  };
  return (
    <>
      <Flex
        className="note-item"
        style={{
          width: browser.tabs ? 248 : note?.width,
          background: note?.color,
        }}
        vertical
        onClick={(e) => noteClick(e)}
      >
        <Flex vertical>
          {!(note?.readFlag === "read_flag_true") && <span className="note-card-circle"></span>}
          <Flex justify="space-between" align="center" className="note-item-top">
            <Typography.Text
              className={classnames(
                "sino-proseMirror-static sino-proseMirror-static-view common-title ",
                note?.readFlag != "read_flag_true" ? "title" : "unread-title",
              )}
            >
              {!note.title ? "无标题" : note.title}
            </Typography.Text>
            <Typography.Text className="day">{formatDate(note?.createTime)}</Typography.Text>
          </Flex>
          {note.type != ONQUIZ ? (
            <span
              className={classnames(
                "sino-proseMirror-static sino-proseMirror-static-view common-content",
                note?.readFlag != "read_flag_true" ? "content" : "unread-content",
              )}
              dangerouslySetInnerHTML={{
                __html: note?.content.replace(
                  /chrome-extension:\/\/[a-zA-Z0-9]+\/images\/textEditor\//g,
                  `chrome-extension://${browser.runtime.id}/images/textEditor/`,
                ),
              }}
            />
          ) : (
            <ReactMarkdown
              className={classnames(
                "sino-proseMirror-static sino-proseMirror-static-view common-content",
                note?.readFlag != "read_flag_true" ? "content" : "unread-content",
              )}
              remarkPlugins={[remarkGfm, remarkBreaks]}
              rehypePlugins={[rehypeRaw]}
            >
              {note?.content}
            </ReactMarkdown>
          )}
        </Flex>

        <Flex className="note-card-footer" align="center" justify="space-between">
          <Flex className="footer-bar">
            <Flex>
              {/* cooperation_type_other 他人创建并@我 */}
              {/* cooperation_type_null  我创建  没有@他人 */}
              {/* cooperation_type_me  我创建并@他人 */}
              {note.cooperationType === "cooperation_type_other" && (
                <>
                  <TeamOutlined className="team-icon" />
                  {note.belongUserName}
                </>
              )}
              {note.cooperationType === "cooperation_type_me" && (
                <>
                  <TeamOutlined className="team-icon" />
                </>
              )}
            </Flex>
          </Flex>
          <Flex className="operte-icon">
            <Flex>
              <Tooltip placement="top" title="评论" getPopupContainer={(triggerNode) => triggerNode.parentNode as any}>
                <Button
                  icon={<CommentOutlined />}
                  size="small"
                  type="text"
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                ></Button>
              </Tooltip>
              <Flex align="center" style={{ color: token.colorTextQuaternary }}>
                {note.commentCount}
              </Flex>
            </Flex>
            {groupId && note.delEnable === 1 && (
              <Tooltip
                placement="top"
                title="剔除便签"
                getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
              >
                <Button
                  icon={<MinusOutlined />}
                  type="text"
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    noteRelationRemoveId(note);
                  }}
                ></Button>
              </Tooltip>
            )}
            <Tooltip
              placement="top"
              title="查看详情"
              getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
            >
              <Button icon={<ProfileOutlined />} size="small" onClick={(e) => goView(e)} type="text"></Button>
            </Tooltip>

            {note.delEnable === 1 ? (
              <Delete
                note={note}
                width={24}
                svgWidth={14}
                onSuccess={() => {
                  onSubmitSuccess?.();
                }}
              />
            ) : null}
          </Flex>
        </Flex>
      </Flex>
    </>
  );
};

export default NoteCard;
