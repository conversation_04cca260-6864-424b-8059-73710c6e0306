@import "@/assets/styles/variables";

.side-panel-content-mine {
  width: 100%;
  .mine-info {
    .mine-info-name {
      user-select: none;
      background: var(--ant-color-fill-quaternary); /* rgba(0, 0, 0, 0.02) */
      border-radius: var(--ant-border-radius-lg);
      padding: var(--ant-padding) var(--ant-padding-sm);

      .mine-info-name-left {
        img {
          border-radius: 10px;
          width: 32px;
          height: 32px;
        }

        .mine-info-name-avatar {
          background-color: var(--ant-color-primary);
          color: var(--ant-color-bg-base);
          height: 100%;
          line-height: 32px;
          text-align: center;
          width: 32px;
          height: 32px;
          border-radius: var(--ant-border-radius);

          font-size: var(--ant-font-size-sm);
        }
      }

      .mine-info-name-right {
        flex: 1;
        .mine-info-name-title {
          font-family: @side-panel-font-family-bold;
          font-size: var(--ant-faont-size);
          color: var(--ant-color-text-base);
          padding-left: var(--ant-padding-sm);
        }

        .mine-info-name-position {
          font-size: var(--ant-faont-size);
          color: var(--ant-color-text-tertiary);
        }
      }
    }

    .mine-info-crop,
    .mine-info-phone {
      margin-top: var(--ant-margin-md);
      background: var(--ant-color-fill-quaternary);
      border-radius: var(--ant-border-radius-lg);
      padding: 0 var(--ant-padding-sm);

      .mine-info-top,
      .mine-info-bottom {
        height: 46px;
        font-size: var(--ant-font-size);
        .mine-info-label {
          color: var(--ant-color-text-tertiary);
          font-family: @side-panel-font-family-bold;
          font-size: var(--ant-font-size-sm);
          padding-right: var(--ant-padding-sm);
        }

        .mine-info-content {
          color: var(--ant-color-text);
          font-size: var(--ant-font-size);
        }
      }

      .mine-info-top {
        border-bottom: 1px solid var(--ant-color-split);
      }
    }
  }
  .mine-info-submit {
    margin-top: var(--ant-margin-md);
  }
  .mine-info-submit-btn {
    flex: 1;
    color: var(--ant-color-text-light-solid);
  }
}
