import React, { useCallback, useEffect, useRef, useState, useMemo } from "react";
import { Button, Input, Card, Typography, List, Space, message, Flex, Divider, Tooltip } from "antd";
import { FileTextOutlined, PauseOutlined, LeftOutlined, RightOutlined } from "@ant-design/icons";
import TopTitle from "../../components/TopTitle";
import { useFetchRequest } from "@/hooks/useFetchRequest";
import MentionsComponent, { MentionsComponentRef } from "./components/FormDetectorMentions";
import { useGetState } from "ahooks";
import { extractFormData as extractFormDataUtil } from "@/utils/formDataExtractor";
import useSSEChat, { GenerationProgress } from "@/hooks/useSSEChat.ts";
import AgentOutput from "@/components/AgentOutput";
import { getUserInfo, UserInfo, getToken, getTenantId } from "@/utils/auth.ts";
import { debounce, throttle } from "@/utils/debounce";
import { getFormElementLabel, detectFormElementType } from "@/utils/frameworkFormDetector";
// import contentData from "./data.json";
import "./index.less";

const { Title, Text } = Typography;

interface ContractInfo {
  party1: string;
  party2: string;
  amount: string;
  paymentTerms: string;
}
type StateType = {
  selectedTab: string;
  content: string | undefined;
  bearer: string;
};

const xinqiaohetongkey = "app-tnAp2ScwikjBhlTT6A4shV2h";
const agentxinqiaoid = "aa139b2b-3563-4e64-94c2-204c336fc881";
const FormFilling: React.FC = () => {
  const [inputText, setInputText] = useState("");
  const [isFilling, setIsFilling] = useState<boolean>(false);
  const [contractInfo, setContractInfo] = useState<ContractInfo | null>(null);
  const mentionsRef = useRef<MentionsComponentRef>(null);
  const resultDomRef = useRef<HTMLDivElement>(null);
  const [formData, setFormData] = useState<any>(null);
  const [state, setState] = useState<StateType>({
    selectedTab: "1",
    content: "",
    bearer: "gpt-4o",
  });
  const [getState, setGetState] = useGetState(state);
  const [bearerList, setBearerList] = useState<any[]>([]);
  const [selectedfrom, setSelectedfrom] = useState<string>("0"); //默认是知识库
  const [localFileUploadResData, setLocalFileUploadResData] = useState<any>(null); //本地文件上传返回数据
  const [userInfo, setUserInfo] = useState<UserInfo>({});
  // 多次生成时当前显示的条目
  const [number, setNumber] = useGetState<number>(0);
  const [contentHeight, setContentHeight] = useState("calc(100vh - 200px)"); // 固定高度，避免动态变化引起抖动
  const fetchRequest = useFetchRequest();
  // SSE会话Hook - 使用自定义的流式数据处理
  const sseChat = useSSEChat();

  // 实时处理的流式数据
  const [processedStreamData, setProcessedStreamData] = useState<string>("");
  // 累积的格式化内容
  const [accumulatedFormattedData, setAccumulatedFormattedData] = useState<string>("");
  // 稳定的格式化数据（减少抖动）
  const [stableFormattedData, setStableFormattedData] = useState<string>("");
  // 已处理的原始数据长度（用于跟踪哪些数据已被格式化）
  const [processedRawLength, setProcessedRawLength] = useState<number>(0);
  // 当前显示的内容状态
  const [displayContent, setDisplayContent] = useState<string>("");

  // 原始数据容器的引用，用于自动滚动
  const rawDataRef = useRef<HTMLDivElement>(null);
  // 格式化内容容器的引用，用于自动滚动
  const formattedContentRef = useRef<HTMLDivElement>(null);
  // 父容器引用，用于高度约束
  const parentContainerRef = useRef<HTMLDivElement>(null);
  // 父容器高度状态
  const [parentHeight, setParentHeight] = useState<number>(0);
  const [showFormPanel, setShowFormPanel] = useState(false);
  // 自动滚动到原始数据容器底部
  useEffect(() => {
    if (rawDataRef.current && sseChat.displayedText) {
      rawDataRef.current.scrollTop = rawDataRef.current.scrollHeight;
    }
  }, [sseChat.displayedText]);

  // 自动滚动到格式化内容底部
  useEffect(() => {
    if (formattedContentRef.current && accumulatedFormattedData) {
      formattedContentRef.current.scrollTop = formattedContentRef.current.scrollHeight;
    }
  }, [accumulatedFormattedData]);

  // 自动滚动到原始数据底部
  useEffect(() => {
    if (rawDataRef.current && sseChat.displayedText) {
      rawDataRef.current.scrollTop = rawDataRef.current.scrollHeight;
    }
  }, [sseChat.displayedText]);

  // 监听父容器高度变化
  useEffect(() => {
    const updateParentHeight = () => {
      if (parentContainerRef.current) {
        const rect = parentContainerRef.current.getBoundingClientRect();
        setParentHeight(rect.height);
      }
    };

    updateParentHeight();
    window.addEventListener("resize", updateParentHeight);
    // 监听 FormDetector 关闭消息
    const handleMessage = (event) => {
      if (event.data.type === "DATA_COLLECTOR_CLOSED") {
        setShowFormPanel(false);
      }
    };
    window.addEventListener("message", handleMessage);
    return () => {
      window.removeEventListener("resize", updateParentHeight);
      window.removeEventListener("message", handleMessage);
    };
  }, []);

  // 计算动态高度分配
  const calculateHeights = useMemo(() => {
    const minRawDataHeight = 120; // 最小高度4行
    const maxRawDataHeight = 200; // 最大高度约8行
    const hasFormattedData = !!stableFormattedData;
    const isStreaming = sseChat.progress !== GenerationProgress.RENDER_FINISHED;

    if (!hasFormattedData || !isStreaming) {
      return { topHeight: "auto", bottomHeight: minRawDataHeight };
    }

    // 根据数据长度动态调整原始数据容器高度
    const dataLength = sseChat.displayedText?.length || 0;
    const estimatedLines = Math.ceil(dataLength / 80); // 假设每行约80字符
    const dynamicHeight = Math.min(maxRawDataHeight, Math.max(minRawDataHeight, estimatedLines * 20));

    // 如果总高度超过父容器，调整上部分高度
    if (parentHeight > 0) {
      const availableHeight = parentHeight - 32; // 减去padding和margin
      if (availableHeight > dynamicHeight) {
        const topMaxHeight = availableHeight - dynamicHeight;
        return {
          topHeight: `${topMaxHeight}px`,
          bottomHeight: dynamicHeight,
        };
      }
    }

    return { topHeight: "auto", bottomHeight: dynamicHeight };
  }, [stableFormattedData, sseChat.progress, parentHeight, sseChat.displayedText]);

  // 创建防抖版本的格式化函数，避免频繁更新
  const debouncedFormatStreamingData = useMemo(
    () =>
      debounce((content: string, callback: (result: string) => void) => {
        const result = formatStreamingData(content);
        callback(result);
      }, 50), // 50ms防抖，提高响应速度
    [],
  );

  // 监听displayedText变化，实时处理流式数据
  useEffect(() => {
    console.log("[调试] sseChat状态:", {
      isRendering: sseChat.isRendering,
      progress: sseChat.progress,
      displayedTextLength: sseChat.displayedText?.length || 0,
      chatListLength: sseChat.chatList.length,
    });

    if (sseChat.displayedText) {
      console.log("[实时处理] 接收到流式数据:", sseChat.displayedText.substring(0, 100));

      const currentRawData = sseChat.displayedText;
      const currentLength = currentRawData.length;

      // 使用防抖版本的格式化函数
      debouncedFormatStreamingData(currentRawData, (fullFormatted) => {
        console.log("[实时处理] 完整格式化后数据:", fullFormatted);

        // 检查是否有新的格式化内容（不是默认的等待状态）
        const hasValidFormatted =
          fullFormatted &&
          !fullFormatted.includes("⏳ **正在接收数据...**") &&
          !fullFormatted.includes("⏳ **等待数据...**") &&
          fullFormatted.trim().length > 0;

        if (hasValidFormatted) {
          // 立即更新累积的格式化数据，确保实时性
          setAccumulatedFormattedData(fullFormatted);

          // 有新的格式化内容，只在内容有显著变化时更新稳定数据（减少抖动）
          if (fullFormatted !== stableFormattedData) {
            const changeThreshold = Math.min(50, fullFormatted.length * 0.1); // 降低变化阈值，提高敏感度
            if (Math.abs(fullFormatted.length - stableFormattedData.length) > changeThreshold) {
              setStableFormattedData(fullFormatted);
              // 估算格式化数据对应的原始数据长度（更保守的估算）
              const estimatedFormattedLength = Math.floor(currentLength * 0.7); // 提高到70%
              setProcessedRawLength(estimatedFormattedLength);
              console.log("[流式输出] 更新稳定的格式化内容", `估算已格式化长度: ${estimatedFormattedLength}`);
            }
          }
        }
      });

      // 更新显示内容 - 混合显示格式化数据和实时原始数据
      let displayContent = "";
      if (stableFormattedData) {
        displayContent = stableFormattedData;
        // 如果还有未处理的原始数据，添加实时流式数据
        if (processedRawLength < currentLength) {
          const remainingRawData = currentRawData.substring(processedRawLength);
          const limitedRawData = limitRawDataLines(remainingRawData, 4);
          if (limitedRawData.trim()) {
            displayContent += "\n\n**实时流式数据:**\n" + limitedRawData;
          }
        }
      } else {
        // 如果没有格式化数据，显示有限的原始数据
        displayContent = limitRawDataLines(currentRawData, 10);
      }
      setDisplayContent(displayContent);
      setProcessedStreamData(displayContent);
    } else {
      // 重置所有状态
      setProcessedStreamData("");
      setAccumulatedFormattedData("");
      setProcessedRawLength(0);
      setDisplayContent("");
    }
  }, [
    sseChat.displayedText,
    sseChat.isRendering,
    sseChat.progress,
    accumulatedFormattedData,
    debouncedFormatStreamingData,
    stableFormattedData,
    processedRawLength,
  ]);

  // 监听流式渲染完成，强制执行最后一次格式化
  useEffect(() => {
    if (!sseChat.isRendering && sseChat.displayedText && sseChat.progress === GenerationProgress.RENDER_FINISHED) {
      console.log("[流式完成] 强制执行最后一次格式化");
      // 强制执行格式化，不使用防抖，直接调用原始函数
      const finalFormatted = formatStreamingData(sseChat.displayedText);
      console.log("[流式完成] 最终格式化结果:", finalFormatted);
      console.log("[流式完成] 原始数据长度:", sseChat.displayedText.length);
      console.log("[流式完成] 格式化数据长度:", finalFormatted?.length || 0);

      if (finalFormatted && finalFormatted.trim().length > 0) {
        setAccumulatedFormattedData(finalFormatted);
        setStableFormattedData(finalFormatted);
        console.log("[流式完成] 更新最终格式化数据完成");

        // 强制触发防抖函数的回调，确保第173行的日志能够输出最终结果
        console.log("[实时处理] 完整格式化后数据:", finalFormatted);
      }
    }
  }, [sseChat.isRendering, sseChat.progress, sseChat.displayedText]);

  // 移除扫描动画相关状态，流式时直接显示格式化内容

  // 自动滚动到底部
  useEffect(() => {
    if (resultDomRef.current && sseChat.displayedText) {
      const container = resultDomRef.current.querySelector(".agent-output-container");
      if (container) {
        requestAnimationFrame(() => {
          const { scrollTop, scrollHeight, clientHeight } = container;
          const isNearBottom = scrollHeight - scrollTop - clientHeight < 50;

          if (scrollHeight > clientHeight && isNearBottom) {
            container.scrollTop = scrollHeight;
          }
        });
      }
    }
  }, [sseChat.displayedText]);

  // 监听流式渲染完成，但不自动重置SSE状态以保持结果显示
  // useEffect(() => {
  //   if (sseChat.progress === GenerationProgress.RENDER_FINISHED && !sseChat.isRendering) {
  //     // 延迟重置SSE状态，解决光标闪烁问题
  //     setTimeout(() => {
  //       sseChat.reset();
  //     }, 100);
  //   }
  // }, [sseChat.progress, sseChat.isRendering]);

  // 获取MentionsComponent的数据
  const getMentionsData = useCallback(() => {
    return (
      mentionsRef.current?.getMentionsData() || {
        query: "",
      }
    );
  }, []);

  // 清空MentionsComponent的数据
  const clearMentionsData = useCallback(() => {
    mentionsRef.current?.setMentionsData({
      query: "",
      localFile: [],
      selectKnowledgeArr: [],
      selectedKnowledgeItem: null,
    });
  }, []);
  // 获取用户信息
  useEffect(() => {
    getUserInfo().then((res) => {
      setUserInfo(res);
    });

    // 获取 bearer 列表
    const initBearerList = async () => {
      try {
        const token = await getToken();
        const tenantId = await getTenantId();
        if (token && tenantId) {
          fetchRequest({
            api: "getAgentList",
            params: {
              path: "/api/v1/agent/list",
            },
            callback: (res: any) => {
              if (res?.data) {
                setBearerList(res.data);
                if (res.data.length > 0) {
                  setState((prev) => ({ ...prev, bearer: res.data[0].id }));
                }
              }
            },
          });
        }
      } catch (error) {
        console.error("Failed to fetch bearer list:", error);
      }
    };

    initBearerList();
  }, []);

  // 提取表单数据的核心函数
  const extractFormData = useCallback(() => {
    // 使用统一的表单数据提取器
    const allFormData = extractFormDataUtil("formDetector");

    console.log("📋 表单数据提取完成", {
      totalForms: allFormData.length,
      formTypes: allFormData.map((form) => form.formType),
      totalFields: allFormData.reduce((sum, form) => sum + form.fields.length, 0),
    });

    return {
      allFormData,
    };
  }, []);

  // 监听SSE状态变化，处理错误情况和完成状态
  useEffect(() => {
    if (isFilling) {
      if (sseChat.progress === GenerationProgress.ERROR) {
        console.error("SSE连接发生错误");
        setIsFilling(false);
        message.error("智能填充处理失败，请重试！");
      } else if (sseChat.progress === GenerationProgress.USER_CANCELED) {
        console.log("用户取消了填充操作");
        setIsFilling(false);
        message.info("已取消智能填充操作");
      } else if (sseChat.progress === GenerationProgress.RENDER_FINISHED) {
        console.log("SSE渲染完成，确保状态恢复");
        // 延迟一点时间确保所有处理都完成
        setTimeout(() => {
          setIsFilling(false);
        }, 200);
      }
    }
  }, [sseChat.progress, isFilling]);

  //处理发起大模型rag
  const handleProcess = async () => {
    console.log("开始走处理按钮");
    try {
      //拿到文本输入框的文本
      const mentionsData = getMentionsData();
      //拿到选的那个知识id
      const selectedKnowledgeItemId = mentionsData.selectedKnowledgeItem?.libId || null;
      console.log(
        "看看条件符合上传不,",
        "填输入框：",
        mentionsData.query.trim(),
        "选的知识id：",
        selectedKnowledgeItemId,
        "本地上传",
        localFileUploadResData,
      );
      //如果文本输入框的文本和选的那个知识id,本地文件都为空，那么就返回
      if (!mentionsData.query.trim() && !selectedKnowledgeItemId && !localFileUploadResData) {
        message.warning("请先输入问题或上传文件或选择知识！");
        return;
      }
      const token = await getToken();
      const tenantId = await getTenantId();
      setIsFilling(true);
      // 移除动态设置contentHeight，保持固定高度避免抖动
      // 重置SSE状态，确保每次处理都是初始状态
      await sseChat.reset();
      // 重置流式输出相关状态
      setProcessedStreamData("");
      setAccumulatedFormattedData("");
      setProcessedRawLength(0);
      setDisplayContent("");
      const extractedData = extractFormData();
      //将当前的表单数据转成json
      const extractedDataJson = JSON.stringify(extractedData, null, 2);
      setNumber(sseChat.chatList.length + 1);
      console.log("开始走流式");
      // 使用SSE流式处理
      sseChat.start({
        url: "/dify/broker/agent/stream",
        headers: {
          "Content-Type": "application/json",
          Token: token || "",
          // Token: "06e7ef55-783b-4c67-aebe-9aa1ffa44375",
        },
        body: {
          insId: "1",
          bizType: "app:agent",
          bizId: agentxinqiaoid, // 使用state中的bearer作为bizId
          agentId: agentxinqiaoid, // 使用state中的bearer作为agentId
          path: "/chat-messages",
          difyJson: {
            conversation_id: "",

            files: localFileUploadResData ? [localFileUploadResData] : null,
            inputs: {
              kbId: selectedKnowledgeItemId,
              json: extractedDataJson, //填充的json缓存里的
              token: token || "",
              // Token: "06e7ef55-783b-4c67-aebe-9aa1ffa44375",
              tenantid: tenantId || "",
              appKey: xinqiaohetongkey, //agent
              textContent: state.content || "",
              // dataSource: selectedfrom,

              // outputTemplate: {
              //   // 模板信息
              //   type: "document",
              //   transfer_method: "local_file",
              //   upload_file_id: localFileUploadResData.upload_file_id,
              // },

              // extractJson: formData, //提取当前页面的json
            },
            response_mode: "streaming",
            user: userInfo.id || "anonymous",
            query: "1",
            noConversationId: "true",
          },
        },
        query: {},

        onFinished: (result: string) => {
          console.log("SSE流式处理完成，结果：", result);

          // // 更严格的错误检测逻辑 - 只检测明确的错误标识
          // const isError =
          //   result &&
          //   (result.includes("❌") ||
          //     result.includes("发生错误") ||
          //     result.includes("请求失败") ||
          //     result.includes("网络错误") ||
          //     result.includes("服务器错误") ||
          //     result.includes("认证失败") ||
          //     result.includes("权限不足") ||
          //     result.startsWith("错误：") ||
          //     result.startsWith("Error:"));

          // if (isError) {
          //   console.error("智能填充处理失败：", result);

          //   // 根据错误内容提供更具体的错误信息
          //   let errorMessage = "智能填充处理失败！";
          //   if (result.includes("网络") || result.includes("连接")) {
          //     errorMessage = "网络连接失败，请检查网络后重试";
          //   } else if (result.includes("超时")) {
          //     errorMessage = "请求超时，请稍后重试";
          //   } else if (result.includes("认证") || result.includes("登录")) {
          //     errorMessage = "身份验证失败，请重新登录";
          //   } else if (result.includes("权限")) {
          //     errorMessage = "权限不足，请联系管理员";
          //   } else if (result.includes("服务器")) {
          //     errorMessage = "服务器内部错误，请稍后重试";
          //   } else {
          //     errorMessage = `智能填充失败：${result}`;
          //   }

          //   message.error(errorMessage);
          //   setIsFilling(false);

          //   // 清理相关状态
          //   clearMentionsData();
          //   setLocalFileUploadResData(null);
          // } else {
          //   // 成功情况
          //   console.log("智能填充成功，开始处理结果...");
          //   message.success("智能填充完成！");

          //   // 重置填充状态，恢复按钮初始状态
          //   setIsFilling(false);
          //   clearMentionsData();
          //   setLocalFileUploadResData(null);

          //   // 这里可以添加处理成功结果的逻辑
          //   // 例如：解析结果并填充到表单中
          //   try {
          //     // 如果结果是JSON格式，可以解析并填充表单
          //     if (result.trim().startsWith("{") && result.trim().endsWith("}")) {
          //       const formData = JSON.parse(result);
          //       console.log("解析到的表单数据：", formData);
          //       // 这里可以添加自动填充表单的逻辑
          //     }
          //   } catch (parseError) {
          //     console.log("结果不是JSON格式，作为文本处理：", result);
          //   }
          // }
        },
      });
    } catch (error) {
      console.error("智能填充初始化失败：", error);

      // 根据错误类型提供更具体的错误信息
      let errorMessage = "智能填充初始化失败！";
      if (error instanceof Error) {
        if (error.message.includes("token")) {
          errorMessage = "获取认证信息失败，请重新登录";
        } else if (error.message.includes("tenant")) {
          errorMessage = "获取租户信息失败，请联系管理员";
        } else if (error.message.includes("form")) {
          errorMessage = "表单数据提取失败，请刷新页面重试";
        } else {
          errorMessage = `初始化失败：${error.message}`;
        }
      }

      message.error(errorMessage);
      setIsFilling(false);

      // 清理相关状态
      clearMentionsData();
      setLocalFileUploadResData(null);
    }
  };
  useEffect(() => {
    if (sseChat.displayedText && isFilling) {
      // 获取新增的文本内容（增量部分）
      const currentText = sseChat.displayedText;

      // 执行流式解析（包括表单字段和表格数据）解析表格表单的数据成固定格式
    }
  }, [sseChat.displayedText, isFilling]);

  // 停止SSE流式处理
  const handleStopFilling = useCallback(async () => {
    sseChat.stop(agentxinqiaoid, xinqiaohetongkey, userInfo?.id || "anonymous");
    setIsFilling(false);
    // 完全重置SSE状态
    await sseChat.reset();
    // 重置流式输出相关状态
    setProcessedStreamData("");
    setAccumulatedFormattedData("");
    setProcessedRawLength(0);
    setDisplayContent("");
    message.info("已停止智能填充处理");
  }, [sseChat, state.bearer, userInfo]);

  // 上一个结果
  const handlePre = () => {
    setNumber(number > 1 ? number - 1 : sseChat.chatList.length);
  };
  // 下一个结果
  const handleNext = () => {
    setNumber(number < sseChat.chatList.length ? number + 1 : 1);
  };

  /** 处理停止 */
  const handleStop = () => {
    sseChat.stop(agentxinqiaoid, xinqiaohetongkey, userInfo?.id || "anonymous", "completion");
  };

  // 限制原始数据显示行数的工具函数
  const limitRawDataLines = (rawData: string, maxLines: number): string => {
    if (!rawData) return rawData;

    const lines = rawData.split("\n");
    if (lines.length <= maxLines) {
      return rawData;
    }

    // 显示最新的几行，而不是最开始的几行
    const limitedLines = lines.slice(-maxLines);
    // const hiddenCount = lines.length - maxLines;
    return limitedLines.join("\n");
  };

  // 处理流式数据的增量解析 - 先表单后表格的固定格式
  const formatStreamingData = (content: string): string => {
    console.log("[formatStreamingData] 流进来的内容", content);
    console.log(
      "[formatStreamingData] 函数入口，接收内容长度:",
      content?.length,
      "前100字符:",
      content?.substring(0, 100),
    );

    if (!content) {
      console.log("[formatStreamingData] 内容为空，返回空字符串");
      return "";
    }

    // 数据去重处理：更保守的重复检测
    let cleanedContent = content;

    // 检测完全相同的JSON结构模式（更严格的匹配）
    const jsonBlockPattern = /\{\s*"allFormData"[\s\S]*?\}\s*\]/g;
    const jsonBlocks = content.match(jsonBlockPattern);

    if (jsonBlocks && jsonBlocks.length > 1) {
      console.log("🔍 检测到多个JSON块:", jsonBlocks.length, "个");

      // 检查是否真的有完全重复的块
      const uniqueBlocks = [...new Set(jsonBlocks)];
      if (uniqueBlocks.length < jsonBlocks.length) {
        console.log("🔍 发现真正重复的JSON块，去重前:", jsonBlocks.length, "去重后:", uniqueBlocks.length);
        // 只有在发现真正重复时才进行替换
        let tempContent = content;
        jsonBlocks.forEach((block) => {
          tempContent = tempContent.replace(block, "");
        });
        // 添加最后一个唯一块
        cleanedContent = tempContent + uniqueBlocks[uniqueBlocks.length - 1];
        console.log("✅ 去重完成，清理后长度:", cleanedContent.length);
      } else {
        console.log("✅ 未发现重复块，保持原内容");
        cleanedContent = content;
      }
    }

    // 更保守的重复字符序列清理（只处理明显的重复）
    const duplicatePattern = /(\{[\s\S]{100,}?)\1{2,}/g;
    if (duplicatePattern.test(cleanedContent)) {
      cleanedContent = cleanedContent.replace(duplicatePattern, "$1");
      console.log("✅ 移除明显重复的字符序列，清理后长度:", cleanedContent.length);
    }

    console.log("[formatStreamingData] 开始处理内容，原始长度:", content.length, "清理后长度:", cleanedContent.length);

    // 使用清理后的内容进行后续处理
    content = cleanedContent;

    let output = "";

    // 阶段1：表单字段处理 - 精确匹配，避免重复
    console.log("[formatStreamingData] 开始检测表单字段");

    // 定义精确的匹配模式，按优先级排序
    const formPatterns = [
      // 模式1：完整的字段对象（包含fieldIndex, label, value）
      /\{\s*"fieldIndex"\s*:\s*(\d+)[^}]*"label"\s*:\s*"([^"]+)"[^}]*"value"\s*:\s*"([^"]*)"/g,
      // 模式2：简单的label-value对
      /"label"\s*:\s*"([^"]+)"\s*,\s*"value"\s*:\s*"([^"]*)"/g,
      // 模式3：中文标签格式（支持单冒号或双冒号）
      /([\u4e00-\u9fa5]{2,6})\s*[:：]{1,2}\s*([^\n\r,}{]{1,30})/g,
    ];

    const processedFields = new Set<string>(); // 跟踪已处理的字段
    let validFieldCount = 0;

    // 只使用第一个成功匹配的模式
    for (let patternIndex = 0; patternIndex < formPatterns.length; patternIndex++) {
      const pattern = formPatterns[patternIndex];
      const matches = [...content.matchAll(pattern)];

      if (matches.length > 0) {
        console.log(`🔍 使用模式${patternIndex + 1}匹配到:`, matches.length, "个字段");

        matches.forEach((match) => {
          let label = "";
          let value = "";
          let fieldIndex: number | undefined;

          // 根据模式解析匹配结果
          if (patternIndex === 0) {
            // 完整字段对象
            fieldIndex = parseInt(match[1]);
            label = match[2];
            value = match[3];
          } else if (patternIndex === 1) {
            // 简单label-value对
            label = match[1];
            value = match[2];
          } else if (patternIndex === 2) {
            // 中文标签格式
            label = match[1];
            value = match[2];
          }

          // 数据清理
          if (label) label = label.trim().replace(/[{}[\]"']/g, "");
          if (value)
            value = value
              .trim()
              .replace(/[{}[\]"']/g, "")
              .replace(/^[:：]+/, ""); // 移除开头的冒号

          // 严格的字段验证
          const invalidLabels = [
            "formIndex",
            "formType",
            "formAction",
            "tagName",
            "type",
            "componentType",
            "framework",
            "fieldIndex",
            "name",
            "id",
            "placeholder",
            "isDisabled",
            "isReadonly",
            "Data",
            "Index",
            "Action",
            "Type",
            "Name",
            "Disabled",
            "Readonly",
            "only",
            "fields",
            "allFormData",
          ];

          if (
            label &&
            value &&
            value.length > 0 &&
            value !== "null" &&
            value !== "undefined" &&
            !invalidLabels.includes(label) &&
            !invalidLabels.some((invalid) => label.toLowerCase() === invalid.toLowerCase())
          ) {
            // 生成唯一标识
            const fieldKey = fieldIndex !== undefined ? `idx_${fieldIndex}` : `${label}_${value.substring(0, 10)}`;

            if (!processedFields.has(fieldKey)) {
              processedFields.add(fieldKey);
              // 智能格式化：只有当label末尾是冒号时才直接使用，否则添加冒号
              const endsWithColon = label.endsWith(":") || label.endsWith("：");
              const formattedLabel = endsWithColon ? label : `${label}:`;
              output += `**${formattedLabel}** ${value}\n\n`;
              validFieldCount++;
              console.log("✅ 添加有效字段:", { label: formattedLabel, value, fieldKey });
            } else {
              console.log("⚠️ 字段重复，跳过:", fieldKey);
            }
          } else {
            console.log("❌ 字段验证失败:", { label, value });
          }
        });

        // 找到有效匹配后停止尝试其他模式
        if (validFieldCount > 0) {
          console.log(`✅ 模式${patternIndex + 1}处理完成，有效字段:`, validFieldCount);
          break;
        }
      }
    }

    // 如果没有找到有效字段，但内容可能包含表单数据
    if (validFieldCount === 0 && (content.includes("label") || content.includes(":"))) {
      console.log("🔍 检测到可能的表单数据，显示处理状态");
      output += "📋 **正在解析表单数据...**\n\n";
    }

    // 阶段2：表格数据处理 - 渐进式展示
    // 只处理 formType 为 'table' 的数据，避免将其他类型数据误处理为表格
    const hasTableData =
      (content.includes('"headers"') || content.includes('"rows"')) &&
      content.includes('"formType"') &&
      content.includes('"table"');
    console.log("🔍 检测表格数据:", hasTableData);

    if (hasTableData) {
      if (!output.includes("## 表格数据")) {
        output += `\n## 表格数据\n\n`;
        console.log("✅ 添加表格标题");
      }

      // 渐进式处理表格数据
      // 匹配formType为table的formIndex，支持两种顺序
      const tableFormIndexMatches = [
        ...content.matchAll(/"formType"\s*:\s*"table"[\s\S]*?"formIndex"\s*:\s*(\d+)/g),
        ...content.matchAll(/"formIndex"\s*:\s*(\d+)[\s\S]*?"formType"\s*:\s*"table"/g),
      ];
      const headersMatches = [...content.matchAll(/"headers"\s*:\s*\[([^\]]+)\]/g)];
      console.log(
        "🔍 找到表格数量 - table formIndex:",
        tableFormIndexMatches.length,
        "headers:",
        headersMatches.length,
      );

      // 获取所有唯一的table类型formIndex值
      const uniqueFormIndexes = [...new Set(tableFormIndexMatches.map((match) => match[1]))].sort(
        (a, b) => Number(a) - Number(b),
      );
      console.log("🔍 唯一表格索引:", uniqueFormIndexes);

      // 只处理唯一的formIndex，避免重复创建表格
      uniqueFormIndexes.forEach((formIndex, index) => {
        // 查找与当前formIndex匹配的headers
        const headersPattern = new RegExp(
          `"formIndex"\\s*:\\s*${formIndex}[\\s\\S]*?"headers"\\s*:\\s*\\[([^\\]]+)\\]`,
          "g",
        );
        const headersMatch = headersPattern.exec(content);

        console.log(`🔍 处理formIndex=${formIndex}的表格头部`);

        // 使用 index + 1 作为表格序号，确保从1开始，并且是连续的
        const tableNumber = index + 1;
        // 添加表格分隔符 - 确保每个表格都有正确的标题
        if (index > 0) {
          output += `\n### 表格 ${tableNumber} \n\n`;
        } else {
          output += `### 表格 ${tableNumber} \n\n`;
        }

        // 处理表格头部和行数据
        let headers = [];
        let hasValidHeaders = false;

        if (headersMatch) {
          try {
            const headersArray = "[" + headersMatch[1] + "]";
            headers = JSON.parse(headersArray);
            console.log(`✅ 解析formIndex=${formIndex}的表格头部:`, headers);

            if (Array.isArray(headers) && headers.length > 0) {
              hasValidHeaders = true;
              output += `| ${headers.join(" | ")} |\n`;
              output += `| ${headers.map(() => "---").join(" | ")} |\n`;
              console.log(`✅ 添加formIndex=${formIndex}的表格头部到输出`);
            }
          } catch (e) {
            console.log(`❌ formIndex=${formIndex}的表格头部解析错误:`, e);
          }
        }

        // 无论是否有headers，都尝试处理行数据
        const tableRowsPattern = new RegExp(
          `"formIndex"\\s*:\\s*${formIndex}[\\s\\S]*?"rows"\\s*:\\s*\\[([\\s\\S]*?)\\](?=\\s*\\}|\\s*,\\s*"tableMetadata)`,
          "g",
        );
        // 重置正则表达式的lastIndex，确保每次都从头开始匹配
        tableRowsPattern.lastIndex = 0;
        const tableRowsMatch = tableRowsPattern.exec(content);

        // 如果没有headers但有行数据，显示处理状态
        if (!hasValidHeaders && tableRowsMatch) {
          output += `**表格 ${formIndex} 正在加载中...**\n\n`;
          console.log(`🔍 formIndex=${formIndex}没有headers但有行数据，显示加载状态`);
        }

        if (tableRowsMatch) {
          const rowsContent = tableRowsMatch[1];
          console.log(`🔍 找到formIndex=${formIndex}的行数据内容`);

          // 解析所有单元格数据
          const cellPattern = /\{[^{}]*?"value"[^{}]*?\}/g;
          const allCellMatches = [...rowsContent.matchAll(cellPattern)];

          if (allCellMatches.length > 0) {
            console.log(`🔍 找到formIndex=${formIndex}的单元格数据:`, allCellMatches.length, "个");

            // 解析并收集所有单元格
            const allCells = [];
            allCellMatches.forEach((cellMatch) => {
              try {
                const cellStr = cellMatch[0];
                const cell = JSON.parse(cellStr);

                if (typeof cell === "object" && cell !== null && "value" in cell) {
                  allCells.push({
                    value: cell.value || "",
                    rowIndex: cell.rowIndex ?? 0,
                    columnIndex: cell.columnIndex ?? 0,
                  });
                }
              } catch (e) {
                console.log(`❌ 单元格解析错误:`, e);
              }
            });

            // 按rowIndex分组
            const rowsMap = new Map();
            allCells.forEach((cell) => {
              if (!rowsMap.has(cell.rowIndex)) {
                rowsMap.set(cell.rowIndex, []);
              }
              rowsMap.get(cell.rowIndex).push(cell);
            });

            // 按rowIndex排序并输出每一行
            const sortedRowIndexes = Array.from(rowsMap.keys()).sort((a, b) => a - b);

            sortedRowIndexes.forEach((rowIndex) => {
              const rowCells = rowsMap.get(rowIndex);
              // 按columnIndex排序
              rowCells.sort((a, b) => a.columnIndex - b.columnIndex);

              const rowValues = rowCells.map((cell) => cell.value);
              const rowOutput = `| ${rowValues.join(" | ")} |`;

              // 为了避免重复，检查当前表格部分是否已包含此行
              const currentTableStart = output.lastIndexOf(`### 表格 ${formIndex}`);
              const currentTableContent = currentTableStart >= 0 ? output.substring(currentTableStart) : output;

              if (!currentTableContent.includes(rowOutput)) {
                output += `${rowOutput}\n`;
                console.log(`✅ 添加formIndex=${formIndex}的第${rowIndex}行数据:`, rowValues);
              } else {
                console.log(`⚠️ 跳过formIndex=${formIndex}的重复行数据:`, rowValues);
              }
            });
          }
        }
      });
    }

    // 阶段3：兜底处理 - 仅在没有匹配到任何字段时使用
    if (!output || output.trim().length === 0) {
      console.log("🔍 进入兜底处理，尝试基础内容识别");

      // 检查是否包含JSON结构标识
      if (content.includes("{") || content.includes("[") || content.includes('"')) {
        output = "🔄 **正在解析数据结构...**\n\n";
        console.log("✅ 显示JSON解析状态");
      } else if (content.includes(":") || content.includes("：")) {
        // 尝试简单的键值对匹配
        const simpleMatches = content.match(/([\u4e00-\u9fa5\w]{2,})\s*[:：]\s*([^\n]{1,30})/g);
        if (simpleMatches && simpleMatches.length > 0) {
          console.log("🔍 兜底模式找到简单键值对:", simpleMatches.length, "个");
          simpleMatches.slice(0, 5).forEach((match) => {
            // 限制最多5个
            const [, key, value] = match.match(/([\u4e00-\u9fa5\w]{2,})\s*[:：]\s*([^\n]{1,30})/) || [];
            if (key && value) {
              output += `**${key.trim()}:** ${value.trim()}\n\n`;
            }
          });
        } else {
          output = "🔄 **正在解析内容...**\n\n";
        }
      } else {
        // 简单的内容清理和格式化
        const cleanContent = content
          .replace(/[{}[\]"]/g, " ") // 移除JSON符号
          .replace(/\s+/g, " ") // 合并多个空格
          .trim()
          .substring(0, 200); // 限制长度

        if (cleanContent.length > 0) {
          output = `📄 **接收到内容:**\n\n${cleanContent}...\n\n`;
          console.log("✅ 显示格式化的原始内容");
        }
      }
    }

    // 最终输出 - 确保总是有内容显示
    if (!output || output.trim().length === 0) {
      console.log("⚠️ 没有生成任何输出，显示接收状态");
      if (content.trim().length > 0) {
        return `⏳ **正在接收数据...** (${content.length} 字符)\n\n`;
      } else {
        return "⏳ **等待数据...**\n\n";
      }
    }

    console.log("✅ formatStreamingData 完成，输出长度:", output.length);
    return output;
  };

  // ==================== 表单填充核心逻辑 ====================

  // 日期格式化函数
  const formatDateForInput = (dateStr: string): string => {
    if (!dateStr) return "";

    // 尝试解析各种日期格式
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) {
      // 如果无法解析，尝试其他格式
      const cleanStr = dateStr.replace(/[年月日]/g, "-").replace(/--/g, "-");
      const newDate = new Date(cleanStr);
      if (!isNaN(newDate.getTime())) {
        return newDate.toISOString().split("T")[0];
      }
      return dateStr; // 返回原始字符串
    }

    return date.toISOString().split("T")[0];
  };

  // 模糊文本匹配函数
  const fuzzyMatch = (text1: string, text2: string, threshold: number = 0.6): boolean => {
    if (!text1 || !text2) return false;

    const str1 = text1.toLowerCase().trim();
    const str2 = text2.toLowerCase().trim();

    if (str1 === str2) return true;
    if (str1.includes(str2) || str2.includes(str1)) return true;

    // 简单的相似度计算
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) return true;

    const editDistance = levenshteinDistance(longer, shorter);
    const similarity = (longer.length - editDistance) / longer.length;

    return similarity >= threshold;
  };

  // 计算编辑距离
  const levenshteinDistance = (str1: string, str2: string): number => {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(matrix[i - 1][j - 1] + 1, matrix[i][j - 1] + 1, matrix[i - 1][j] + 1);
        }
      }
    }

    return matrix[str2.length][str1.length];
  };

  // 填充选择框元素（一键填充优化版）
  const fillSelectElement = (element: HTMLSelectElement, value: string): boolean => {
    if (!element || !value) return false;

    // 首先尝试精确匹配
    for (let i = 0; i < element.options.length; i++) {
      const option = element.options[i];
      if (option.text === value || option.value === value) {
        element.selectedIndex = i;
        element.dispatchEvent(new Event("change", { bubbles: true }));
        return true;
      }
    }

    // 模糊匹配
    for (let i = 0; i < element.options.length; i++) {
      const option = element.options[i];
      if (fuzzyMatch(option.text, value) || fuzzyMatch(option.value, value)) {
        element.selectedIndex = i;
        element.dispatchEvent(new Event("change", { bubbles: true }));
        return true;
      }
    }

    return false;
  };

  // 填充日期元素
  const fillDateElement = (element: HTMLInputElement, value: string): boolean => {
    if (!element || !value) return false;

    if (element.disabled || element.readOnly) return false;

    const formattedDate = formatDateForInput(value);
    // 如果日期格式转换失败，formatDateForInput会返回原始值
    // 这里直接使用返回的值，无论是格式化后的日期还是原始字符串
    element.value = formattedDate;

    // 日历组件需要完整的事件模拟，包括点击
    element.dispatchEvent(new Event("focus", { bubbles: true }));
    element.dispatchEvent(new Event("click", { bubbles: true }));
    element.dispatchEvent(new Event("input", { bubbles: true }));
    element.dispatchEvent(new Event("change", { bubbles: true }));
    element.dispatchEvent(new Event("blur", { bubbles: true }));

    // 触发自定义日期变更事件
    element.dispatchEvent(
      new CustomEvent("datechange", {
        bubbles: true,
        detail: { value: formattedDate },
      }),
    );

    console.log(`✅ 日期元素填充：${formattedDate} (原始值: ${value})`);
    return true;
  };

  // 填充复选框和单选框
  const fillCheckboxRadioElement = (element: HTMLInputElement, value: string): boolean => {
    if (!element || !value) return false;

    const boolValue = ["true", "1", "yes", "是", "checked", "selected"].includes(value.toLowerCase());

    if (element.type === "checkbox" || element.type === "radio") {
      element.checked = boolValue;
      element.dispatchEvent(new Event("change", { bubbles: true }));
      return true;
    }

    return false;
  };

  // 填充Ant Design输入框
  const fillAntDesignInput = (container: HTMLElement, value: string): boolean => {
    try {
      const input = container.querySelector("input, textarea") as HTMLInputElement | HTMLTextAreaElement;
      if (!input) {
        console.warn(`⚠️ Ant Design Input中未找到input/textarea元素`);
        return false;
      }

      // 使用原生setter确保值被正确设置
      const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
        input.tagName === "TEXTAREA" ? window.HTMLTextAreaElement.prototype : window.HTMLInputElement.prototype,
        "value",
      )?.set;

      if (nativeInputValueSetter) {
        nativeInputValueSetter.call(input, value);
      } else {
        input.value = value;
      }

      // 触发事件，包括click事件来打开面板
      const events = ["click", "focus", "input", "change", "blur"];
      events.forEach((eventType) => {
        const event = new Event(eventType, { bubbles: true });
        input.dispatchEvent(event);
      });

      console.log(`✅ Ant Design Input填充成功：${value}`);
      return true;
    } catch (error) {
      console.error(`❌ Ant Design Input填充失败：`, error);
      return false;
    }
  };

  // 填充Ant Design级联选择器
  const fillAntDesignCascader = (container: HTMLElement, value: string): boolean => {
    try {
      const input = container.querySelector("input") as HTMLInputElement;
      if (!input) {
        console.warn(`⚠️ Ant Design Cascader中未找到input元素`);
        return false;
      }

      // 使用原生setter确保值被正确设置
      const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value")?.set;

      if (nativeInputValueSetter) {
        nativeInputValueSetter.call(input, value);
      } else {
        input.value = value;
      }

      // 触发事件，包括click事件来打开面板
      const events = ["click", "focus", "input", "change", "blur"];
      events.forEach((eventType) => {
        const event = new Event(eventType, { bubbles: true });
        input.dispatchEvent(event);
      });

      console.log(`✅ Ant Design Cascader填充成功：${value}`);
      return true;
    } catch (error) {
      console.error(`❌ Ant Design Cascader填充失败：`, error);
      return false;
    }
  };

  // 填充Ant Design日期选择器
  const fillAntDesignDatePicker = (container: HTMLElement, value: string): boolean => {
    try {
      const input = container.querySelector("input") as HTMLInputElement;
      if (!input) {
        console.warn(`⚠️ Ant Design DatePicker中未找到input元素`);
        return false;
      }

      // 检查是否已经填充过相同的值
      if (input.value && input.value.trim() === value.trim()) {
        console.log(`🔄 Ant Design DatePicker已有相同值，跳过填充：${value}`);
        return true;
      }

      console.log(`🗓️ 找到Ant Design DatePicker输入框，准备填充日期：${value}`);

      const formattedDate = formatDateForInput(value);
      // 如果日期格式转换失败，formatDateForInput会返回原始值
      // 这里直接使用返回的值，无论是格式化后的日期还是原始字符串
      if (!formattedDate) {
        console.warn(`⚠️ 日期值为空，跳过填充`);
        return false;
      }

      // 验证日期是否有效，避免NAN-NAN问题
      const testDate = new Date(formattedDate);
      if (isNaN(testDate.getTime())) {
        console.warn(`⚠️ 日期格式无效: ${formattedDate}，尝试直接输入到input框`);
        // 直接设置input值作为后备方案
        try {
          input.value = value;
          input.dispatchEvent(new Event("input", { bubbles: true }));
          input.dispatchEvent(new Event("change", { bubbles: true }));
          console.log(`✅ 直接输入方式填充成功: ${value}`);
          return true;
        } catch (error) {
          console.error(`❌ 直接输入方式也失败:`, error);
          return false;
        }
      }

      try {
        // 方法1: 尝试通过React Fiber实例更新（最佳方案）
        const reactFiberKeys = [
          "_reactInternalFiber",
          "_reactInternalInstance",
          "__reactInternalInstance",
          "_reactInternals",
        ];
        let reactFiber = null;

        // 尝试从input和container中查找React Fiber
        for (const key of reactFiberKeys) {
          reactFiber = (input as any)[key] || (container as any)[key];
          if (reactFiber) break;
        }

        if (reactFiber) {
          console.log(`🎯 找到React Fiber实例，尝试通过React更新`);

          // 尝试通过memoizedProps.onChange
          if (reactFiber.memoizedProps && reactFiber.memoizedProps.onChange) {
            reactFiber.memoizedProps.onChange({
              target: { value: formattedDate },
              currentTarget: { value: formattedDate },
            });
            console.log(`✅ Ant Design DatePicker通过React onChange填充成功：${formattedDate}`);
            return true;
          }

          // 尝试通过pendingProps.onChange
          if (reactFiber.pendingProps && reactFiber.pendingProps.onChange) {
            reactFiber.pendingProps.onChange({
              target: { value: formattedDate },
              currentTarget: { value: formattedDate },
            });
            console.log(`✅ Ant Design DatePicker通过React pendingProps填充成功：${formattedDate}`);
            return true;
          }

          // 尝试查找父级React组件
          let parentFiber = reactFiber.return || reactFiber.parent;
          let attempts = 0;
          while (parentFiber && attempts < 5) {
            if (parentFiber.memoizedProps && parentFiber.memoizedProps.onChange) {
              console.log(`🎯 找到父级React组件，尝试更新`);
              parentFiber.memoizedProps.onChange({
                target: { value: formattedDate },
                currentTarget: { value: formattedDate },
              });
              console.log(`✅ Ant Design DatePicker通过父级React填充成功：${formattedDate}`);
              return true;
            }
            parentFiber = parentFiber.return || parentFiber.parent;
            attempts++;
          }
        }

        // 方法2: 查找父级DOM元素的React实例
        let parentElement = container.parentElement;
        let attempts = 0;
        while (parentElement && attempts < 5) {
          for (const key of reactFiberKeys) {
            const parentReact = (parentElement as any)[key];
            if (parentReact && parentReact.memoizedProps && parentReact.memoizedProps.onChange) {
              console.log(`🎯 找到父级DOM的React实例，尝试更新`);
              parentReact.memoizedProps.onChange({
                target: { value: formattedDate },
                currentTarget: { value: formattedDate },
              });
              console.log(`✅ Ant Design DatePicker通过父级DOM React填充成功：${formattedDate}`);
              return true;
            }
          }
          parentElement = parentElement.parentElement;
          attempts++;
        }

        // 方法3: 备用方案 - 直接设置值并触发事件
        console.log(`🔄 React实例方法失败，使用备用方案`);

        // 使用原生setter确保值被正确设置
        const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value")?.set;
        if (nativeInputValueSetter) {
          nativeInputValueSetter.call(input, formattedDate);
        } else {
          input.value = formattedDate;
        }

        // 触发多种事件确保框架能够响应，包括click事件来打开面板
        const events = ["click", "focus", "input", "change", "blur"];

        events.forEach((eventType) => {
          try {
            const event = new Event(eventType, { bubbles: true, cancelable: true });
            input.dispatchEvent(event);
          } catch (e) {
            console.warn(`触发${eventType}事件失败:`, e);
          }
        });

        // 单独处理InputEvent，因为它可能在某些环境中不可用
        try {
          if (typeof InputEvent !== "undefined") {
            const inputEvent = new InputEvent("input", {
              bubbles: true,
              cancelable: true,
              data: formattedDate,
            });
            input.dispatchEvent(inputEvent);
          }
        } catch (e) {
          console.warn("触发InputEvent失败:", e);
        }

        // 额外触发Ant Design特定事件
        try {
          const customEvent = new CustomEvent("ant.form.change", {
            bubbles: true,
            detail: { value: formattedDate },
          });
          input.dispatchEvent(customEvent);
        } catch (e) {
          console.warn("触发Ant Design自定义事件失败:", e);
        }

        console.log(`✅ Ant Design DatePicker填充成功：${formattedDate}`);
        return true;
      } catch (error) {
        console.error(`❌ Ant Design DatePicker填充失败：`, error);
        return false;
      }
    } catch (error) {
      console.error("Ant Design DatePicker填充失败：", error);
      return false;
    }
  };

  // 填充Ant Design下拉选择器
  const fillAntDesignSelect = (container: HTMLElement, value: string): boolean => {
    try {
      const selector = container.querySelector(".ant-select-selector") as HTMLElement;
      if (!selector) {
        console.warn(`⚠️ Ant Design Select中未找到选择器元素`);
        return false;
      }

      console.log(`📋 找到Ant Design Select选择器，准备选择：${value}`);
      // 点击打开下拉框
      selector.click();

      setTimeout(() => {
        // 查找所有可见的Ant Design Select下拉框
        const dropdowns = document.querySelectorAll(".ant-select-dropdown:not(.ant-select-dropdown-hidden)");
        let targetDropdown = null;

        // 如果只有一个可见下拉框，直接使用
        if (dropdowns.length === 1) {
          targetDropdown = dropdowns[0];
        } else if (dropdowns.length > 1) {
          // 如果有多个下拉框，尝试找到最近显示的那个（通常是z-index最高的）
          let maxZIndex = -1;
          for (const dropdown of dropdowns) {
            const style = window.getComputedStyle(dropdown as HTMLElement);
            const zIndex = parseInt(style.zIndex) || 0;
            if (zIndex > maxZIndex) {
              maxZIndex = zIndex;
              targetDropdown = dropdown;
            }
          }
        }

        // 直接填充输入框的函数
        const fillInputDirectly = () => {
          const input = container.querySelector("input") as HTMLInputElement;
          if (input) {
            // 使用原生setter确保值被正确设置，覆盖已有值
            const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
              window.HTMLInputElement.prototype,
              "value",
            )?.set;

            if (nativeInputValueSetter) {
              nativeInputValueSetter.call(input, value);
            } else {
              input.value = value;
            }

            input.dispatchEvent(new Event("input", { bubbles: true }));
            input.dispatchEvent(new Event("change", { bubbles: true }));
            input.dispatchEvent(new Event("blur", { bubbles: true }));
            console.log(`✅ Ant Design Select直接填充输入框：${value}`);
            return true;
          }
          return false;
        };

        if (!targetDropdown) {
          console.warn("未找到Ant Design Select下拉框，直接填充输入框");
          return fillInputDirectly();
        }

        // 在对应的下拉框内查找选项
        const options = targetDropdown.querySelectorAll(".ant-select-item-option");
        console.log(`🔍 在当前下拉框中找到${options.length}个选项`);

        // 如果没有选项，直接填充输入框
        if (options.length === 0) {
          console.warn("未找到选项，直接填充输入框");
          // 关闭下拉框
          const backdrop = document.querySelector(".ant-select-dropdown") || document.body;
          if (backdrop) {
            (backdrop as HTMLElement).click();
          }
          return fillInputDirectly();
        }

        let optionFound = false;
        for (const option of options) {
          const optionText = option.textContent?.trim() || "";
          const searchValue = value.trim();
          if (optionText === searchValue || optionText.toLowerCase().includes(searchValue.toLowerCase())) {
            (option as HTMLElement).click();
            console.log(`✅ Ant Design Select选择：${optionText}`);
            optionFound = true;
            return true;
          }
        }

        // 如果没有找到匹配的选项，直接在输入框中填充值
        if (!optionFound) {
          console.warn(`⚠️ Ant Design Select未找到匹配的选项：${value}，直接填充输入框`);

          // 关闭下拉框
          const backdrop = document.querySelector(".ant-select-dropdown") || document.body;
          if (backdrop) {
            (backdrop as HTMLElement).click();
          }

          return fillInputDirectly();
        }
      }, 100);

      return true;
    } catch (error) {
      console.error("Ant Design Select填充失败：", error);
      return false;
    }
  };

  // 填充Material-UI TextField组件
  const fillMaterialUITextField = (container: HTMLElement, value: string): boolean => {
    try {
      const input = container.querySelector("input, textarea") as HTMLInputElement | HTMLTextAreaElement;
      if (!input) {
        console.warn(`⚠️ Material-UI TextField中未找到input/textarea元素`);
        return false;
      }

      let finalValue = value;

      // 如果是日期类型的输入框，尝试格式化日期
      if (input.type === "date" || container.classList.contains("MuiDatePicker")) {
        const formattedDate = formatDateForInput(value);
        // 如果日期格式转换失败，formatDateForInput会返回原始值
        // 这里直接使用返回的值，无论是格式化后的日期还是原始字符串
        if (!formattedDate) {
          console.warn(`⚠️ 日期值为空，跳过填充`);
          return false;
        }
        finalValue = formattedDate;
      }

      // 使用原生setter确保值被正确设置
      const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
        input.tagName === "TEXTAREA" ? window.HTMLTextAreaElement.prototype : window.HTMLInputElement.prototype,
        "value",
      )?.set;

      if (nativeInputValueSetter) {
        nativeInputValueSetter.call(input, finalValue);
      } else {
        input.value = finalValue;
      }

      // 触发事件，包括click事件来打开面板
      const events = ["click", "focus", "input", "change", "blur"];
      events.forEach((eventType) => {
        const event = new Event(eventType, { bubbles: true });
        input.dispatchEvent(event);
      });

      console.log(`✅ Material-UI TextField填充成功：${finalValue}`);
      return true;
    } catch (error) {
      console.error(`❌ Material-UI TextField填充失败：`, error);
      return false;
    }
  };

  // 填充Material-UI Input组件
  const fillMaterialUIInput = (container: HTMLElement, value: string): boolean => {
    try {
      const input = container.querySelector("input") as HTMLInputElement;
      if (!input) {
        console.warn(`⚠️ Material-UI Input中未找到input元素`);
        return false;
      }

      // 使用原生setter确保值被正确设置
      const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value")?.set;

      if (nativeInputValueSetter) {
        nativeInputValueSetter.call(input, value);
      } else {
        input.value = value;
      }

      // 触发事件，包括click事件来打开面板
      const events = ["click", "focus", "input", "change", "blur"];
      events.forEach((eventType) => {
        const event = new Event(eventType, { bubbles: true });
        input.dispatchEvent(event);
      });

      console.log(`✅ Material-UI Input填充成功：${value}`);
      return true;
    } catch (error) {
      console.error(`❌ Material-UI Input填充失败：`, error);
      return false;
    }
  };

  // 填充Material-UI Select组件
  const fillMaterialUISelect = (container: HTMLElement, value: string): boolean => {
    try {
      const selectButton = container.querySelector('[role="button"]') as HTMLElement;
      if (!selectButton) {
        console.warn(`⚠️ Material-UI Select中未找到按钮元素`);
        return false;
      }

      console.log(`📋 找到Material-UI Select按钮，准备选择：${value}`);
      // 点击打开下拉框
      selectButton.click();

      setTimeout(() => {
        // 查找所有可见的Material-UI Select下拉框
        const dropdowns = document.querySelectorAll(".MuiPaper-root .MuiMenu-list, .MuiPopover-paper .MuiMenu-list");
        let targetDropdown = null;

        // 如果只有一个可见下拉框，直接使用
        if (dropdowns.length === 1) {
          targetDropdown = dropdowns[0];
        } else if (dropdowns.length > 1) {
          // 如果有多个下拉框，尝试找到最近显示的那个（通常是z-index最高的）
          let maxZIndex = -1;
          for (const dropdown of dropdowns) {
            const style = window.getComputedStyle(dropdown as HTMLElement);
            const zIndex = parseInt(style.zIndex) || 0;
            if (zIndex > maxZIndex) {
              maxZIndex = zIndex;
              targetDropdown = dropdown;
            }
          }
        }

        // 直接填充输入框的函数
        const fillInputDirectly = () => {
          const input = container.querySelector("input") as HTMLInputElement;
          if (input) {
            // 使用原生setter确保值被正确设置，覆盖已有值
            const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
              window.HTMLInputElement.prototype,
              "value",
            )?.set;

            if (nativeInputValueSetter) {
              nativeInputValueSetter.call(input, value);
            } else {
              input.value = value;
            }

            input.dispatchEvent(new Event("input", { bubbles: true }));
            input.dispatchEvent(new Event("change", { bubbles: true }));
            input.dispatchEvent(new Event("blur", { bubbles: true }));
            console.log(`✅ Material-UI Select直接填充输入框：${value}`);
            return true;
          }
          return false;
        };

        if (!targetDropdown) {
          console.warn("未找到Material-UI Select下拉框，直接填充输入框");
          return fillInputDirectly();
        }

        // 在对应的下拉框内查找选项
        const options = targetDropdown.querySelectorAll('.MuiMenuItem-root, [role="option"]');
        console.log(`🔍 在当前下拉框中找到${options.length}个选项`);

        // 如果没有选项，直接填充输入框
        if (options.length === 0) {
          console.warn("未找到选项，直接填充输入框");
          // 关闭下拉框
          const backdrop = document.querySelector(".MuiBackdrop-root") || document.body;
          if (backdrop) {
            (backdrop as HTMLElement).click();
          }
          return fillInputDirectly();
        }

        let optionFound = false;
        for (const option of options) {
          const optionText = option.textContent?.trim() || "";
          const searchValue = value.trim();
          if (optionText === searchValue || optionText.toLowerCase().includes(searchValue.toLowerCase())) {
            (option as HTMLElement).click();
            console.log(`✅ Material-UI Select选择：${optionText}`);
            optionFound = true;
            return true;
          }
        }

        // 如果没有找到匹配的选项，直接在输入框中填充值
        if (!optionFound) {
          console.warn(`⚠️ Material-UI Select未找到匹配的选项：${value}，直接填充输入框`);

          // 关闭下拉框
          const backdrop = document.querySelector(".MuiBackdrop-root") || document.body;
          if (backdrop) {
            (backdrop as HTMLElement).click();
          }

          return fillInputDirectly();
        }
      }, 100);

      return true;
    } catch (error) {
      console.error("Material-UI Select填充失败：", error);
      return false;
    }
  };

  // 填充Vuetify Input组件
  const fillVuetifyInput = (container: HTMLElement, value: string): boolean => {
    try {
      const input = container.querySelector("input, textarea") as HTMLInputElement | HTMLTextAreaElement;
      if (!input) {
        console.warn(`⚠️ Vuetify Input中未找到input/textarea元素`);
        return false;
      }

      // 使用原生setter确保值被正确设置
      const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
        input.tagName === "TEXTAREA" ? window.HTMLTextAreaElement.prototype : window.HTMLInputElement.prototype,
        "value",
      )?.set;

      if (nativeInputValueSetter) {
        nativeInputValueSetter.call(input, value);
      } else {
        input.value = value;
      }

      // 触发事件，包括click事件来打开面板
      const events = ["click", "focus", "input", "change", "blur"];
      events.forEach((eventType) => {
        const event = new Event(eventType, { bubbles: true });
        input.dispatchEvent(event);
      });

      console.log(`✅ Vuetify Input填充成功：${value}`);
      return true;
    } catch (error) {
      console.error(`❌ Vuetify Input填充失败：`, error);
      return false;
    }
  };

  // 填充Vuetify DatePicker组件
  const fillVuetifyDatePicker = (container: HTMLElement, value: string): boolean => {
    try {
      const input = container.querySelector("input") as HTMLInputElement;
      if (!input) {
        console.warn(`⚠️ Vuetify DatePicker中未找到input元素`);
        return false;
      }

      const formattedDate = formatDateForInput(value);
      // 如果日期格式转换失败，formatDateForInput会返回原始值
      // 这里直接使用返回的值，无论是格式化后的日期还是原始字符串
      if (!formattedDate) {
        console.warn(`⚠️ 日期值为空，跳过填充`);
        return false;
      }

      // 使用原生setter确保值被正确设置
      const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value")?.set;

      if (nativeInputValueSetter) {
        nativeInputValueSetter.call(input, formattedDate);
      } else {
        input.value = formattedDate;
      }

      // 触发事件，包括click事件来打开面板
      const events = ["click", "focus", "input", "change", "blur"];
      events.forEach((eventType) => {
        const event = new Event(eventType, { bubbles: true });
        input.dispatchEvent(event);
      });

      console.log(`✅ Vuetify DatePicker填充成功：${formattedDate}`);
      return true;
    } catch (error) {
      console.error(`❌ Vuetify DatePicker填充失败：`, error);
      return false;
    }
  };

  // 填充Vuetify Textarea组件
  const fillVuetifyTextarea = (container: HTMLElement, value: string): boolean => {
    try {
      const textarea = container.querySelector("textarea") as HTMLTextAreaElement;
      if (!textarea) {
        console.warn(`⚠️ Vuetify Textarea中未找到textarea元素`);
        return false;
      }

      // 使用原生setter确保值被正确设置
      const nativeTextareaSetter = Object.getOwnPropertyDescriptor(window.HTMLTextAreaElement.prototype, "value")?.set;

      if (nativeTextareaSetter) {
        nativeTextareaSetter.call(textarea, value);
      } else {
        textarea.value = value;
      }

      // 触发事件，包括click事件来打开面板
      const events = ["click", "focus", "input", "change", "blur"];
      events.forEach((eventType) => {
        const event = new Event(eventType, { bubbles: true });
        textarea.dispatchEvent(event);
      });

      console.log(`✅ Vuetify Textarea填充成功：${value}`);
      return true;
    } catch (error) {
      console.error(`❌ Vuetify Textarea填充失败：`, error);
      return false;
    }
  };

  // 填充Vuetify Select组件
  const fillVuetifySelect = (container: HTMLElement, value: string): boolean => {
    try {
      const input = container.querySelector("input") as HTMLInputElement;
      if (!input) {
        console.warn(`⚠️ Vuetify Select中未找到input元素`);
        return false;
      }

      console.log(`📋 找到Vuetify Select输入框，准备选择：${value}`);
      // 点击打开下拉框
      container.click();

      setTimeout(() => {
        // 查找所有可见的Vuetify Select下拉框
        const dropdowns = document.querySelectorAll(
          ".v-menu__content:not(.v-menu__content--hidden), .v-overlay__content:not(.v-overlay__content--hidden)",
        );
        let targetDropdown = null;

        // 如果只有一个可见下拉框，直接使用
        if (dropdowns.length === 1) {
          targetDropdown = dropdowns[0];
        } else if (dropdowns.length > 1) {
          // 如果有多个下拉框，尝试找到最近显示的那个（通常是z-index最高的）
          let maxZIndex = -1;
          for (const dropdown of dropdowns) {
            const style = window.getComputedStyle(dropdown as HTMLElement);
            const zIndex = parseInt(style.zIndex) || 0;
            if (zIndex > maxZIndex) {
              maxZIndex = zIndex;
              targetDropdown = dropdown;
            }
          }
        }

        // 直接填充输入框的函数
        const fillInputDirectly = () => {
          if (input) {
            // 使用原生setter确保值被正确设置，覆盖已有值
            const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
              window.HTMLInputElement.prototype,
              "value",
            )?.set;

            if (nativeInputValueSetter) {
              nativeInputValueSetter.call(input, value);
            } else {
              input.value = value;
            }

            input.dispatchEvent(new Event("input", { bubbles: true }));
            input.dispatchEvent(new Event("change", { bubbles: true }));
            input.dispatchEvent(new Event("blur", { bubbles: true }));
            console.log(`✅ Vuetify Select直接填充输入框：${value}`);
            return true;
          }
          return false;
        };

        if (!targetDropdown) {
          console.warn("未找到Vuetify Select下拉框，直接填充输入框");
          return fillInputDirectly();
        }

        // 在对应的下拉框内查找选项
        const options = targetDropdown.querySelectorAll(".v-list-item");
        console.log(`🔍 在当前下拉框中找到${options.length}个选项`);

        // 如果没有选项，直接填充输入框
        if (options.length === 0) {
          console.warn("未找到选项，直接填充输入框");
          // 关闭下拉框
          const backdrop = document.querySelector(".v-overlay__scrim") || document.body;
          if (backdrop) {
            (backdrop as HTMLElement).click();
          }
          return fillInputDirectly();
        }

        let optionFound = false;
        for (const option of options) {
          const optionText = option.textContent?.trim() || "";
          const searchValue = value.trim();
          if (optionText === searchValue || optionText.toLowerCase().includes(searchValue.toLowerCase())) {
            (option as HTMLElement).click();
            console.log(`✅ Vuetify Select选择：${optionText}`);
            optionFound = true;
            return true;
          }
        }

        // 如果没有找到匹配的选项，直接在输入框中填充值
        if (!optionFound) {
          console.warn(`⚠️ Vuetify Select未找到匹配的选项：${value}，直接填充输入框`);

          // 关闭下拉框
          const backdrop = document.querySelector(".v-overlay__scrim") || document.body;
          if (backdrop) {
            (backdrop as HTMLElement).click();
          }

          return fillInputDirectly();
        }
      }, 100);

      return true;
    } catch (error) {
      console.error("Vuetify Select填充失败：", error);
      return false;
    }
  };

  // Element UI 组件统一填充函数 - 参考 content/formDetector 实现
  const fillElementUIComponent = (
    container: HTMLElement,
    value: string,
    label?: string,
    tableIndex?: number,
    rowIndex?: number,
    columnIndex?: number,
    isNewRow?: boolean,
  ): boolean => {
    if (!container || !value) return false;

    try {
      // Element UI Select 处理
      if (container.classList.contains("el-select")) {
        const input = container.querySelector(".el-input__inner") as HTMLInputElement;
        if (input && input !== null) {
          // 检查是否已经填充过相同的值
          if (input.value && input.value.trim() === value.trim()) {
            console.log(`🔄 Element UI Select已有相同值，跳过填充：${value}`);
            return true;
          }

          console.log(`📋 找到Element UI Select输入框，直接填充：${value}`);
          console.log(`📍 Select容器信息：`, {
            className: container.className,
            id: container.id,
            name: input.name,
            placeholder: input.placeholder,
            currentValue: input.value,
          });

          // 直接在输入框中填充值（参考 content/formDetector 的简化实现）
          input.value = value;

          // 根据isNewRow参数决定是否触发click事件
          if (isNewRow === true) {
            // 新行：只触发必要的事件，不包括click
            const events = ["input", "change", "blur"];
            events.forEach((eventType) => {
              const event = new Event(eventType, { bubbles: true });
              input.dispatchEvent(event);
            });
          } else {
            // 原有行和默认情况：只触发基本事件，不触发click避免下拉框展开
            const events = ["input", "change", "blur"];
            events.forEach((eventType) => {
              const event = new Event(eventType, { bubbles: true });
              input.dispatchEvent(event);
            });
          }

          console.log(`✅ Element UI Select填充成功：${value}`);
          return true;
        } else {
          console.warn(`⚠️ Element UI Select中未找到.el-input__inner元素`);
        }
      }

      // Element UI Input - 普通输入框（排除日期选择器）
      if (
        container.classList.contains("el-input") &&
        !container.classList.contains("el-date-editor") &&
        !container.classList.contains("el-date-picker")
      ) {
        const input = container.querySelector(".el-input__inner") as HTMLInputElement;
        if (input && input !== null) {
          console.log(`📋 找到Element UI Input输入框，直接填充：${value}`);
          input.value = value;

          // 根据isNewRow参数决定是否触发click事件
          if (isNewRow === true) {
            // 新行：只触发必要的事件，不包括click
            const events = ["input", "change", "blur"];
            events.forEach((eventType) => {
              const event = new Event(eventType, { bubbles: true });
              input.dispatchEvent(event);
            });
          } else {
            // 原有行和默认情况：只触发基本事件，不触发click避免下拉框展开
            const events = ["input", "change", "blur"];
            events.forEach((eventType) => {
              const event = new Event(eventType, { bubbles: true });
              input.dispatchEvent(event);
            });
          }

          console.log(`✅ Element UI Input填充完成：${value}`);
          return true;
        } else {
          console.warn(`⚠️ Element UI Input中未找到.el-input__inner元素`);
        }
      }

      // Element UI Textarea - 文本域
      if (container.classList.contains("el-textarea")) {
        const textarea = container.querySelector(".el-textarea__inner") as HTMLTextAreaElement;
        if (textarea && textarea !== null) {
          console.log(`📋 找到Element UI Textarea，直接填充：${value}`);
          textarea.value = value;
          const events = ["input", "change", "blur"];
          events.forEach((eventType) => {
            const event = new Event(eventType, { bubbles: true });
            textarea.dispatchEvent(event);
          });

          console.log(`✅ Element UI Textarea填充完成：${value}`);
          return true;
        } else {
          console.warn(`⚠️ Element UI Textarea中未找到.el-textarea__inner元素`);
        }
      }

      // Element UI DatePicker (支持 el-date-picker 和 el-date-editor)
      if (container.classList.contains("el-date-picker") || container.classList.contains("el-date-editor")) {
        return fillElementUIDatePicker(container, value, label, tableIndex, rowIndex, columnIndex, isNewRow);
      }

      return false;
    } catch (error) {
      console.error("Element UI组件填充失败：", error);
      return false;
    }
  };

  // 填充Element UI日期选择器
  const fillElementUIDatePicker = (
    container: HTMLElement,
    value: string,
    label?: string,
    tableIndex?: number,
    rowIndex?: number,
    columnIndex?: number,
    isNewRow?: boolean,
  ): boolean => {
    try {
      const input = container.querySelector("input") as HTMLInputElement;
      if (!input) {
        console.warn(`⚠️ Element UI DatePicker中未找到input元素`);
        return false;
      }

      // 设置表格位置信息到input的dataset中（用于面板关联）
      if (tableIndex !== undefined) {
        input.dataset.tableIndex = tableIndex.toString();
      }
      if (rowIndex !== undefined) {
        input.dataset.rowIndex = rowIndex.toString();
      }
      if (columnIndex !== undefined) {
        input.dataset.columnIndex = columnIndex.toString();
      }

      console.log(`📍 设置面板关联信息：`, {
        tableIndex,
        rowIndex,
        columnIndex,
        relatedInput: input,
      });

      // 检查是否已经填充过相同的值
      if (input.value && input.value.trim() === value.trim()) {
        console.log(`🔄 Element UI DatePicker已有相同值，跳过填充：${value}`);
        return true;
      }

      console.log(`🗓️ 找到Element UI DatePicker输入框，准备填充日期：${value}`);
      console.log(`📍 DatePicker容器信息：`, {
        className: container.className,
        id: container.id,
        name: input.name,
        placeholder: input.placeholder,
        currentValue: input.value,
        isNewRow: isNewRow,
      });

      const formattedDate = formatDateForInput(value);
      // 如果日期格式转换失败，formatDateForInput会返回原始值
      // 这里直接使用返回的值，无论是格式化后的日期还是原始字符串
      if (!formattedDate) {
        console.warn(`⚠️ 日期值为空，跳过填充`);
        return false;
      }

      // 对于新创建的行，只填值不模拟点击
      if (isNewRow) {
        console.log(`🆕 新创建行的日历组件，只填值不模拟点击`);
        try {
          input.value = formattedDate;
          input.dispatchEvent(new Event("input", { bubbles: true }));
          input.dispatchEvent(new Event("change", { bubbles: true }));
          input.dispatchEvent(new Event("blur", { bubbles: true }));
          console.log(`✅ 新行日历组件直接填值成功：${formattedDate}`);
          return true;
        } catch (error) {
          console.error(`❌ 新行日历组件直接填值失败：`, error);
          return false;
        }
      }

      // 对于原有行，继续使用模拟点击的方式
      console.log(`🔄 原有行的日历组件，使用模拟点击方式`);

      try {
        // 方法1: 尝试通过Vue实例更新（最佳方案）
        // const vueInstance = (input as any).__vue__ || (container as any).__vue__;
        // if (vueInstance) {
        //   console.log(`🎯 找到Vue实例，尝试通过Vue更新`);

        //   // 尝试多种Vue实例更新方式
        //   if (vueInstance.$emit) {
        //     vueInstance.$emit('input', formattedDate);
        //     vueInstance.$emit('change', formattedDate);
        //     console.log(`✅ Element UI DatePicker通过Vue $emit填充成功：${formattedDate}`);
        //     return true;
        //   }

        //   // 尝试直接设置Vue组件的value
        //   if (vueInstance.value !== undefined) {
        //     vueInstance.value = formattedDate;
        //     if (vueInstance.$forceUpdate) {
        //       vueInstance.$forceUpdate();
        //     }
        //     console.log(`✅ Element UI DatePicker通过Vue value填充成功：${formattedDate}`);
        //     return true;
        //   }
        // }

        // // 方法2: 查找父级Vue组件实例
        // let parentElement = container.parentElement;
        // let attempts = 0;
        // while (parentElement && attempts < 5) {
        //   const parentVue = (parentElement as any).__vue__;
        //   if (parentVue && parentVue.$emit) {
        //     console.log(`🎯 找到父级Vue实例，尝试更新`);
        //     parentVue.$emit('input', formattedDate);
        //     parentVue.$emit('change', formattedDate);
        //     console.log(`✅ Element UI DatePicker通过父级Vue填充成功：${formattedDate}`);
        //     return true;
        //   }
        //   parentElement = parentElement.parentElement;
        //   attempts++;
        // }
        // const vueInstance = findVueInstance(container); // 使用辅助函数

        // if (vueInstance) {
        //   // 如果找到了，就执行之前的最佳方案
        //   const dateObject = new Date(value);
        //   if (isNaN(dateObject.getTime())) {
        //     return false;
        //   }
        //   vueInstance.$emit("input", dateObject);
        //   vueInstance.$emit("change", dateObject);
        //   console.log("✅ 通过父元素的Vue实例填充成功!");
        //   return true;
        // }
        // 辅助函数：判断面板是否与当前输入框相关（优化表格场景）
        const isElementNearInput = (panel: HTMLElement, inputElement: HTMLElement): boolean => {
          // 优先检查表格位置匹配（最精确）
          const inputTableIndex = inputElement.dataset.tableIndex;
          const inputRowIndex = inputElement.dataset.rowIndex;
          const inputColumnIndex = inputElement.dataset.columnIndex;

          const panelTableIndex = panel.dataset.tableIndex;
          const panelRowIndex = panel.dataset.rowIndex;
          const panelColumnIndex = panel.dataset.columnIndex;

          // 如果都有表格位置信息，进行精确匹配
          if (
            inputTableIndex &&
            inputRowIndex &&
            inputColumnIndex &&
            panelTableIndex &&
            panelRowIndex &&
            panelColumnIndex
          ) {
            return (
              inputTableIndex === panelTableIndex &&
              inputRowIndex === panelRowIndex &&
              inputColumnIndex === panelColumnIndex
            );
          }

          // 如果在表格环境中但面板没有完整位置信息，进行部分匹配
          if (inputTableIndex && inputRowIndex) {
            // 如果面板有部分位置信息，检查是否匹配
            if (panelTableIndex && panelRowIndex) {
              // 表格和行都匹配才认为相关
              return inputTableIndex === panelTableIndex && inputRowIndex === panelRowIndex;
            }

            // 如果面板完全没有位置信息，检查DOM结构（同一个单元格内）
            const inputCell = inputElement.closest("td, th");
            const panelCell = panel.closest("td, th");
            if (inputCell && panelCell && inputCell === panelCell) {
              return true;
            }
          }

          // 非表格环境或作为后备方案，使用距离判断
          const panelRect = panel.getBoundingClientRect();
          const inputRect = inputElement.getBoundingClientRect();

          // 对于表格场景，使用更小的阈值避免跨行干扰
          const threshold = inputTableIndex ? 150 : 300;

          const distance = Math.sqrt(
            Math.pow(panelRect.left + panelRect.width / 2 - (inputRect.left + inputRect.width / 2), 2) +
              Math.pow(panelRect.top + panelRect.height / 2 - (inputRect.top + inputRect.height / 2), 2),
          );

          return distance <= threshold;
        };

        // 方法3: 备用方案 - 直接设置值并触发事件
        console.log(`🔄 Vue实例方法失败，使用备用方案`);

        const PANEL_SELECTOR =
          ".el-picker-panel.el-date-picker, .el-picker-panel[class*='date-picker'], .el-date-picker__panel, .el-picker-panel";
        const PREV_MONTH_SELECTOR = ".el-picker-panel__icon-btn.el-date-picker__prev-btn.el-icon-arrow-left";
        const NEXT_MONTH_SELECTOR = ".el-picker-panel__icon-btn.el-date-picker__next-btn.el-icon-arrow-right";
        const HEADER_LABEL_SELECTOR = ".el-date-picker__header-label";

        // 为当前输入框生成唯一标识，考虑表格环境
        let inputId = input.id || input.name;
        if (!inputId) {
          // 检查是否在表格环境中
          const tableCell = input.closest("td, th");
          const tableRow = input.closest("tr");
          const table = input.closest("table, .el-table, .ant-table, .v-data-table, .MuiTable-root");
          if (tableCell && tableRow && table) {
            // 在表格环境中，生成基于位置的唯一标识
            const rowIndex = Array.from(table.querySelectorAll("tr")).indexOf(tableRow);
            const cellIndex = Array.from(tableRow.querySelectorAll("td, th")).indexOf(tableCell);
            const tableIndex = Array.from(
              document.querySelectorAll("table, .el-table, .ant-table, .v-data-table, .MuiTable-root"),
            ).indexOf(table);

            inputId = inputId || `table_${tableIndex}_row_${rowIndex}_col_${cellIndex}_datepicker_${Date.now()}`;

            // 设置表格位置相关的数据属性
            input.dataset.tableIndex = tableIndex.toString();
            input.dataset.rowIndex = rowIndex.toString();
            input.dataset.columnIndex = cellIndex.toString();

            console.log(`🏷️ 表格日历组件标识: table=${tableIndex}, row=${rowIndex}, col=${cellIndex}`);
          } else {
            // 非表格环境，使用原有逻辑
            inputId = inputId || `datepicker_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          }
        }

        if (!input.dataset.pickerId) {
          input.dataset.pickerId = inputId;
        }

        // 新策略：不再清理面板，让所有日历面板保持打开状态
        // 面板的统一关闭将在handleFillForm函数的最后执行
        console.log(`🔄 步骤1: 跳过面板清理，保持所有日历面板打开状态`);
        console.log(`📝 所有面板将在表单填充完成后统一关闭`);

        // 分阶段触发事件，避免过度刺激导致问题
        const primaryEvents = ["focus", "click"];
        // const secondaryEvents = ["input", "change"];

        // 先触发主要事件
        primaryEvents.forEach((eventType) => {
          try {
            const event = new Event(eventType, { bubbles: true, cancelable: true });
            input.dispatchEvent(event);
          } catch (e) {
            console.warn(`触发${eventType}事件失败:`, e);
          }
        });

        // 延迟触发次要事件
        // setTimeout(() => {
        //   secondaryEvents.forEach((eventType) => {
        //     try {
        //       const event = new Event(eventType, { bubbles: true, cancelable: true });
        //       input.dispatchEvent(event);
        //     } catch (e) {
        //       console.warn(`触发${eventType}事件失败:`, e);
        //     }
        //   });
        // }, 100);

        setTimeout(() => {
          console.log(`🔍 步骤2: 查找与当前输入框关联的日历面板 (延迟800ms确保面板重新创建)`);

          // 使用函数开始时已经生成的 inputId，确保一致性
          const currentInputId = input.dataset.pickerId || inputId;
          console.log(`📍 当前输入框ID: ${currentInputId}`);

          // 改进的面板隔离机制：更智能地保护其他组件的面板
          const allExistingPanels = document.querySelectorAll(PANEL_SELECTOR);
          console.log(`🔍 找到 ${allExistingPanels.length} 个现有面板，执行改进的面板隔离`);

          let availablePanelsCount = 0;

          allExistingPanels.forEach((panel, index) => {
            const panelElement = panel as HTMLElement;
            console.log(`  面板 ${index}: ${panelElement.className}, 关联输入: ${panelElement.dataset.relatedInput}`);

            // 跳过已经被标记为待清理的面板
            if (panelElement.dataset.pendingCleanup === "true") {
              console.log(`    ⏭️ 面板 ${index} 已标记待清理，跳过`);
              return;
            }

            // 检查面板可见性
            const panelRect = panelElement.getBoundingClientRect();
            const isVisible =
              panelRect.width > 0 &&
              panelRect.height > 0 &&
              window.getComputedStyle(panelElement).visibility !== "hidden" &&
              window.getComputedStyle(panelElement).display !== "none" &&
              panelElement.style.zIndex !== "-1";

            if (isVisible) {
              availablePanelsCount++;
            }

            // 检查面板是否属于其他输入框且需要保护
            const belongsToOtherInput =
              panelElement.dataset.relatedInput && panelElement.dataset.relatedInput !== currentInputId;

            if (belongsToOtherInput) {
              // 在表格环境中，检查是否在不同的位置
              const panelTableIndex = panelElement.dataset.tableIndex;
              const panelRowIndex = panelElement.dataset.rowIndex;
              const panelColumnIndex = panelElement.dataset.columnIndex;
              const currentTableIndex = input.dataset.tableIndex;
              const currentRowIndex = input.dataset.rowIndex;
              const currentColumnIndex = input.dataset.columnIndex;

              // 如果在不同的表格位置，完全保护
              if (panelTableIndex && panelRowIndex && currentTableIndex && currentRowIndex) {
                if (
                  panelTableIndex !== currentTableIndex ||
                  panelRowIndex !== currentRowIndex ||
                  panelColumnIndex !== currentColumnIndex
                ) {
                  console.log(`    ✅ 面板 ${index} 在不同表格位置，完全保护`);
                  // 确保面板保持可见和可交互
                  panelElement.style.opacity = "1";
                  panelElement.style.pointerEvents = "auto";
                  panelElement.style.zIndex = "";
                  delete panelElement.dataset.temporarilyHidden;
                  return;
                }
              }

              // 检查面板是否正在被用户交互或最近被操作
              const isInteracting =
                panelElement.matches(":hover") ||
                panelElement.querySelector(":focus") ||
                panelElement.querySelector(".is-selected") ||
                panelElement.querySelector(".el-date-table__cell.current");

              if (isInteracting) {
                console.log(`    ⚠️ 面板 ${index} 正在交互中，完全保护`);
                return;
              }

              // 检查面板的最后活动时间（如果有的话）
              const lastActivity = panelElement.dataset.lastActivity;
              if (lastActivity) {
                const timeSinceActivity = Date.now() - parseInt(lastActivity);
                if (timeSinceActivity < 5000) {
                  // 5秒内有活动
                  console.log(`    ⏰ 面板 ${index} 最近有活动，暂时保护`);
                  return;
                }
              }

              // 确保至少保留一个可用面板，避免全部隐藏
              if (availablePanelsCount <= 1 && isVisible) {
                console.log(`    🛡️ 面板 ${index} 是最后一个可用面板，强制保护`);
                panelElement.style.opacity = "1";
                panelElement.style.pointerEvents = "auto";
                panelElement.style.zIndex = "";
                delete panelElement.dataset.temporarilyHidden;
                return;
              }

              // 新策略：不隐藏面板，只是标记为非当前操作面板
              console.log(`    📝 面板 ${index} 标记为非当前操作面板，但保持可见`);
              // 不修改面板的可见性，只是标记它不是当前操作的目标
              panelElement.dataset.isCurrentTarget = "false";
              // 保持面板完全可见和可交互
              panelElement.style.opacity = "1";
              panelElement.style.pointerEvents = "auto";
              panelElement.style.zIndex = "";
              delete panelElement.dataset.temporarilyHidden;
            } else {
              console.log(`    ✅ 面板 ${index} 无关联或属于当前输入框，保持可用状态`);
              // 不在这里直接标记为当前操作目标，而是在后续评分中决定
              panelElement.dataset.isCurrentTarget = "false";
              // 如果面板无关联，暂时不设置relatedInput，让评分逻辑来决定
              if (!panelElement.dataset.relatedInput) {
                // 面板无关联，保持未关联状态
              } else if (panelElement.dataset.relatedInput === currentInputId) {
                // 面板已经属于当前输入框，保持关联
              }
            }
          });

          console.log(`📊 面板隔离完成，剩余可用面板: ${availablePanelsCount}`);

          // 查找所有可能的日历面板
          const allPanels = document.querySelectorAll(PANEL_SELECTOR);
          let pickerPanel: Element | null = null;

          // 过滤出可见的面板
          const visiblePanels = Array.from(allPanels).filter((panel) => {
            const panelElement = panel as HTMLElement;
            const panelRect = panelElement.getBoundingClientRect();
            const isVisible =
              panelRect.width > 0 &&
              panelRect.height > 0 &&
              window.getComputedStyle(panelElement).visibility !== "hidden" &&
              window.getComputedStyle(panelElement).display !== "none";
            return isVisible;
          });

          console.log(`🔍 找到 ${allPanels.length} 个面板，其中 ${visiblePanels.length} 个可见`);

          if (visiblePanels.length === 0) {
            console.error("❌ 失败：未找到任何可用的日历弹窗。");
            return;
          } else if (visiblePanels.length === 1) {
            // 只有一个可见面板，直接使用
            pickerPanel = visiblePanels[0];
            console.log("✅ 找到唯一可见日历面板，直接使用");
          } else {
            // 多个可见面板，需要找到与当前输入框关联的面板
            console.log(`🔍 找到${visiblePanels.length}个可见日历面板，正在匹配关联面板...`);

            const inputRect = input.getBoundingClientRect();
            console.log(`📍 输入框位置: x=${inputRect.left}, y=${inputRect.top}, id=${currentInputId}`);

            // 方法1: 通过DOM关系查找 - 查找共同的父容器
            let bestPanel: Element | null = null;
            let bestScore = -1;

            visiblePanels.forEach((panel, index) => {
              const panelElement = panel as HTMLElement;
              const panelRect = panelElement.getBoundingClientRect();

              // 检查面板可见性
              const isVisible =
                panelRect.width > 0 &&
                panelRect.height > 0 &&
                window.getComputedStyle(panelElement).visibility !== "hidden" &&
                window.getComputedStyle(panelElement).display !== "none";

              if (!isVisible) {
                console.log(`  面板${index}: 不可见，跳过`);
                return;
              }

              let score = 0;

              // 评分标准0: 智能判断最合适的面板
              // 优先选择已经与当前输入框关联的面板
              if (panelElement.dataset.relatedInput === currentInputId) {
                score += 1000; // 已关联的面板获得最高优先级
                console.log(`  面板${index}: 已与当前输入框关联，获得最高优先级 +1000`);
              } else if (!panelElement.dataset.relatedInput) {
                // 未关联的面板，在表格环境中需要更精确的判断
                if (input.dataset.tableIndex && input.dataset.rowIndex && input.dataset.columnIndex) {
                  // 在表格环境中，优先选择位置最近且未被占用的面板
                  const distance = Math.sqrt(
                    Math.pow(panelRect.left - inputRect.left, 2) + Math.pow(panelRect.top - inputRect.bottom, 2),
                  );
                  if (distance < 200) {
                    // 距离较近的未关联面板
                    score += 800; // 给予高优先级，但低于已关联面板
                    console.log(`  面板${index}: 表格环境中距离较近的未关联面板 +800`);
                  } else {
                    score += 100; // 距离较远的未关联面板
                    console.log(`  面板${index}: 表格环境中距离较远的未关联面板 +100`);
                  }
                } else {
                  // 非表格环境中，未关联面板获得中等优先级
                  score += 500;
                  console.log(`  面板${index}: 非表格环境中的未关联面板 +500`);
                }
              } else {
                // 已被其他输入框关联的面板，在表格环境中应该被排除
                if (input.dataset.tableIndex && input.dataset.rowIndex && input.dataset.columnIndex) {
                  console.log(`  面板${index}: 已被其他输入框关联，在表格环境中跳过`);
                  return; // 跳过这个面板
                } else {
                  score -= 200; // 非表格环境中，已被其他输入框关联的面板扣分
                  console.log(`  面板${index}: 已被其他输入框关联，扣分 -200`);
                }
              }

              // 评分标准1: DOM层级关系 - 查找共同祖先
              const commonAncestor = findCommonAncestor(input, panelElement);
              if (commonAncestor) {
                const inputDepth = getElementDepth(input, commonAncestor);
                const panelDepth = getElementDepth(panelElement, commonAncestor);
                // 层级越近得分越高
                score += Math.max(0, 20 - Math.abs(inputDepth - panelDepth));
                console.log(`  面板${index}: DOM关系得分 ${Math.max(0, 20 - Math.abs(inputDepth - panelDepth))}`);
              }

              // 评分标准2: 位置关系 - 面板应该在输入框附近
              const distance = Math.sqrt(
                Math.pow(panelRect.left - inputRect.left, 2) + Math.pow(panelRect.top - inputRect.bottom, 2),
              );
              const proximityScore = Math.max(0, 50 - distance / 10);
              score += proximityScore;
              console.log(`  面板${index}: 位置得分 ${proximityScore.toFixed(1)} (距离: ${distance.toFixed(1)}px)`);

              // 评分标准3: z-index - 更高的z-index表示更近期打开
              const zIndex = parseInt(window.getComputedStyle(panelElement).zIndex) || 0;
              score += Math.min(zIndex / 100, 10); // 最多10分
              console.log(`  面板${index}: z-index得分 ${Math.min(zIndex / 100, 10).toFixed(1)} (z-index: ${zIndex})`);

              // 评分标准4: 面板活动时间 - 优先选择最近活动的面板
              const lastActivity = panelElement.dataset.lastActivity;
              if (lastActivity) {
                const timeSinceActivity = Date.now() - parseInt(lastActivity);
                if (timeSinceActivity < 2000) {
                  // 2秒内有活动
                  score += 50;
                  console.log(`  面板${index}: 最近活动面板得分 +50`);
                } else if (timeSinceActivity < 5000) {
                  // 5秒内有活动
                  score += 20;
                  console.log(`  面板${index}: 近期活动面板得分 +20`);
                }
              }

              // 评分标准5: 表格位置关联 - 如果在表格环境中
              if (input.dataset.tableIndex && input.dataset.rowIndex && input.dataset.columnIndex) {
                const inputTableIndex = input.dataset.tableIndex;
                const inputRowIndex = input.dataset.rowIndex;
                const inputColumnIndex = input.dataset.columnIndex;

                // 检查面板是否与同一表格位置相关
                if (
                  panelElement.dataset.tableIndex === inputTableIndex &&
                  panelElement.dataset.rowIndex === inputRowIndex &&
                  panelElement.dataset.columnIndex === inputColumnIndex
                ) {
                  score += 200; // 表格位置完全匹配得超高分，确保优先选择
                  console.log(`  面板${index}: 表格位置完全匹配得分 200`);
                } else if (
                  panelElement.dataset.tableIndex ||
                  panelElement.dataset.rowIndex ||
                  panelElement.dataset.columnIndex
                ) {
                  // 如果面板有表格位置信息但不匹配，在表格环境中应该排除
                  console.log(
                    `  面板${index}: 表格位置不匹配，跳过 (面板: table=${panelElement.dataset.tableIndex}, row=${panelElement.dataset.rowIndex}, col=${panelElement.dataset.columnIndex})`,
                  );
                  return; // 跳过这个面板
                }

                // 检查面板是否在同一表格的合理范围内（仅对未标记位置的面板）
                if (!panelElement.dataset.tableIndex) {
                  const tableCell = input.closest("td, th");
                  if (tableCell) {
                    const tableCellRect = tableCell.getBoundingClientRect();
                    const isWithinTableCell =
                      panelRect.left >= tableCellRect.left - 50 &&
                      panelRect.right <= tableCellRect.right + 50 &&
                      panelRect.top >= tableCellRect.bottom - 10;

                    if (isWithinTableCell) {
                      score += 30; // 在表格单元格范围内得分
                      console.log(`  面板${index}: 表格单元格范围内得分 30`);
                    }
                  }
                }
              }

              console.log(`  面板${index}: 总得分 ${score.toFixed(1)}`);

              if (score > bestScore) {
                bestScore = score;
                bestPanel = panelElement;
              }
            });

            if (bestPanel) {
              pickerPanel = bestPanel;
              console.log(`✅ 通过综合评分找到最佳关联面板 (得分: ${bestScore.toFixed(1)})`);

              // 标记为当前操作目标并建立关联
              (bestPanel as HTMLElement).dataset.isCurrentTarget = "true";
              if (currentInputId) {
                (bestPanel as HTMLElement).dataset.relatedInput = currentInputId;
                (bestPanel as HTMLElement).dataset.lastActivity = Date.now().toString();
                (bestPanel as HTMLElement).dataset.panelOwner = "current";
              }

              // 为面板添加完整的表格位置信息
              if (input.dataset.tableIndex && input.dataset.rowIndex && input.dataset.columnIndex) {
                (bestPanel as HTMLElement).dataset.tableIndex = input.dataset.tableIndex;
                (bestPanel as HTMLElement).dataset.rowIndex = input.dataset.rowIndex;
                (bestPanel as HTMLElement).dataset.columnIndex = input.dataset.columnIndex;
                console.log(
                  `🏷️ 为面板设置完整表格位置标记: table=${input.dataset.tableIndex}, row=${input.dataset.rowIndex}, col=${input.dataset.columnIndex}`,
                );
              }

              // 确保面板完全可见和可交互
              (bestPanel as HTMLElement).style.opacity = "1";
              (bestPanel as HTMLElement).style.pointerEvents = "auto";
              (bestPanel as HTMLElement).style.zIndex = "";
              delete (bestPanel as HTMLElement).dataset.temporarilyHidden;
              delete (bestPanel as HTMLElement).dataset.pendingCleanup;
            }

            // 方法2: 备用方案 - 如果评分方法失败，使用最新的可见且未被关联的面板
            if (!pickerPanel) {
              console.log(`⚠️ 评分方法未找到合适面板，启用备用方案`);
              for (let i = visiblePanels.length - 1; i >= 0; i--) {
                const panel = visiblePanels[i] as HTMLElement;
                const isNotRelated = !panel.dataset.relatedInput || panel.dataset.relatedInput === currentInputId;

                // 在表格环境中，额外检查表格位置冲突
                let isTablePositionSafe = true;
                if (input.dataset.tableIndex && input.dataset.rowIndex && input.dataset.columnIndex) {
                  // 如果面板已经有表格位置信息且与当前输入框不匹配，则跳过
                  if (
                    panel.dataset.tableIndex &&
                    (panel.dataset.tableIndex !== input.dataset.tableIndex ||
                      panel.dataset.rowIndex !== input.dataset.rowIndex ||
                      panel.dataset.columnIndex !== input.dataset.columnIndex)
                  ) {
                    isTablePositionSafe = false;
                    console.log(`    备用面板 ${i}: 表格位置冲突，跳过`);
                  }
                }

                if (isNotRelated && isTablePositionSafe) {
                  pickerPanel = panel;
                  console.log(`⚠️ 使用备用方案：最后一个可见面板 (索引: ${i})`);

                  // 为备用方案选中的面板添加完整的关联标记
                  if (currentInputId) {
                    (panel as HTMLElement).dataset.relatedInput = currentInputId;
                    (panel as HTMLElement).dataset.lastActivity = Date.now().toString();
                    (panel as HTMLElement).dataset.panelOwner = "current";
                  }

                  // 为面板添加完整的表格位置信息
                  if (input.dataset.tableIndex && input.dataset.rowIndex && input.dataset.columnIndex) {
                    (panel as HTMLElement).dataset.tableIndex = input.dataset.tableIndex;
                    (panel as HTMLElement).dataset.rowIndex = input.dataset.rowIndex;
                    (panel as HTMLElement).dataset.columnIndex = input.dataset.columnIndex;
                    console.log(
                      `🏷️ 备用方案面板设置完整表格位置标记: table=${input.dataset.tableIndex}, row=${input.dataset.rowIndex}, col=${input.dataset.columnIndex}`,
                    );
                  }

                  // 确保备用面板完全可见和可交互
                  (panel as HTMLElement).style.opacity = "1";
                  (panel as HTMLElement).style.pointerEvents = "auto";
                  (panel as HTMLElement).style.zIndex = "";
                  delete (panel as HTMLElement).dataset.temporarilyHidden;
                  delete (panel as HTMLElement).dataset.pendingCleanup;

                  break;
                }
              }
            }
          }

          // 辅助函数：查找两个元素的共同祖先
          function findCommonAncestor(elem1: Element, elem2: Element): Element | null {
            const ancestors1 = [];
            let current: Element | null = elem1;
            while (current) {
              ancestors1.push(current);
              current = current.parentElement;
            }

            current = elem2;
            while (current) {
              if (ancestors1.includes(current)) {
                return current;
              }
              current = current.parentElement;
            }
            return null;
          }

          // 辅助函数：计算元素在祖先中的深度
          function getElementDepth(element: Element, ancestor: Element): number {
            let depth = 0;
            let current: Element | null = element;
            while (current && current !== ancestor) {
              depth++;
              current = current.parentElement;
            }
            return current === ancestor ? depth : -1;
          }

          if (!pickerPanel) {
            console.error("❌ 失败：无法确定要操作的日历面板。");
            return;
          }

          console.log(`✅ 成功确定目标日历面板: ${pickerPanel.className}`);

          // 确保面板有完整的关联信息和活动状态
          const panelElement = pickerPanel as HTMLElement;
          if (!panelElement.dataset.relatedInput && inputId) {
            panelElement.dataset.relatedInput = inputId;
          }

          // 更新面板活动时间，标记为正在使用
          panelElement.dataset.lastActivity = Date.now().toString();
          panelElement.dataset.panelOwner = "current";
          panelElement.dataset.isActive = "true";

          // 确保表格位置信息完整
          if (input.dataset.tableIndex && !panelElement.dataset.tableIndex) {
            panelElement.dataset.tableIndex = input.dataset.tableIndex;
          }
          if (input.dataset.rowIndex && !panelElement.dataset.rowIndex) {
            panelElement.dataset.rowIndex = input.dataset.rowIndex;
          }
          if (input.dataset.columnIndex && !panelElement.dataset.columnIndex) {
            panelElement.dataset.columnIndex = input.dataset.columnIndex;
          }

          // 确保面板完全可见和可交互
          panelElement.style.opacity = "1";
          panelElement.style.pointerEvents = "auto";
          panelElement.style.zIndex = "";
          delete panelElement.dataset.temporarilyHidden;
          delete panelElement.dataset.pendingCleanup;

          console.log(
            `📋 面板关联信息: relatedInput=${panelElement.dataset.relatedInput}, tableIndex=${panelElement.dataset.tableIndex}, rowIndex=${panelElement.dataset.rowIndex}, columnIndex=${panelElement.dataset.columnIndex}`,
          );
          console.log(
            `📐 面板位置: ${pickerPanel.getBoundingClientRect().left}, ${pickerPanel.getBoundingClientRect().top}`,
          );
          console.log(
            `👁️ 面板可见性: ${window.getComputedStyle(pickerPanel as HTMLElement).visibility}, display: ${window.getComputedStyle(pickerPanel as HTMLElement).display}`,
          );

          // 根据你的截图定制的日期单元格选择器
          const DAY_CELL_SELECTOR = "td.available span";
          const targetDate = new Date(formattedDate);

          // 再次验证日期有效性，避免NAN-NAN问题
          if (isNaN(targetDate.getTime())) {
            console.error(`❌ 目标日期无效: ${formattedDate}，尝试直接输入`);
            try {
              input.value = value;
              input.dispatchEvent(new Event("input", { bubbles: true }));
              input.dispatchEvent(new Event("change", { bubbles: true }));
              console.log(`✅ 面板方式失败后直接输入成功: ${value}`);
              return true;
            } catch (error) {
              console.error(`❌ 直接输入也失败:`, error);
              return false;
            }
          }

          const targetYear = targetDate.getFullYear();
          const targetMonth = targetDate.getMonth(); // 0-11 for Jan-Dec
          const targetDay = targetDate.getDate().toString();

          console.log(`🎯 验证通过的目标日期: ${targetYear}-${targetMonth + 1}-${targetDay}`);

          // 动态计算最大尝试次数：基于目标日期与当前日期的差距
          const currentDate = new Date();
          const monthsDiff = Math.abs(
            (targetYear - currentDate.getFullYear()) * 12 + (targetMonth - currentDate.getMonth()),
          );
          const maxAttempts = Math.max(24, monthsDiff + 6); // 至少24次，或者根据月份差距+6次缓冲

          console.log(`🎯 目标日期: ${targetYear}-${targetMonth + 1}-${targetDay}`);
          console.log(
            `📅 当前日期: ${currentDate.getFullYear()}-${currentDate.getMonth() + 1}-${currentDate.getDate()}`,
          );
          console.log(`🔢 计算的最大尝试次数: ${maxAttempts} (月份差距: ${monthsDiff})`);

          // 使用闭包变量跟踪导航状态，防止死循环
          let lastMonth = "";
          let sameMonthCount = 0;

          // 使用一个递归函数来导航到正确的月份
          const navigateToCorrectMonth = (attempt = 0) => {
            // 更新面板活动时间，表示正在使用中
            if (pickerPanel) {
              (pickerPanel as HTMLElement).dataset.lastActivity = Date.now().toString();
            }

            // 安全阀：防止无限循环
            if (attempt > maxAttempts) {
              console.error(`❌ 失败：翻页次数过多 (${attempt}/${maxAttempts})，可能陷入了死循环或目标日期过远。`);

              // 清理面板活动状态
              if (pickerPanel) {
                delete (pickerPanel as HTMLElement).dataset.isActive;
              }

              // 尝试直接输入作为后备方案
              try {
                input.value = value;
                input.dispatchEvent(new Event("input", { bubbles: true }));
                input.dispatchEvent(new Event("change", { bubbles: true }));
                console.log(`✅ 导航失败后直接输入成功: ${value}`);
              } catch (error) {
                console.error(`❌ 直接输入也失败:`, error);
              }
              return;
            }

            // 验证面板是否仍然有效和可见
            const panelRect = pickerPanel.getBoundingClientRect();
            const isVisible =
              panelRect.width > 0 &&
              panelRect.height > 0 &&
              window.getComputedStyle(pickerPanel as HTMLElement).visibility !== "hidden" &&
              window.getComputedStyle(pickerPanel as HTMLElement).display !== "none";

            if (!isVisible) {
              console.error(`❌ 失败：面板在第 ${attempt} 次尝试时变为不可见`);
              return;
            }

            console.log(`🔄 第 ${attempt + 1} 次尝试导航 (最大: ${maxAttempts})`);

            const headerLabels = pickerPanel.querySelectorAll(HEADER_LABEL_SELECTOR);
            if (headerLabels.length < 2) {
              console.error(
                `❌ 失败：找不到足够的年月显示标签 ("${HEADER_LABEL_SELECTOR}")，只找到了 ${headerLabels.length} 个`,
              );
              return;
            }

            // 从分离的元素中解析当前显示的年月
            const yearText = headerLabels[0].textContent || ""; // e.g., "2025 年"
            const monthText = headerLabels[1].textContent || ""; // e.g., "8 月"

            const yearMatch = yearText.match(/(\d{4})/);
            const monthMatch = monthText.match(/(\d{1,2})/);

            if (!yearMatch || !monthMatch) {
              console.error(`❌ 失败：无法从 "${yearText}" 和 "${monthText}" 中解析出年月`);
              return;
            }

            const currentYear = parseInt(yearMatch[1], 10);
            const currentMonth = parseInt(monthMatch[1], 10) - 1; // 转换为 0-11

            console.log(`目标: ${targetYear}-${targetMonth + 1} | 当前显示: ${currentYear}-${currentMonth + 1}`);

            // 检查年月是否匹配
            if (currentYear === targetYear && currentMonth === targetMonth) {
              console.log("✅ 年月已匹配! 准备点击日期...");
              clickDay();
              return;
            }

            // 防止死循环：检查是否在同一个月份上卡住了
            const currentMonthKey = `${currentYear}-${currentMonth}`;
            if (!lastMonth) {
              lastMonth = currentMonthKey;
              sameMonthCount = 1;
            } else if (lastMonth === currentMonthKey) {
              sameMonthCount++;
              if (sameMonthCount > 3) {
                console.error(`❌ 失败：在同一月份 ${currentMonthKey} 上卡住了，可能是导航按钮失效`);
                // 尝试直接输入作为后备方案
                try {
                  input.value = value;
                  input.dispatchEvent(new Event("input", { bubbles: true }));
                  input.dispatchEvent(new Event("change", { bubbles: true }));
                  console.log(`✅ 导航卡住后直接输入成功: ${value}`);
                } catch (error) {
                  console.error(`❌ 直接输入也失败:`, error);
                }
                return;
              }
            } else {
              lastMonth = currentMonthKey;
              sameMonthCount = 1;
            }

            // 判断是前进还是后退
            let navigationSuccess = false;
            if (currentYear < targetYear || (currentYear === targetYear && currentMonth < targetMonth)) {
              console.log("  -> 点击 '下一月'");
              const nextBtn = pickerPanel.querySelector(NEXT_MONTH_SELECTOR) as HTMLElement;
              if (nextBtn) {
                nextBtn.click();
                console.log(`    ✅ 成功点击下一月按钮`);
                navigationSuccess = true;
              } else {
                console.error(`    ❌ 找不到下一月按钮: ${NEXT_MONTH_SELECTOR}`);
                return;
              }
            } else {
              console.log("  -> 点击 '上一月'");
              const prevBtn = pickerPanel.querySelector(PREV_MONTH_SELECTOR) as HTMLElement;
              if (prevBtn) {
                prevBtn.click();
                console.log(`    ✅ 成功点击上一月按钮`);
                navigationSuccess = true;
              } else {
                console.error(`    ❌ 找不到上一月按钮: ${PREV_MONTH_SELECTOR}`);
                return;
              }
            }

            // 只有在导航成功时才继续下一次尝试
            if (navigationSuccess) {
              // UI需要时间渲染，再次调用自己进行下一次检查
              setTimeout(() => navigateToCorrectMonth(attempt + 1), 150);
            }
          };

          const clickDay = () => {
            console.log(`🎯 开始查找目标日期: ${targetDay}`);
            const dayCells = pickerPanel.querySelectorAll(DAY_CELL_SELECTOR);
            console.log(`📅 找到 ${dayCells.length} 个日期单元格`);

            let targetCell: HTMLElement | null = null;
            const availableDays: string[] = [];

            for (const cell of dayCells) {
              const dayText = cell.textContent?.trim();
              availableDays.push(dayText || "empty");
              if (dayText === targetDay) {
                targetCell = cell as HTMLElement;
                console.log(`✅ 找到目标日期单元格: ${dayText}`);
                break;
              }
            }

            console.log(`📋 可用日期: [${availableDays.join(", ")}]`);

            if (targetCell) {
              targetCell.click();
              console.log(`🎉 成功模拟点击，日期 ${targetDay} 已选择!`);

              // 清理面板活动状态，标记操作完成
              if (pickerPanel) {
                delete (pickerPanel as HTMLElement).dataset.isActive;
                (pickerPanel as HTMLElement).dataset.lastActivity = Date.now().toString();
              }
            } else {
              console.warn(`⚠️ 在目标月份中未找到可用日期: "${targetDay}"`);
              console.warn(`💡 尝试使用备用选择器查找日期...`);

              // 备用选择器尝试
              const altDayCells = pickerPanel.querySelectorAll(
                'td span, .el-date-table__cell span, [class*="date"] span',
              );
              for (const cell of altDayCells) {
                if (cell.textContent?.trim() === targetDay) {
                  (cell as HTMLElement).click();
                  console.log(`🎉 使用备用选择器成功点击日期 ${targetDay}!`);

                  // 清理面板活动状态，标记操作完成
                  if (pickerPanel) {
                    delete (pickerPanel as HTMLElement).dataset.isActive;
                    (pickerPanel as HTMLElement).dataset.lastActivity = Date.now().toString();
                  }
                  return;
                }
              }

              console.error(`❌ 所有方法都无法找到目标日期`);
              document.body.click();
            }
          };
          navigateToCorrectMonth();
        }, 800); // 800ms的延迟确保面板完全重新创建

        return true;
        // 单独处理InputEvent，因为它可能在某些环境中不可用
        // try {
        //   if (typeof InputEvent !== "undefined") {
        //     const inputEvent = new InputEvent("input", {
        //       bubbles: true,
        //       cancelable: true,
        //       data: formattedDate,
        //     });
        //     input.dispatchEvent(inputEvent);
        //   }
        // } catch (e) {
        //   console.warn("触发InputEvent失败:", e);
        // }

        // // 额外触发Element UI特定事件
        // try {
        //   const customEvent = new CustomEvent("el.form.change", {
        //     bubbles: true,
        //     detail: { value: formattedDate },
        //   });
        //   input.dispatchEvent(customEvent);
        // } catch (e) {
        //   console.warn("触发Element UI自定义事件失败:", e);
        // }

        console.log(`✅ Element UI DatePicker填充成功：${formattedDate}`);
      } catch (error) {
        console.error(`❌ Element UI DatePicker填充失败：`, error);
        return false;
      }
    } catch (error) {
      console.error("Element UI DatePicker填充失败：", error);
      return false;
    }
  };

  // 填充UI框架组件（一键填充优化版）- 参考 content/formDetector 实现
  const fillUIFrameworkComponent = (
    element: HTMLElement,
    value: string,
    tableIndex?: number,
    rowIndex?: number,
    columnIndex?: number,
    isNewRow?: boolean,
  ): boolean => {
    if (!element || !value) return false;

    try {
      // 优先使用统一的 Element UI 组件处理函数
      if (
        element.classList.contains("el-select") ||
        element.classList.contains("el-input") ||
        element.classList.contains("el-textarea") ||
        element.classList.contains("el-date-picker") ||
        element.classList.contains("el-date-editor")
      ) {
        return fillElementUIComponent(element, value, undefined, tableIndex, rowIndex, columnIndex, isNewRow);
      }

      // Element UI DatePicker 单独处理（向后兼容）
      if (element.classList.contains("el-date-picker") || element.classList.contains("el-date-editor")) {
        return fillElementUIDatePicker(element, value, undefined, tableIndex, rowIndex, columnIndex, isNewRow);
      }

      // Ant Design 组件处理
      if (element.classList.contains("ant-select")) {
        return fillAntDesignSelect(element, value);
      }
      if (element.classList.contains("ant-input")) {
        return fillAntDesignInput(element, value);
      }
      if (element.classList.contains("ant-picker")) {
        return fillAntDesignDatePicker(element, value);
      }
      if (element.classList.contains("ant-cascader")) {
        return fillAntDesignCascader(element, value);
      }

      // Material-UI 组件处理
      if (element.classList.contains("MuiDatePicker")) {
        return fillMaterialUITextField(element, value);
      }
      if (element.classList.contains("MuiTextField-root") || element.classList.contains("MuiFormControl-root")) {
        return fillMaterialUITextField(element, value);
      }
      if (element.classList.contains("MuiSelect-root")) {
        return fillMaterialUISelect(element, value);
      }
      if (element.classList.contains("MuiInput-root")) {
        return fillMaterialUIInput(element, value);
      }

      // Vuetify 组件处理
      if (element.classList.contains("v-input") || element.classList.contains("v-text-field")) {
        return fillVuetifyInput(element, value);
      }
      if (element.classList.contains("v-select")) {
        return fillVuetifySelect(element, value);
      }
      if (element.classList.contains("v-date-picker") || element.classList.contains("v-text-field")) {
        return fillVuetifyDatePicker(element, value);
      }
      if (element.classList.contains("v-textarea")) {
        return fillVuetifyTextarea(element, value);
      }

      // 如果不是已知的UI框架组件，返回false让其他处理逻辑接管
      return false;
    } catch (error) {
      console.error("UI框架组件填充失败：", error);
      return false;
    }
  };

  // 智能匹配填充表单元素
  const fillFormElementWithSmartMatching = (label: string, value: string, fieldIndex?: number): boolean => {
    if (!label || !value) {
      console.warn(`❌ 填充参数无效: label="${label}", value="${value}"`);
      return false;
    }

    console.log(
      `\n🎯 开始填充字段: "${label}" = "${value}"${fieldIndex !== undefined ? ` (fieldIndex: ${fieldIndex})` : ""}`,
    );

    // 获取所有可能的表单元素
    const allFormElementsRaw = Array.from(
      document.querySelectorAll(
        'input:not([type="hidden"]):not([type="submit"]):not([type="button"]):not([type="reset"]), select, textarea',
      ),
    ) as HTMLElement[];

    // 过滤掉不可见的表单元素以优化性能
    const allFormElements = allFormElementsRaw.filter((element) => {
      const style = window.getComputedStyle(element);
      const rect = element.getBoundingClientRect();

      // 检查元素是否可见
      const isVisible =
        style.display !== "none" &&
        style.visibility !== "hidden" &&
        style.opacity !== "0" &&
        rect.width > 0 &&
        rect.height > 0;

      return isVisible;
    });

    console.log(
      `📊 页面表单元素统计: 共找到 ${allFormElementsRaw.length} 个表单元素，其中 ${allFormElements.length} 个可见`,
    );

    // 打印前5个元素的详细信息用于调试
    if (allFormElements.length > 0) {
      console.log(`📋 前5个表单元素详情:`);
      allFormElements.slice(0, 5).forEach((el, index) => {
        const input = el as HTMLInputElement;
        console.log(
          `  ${index + 1}. ${el.tagName.toLowerCase()}${input.type ? `[type="${input.type}"]` : ""} - name: "${input.name || "N/A"}", placeholder: "${input.placeholder || "N/A"}", value: "${input.value || ""}", class: "${el.className || "N/A"}"`,
        );
      });
      if (allFormElements.length > 5) {
        console.log(`  ... 还有 ${allFormElements.length - 5} 个元素`);
      }
    }

    // 策略1: 优先通过fieldIndex匹配（如果提供）
    // fieldIndex是基于页面中所有表单元素的DOM顺序分配的（1基索引）
    if (fieldIndex !== undefined && fieldIndex > 0 && fieldIndex <= allFormElements.length) {
      const targetElement = allFormElements[fieldIndex - 1]; // 转换为0基索引
      console.log(`🎯 尝试通过fieldIndex(${fieldIndex})匹配元素:`, {
        targetElement: targetElement.tagName,
        className: targetElement.className,
        name: (targetElement as any).name,
        placeholder: (targetElement as any).placeholder,
        currentValue: (targetElement as any).value,
        label: label,
      });

      if (
        targetElement &&
        fillSingleFormField(targetElement as HTMLInputElement, value, undefined, undefined, undefined, false)
      ) {
        console.log(`✅ 通过fieldIndex(${fieldIndex})匹配成功填充: ${label}`);
        return true;
      } else {
        console.warn(`⚠️ fieldIndex(${fieldIndex})匹配的元素填充失败`);
      }
    } else if (fieldIndex !== undefined) {
      console.warn(`⚠️ fieldIndex(${fieldIndex})超出范围，总共${allFormElements.length}个元素`);
    }

    // 策略2: 通过getFormElementLabel函数获取的标签匹配（完全匹配）
    let bestMatch: { element: HTMLElement; score: number } | null = null;
    const matchCandidates: Array<{ element: HTMLElement; label: string; score: number; currentValue: string }> = [];

    console.log(`🔍 开始标签匹配分析，目标标签: "${label}"`);

    for (const element of allFormElements) {
      const elementLabel = getFormElementLabel(element);
      if (!elementLabel) {
        console.log(`⚠️ 元素无标签:`, element);
        continue;
      }

      let score = 0;
      const elementLabelLower = elementLabel.toLowerCase();
      const labelLower = label.toLowerCase();
      const currentValue = (element as HTMLInputElement).value || "";

      // 完全匹配得分最高
      if (elementLabelLower === labelLower) {
        score = 100;
        console.log(`🎯 完全匹配: "${elementLabel}" (得分: ${score})`);
      }
      // 包含匹配
      else if (elementLabelLower.includes(labelLower)) {
        score = 80;
        console.log(`📍 包含匹配: "${elementLabel}" 包含 "${label}" (得分: ${score})`);
      }
      // 反向包含匹配
      else if (labelLower.includes(elementLabelLower)) {
        score = 70;
        console.log(`📍 反向包含匹配: "${label}" 包含 "${elementLabel}" (得分: ${score})`);
      }
      // 模糊匹配
      else if (fuzzyMatch(elementLabel, label)) {
        score = 60;
        console.log(`🔄 模糊匹配: "${elementLabel}" 与 "${label}" (得分: ${score})`);
      }

      if (score > 0) {
        // 不再优先选择未填充的元素，允许覆盖已有值
        const finalScore = score;

        matchCandidates.push({
          element,
          label: elementLabel,
          score: finalScore,
          currentValue,
        });

        console.log(`📊 候选元素: "${elementLabel}", 当前值: "${currentValue}", 最终得分: ${finalScore}`);

        if (!bestMatch || finalScore > bestMatch.score) {
          bestMatch = { element, score: finalScore };
        }
      } else {
        console.log(`❌ 无匹配: "${elementLabel}" 与 "${label}"`);
      }
    }

    if (matchCandidates.length > 0) {
      console.log(`📋 匹配候选列表 (共${matchCandidates.length}个):`);
      matchCandidates
        .sort((a, b) => b.score - a.score)
        .forEach((candidate, index) => {
          console.log(
            `  ${index + 1}. "${candidate.label}" (得分: ${candidate.score}, 当前值: "${candidate.currentValue}")`,
          );
        });
    } else {
      console.log(`❌ 未找到任何匹配的候选元素`);
    }

    if (
      bestMatch &&
      fillSingleFormField(bestMatch.element as HTMLInputElement, value, undefined, undefined, undefined, false)
    ) {
      console.log(`✅ 通过getFormElementLabel匹配成功填充: ${label} (得分: ${bestMatch.score})`);
      return true;
    }

    console.log(`\n❌ 所有匹配策略均失败，未找到匹配的字段: "${label}"`);
    console.log(`📊 详细失败分析:`);
    console.log(`  - 目标字段: "${label}" (值: "${value}")`);
    console.log(
      `  - fieldIndex匹配: ${fieldIndex !== undefined ? `已尝试 (${fieldIndex}/${allFormElements.length})` : "未提供fieldIndex"}`,
    );
    console.log(`  - 标签匹配候选: ${matchCandidates.length}个`);

    console.log(`  - 页面总表单元素: ${allFormElements.length}个`);

    // 提供可能的匹配建议
    if (allFormElements.length > 0) {
      console.log(`💡 可能的匹配建议:`);
      const suggestions = allFormElements.slice(0, 3).map((el, index) => {
        const input = el as HTMLInputElement;
        const elementLabel = getFormElementLabel(el);
        return `    ${index + 1}. ${el.tagName.toLowerCase()}${input.type ? `[${input.type}]` : ""} - label: "${elementLabel || "N/A"}", name: "${input.name || "N/A"}", placeholder: "${input.placeholder || "N/A"}"`;
      });
      suggestions.forEach((suggestion) => console.log(suggestion));
    }

    return false;
  };

  // 填充单个表单字段
  const fillSingleFormField = (
    element: HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement,
    value: string,
    tableIndex?: number,
    rowIndex?: number,
    columnIndex?: number,
    isNewRow?: boolean,
  ): boolean => {
    if (!element || !value) {
      console.warn(`⚠️ fillSingleFormField参数无效: element=${!!element}, value="${value}"`);
      return false;
    }

    console.log(
      `🔧 尝试填充单个字段: ${element.tagName.toLowerCase()}${(element as HTMLInputElement).type ? `[${(element as HTMLInputElement).type}]` : ""} = "${value}"`,
    );

    if (element.disabled) {
      console.warn(`⚠️ 元素被禁用，跳过填充`);
      return false;
    }
    // 检查只读属性，但Element UI框架组件除外
    if ("readOnly" in element && element.readOnly) {
      // Element UI框架的组件不受只读规则限制，因为编译后的select的input就是readonly
      const isElementUI = element.closest(".el-select, .el-textarea, .el-date-picker, .el-date-editor");
      if (!isElementUI) {
        console.warn(`⚠️ 元素为只读，跳过填充`);
        return false;
      } else {
        console.log(`🎯 Element UI组件忽略只读属性检查`);
      }
    }

    // 检查元素是否可见
    const style = window.getComputedStyle(element);
    if (style.display === "none" || style.visibility === "hidden") {
      console.warn(`⚠️ 元素不可见，跳过填充 (display: ${style.display}, visibility: ${style.visibility})`);
      return false;
    }

    try {
      // 优先尝试UI框架组件填充
      const parent = element.closest(".el-input, .el-select, .el-date-editor, .ant-input, .ant-select, .ant-picker");
      if (
        parent &&
        fillUIFrameworkComponent(parent as HTMLElement, value, tableIndex, rowIndex, columnIndex, isNewRow)
      ) {
        return true;
      }

      // 根据元素类型填充
      if (element.tagName === "SELECT") {
        return fillSelectElement(element as HTMLSelectElement, value);
      } else if (element.type === "date") {
        return fillDateElement(element as HTMLInputElement, value);
      } else if (element.type === "checkbox" || element.type === "radio") {
        return fillCheckboxRadioElement(element as HTMLInputElement, value);
      } else {
        // 普通文本输入（参考formDetector逻辑）
        const inputElement = element as HTMLInputElement | HTMLTextAreaElement;

        // 使用原生setter确保值被正确设置
        const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
          inputElement.tagName === "TEXTAREA"
            ? window.HTMLTextAreaElement.prototype
            : window.HTMLInputElement.prototype,
          "value",
        )?.set;

        if (nativeInputValueSetter) {
          nativeInputValueSetter.call(inputElement, value);
        } else {
          inputElement.value = value;
        }

        // 触发多种事件确保框架能够响应，包括click事件来打开面板
        const events = ["click", "focus", "input", "change", "blur"];
        events.forEach((eventType) => {
          try {
            const event = new Event(eventType, { bubbles: true, cancelable: true });
            inputElement.dispatchEvent(event);
          } catch (e) {
            console.warn(`触发${eventType}事件失败:`, e);
          }
        });

        // 触发InputEvent（某些框架需要）
        try {
          const inputEvent = new InputEvent("input", {
            bubbles: true,
            cancelable: true,
            data: value,
          });
          inputElement.dispatchEvent(inputEvent);
        } catch (e) {
          console.warn("触发InputEvent失败:", e);
        }

        console.log(`✅ 传统表单元素填充：${value}`);
        return true;
      }
    } catch (error) {
      console.error("填充字段时发生错误:", error);
      return false;
    }
  };

  // 解析完整数据并填充
  const parseCompleteDataAndFill = (content?: string): void => {
    if (!content) {
      console.log("没有内容需要解析");
      return;
    }

    console.log("开始解析完整数据:", content);

    // 解析表单字段
    parseFormFieldsFromComplete(content);

    // 解析表格数据
    parseTableDataFromComplete(content);
    // // 解析表单字段
    // parseFormFieldsFromComplete();

    // // 解析表格数据
    // parseTableDataFromComplete();
  };

  // 从完整数据中解析表单字段
  const parseFormFieldsFromComplete = (content?: string): void => {
    console.log("开始解析表单字段");

    try {
      // 尝试解析JSON格式的完整表单数据
      let formData;
      try {
        //formData = contentData as any; // 直接使用导入的数据,避免JSON.parse解析
        formData = JSON.parse(content);
      } catch (e) {
        console.log("内容不是JSON格式，尝试提取JSON片段");
        // 尝试从文本中提取JSON片段
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          formData = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error("无法解析JSON数据");
        }
      }

      console.log("解析到的表单数据:", formData);

      // 检查是否有表单字段数据
      let fieldsToFill = [];
      let formType = null;

      // 处理不同的数据结构
      if (formData.allFormData && Array.isArray(formData.allFormData)) {
        // 处理 {allFormData: Array} 格式
        console.log("检测到allFormData格式");
        for (const item of formData.allFormData) {
          if (item.fields && Array.isArray(item.fields)) {
            fieldsToFill.push(...item.fields);
            if (item.formType) formType = item.formType;
          }
        }
        // 如果顶层有formType，使用顶层的
        if (formData.formType) formType = formData.formType;
      }

      console.log(`找到表单类型: ${formType || "未知"}`);
      console.log("提取的字段数据:", fieldsToFill);

      console.log(`找到 ${fieldsToFill.length} 个字段待填充`);

      if (fieldsToFill.length === 0) {
        console.log("未找到字段数据，尝试使用传统模式匹配");
        parseFormFieldsWithPatternMatching(content);
        return;
      }

      // 使用fieldIndex进行精确填充
      let successCount = 0;
      for (const field of fieldsToFill) {
        if (field.fieldIndex !== undefined && field.value !== undefined && field.value !== null && field.value !== "") {
          console.log(
            `尝试通过fieldIndex(${field.fieldIndex})填充字段: ${field.label || field.tagName || "N/A"} = ${field.value}`,
          );
          if (fillFormElementByFieldIndex(field.fieldIndex, field.value, field.label)) {
            successCount++;
          }
        } else {
          console.log(`跳过字段 - fieldIndex: ${field.fieldIndex}, value: ${field.value}, label: ${field.label}`);
        }
      }

      console.log(`成功填充 ${successCount} 个表单字段`);
    } catch (error) {
      console.error("解析表单数据失败:", error);
      console.log("回退到传统模式匹配");
      parseFormFieldsWithPatternMatching(content);
    }
  };

  // 传统的模式匹配填充方法（作为备选方案）
  const parseFormFieldsWithPatternMatching = (content: string): void => {
    console.log("使用传统模式匹配解析表单字段");

    // 多种匹配模式
    const formPatterns = [
      /"label"\s*:\s*"([^"]+)"[^}]*"value"\s*:\s*"([^"]*)"/g,
      /"?([^"\n:：]+)"?\s*[:：]\s*"?([^"\n,}]+)"?/g,
      /([\u4e00-\u9fa5]+)\s*[:：]\s*([^\n,}]+)/g,
      /(\w+)\s*=\s*"?([^"\n,}]+)"?/g,
      /"([^"]+)"\s*:\s*"([^"]*)"/g,
      /([^\n:：=,{}]+)\s*[:：=]\s*([^\n,{}]+)/g,
    ];

    let fieldCount = 0;

    for (const pattern of formPatterns) {
      const matches = [...content.matchAll(pattern)];
      if (matches.length > 0) {
        console.log(`使用模式匹配到 ${matches.length} 个字段`);

        for (const match of matches) {
          let label, value;

          if (match[0].includes('"label"')) {
            const labelMatch = match[0].match(/"label"\s*:\s*"([^"]+)"/);
            const valueMatch = match[0].match(/"value"\s*:\s*"([^"]*)"/);
            if (labelMatch && valueMatch) {
              label = labelMatch[1];
              value = valueMatch[1];
            }
          } else if (match.length >= 3) {
            label = match[1]?.replace(/["']/g, "").trim();
            value = match[2]?.replace(/["',}]/g, "").trim();
          }

          if (label && value && value !== "null" && value !== "undefined" && value.length > 0) {
            console.log(`尝试填充字段: ${label} = ${value}`);
            if (fillFormElementWithSmartMatching(label, value)) {
              fieldCount++;
            }
          }
        }

        if (fieldCount > 0) {
          console.log(`成功填充 ${fieldCount} 个表单字段`);
          break;
        }
      }
    }

    if (fieldCount === 0) {
      console.log("未找到可填充的表单字段");
    }
  };

  // 通过fieldIndex精确填充表单元素（与提取逻辑保持一致）
  const fillFormElementByFieldIndex = (fieldIndex: number, value: string, label?: string): boolean => {
    console.log(`🎯 通过fieldIndex(${fieldIndex})填充字段: ${label || "N/A"} = ${value}`);

    // 使用与提取时完全相同的选择器
    const allInputs = document.querySelectorAll(
      "input, textarea, select, .el-input, .el-select, .el-textarea, .el-checkbox, .el-radio, .el-switch, .el-date-picker, .el-time-picker, .el-cascader, .el-slider, .el-rate, .el-upload, .ant-input, .ant-select, .ant-checkbox, .ant-radio, .ant-switch, .ant-date-picker, .ant-time-picker, .ant-cascader, .ant-slider, .ant-rate, .ant-upload, .ant-input-number, .v-text-field, .v-select, .v-checkbox, .v-radio, .v-switch, .v-date-picker, .v-time-picker, .v-slider, .v-rating, .MuiTextField-root, .MuiSelect-root, .MuiCheckbox-root, .MuiRadio-root, .MuiSwitch-root, .MuiSlider-root",
    );

    console.log(`📊 页面总共找到 ${allInputs.length} 个表单元素`);

    // fieldIndex是1基索引，需要转换为0基索引
    if (fieldIndex > 0 && fieldIndex <= allInputs.length) {
      const targetElement = allInputs[fieldIndex - 1];
      console.log(`🎯 找到目标元素(索引${fieldIndex}):`, {
        tagName: targetElement.tagName,
        className: targetElement.className,
        name: (targetElement as any).name,
        id: (targetElement as any).id,
        currentValue: (targetElement as any).value,
      });

      // 查找实际的输入元素（处理框架组件）
      const actualInput = findActualInputElement(targetElement as HTMLElement);
      if (actualInput && fillSingleFormField(actualInput, value, undefined, undefined, undefined, false)) {
        console.log(`✅ 通过fieldIndex(${fieldIndex})成功填充: ${label || "N/A"}`);
        return true;
      } else {
        console.warn(`❌ 通过fieldIndex(${fieldIndex})填充失败: ${label || "N/A"}`);
        return false;
      }
    } else {
      console.warn(`❌ fieldIndex(${fieldIndex})超出范围，总共${allInputs.length}个元素`);
      return false;
    }
  };

  // 查找实际的输入元素（处理框架组件）
  const findActualInputElement = (
    element: HTMLElement,
  ): HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement | null => {
    // 如果本身就是输入元素
    if (element.tagName === "INPUT" || element.tagName === "SELECT" || element.tagName === "TEXTAREA") {
      return element as HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement;
    }

    // 使用框架检测函数来获取元素信息
    const formInfo = detectFormElementType(element);

    // 根据检测到的框架类型，使用更精确的查找策略
    if (formInfo.framework !== "unknown" && formInfo.framework !== "native") {
      // 对于框架组件，根据不同框架使用不同的查找策略
      switch (formInfo.framework) {
        case "elementUI":
          return findElementUIActualInput(element);
        case "antDesign":
          return findAntDesignActualInput(element);
        case "vuetify":
          return findVuetifyActualInput(element);
        case "materialUI":
          return findMaterialUIActualInput(element);
      }
    }

    // 通用查找策略（原生HTML或未识别的框架）
    const actualInput = element.querySelector("input, select, textarea") as
      | HTMLInputElement
      | HTMLSelectElement
      | HTMLTextAreaElement;
    if (actualInput) {
      return actualInput;
    }

    return null;
  };

  // Element UI 组件的实际输入元素查找
  const findElementUIActualInput = (
    element: HTMLElement,
  ): HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement | null => {
    // 优先查找特定的 Element UI 输入元素
    const elInput = element.querySelector(".el-input__inner, .el-textarea__inner") as
      | HTMLInputElement
      | HTMLTextAreaElement;
    if (elInput) return elInput;

    // 对于选择器，查找隐藏的 input
    const elSelectInput = element.querySelector(".el-select input") as HTMLInputElement;
    if (elSelectInput) return elSelectInput;

    // 通用查找
    return element.querySelector("input, select, textarea") as
      | HTMLInputElement
      | HTMLSelectElement
      | HTMLTextAreaElement;
  };

  // Ant Design 组件的实际输入元素查找
  const findAntDesignActualInput = (
    element: HTMLElement,
  ): HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement | null => {
    // 对于 Ant Design 选择器，查找 selector 内的 input
    const antSelectInput = element.querySelector(".ant-select-selector input") as HTMLInputElement;
    if (antSelectInput) return antSelectInput;

    // 通用查找
    return element.querySelector("input, select, textarea") as
      | HTMLInputElement
      | HTMLSelectElement
      | HTMLTextAreaElement;
  };

  // Vuetify 组件的实际输入元素查找
  const findVuetifyActualInput = (
    element: HTMLElement,
  ): HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement | null => {
    // Vuetify 通常直接使用原生 input 元素
    return element.querySelector("input, select, textarea") as
      | HTMLInputElement
      | HTMLSelectElement
      | HTMLTextAreaElement;
  };

  // Material-UI 组件的实际输入元素查找
  const findMaterialUIActualInput = (
    element: HTMLElement,
  ): HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement | null => {
    // Material-UI 通常直接使用原生 input 元素
    return element.querySelector("input, select, textarea") as
      | HTMLInputElement
      | HTMLSelectElement
      | HTMLTextAreaElement;
  };

  // 从完整数据中解析表格数据
  const parseTableDataFromComplete = (content?: string): void => {
    console.log("开始解析表格数据", content);

    try {
      let jsonData;

      try {
        // 解析JSON数据
        jsonData = JSON.parse(content);
        //jsonData = contentData as any; // 直接使用导入的数据,避免JSON.parse解析
      } catch (e) {
        console.log("内容不是JSON格式，尝试提取JSON片段");
        // 尝试从文本中提取JSON片段
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          jsonData = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error("无法解析JSON数据");
        }
      }

      console.log("使用导入的data.json文件:", jsonData);

      // 遍历所有表单数据，查找表格类型
      for (const formData of jsonData.allFormData) {
        if (formData.formType === "table" && formData.tableData && formData.tableMetadata) {
          console.log("找到表格数据:", formData);

          const { headers, rows } = formData.tableData;
          const tableMetadata = formData.tableMetadata;

          if (headers && headers.length > 0) {
            // 使用表格元数据精确定位表格
            const targetTable = findTableByMetadata(tableMetadata);

            if (targetTable) {
              console.log("成功定位到目标表格:", targetTable);

              // 处理行数据 - 如果没有原始数据，根据表头创建空行
              let processedRows = [];

              if (rows && rows.length > 0) {
                // 转换现有行数据格式
                processedRows = rows.map((row: any) => {
                  const convertedRow: any = {};
                  Object.keys(row).forEach((key) => {
                    if (row[key] && typeof row[key] === "object" && "value" in row[key]) {
                      convertedRow[key] = row[key].value;
                    } else {
                      convertedRow[key] = row[key] || "";
                    }
                  });
                  return convertedRow;
                });
              } else {
                // 根据表头创建空行数据
                const emptyRow: any = {};
                headers.forEach((header: string) => {
                  emptyRow[header] = "";
                });
                processedRows = [emptyRow];
                console.log("根据表头创建了空行数据:", processedRows);
              }

              // 填充表格数据到精确定位的表格
              fillTableWithDataByElement(targetTable, headers, processedRows);
            } else {
              console.warn("无法定位到目标表格，尝试使用选择器:", tableMetadata.tableSelector);
              // 回退到使用选择器
              if (tableMetadata.tableSelector) {
                const fallbackTable = document.querySelector(tableMetadata.tableSelector);
                if (fallbackTable) {
                  console.log("使用选择器成功找到表格:", fallbackTable);
                  let processedRows = [];
                  if (rows && rows.length > 0) {
                    processedRows = rows.map((row: any) => {
                      const convertedRow: any = {};
                      Object.keys(row).forEach((key) => {
                        if (row[key] && typeof row[key] === "object" && "value" in row[key]) {
                          convertedRow[key] = row[key].value;
                        } else {
                          convertedRow[key] = row[key] || "";
                        }
                      });
                      return convertedRow;
                    });
                  } else {
                    const emptyRow: any = {};
                    headers.forEach((header: string) => {
                      emptyRow[header] = "";
                    });
                    processedRows = [emptyRow];
                  }
                  fillTableWithDataByElement(fallbackTable as HTMLElement, headers, processedRows);
                }
              }
            }
          }
        }
      }
    } catch (e) {
      console.error("解析表格数据失败:", e);
    }
  };

  // 根据表格元数据精确定位表格
  const findTableByMetadata = (tableMetadata: any): HTMLElement | null => {
    try {
      // 优先使用CSS选择器
      if (tableMetadata.tableSelector) {
        const table = document.querySelector(tableMetadata.tableSelector);
        if (table) {
          console.log("通过CSS选择器找到表格:", table);
          return table as HTMLElement;
        }
      }

      // 回退到XPath
      if (tableMetadata.tableXPath) {
        const result = document.evaluate(
          tableMetadata.tableXPath,
          document,
          null,
          XPathResult.FIRST_ORDERED_NODE_TYPE,
          null,
        );
        if (result.singleNodeValue) {
          console.log("通过XPath找到表格:", result.singleNodeValue);
          return result.singleNodeValue as HTMLElement;
        }
      }

      // 最后尝试通过结构特征匹配
      if (tableMetadata.structuralFingerprint) {
        const { headerTexts, headerCount } = tableMetadata.structuralFingerprint;
        const tables = document.querySelectorAll("table");

        for (const table of tables) {
          const headers = table.querySelectorAll("th");
          if (headers.length === headerCount) {
            const tableHeaderTexts = Array.from(headers).map((th) => th.textContent?.trim() || "");
            // 检查表头文本是否匹配
            const matchCount = headerTexts.filter((text: string) => tableHeaderTexts.includes(text)).length;

            if (matchCount >= headerTexts.length * 0.8) {
              // 80%匹配度
              console.log("通过结构特征找到表格:", table);
              return table as HTMLElement;
            }
          }
        }
      }

      console.warn("无法通过元数据定位表格");
      return null;
    } catch (error) {
      console.error("定位表格时发生错误:", error);
      return null;
    }
  };

  // 在表格单元格中查找表单元素（参考formDetector实现）
  const findFormElementInCell = (
    cell: HTMLElement,
  ): HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement | null => {
    // 1. 直接检查当前单元格是否为表单元素
    const tagName = cell.tagName.toLowerCase();
    if (["input", "select", "textarea"].includes(tagName)) {
      return cell as HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement;
    }

    // 2. 检查Element UI组件（扩展支持更多组件）
    const elementUISelectors = [
      ".el-input",
      ".el-select",
      ".el-textarea",
      ".el-date-picker",
      ".el-date-editor",
      ".el-time-picker",
      ".el-cascader",
      ".el-input-number",
      ".el-autocomplete",
      ".el-color-picker",
      ".el-rate",
      ".el-slider",
      ".el-switch",
    ];

    for (const selector of elementUISelectors) {
      const wrapper = cell.querySelector(selector);
      if (wrapper) {
        const actualInput = wrapper.querySelector("input, textarea, select");
        if (actualInput) {
          console.log(`🎯 找到Element UI表单元素: ${selector}`);
          return actualInput as HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement;
        }
      }
    }

    // 3. 检查Ant Design组件（扩展支持更多组件）
    const antdSelectors = [
      ".ant-input",
      ".ant-select",
      ".ant-picker",
      ".ant-cascader",
      ".ant-input-number",
      ".ant-auto-complete",
      ".ant-mentions",
      ".ant-tree-select",
      ".ant-time-picker",
      ".ant-date-picker",
      ".ant-slider",
      ".ant-rate",
      ".ant-switch",
      ".ant-upload",
    ];

    for (const selector of antdSelectors) {
      const wrapper = cell.querySelector(selector);
      if (wrapper) {
        const actualInput = wrapper.querySelector("input, textarea, select");
        if (actualInput) {
          console.log(`🎯 找到Ant Design表单元素: ${selector}`);
          return actualInput as HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement;
        }
      }
    }

    // 4. 检查Vuetify组件（扩展支持更多组件）
    const vuetifySelectors = [
      ".v-text-field",
      ".v-select",
      ".v-date-picker",
      ".v-textarea",
      ".v-time-picker",
      ".v-autocomplete",
      ".v-combobox",
      ".v-file-input",
      ".v-slider",
      ".v-rating",
    ];

    for (const selector of vuetifySelectors) {
      const wrapper = cell.querySelector(selector);
      if (wrapper) {
        const actualInput = wrapper.querySelector("input, textarea, select");
        if (actualInput) {
          console.log(`🎯 找到Vuetify表单元素: ${selector}`);
          return actualInput as HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement;
        }
      }
    }

    // 5. 检查Material-UI组件（扩展支持更多组件）
    const muiSelectors = [
      ".MuiTextField-root",
      ".MuiSelect-root",
      ".MuiDatePicker",
      ".MuiTimePicker",
      ".MuiAutocomplete-root",
      ".MuiSlider-root",
      ".MuiRating-root",
      ".MuiSwitch-root",
    ];

    for (const selector of muiSelectors) {
      const wrapper = cell.querySelector(selector);
      if (wrapper) {
        const actualInput = wrapper.querySelector("input, textarea, select");
        if (actualInput) {
          console.log(`🎯 找到Material-UI表单元素: ${selector}`);
          return actualInput as HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement;
        }
      }
    }

    // 6. 检查可编辑元素（contenteditable）
    const editableElement = cell.querySelector('[contenteditable="true"]') as HTMLElement;
    if (editableElement) {
      console.log(`🎯 找到可编辑元素: contenteditable`);
      // 为contenteditable元素创建一个虚拟的input接口
      const virtualInput = {
        tagName: "DIV",
        type: "text",
        value: editableElement.textContent || "",
        focus: () => editableElement.focus(),
        blur: () => editableElement.blur(),
        addEventListener: (event: string, handler: any) => editableElement.addEventListener(event, handler),
        dispatchEvent: (event: Event) => editableElement.dispatchEvent(event),
      } as any;
      return virtualInput;
    }

    // 7. 通用查找：直接在单元格内查找原生表单元素
    const nativeInput = cell.querySelector("input, textarea, select");
    if (nativeInput) {
      console.log(`🎯 找到原生表单元素: ${nativeInput.tagName.toLowerCase()}`);
      return nativeInput as HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement;
    }

    // 8. 检查是否有data-*属性标识的自定义表单组件
    const customFormElement = cell.querySelector("[data-form-element], [data-input], [data-field]");
    if (customFormElement) {
      const nestedInput = customFormElement.querySelector("input, textarea, select");
      if (nestedInput) {
        console.log(`🎯 找到自定义表单组件内的表单元素`);
        return nestedInput as HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement;
      }
    }

    console.log(`❌ 单元格中未找到表单元素`);
    return null;
  };

  // 填充指定表格元素的数据
  const fillTableWithDataByElement = (tableElement: HTMLElement, headers: string[], rows: any[]): void => {
    try {
      console.log("开始填充表格数据:", { tableElement, headers, rows });

      // 检测表格框架类型
      const frameworkInfo = detectTableFramework(tableElement);
      console.log("检测到表格框架:", frameworkInfo);

      // 根据框架类型查找tbody
      let tbody: HTMLElement;
      if (frameworkInfo.framework === "element-ui") {
        tbody =
          tableElement.querySelector(".el-table__body tbody") ||
          tableElement.querySelector("tbody") ||
          tableElement.querySelector(".el-table__body") ||
          tableElement;
      } else if (frameworkInfo.framework === "ant-design") {
        tbody = tableElement.querySelector(".ant-table-tbody") || tableElement.querySelector("tbody") || tableElement;
      } else {
        tbody = tableElement.querySelector("tbody") || tableElement;
      }

      // 获取现有的数据行（排除表头行）
      const existingDataRows = Array.from(tbody.querySelectorAll("tr")).filter((row) => {
        return row.querySelector("th") === null; // 不是表头行
      });

      console.log(`📊 当前表格有${existingDataRows.length}行数据，需要填充${rows.length}行`);

      // 检查是否有空状态提示，如果有则移除
      if (existingDataRows.length === 0) {
        console.log("🗑️ 检测到空表格，移除空状态提示");
        removeTableEmptyState(tableElement);
      }

      // 智能处理表格行：更新现有行，创建缺失行
      rows.forEach((rowData, rowIndex) => {
        let targetRow: HTMLElement;
        let isNewRow = false;

        if (rowIndex < existingDataRows.length) {
          // 使用现有行
          targetRow = existingDataRows[rowIndex] as HTMLElement;

          // 确保现有行的表单元素有正确的数据属性
          const formElements = targetRow.querySelectorAll(
            'input, select, textarea, [class*="el-"], [class*="ant-"], [class*="v-"], [class*="Mui"]',
          );
          formElements.forEach((element) => {
            const htmlElement = element as HTMLElement;

            // 设置表格位置数据
            const tableIndex = Array.from(document.querySelectorAll("table")).indexOf(tableElement as HTMLTableElement);
            htmlElement.dataset.tableIndex = tableIndex.toString();
            htmlElement.dataset.rowIndex = rowIndex.toString();

            // 设置列索引
            const cell = htmlElement.closest("td, th");
            if (cell) {
              const cellIndex = Array.from(targetRow.children).indexOf(cell as HTMLElement);
              htmlElement.dataset.columnIndex = cellIndex.toString();
            }

            // 确保有pickerId
            if (!htmlElement.dataset.pickerId) {
              const pickerId = `picker_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
              htmlElement.dataset.pickerId = pickerId;
            }
          });

          console.log(`🔄 更新现有行${rowIndex}，已设置数据属性`);
        } else {
          // 创建新行
          const newRow = createTableRow(tableElement, headers.length, frameworkInfo, existingDataRows);
          if (newRow) {
            targetRow = newRow;
            isNewRow = true;
            tbody.appendChild(newRow);

            // 重要：设置新行的rowIndex，确保表单元素能正确关联面板
            if ((newRow as any).setRowIndex) {
              (newRow as any).setRowIndex();
            }

            console.log(`➕ 创建新行${rowIndex}，已设置rowIndex`);
          } else {
            console.warn(`⚠️ 无法创建新行${rowIndex}`);
            return;
          }
        }

        // 填充单元格数据
        headers.forEach((header, colIndex) => {
          const cellValue = rowData[header] || "";
          const cell = targetRow.children[colIndex] as HTMLElement;

          if (cell) {
            // 检查单元格是否包含表单元素
            const formElement = findFormElementInCell(cell);

            if (formElement) {
              // 表单单元格：使用fillSingleFormField填充
              console.log(`📝 填充表格表单元素：行${rowIndex}, 列${colIndex} = ${cellValue}`);
              if (fillSingleFormField(formElement, cellValue, 0, rowIndex, colIndex, isNewRow)) {
                // 添加成功填充的视觉反馈
                const originalStyle = {
                  backgroundColor: formElement.style.backgroundColor,
                  border: formElement.style.border,
                };

                formElement.style.backgroundColor = "#f6ffed";
                formElement.style.border = "1px solid #52c41a";

                setTimeout(() => {
                  formElement.style.backgroundColor = originalStyle.backgroundColor;
                  formElement.style.border = originalStyle.border;
                }, 2000);
              } else {
                console.warn(`⚠️ 表格表单元素填充失败：行${rowIndex}, 列${colIndex}`);
              }
            } else {
              // 普通文本单元格：直接设置文本内容
              console.log(`📄 填充文本单元格：行${rowIndex}, 列${colIndex} = ${cellValue}`);

              // 查找单元格内的文本容器
              const textContainer = cell.querySelector(".cell, .ant-table-cell-content") || cell;
              textContainer.textContent = cellValue;
            }

            // 添加视觉反馈
            cell.style.backgroundColor = "#e6f7ff";
            cell.style.color = "#1890ff";
            setTimeout(() => {
              cell.style.backgroundColor = "";
              cell.style.color = "";
            }, 1000);
          }
        });
      });

      // 如果数据行数少于现有行数，隐藏多余的行
      if (rows.length < existingDataRows.length) {
        for (let i = rows.length; i < existingDataRows.length; i++) {
          const excessRow = existingDataRows[i] as HTMLElement;
          excessRow.style.display = "none";
          console.log(`🙈 隐藏多余行${i}`);
        }
      }

      console.log("✅ 表格数据填充完成");
    } catch (error) {
      console.error("填充表格数据时发生错误:", error);
    }
  };

  // 检测表格框架类型
  const detectTableFramework = (tableElement: HTMLElement) => {
    // Element UI 检测
    if (
      tableElement.classList.contains("el-table") ||
      tableElement.closest(".el-table") ||
      tableElement.querySelector(".el-table__header, .el-table__body")
    ) {
      return { framework: "element-ui", version: "unknown" };
    }

    // Ant Design 检测
    if (
      tableElement.classList.contains("ant-table") ||
      tableElement.closest(".ant-table") ||
      tableElement.querySelector(".ant-table-thead, .ant-table-tbody")
    ) {
      return { framework: "ant-design", version: "unknown" };
    }

    // Vuetify 检测
    if (tableElement.classList.contains("v-data-table") || tableElement.closest(".v-data-table")) {
      return { framework: "vuetify", version: "unknown" };
    }

    // Material-UI 检测
    if (tableElement.classList.contains("MuiTable-root") || tableElement.closest(".MuiTable-root")) {
      return { framework: "material-ui", version: "unknown" };
    }

    return { framework: "native", version: "html" };
  };

  // 创建表格行
  // 分析现有行的DOM结构
  const analyzeExistingRowStructure = (existingRow: HTMLElement): any[] => {
    const cellStructures: any[] = [];
    const cells = existingRow.children;

    for (let i = 0; i < cells.length; i++) {
      const cell = cells[i] as HTMLElement;
      const structure = {
        tagName: cell.tagName,
        className: cell.className,
        attributes: {},
        children: [],
        hasFormElement: false,
        formElementInfo: null,
      };

      // 复制所有属性
      for (let j = 0; j < cell.attributes.length; j++) {
        const attr = cell.attributes[j];
        if (attr.name !== "class") {
          structure.attributes[attr.name] = attr.value;
        }
      }

      // 分析子元素结构
      const analyzeChildren = (element: HTMLElement): any[] => {
        const childStructures: any[] = [];
        for (let k = 0; k < element.children.length; k++) {
          const child = element.children[k] as HTMLElement;
          const childStructure = {
            tagName: child.tagName,
            className: child.className,
            attributes: {},
            textContent: child.textContent?.trim() || "",
            children: analyzeChildren(child),
          };

          // 复制子元素属性
          for (let l = 0; l < child.attributes.length; l++) {
            const attr = child.attributes[l];
            if (attr.name !== "class") {
              childStructure.attributes[attr.name] = attr.value;
            }
          }

          childStructures.push(childStructure);
        }
        return childStructures;
      };

      structure.children = analyzeChildren(cell);

      // 检查是否包含表单元素
      const formElement = findFormElementInCell(cell);
      if (formElement) {
        structure.hasFormElement = true;
        structure.formElementInfo = {
          tagName: formElement.tagName,
          type: (formElement as HTMLInputElement).type || "",
          className: formElement.className,
          name: (formElement as HTMLInputElement).name || "",
          placeholder: (formElement as HTMLInputElement).placeholder || "",
        };
      }

      cellStructures.push(structure);
    }

    return cellStructures;
  };

  // 创建自定义日期选择器下拉面板
  const createCustomDatePicker = (element: HTMLElement) => {
    console.log("📅 创建自定义日期选择器下拉面板");

    // 创建日期选择器面板
    const datePanel = document.createElement("div");
    datePanel.className = "el-picker-panel el-date-picker el-popper";
    datePanel.style.cssText = `
      position: absolute;
      z-index: 9999;
      background: white;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
      padding: 12px;
      min-width: 300px;
      display: none;
    `;

    // 创建简单的日期输入
    const dateInput = document.createElement("input");
    dateInput.type = "date";
    dateInput.style.cssText = "width: 100%; padding: 8px; border: 1px solid #dcdfe6; border-radius: 4px;";
    datePanel.appendChild(dateInput);

    // 添加确认按钮
    const confirmBtn = document.createElement("button");
    confirmBtn.textContent = "确定";
    confirmBtn.style.cssText =
      "margin-top: 8px; padding: 6px 12px; background: #409eff; color: white; border: none; border-radius: 4px; cursor: pointer;";
    confirmBtn.onclick = () => {
      const targetInput = element.querySelector("input") as HTMLInputElement;
      if (targetInput && dateInput.value) {
        targetInput.value = dateInput.value;
        targetInput.dispatchEvent(new Event("input", { bubbles: true }));
        targetInput.dispatchEvent(new Event("change", { bubbles: true }));
      }
      datePanel.style.display = "none";
    };
    datePanel.appendChild(confirmBtn);

    document.body.appendChild(datePanel);

    // 绑定点击事件显示面板
    element.addEventListener("click", (e) => {
      e.stopPropagation();
      const rect = element.getBoundingClientRect();
      datePanel.style.left = rect.left + "px";
      datePanel.style.top = rect.bottom + 5 + "px";
      datePanel.style.display = "block";
    });

    // 点击其他地方隐藏面板
    document.addEventListener("click", (e) => {
      if (!datePanel.contains(e.target as Node) && !element.contains(e.target as Node)) {
        datePanel.style.display = "none";
      }
    });
  };

  // 创建自定义选择器下拉面板
  const createCustomSelectDropdown = (element: HTMLElement) => {
    console.log("📋 创建自定义选择器下拉面板");

    // 创建下拉面板
    const dropdown = document.createElement("div");
    dropdown.className = "el-select-dropdown el-popper";
    dropdown.style.cssText = `
      position: absolute;
      z-index: 9999;
      background: white;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
      max-height: 200px;
      overflow-y: auto;
      display: none;
    `;

    // 创建一些示例选项
    const options = ["选项1", "选项2", "选项3", "自定义选项"];
    options.forEach((optionText) => {
      const option = document.createElement("div");
      option.className = "el-select-dropdown__item";
      option.textContent = optionText;
      option.style.cssText = "padding: 8px 12px; cursor: pointer; hover:background-color: #f5f7fa;";

      option.addEventListener("mouseenter", () => {
        option.style.backgroundColor = "#f5f7fa";
      });

      option.addEventListener("mouseleave", () => {
        option.style.backgroundColor = "transparent";
      });

      option.onclick = () => {
        const targetInput = element.querySelector("input") as HTMLInputElement;
        if (targetInput) {
          targetInput.value = optionText;
          targetInput.dispatchEvent(new Event("input", { bubbles: true }));
          targetInput.dispatchEvent(new Event("change", { bubbles: true }));
        }
        dropdown.style.display = "none";
      };

      dropdown.appendChild(option);
    });

    document.body.appendChild(dropdown);

    // 绑定点击事件显示下拉面板
    element.addEventListener("click", (e) => {
      e.stopPropagation();
      const rect = element.getBoundingClientRect();
      dropdown.style.left = rect.left + "px";
      dropdown.style.top = rect.bottom + 5 + "px";
      dropdown.style.display = "block";
    });

    // 点击其他地方隐藏下拉面板
    document.addEventListener("click", (e) => {
      if (!dropdown.contains(e.target as Node) && !element.contains(e.target as Node)) {
        dropdown.style.display = "none";
      }
    });
  };

  // 处理自定义组件点击事件
  const handleCustomComponentClick = (element: HTMLElement, e: Event) => {
    console.log("🖱️ 处理自定义组件点击事件:", element.className);
    e.stopPropagation();

    // 尝试触发原生事件
    const input = element.querySelector("input") as HTMLInputElement;
    if (input) {
      input.focus();
      input.click();
    }

    // 如果是日期组件，尝试显示自定义日期面板
    if (element.className.includes("el-date-editor")) {
      console.log("📅 触发日期选择器");
    }

    // 如果是选择器组件，尝试显示自定义下拉面板
    if (element.className.includes("el-select")) {
      console.log("📋 触发选择器下拉");
    }
  };

  // 基于现有行结构创建新行
  const createRowFromStructure = (cellStructures: any[], tableElement?: HTMLElement): HTMLTableRowElement => {
    const newRow = document.createElement("tr");

    cellStructures.forEach((cellStructure, cellIndex) => {
      const newCell = document.createElement(cellStructure.tagName.toLowerCase());
      newCell.className = cellStructure.className;

      // 设置属性
      Object.keys(cellStructure.attributes).forEach((attrName) => {
        newCell.setAttribute(attrName, cellStructure.attributes[attrName]);
      });

      // 递归创建子元素
      const createChildren = (parent: HTMLElement, childrenStructures: any[]) => {
        childrenStructures.forEach((childStructure) => {
          const childElement = document.createElement(childStructure.tagName.toLowerCase());
          childElement.className = childStructure.className;

          // 设置子元素属性
          Object.keys(childStructure.attributes).forEach((attrName) => {
            childElement.setAttribute(attrName, childStructure.attributes[attrName]);
          });

          // 如果没有子元素且有文本内容，清空文本内容（新行应该是空的）
          if (childStructure.children.length === 0 && childStructure.textContent) {
            childElement.textContent = "";
          }

          // 为表单元素设置表格位置数据属性
          if (
            childElement.tagName.toLowerCase() === "input" ||
            childElement.tagName.toLowerCase() === "select" ||
            childElement.tagName.toLowerCase() === "textarea" ||
            childElement.className.includes("el-") ||
            childElement.className.includes("ant-") ||
            childElement.className.includes("v-") ||
            childElement.className.includes("Mui")
          ) {
            // 设置表格位置数据
            if (tableElement) {
              const tableIndex = Array.from(document.querySelectorAll("table")).indexOf(
                tableElement as HTMLTableElement,
              );
              childElement.dataset.tableIndex = tableIndex.toString();
            }

            // 行索引将在插入表格后设置
            childElement.dataset.columnIndex = cellIndex.toString();

            // 生成唯一的picker ID
            const pickerId = `picker_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            childElement.dataset.pickerId = pickerId;

            console.log(
              `🏷️ 为表单元素设置数据属性: tableIndex=${childElement.dataset.tableIndex}, columnIndex=${childElement.dataset.columnIndex}, pickerId=${pickerId}`,
            );
          }

          // 递归创建子元素的子元素
          if (childStructure.children.length > 0) {
            createChildren(childElement, childStructure.children);
          }

          parent.appendChild(childElement);
        });
      };

      createChildren(newCell, cellStructure.children);
      newRow.appendChild(newCell);
    });

    // 为新创建的行添加事件监听器和框架组件初始化
    setTimeout(() => {
      try {
        console.log("🔧 开始为新行绑定事件和初始化组件 - createRowFromStructure");

        // 定义事件处理函数，避免在循环中重复定义
        const createElementClickHandler = (element: HTMLElement) => {
          return function (e: Event) {
            console.log("📅 Element UI组件被点击:", element.className);
            // 只在必要时触发focus事件，避免重复触发多个事件
            if (!element.matches(":focus")) {
              element.focus();
            }
          };
        };

        const createAntClickHandler = (element: HTMLElement) => {
          return function (e: Event) {
            console.log("📅 Ant Design组件被点击:", element.className);
            // 只在必要时触发focus事件，避免重复触发多个事件
            if (!element.matches(":focus")) {
              element.focus();
            }
          };
        };

        // 查找新行中的表单元素并重新绑定事件
        const formElements = newRow.querySelectorAll(
          'input, select, textarea, [class*="el-"], [class*="ant-"], [class*="v-"], [class*="Mui"]',
        );
        console.log("🔍 找到表单元素数量 - createRowFromStructure:", formElements.length);

        formElements.forEach((element, index) => {
          console.log(`🔍 处理第${index + 1}个元素:`, element.className);
          const htmlElement = element as HTMLElement;

          // 为Element UI组件重新绑定事件
          if (htmlElement.className.includes("el-")) {
            console.log("🎯 处理Element UI组件 - createRowFromStructure:", htmlElement.className);

            // 触发多种初始化事件
            ["DOMNodeInserted", "DOMContentLoaded", "load"].forEach((eventType) => {
              htmlElement.dispatchEvent(new Event(eventType, { bubbles: true }));
            });

            // 尝试重新初始化Vue组件实例
            try {
              console.log("🔍 开始查找Vue根实例 - createRowFromStructure");
              // 查找最近的Vue根实例 - 扩大搜索范围
              let vueRoot = null;
              let currentElement: Element | null = htmlElement;
              let attempts = 0;

              // 首先检查当前元素及其父元素链
              while (currentElement && attempts < 20) {
                console.log(`🔍 检查第${attempts + 1}个元素:`, currentElement.tagName, currentElement.className);

                // 检查多种Vue实例属性
                const vueProps = [
                  "__vue__", // Vue 2 实例
                  "_vnode", // Vue 2 虚拟节点
                  "__vueParentComponent", // Vue 3 父组件
                  "__vue_app__", // Vue 3 应用实例
                  "_instance", // Vue 3 组件实例
                  "__VUE__", // 全局Vue实例
                  "$vue", // 自定义Vue实例
                  "vue", // 简单Vue实例
                  "__vue_component__", // Vue组件实例
                  "_vue", // 备用Vue实例
                ];

                for (const prop of vueProps) {
                  const instance = (currentElement as any)[prop];
                  if (instance) {
                    // 对于_vnode，需要获取context
                    if (prop === "_vnode" && instance.context) {
                      vueRoot = instance.context;
                      console.log(`✅ 找到Vue根实例(${prop}.context):`, !!vueRoot);
                      break;
                    } else if (prop !== "_vnode") {
                      vueRoot = instance;
                      console.log(`✅ 找到Vue根实例(${prop}):`, !!vueRoot);
                      break;
                    }
                  }
                }

                if (vueRoot) break;

                currentElement = currentElement.parentElement;
                attempts++;
              }

              // 如果还没找到，尝试查找表格容器的Vue实例
              if (!vueRoot) {
                const containers = [
                  htmlElement.closest(".el-table"),
                  htmlElement.closest("[data-v-]"),
                  htmlElement.closest(".vue-component"),
                  htmlElement.closest('[id*="app"]'),
                  htmlElement.closest('[class*="vue"]'),
                ];

                for (const container of containers) {
                  if (container) {
                    console.log("🔍 尝试从容器查找Vue实例:", container.tagName, container.className);
                    const vueProps = ["__vue__", "_vnode", "__vueParentComponent", "__vue_app__", "_instance"];

                    for (const prop of vueProps) {
                      const instance = (container as any)[prop];
                      if (instance) {
                        vueRoot = prop === "_vnode" && instance.context ? instance.context : instance;
                        console.log(`✅ 从容器找到Vue实例(${prop}):`, !!vueRoot);
                        break;
                      }
                    }
                    if (vueRoot) break;
                  }
                }
              }

              // 如果还没找到，尝试从全局查找Vue应用实例
              if (!vueRoot) {
                console.log("🔍 尝试从全局查找Vue应用实例");
                const globalSelectors = ["#app", '[id*="app"]', "body", "html", ".vue-app", "[data-app]"];

                for (const selector of globalSelectors) {
                  const element = document.querySelector(selector);
                  if (element) {
                    const vueProps = [
                      "__vue__",
                      "_vnode",
                      "__vueParentComponent",
                      "__vue_app__",
                      "_instance",
                      "__VUE__",
                    ];

                    for (const prop of vueProps) {
                      const instance = (element as any)[prop];
                      if (instance) {
                        vueRoot = prop === "_vnode" && instance.context ? instance.context : instance;
                        console.log(`✅ 从全局元素(${selector})找到Vue实例(${prop}):`, !!vueRoot);
                        break;
                      }
                    }
                    if (vueRoot) break;
                  }
                }
              }

              // 最后尝试从window对象查找Vue实例
              if (!vueRoot) {
                console.log("🔍 尝试从window对象查找Vue实例");
                const windowVueProps = ["Vue", "__VUE__", "vue", "$vue", "app"];

                for (const prop of windowVueProps) {
                  const instance = (window as any)[prop];
                  if (instance && (instance.$forceUpdate || instance.config || instance.version)) {
                    vueRoot = instance;
                    console.log(`✅ 从window对象找到Vue实例(${prop}):`, !!vueRoot);
                    break;
                  }
                }
              }

              if (vueRoot && vueRoot.$forceUpdate) {
                console.log("🔄 触发Vue组件强制更新 - createRowFromStructure");
                vueRoot.$forceUpdate();

                // 触发Vue的响应式更新
                if (vueRoot.$nextTick) {
                  vueRoot.$nextTick(() => {
                    console.log("✅ Vue组件更新完成 - createRowFromStructure");

                    // 延迟重新初始化Element UI组件
                    setTimeout(() => {
                      console.log("🔄 延迟重新初始化Element UI组件");
                      // 重新触发Element UI组件的初始化事件
                      const events = ["DOMNodeInserted", "DOMContentLoaded", "load"];
                      events.forEach((eventType) => {
                        const event = new Event(eventType, { bubbles: true });
                        htmlElement.dispatchEvent(event);
                      });
                    }, 100);
                  });
                }
              } else {
                console.log("⚠️ 未找到Vue根实例或$forceUpdate方法");

                // 即使没有找到Vue实例，也尝试多种备用方案重新初始化Element UI组件
                setTimeout(() => {
                  console.log("🔄 备用方案1：直接重新初始化Element UI组件");
                  const events = ["DOMNodeInserted", "DOMContentLoaded", "load", "DOMSubtreeModified"];
                  events.forEach((eventType) => {
                    const event = new Event(eventType, { bubbles: true });
                    htmlElement.dispatchEvent(event);
                    // 也在父元素上触发事件
                    if (htmlElement.parentElement) {
                      htmlElement.parentElement.dispatchEvent(event);
                    }
                  });

                  // 尝试触发Element UI特定的初始化
                  if (typeof (window as any).ElementUI !== "undefined") {
                    console.log("🔄 备用方案2：尝试Element UI重新初始化");
                    // 重新应用Element UI组件
                    const elementUIEvents = ["el.form.addField", "el.form.removeField"];
                    elementUIEvents.forEach((eventType) => {
                      htmlElement.dispatchEvent(new CustomEvent(eventType, { bubbles: true }));
                    });
                  }

                  // 尝试手动重新绑定事件和创建自定义下拉面板
                  console.log("🔄 备用方案3：手动重新绑定组件事件并创建自定义下拉面板");
                  const clickableElements = htmlElement.querySelectorAll(
                    ".el-input__inner, .el-date-editor, .el-select",
                  );
                  clickableElements.forEach((el) => {
                    const element = el as HTMLElement;

                    // 为日历组件创建自定义下拉面板
                    if (element?.className?.includes("el-date-editor") || element?.closest(".el-date-editor")) {
                      console.log("📅 为日历组件创建自定义下拉面板");
                      createCustomDatePicker(element);
                    }

                    // 为select组件创建自定义下拉面板
                    if (element?.className?.includes("el-select") || element?.closest(".el-select")) {
                      console.log("📋 为select组件创建自定义下拉面板");
                      createCustomSelectDropdown(element);
                    }

                    // 重新添加通用点击事件监听器
                    if (!element?.dataset?.customEventBound) {
                      element.addEventListener(
                        "click",
                        (e) => {
                          e.stopPropagation();
                          console.log("🖱️ 自定义点击事件触发:", element?.className);
                          handleCustomComponentClick(element, e);
                        },
                        { capture: true },
                      );
                      if (element?.dataset) {
                        element.dataset.customEventBound = "true";
                      }
                    }
                  });
                }, 200);
              }
            } catch (error) {
              console.log("⚠️ Vue组件更新失败 - createRowFromStructure:", error);
            }

            // 为日期选择器和输入框添加增强的点击事件监听
            if (
              htmlElement.className.includes("el-date-editor") ||
              htmlElement.className.includes("el-input") ||
              htmlElement.className.includes("el-select")
            ) {
              // 检查是否已经绑定过事件，避免重复绑定
              if (!htmlElement.dataset.eventBound) {
                const clickHandler = createElementClickHandler(htmlElement);
                htmlElement.addEventListener("click", clickHandler, { capture: true });
                htmlElement.dataset.eventBound = "true";
              }
            }
          }

          // 为Ant Design组件重新绑定事件
          if (htmlElement.className.includes("ant-")) {
            console.log("🐜 处理Ant Design组件:", htmlElement.className);

            ["DOMNodeInserted", "DOMContentLoaded", "load"].forEach((eventType) => {
              htmlElement.dispatchEvent(new Event(eventType, { bubbles: true }));
            });

            if (
              htmlElement.className.includes("ant-picker") ||
              htmlElement.className.includes("ant-select") ||
              htmlElement.className.includes("ant-input")
            ) {
              // 检查是否已经绑定过事件，避免重复绑定
              if (!htmlElement.dataset.eventBound) {
                const clickHandler = createAntClickHandler(htmlElement);
                htmlElement.addEventListener("click", clickHandler, { capture: true });
                htmlElement.dataset.eventBound = "true";
              }
            }
          }

          // 为Vue组件重新绑定事件
          if (htmlElement.className.includes("v-")) {
            console.log("🟢 处理Vue组件:", htmlElement.className);
            ["DOMNodeInserted", "DOMContentLoaded", "load"].forEach((eventType) => {
              htmlElement.dispatchEvent(new Event(eventType, { bubbles: true }));
            });
          }

          // 为Material-UI组件重新绑定事件
          if (htmlElement.className.includes("Mui")) {
            console.log("🔵 处理Material-UI组件:", htmlElement.className);
            ["DOMNodeInserted", "DOMContentLoaded", "load"].forEach((eventType) => {
              htmlElement.dispatchEvent(new Event(eventType, { bubbles: true }));
            });
          }
        });

        // 触发整行的重新初始化
        newRow.dispatchEvent(new Event("DOMNodeInserted", { bubbles: true }));

        // 额外的Element UI初始化步骤
        setTimeout(() => {
          try {
            // 查找新行中的所有Element UI日期组件
            const dateComponents = newRow.querySelectorAll(".el-date-editor, .el-date-picker");
            dateComponents.forEach((component) => {
              const htmlComponent = component as HTMLElement;

              // 模拟用户交互来激活组件
              const focusEvent = new FocusEvent("focus", { bubbles: true });
              const blurEvent = new FocusEvent("blur", { bubbles: true });

              htmlComponent.dispatchEvent(focusEvent);
              setTimeout(() => {
                htmlComponent.dispatchEvent(blurEvent);
              }, 50);

              console.log("🎯 激活Element UI日期组件:", htmlComponent.className);
            });
          } catch (error) {
            console.log("⚠️ Element UI组件激活失败:", error);
          }
        }, 300);

        console.log("✅ 新行事件绑定完成，处理了", formElements.length, "个表单元素");
      } catch (error) {
        console.error("❌ 新行事件绑定失败:", error);
      }
    }, 100);

    return newRow;
  };

  const createTableRow = (
    tableElement: HTMLElement,
    columnCount: number,
    frameworkInfo: any,
    existingRows?: HTMLElement[],
  ): HTMLTableRowElement | null => {
    try {
      let newRow: HTMLTableRowElement;

      // 如果有现有行，基于现有行的DOM结构创建新行
      if (existingRows && existingRows.length > 0) {
        console.log(`🔍 基于现有行结构创建新行，现有行数量: ${existingRows.length}`);

        // 选择第一个现有行作为模板（通常第一行包含完整的结构）
        const templateRow = existingRows[0];
        const cellStructures = analyzeExistingRowStructure(templateRow);

        console.log(
          `📋 分析到的单元格结构:`,
          cellStructures.map((s) => ({
            tagName: s.tagName,
            className: s.className,
            hasFormElement: s.hasFormElement,
            childrenCount: s.children.length,
          })),
        );

        newRow = createRowFromStructure(cellStructures, tableElement);
        console.log(`✅ 基于现有行结构创建新行成功`);
      } else {
        console.log(`🏗️ 使用框架模板创建新行，框架: ${frameworkInfo.framework}`);

        // 根据框架类型创建行（原有逻辑）
        if (frameworkInfo.framework === "element-ui") {
          newRow = document.createElement("tr");
          newRow.className = "el-table__row";

          for (let i = 0; i < columnCount; i++) {
            const cell = document.createElement("td");
            cell.className = "el-table__cell";
            const cellDiv = document.createElement("div");
            cellDiv.className = "cell";
            cell.appendChild(cellDiv);
            newRow.appendChild(cell);
          }
        } else if (frameworkInfo.framework === "ant-design") {
          newRow = document.createElement("tr");
          newRow.className = "ant-table-row";

          for (let i = 0; i < columnCount; i++) {
            const cell = document.createElement("td");
            cell.className = "ant-table-cell";
            newRow.appendChild(cell);
          }
        } else if (frameworkInfo.framework === "vuetify") {
          newRow = document.createElement("tr");

          for (let i = 0; i < columnCount; i++) {
            const cell = document.createElement("td");
            cell.className = "v-data-table__td";
            newRow.appendChild(cell);
          }
        } else if (frameworkInfo.framework === "material-ui") {
          newRow = document.createElement("tr");
          newRow.className = "MuiTableRow-root";

          for (let i = 0; i < columnCount; i++) {
            const cell = document.createElement("td");
            cell.className = "MuiTableCell-root";
            newRow.appendChild(cell);
          }
        } else {
          // 原生HTML表格
          newRow = document.createElement("tr");

          for (let i = 0; i < columnCount; i++) {
            const cell = document.createElement("td");
            newRow.appendChild(cell);
          }
        }
      }

      // 移除空状态提示
      removeTableEmptyState(tableElement);

      // 为新行设置正确的rowIndex（需要在插入表格后调用）
      const setRowIndexForNewRow = (row: HTMLTableRowElement, table: HTMLElement) => {
        setTimeout(() => {
          try {
            const tbody = table.querySelector("tbody") || table;
            const allRows = Array.from(tbody.querySelectorAll("tr"));
            const rowIndex = allRows.indexOf(row);

            if (rowIndex >= 0) {
              // 为新行中的所有表单元素设置rowIndex
              const formElements = row.querySelectorAll(
                '[data-picker-id], input, select, textarea, [class*="el-"], [class*="ant-"], [class*="v-"], [class*="Mui"]',
              );
              formElements.forEach((element) => {
                const htmlElement = element as HTMLElement;
                htmlElement.dataset.rowIndex = rowIndex.toString();
                console.log(`🏷️ 设置rowIndex: ${rowIndex} for element:`, htmlElement.className);
              });

              console.log(`✅ 新行rowIndex设置完成: ${rowIndex}`);
            }
          } catch (error) {
            console.error("❌ 设置rowIndex失败:", error);
          }
        }, 50);
      };

      // 将设置函数附加到新行对象上，供外部调用
      (newRow as any).setRowIndex = () => setRowIndexForNewRow(newRow, tableElement);

      return newRow;
    } catch (error) {
      console.error("创建表格行失败:", error);
      // 回退到原生HTML方式
      const newRow = document.createElement("tr");
      for (let i = 0; i < columnCount; i++) {
        const cell = document.createElement("td");
        newRow.appendChild(cell);
      }
      return newRow;
    }
  };

  // 移除表格空状态提示
  const removeTableEmptyState = (tableElement: HTMLElement): void => {
    try {
      // 查找各种可能的空状态提示元素
      const emptySelectors = [
        ".ant-empty",
        ".el-table__empty-block",
        ".el-table__empty-text",
        ".v-data-table__empty-wrapper",
        ".MuiTableBody-root .MuiTableRow-root .MuiTableCell-root[colspan]",
        '[class*="empty"]',
        '[class*="no-data"]',
      ];

      emptySelectors.forEach((selector) => {
        const elements = tableElement.querySelectorAll(selector);
        elements.forEach((element) => {
          const textContent = element.textContent?.trim().toLowerCase() || "";
          if (
            textContent.includes("暂无数据") ||
            textContent.includes("no data") ||
            textContent.includes("empty") ||
            textContent.includes("没有数据")
          ) {
            (element as HTMLElement).style.display = "none";
          }
        });
      });

      // 查找包含空状态文本的单元格
      const cells = tableElement.querySelectorAll("td, th");
      cells.forEach((cell) => {
        const textContent = cell.textContent?.trim().toLowerCase() || "";
        if (
          textContent === "暂无数据" ||
          textContent === "no data" ||
          textContent === "empty" ||
          textContent === "没有数据"
        ) {
          (cell as HTMLElement).style.display = "none";
          // 如果整行只有这一个单元格，隐藏整行
          const row = cell.closest("tr");
          if (row && row.children.length === 1) {
            (row as HTMLElement).style.display = "none";
          }
        }
      });

      console.log("已移除表格空状态提示");
    } catch (error) {
      console.error("移除表格空状态提示失败:", error);
    }
  };

  const handleFillForm = () => {
    console.log("开始一键填充表单");

    // 获取完整的流式输出数据
    const completeData =
      number > 0 && sseChat.chatList[number - 1] && sseChat.chatList[number - 1].length > 0
        ? sseChat.chatList[number - 1][1].content
        : sseChat.displayedText || "";

    if (!completeData) {
      message.warning("没有可用的数据进行填充");
      return;
    }

    console.log("准备填充的完整数据:", completeData);

    try {
      parseCompleteDataAndFill(completeData);
      // parseCompleteDataAndFill();
      // completeData

      message.success("表单填充完成！");
    } catch (error) {
      console.error("填充过程中发生错误:", error);
      message.error("填充失败，请检查数据格式");
    }
  };

  // 处理状态变化
  const handleChange = useCallback(
    (value: any, key: string) => {
      const newState = { ...state, [key]: value };
      setState(newState);
      setGetState(newState);
    },
    [state, setGetState],
  );

  // 处理MentionsComponent的查询变化
  const handleQueryChange = useCallback(
    (val: string) => {
      handleChange(val, "content");
    },
    [handleChange],
  );

  return (
    <div className="form-filling-container">
      <div className="form-filling-hearder">
        <div className="left">快速填充</div>
        <Button
          type="primary"
          style={{}}
          size="small"
          onClick={() => {
            const newShowPanel = !showFormPanel;
            setShowFormPanel(newShowPanel);
            // 向主页面发送消息控制表单检测面板，并触发表单数据提取
            window.postMessage(
              {
                type: "TOGGLE_FORM_DETECTOR",
                show: newShowPanel,
                extractFormData: true, // 新增标志，表示需要提取表单数据
              },
              "*",
            );
          }}
        >
          一键提取
        </Button>
      </div>

      <div className="form-filling-content">
        <div className="form-input-section">
          <MentionsComponent
            ref={mentionsRef}
            agentId={state.bearer}
            formData={formData}
            onQueryChange={handleQueryChange}
            selectedfrom={selectedfrom}
            setSelectedfrom={setSelectedfrom}
            setLocalFileUploadResData={setLocalFileUploadResData}
          />
          <Flex align="center" justify="center" style={{ margin: "16px 0 0 0" }}>
            <Button type="primary" style={{ width: "100%" }} onClick={handleProcess}>
              处理
            </Button>

            {isFilling && (
              <Button type="primary" danger onClick={handleStopFilling} style={{ minWidth: "60px", marginLeft: "8px" }}>
                停止
              </Button>
            )}
          </Flex>
        </div>
        {/* AI输出区域 */}
        <Flex
          className="result"
          vertical
          style={{ height: contentHeight }} // 移除transition动画，避免抖动
          ref={resultDomRef}
        >
          <Flex className="result-header" vertical>
            <Divider className="title" orientation="left">
              <Typography.Text className="title-text"> 结果</Typography.Text>
            </Divider>
            {/* {sseChat.progress != 0 && sseChat.isRendering == false ? (
              <Flex className="right" onClick={handleStop}>
                <Tooltip
                  placement="top"
                  title="停止生成"
                  getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                >
                  <Button className="side-sub-btn-stop" icon={<PauseOutlined />}>
                    停止生成
                  </Button>
                </Tooltip>
              </Flex>
            ) : null}
            {/* // 分页 */}
            {/* {sseChat.chatList.length > 1 ? (
              <Flex className="res-pagination" justify="end" align="center">
                <LeftOutlined className="res-pagination-btn" onClick={handlePre} />
                <Flex className="res-pagination-text">
                  {number}/{sseChat.chatList.length}
                </Flex>
                <RightOutlined className="res-pagination-btn" onClick={handleNext} />
              </Flex>
            ) : null} */}
          </Flex>
          <Flex
            className="side-write-result"
            vertical
            style={{ height: "100%", overflow: "hidden" }}
            ref={parentContainerRef}
          >
            {sseChat.progress !== GenerationProgress.INITIALIZED && (
              <>
                {/* 上部分：格式化内容容器 */}
                {stableFormattedData && (
                  <div
                    ref={formattedContentRef}
                    className="formatted-content-container"
                    style={{
                      flex: sseChat.progress === GenerationProgress.RENDER_FINISHED ? "1" : "none",
                      height: "auto",
                      maxHeight:
                        sseChat.progress !== GenerationProgress.RENDER_FINISHED ? calculateHeights.topHeight : "none",
                      overflow: "auto",
                      backgroundColor: "#f8f9fa",
                      padding: "12px",
                      paddingBottom: 0,
                    }}
                  >
                    <AgentOutput
                      content={accumulatedFormattedData || stableFormattedData}
                      finished={sseChat.progress === GenerationProgress.RENDER_FINISHED}
                      role="assistant"
                    />
                  </div>
                )}

                {/* 下部分：原始数据容器 */}
                {sseChat.progress !== GenerationProgress.RENDER_FINISHED && (
                  <div
                    className="raw-data-container"
                    style={{
                      height: `${calculateHeights.bottomHeight}px`,
                      overflow: "auto",
                      backgroundColor: "#f8f9fa",
                      padding: "32px",
                      fontFamily: "monospace",
                      fontSize: "12px",
                      lineHeight: "1.4",
                      whiteSpace: "pre-wrap",
                      wordBreak: "break-word",
                    }}
                    ref={rawDataRef}
                  >
                    {sseChat.displayedText || "正在接收数据..."}
                  </div>
                )}

                {sseChat.progress === GenerationProgress.RENDER_FINISHED ? (
                  <Flex justify="center" style={{ margin: "16px 0 0 0" }}>
                    <Button type="primary" onClick={handleFillForm}>
                      一键填充
                    </Button>
                  </Flex>
                ) : null}
              </>
            )}
          </Flex>
        </Flex>
      </div>
    </div>
  );
};

export default FormFilling;
