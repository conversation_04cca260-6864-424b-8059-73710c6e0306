.form-filling-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  .form-filling-hearder {
    position: absolute;
    width: 90%;
    top: 13px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      color: transparent;
      background: linear-gradient(116deg, #1888ff 16%, #2f54eb 88%);
      -webkit-background-clip: text;
      user-select: none;
      line-height: var(--ant-line-height-heading-2);
      font-family: AlibabaPuHuiTi_2_85_Bold;
      font-size: var(--ant-font-size-heading-4);
    }
  }
  .form-filling-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;

    .form-input-section {
      flex-shrink: 0; // 不允许收缩
    }

    .result {
      display: flex;
      flex-direction: column;
      flex: 1;
      min-height: 0; // 允许flex子元素收缩

      .result-header {
      }
      .side-write-result {
        flex: 1;
        min-height: 300px; // 设置最小高度避免抖动
        // 扫描动画样式
        display: flex;
        // justify-content: center;
        flex-flow: column;
        .agent-output-container {
          position: relative;
          overflow: auto;
          height: 100%;
          // min-height: 200px; // 为输出容器设置最小高度
          padding: 16px;
          background: #f8f9fa;
          border-radius: 8px;
          // 防止内容变化时的布局跳动
          contain: layout style;

          .scan-line {
            position: absolute;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(
              90deg,
              transparent 0%,
              rgba(24, 144, 255, 0.3) 20%,
              #1890ff 50%,
              rgba(24, 144, 255, 0.3) 80%,
              transparent 100%
            );
            box-shadow:
              0 0 15px #1890ff,
              0 0 30px rgba(24, 144, 255, 0.4),
              0 0 45px rgba(24, 144, 255, 0.2);
            transition: top 0.02s linear; // 更快更平滑的过渡
            z-index: 1000;
            animation: scanGlow 0.5s ease-in-out infinite alternate;

            &::before {
              content: "";
              position: absolute;
              top: -2px;
              left: 0;
              right: 0;
              height: 7px;
              background: linear-gradient(
                90deg,
                transparent 0%,
                rgba(24, 144, 255, 0.2) 30%,
                rgba(24, 144, 255, 0.4) 50%,
                rgba(24, 144, 255, 0.2) 70%,
                transparent 100%
              );
              filter: blur(2px);
            }
          }

          // 扫描发光动画
          @keyframes scanGlow {
            0% {
              box-shadow:
                0 0 15px #1890ff,
                0 0 30px rgba(24, 144, 255, 0.4),
                0 0 45px rgba(24, 144, 255, 0.2);
            }
            100% {
              box-shadow:
                0 0 20px #1890ff,
                0 0 40px rgba(24, 144, 255, 0.6),
                0 0 60px rgba(24, 144, 255, 0.3);
            }
          }

          .custom-stream-output {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            white-space: pre-wrap;
            word-break: break-word;
          }

          .cursor-blink {
            animation: blink 1s infinite;
            color: #1890ff;
            font-weight: bold;
          }

          @keyframes blink {
            0%,
            50% {
              opacity: 1;
            }
            51%,
            100% {
              opacity: 0;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .form-filling-container {
    .form-filling-content {
      padding: 12px;

      .input-section,
      .result-section {
        .ant-card-body {
          padding: 16px;
        }
      }
    }
  }
}
