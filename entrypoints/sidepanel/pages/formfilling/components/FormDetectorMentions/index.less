.formDetector-mentions-components {
  position: relative;
  width: 100%;
  // border: 1px solid #cecfcb;
  border-radius: 11px;
  .ant-mentions {
    border: none;
  }
  .ant-mentions-outlined:focus {
    border: none !important;
  }
  .ant-mentions-outlined:focus-within {
    border: none !important;
  }
  .ant-mentions-focused {
    border: none !important;
  }
  .knowledge-base {
    margin-right: var(--ant-margin-sm);
    margin-top: var(--ant-margin-xs);
    box-sizing: border-box;
    flex: 1;
    position: relative;
    .knowledge-base-info {
      gap: var(--ant-margin-xxs);
      overflow-x: auto;
      flex: 1;
    }
    .right-icon,
    .left-icon {
      position: absolute;
      // top: var(--ant-padding-sm);
      width: 25px;
      height: 52px;
    }
    .right-icon {
      right: 0px;
      background: linear-gradient(270deg, #ffffff 36%, rgba(255, 255, 255, 0.18) 67%, rgba(255, 255, 255, 0) 100%);
    }
    .left-icon {
      left: 0px;
      background: linear-gradient(270deg, rgba(255, 255, 255, 0) 36%, rgba(255, 255, 255, 0.18) 67%, #ffffff 100%);
    }
    .left,
    .right {
      min-width: 0px;
      width: 20px;
      height: 20px;
      position: absolute;
      font-size: var(--ant-font-size);
      border: none;
      z-index: 10;
      background: var(--ant-color-bg-base);
      box-shadow:
        0px 2.5px 5px -1px rgba(0, 0, 0, 0.12),
        0px 5px 6.25px 0px rgba(0, 0, 0, 0.08),
        0px 1.25px 12.5px 0px rgba(0, 0, 0, 0.05);
      .anticon {
        font-size: var(--ant-font-size);
        color: var(--ant-color-text);
      }
    }
    .left {
      left: 2px;
      top: 50%;
      transform: translateY(-50%);
    }
    .right {
      right: 2px;
      top: 50%;
      transform: translateY(-50%);
    }
    .knowledge-load {
      width: 24px;
      height: 48px;
      margin-right: var(--ant-margin-xs);
      margin > div {
        align-items: center;
      }
    }

    .sino-relation-icon {
      color: #813ce0;
      padding-top: 1.5px;
    }

    .extend-icon {
      width: 24px;
      height: 24px;
    }
    .knowledge-content-base {
      background: var(--ant-color-fill-tertiary);
      border-radius: var(--ant-border-radius);
      padding: 0px var(--ant-padding-xs);
      box-sizing: border-box;
      height: 52px;
      flex: 1;
      max-width: 240px;
      &:hover {
        .close {
          display: block;
        }
      }
    }
    .knowledge-content-base-flex {
      // width: calc(100% - 28px);
      position: relative;
      gap: var(--ant-margin-xxs);
      .close {
        z-index: 10;
        background: #fff;
        color: var(--ant-color-text-tertiary);
        font-size: var(--ant-font-size-lg);
        position: absolute;
        right: -4px;
        top: 4px;
        border-radius: 50%;
        display: none;
      }
    }
    .knowledge-content-base-item {
      white-space: nowrap;
      // width: 94%;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .knowledge-content-base-width {
      width: 108px !important;
      max-width: 108px !important;
    }
    .knowledge-base-item {
      width: 100%;
      white-space: nowrap;
      width: 90%;
      margin-left: var(--ant-margin-xs);
      overflow: hidden;
      text-overflow: ellipsis;
      height: 52px;
    }

    .knowledge-base-title {
      font-size: var(--ant-font-size);
      color: var(--ant-color-text);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .first-title {
      padding-left: 4px;
    }

    .two-title {
      font-size: var(--ant-font-size-sm);
      color: var(--ant-color-text-tertiary);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .text-input {
    width: 100%;
    border: 1px solid var(--ant-color-border);
    border-radius: var(--ant-border-radius-lg);
    position: relative;
    &:hover {
      border-color: var(--ant-color-primary-hover);
    }
    &:focus {
      border-color: var(--ant-color-primary-active);
    }
  }
  .text-input-mentions {
    border-radius: var(--ant-border-radius-lg);
    height: 108px;
    padding: var(--ant-padding-sm);
    border: 0px;
    &:focus,
    &:focus-within {
      box-shadow: none;
    }
    > textarea {
      padding: 0px;
      &:focus {
        box-shadow: none;
        border: none;
      }
    }
  }
}

@import "./Knowledge.less";
