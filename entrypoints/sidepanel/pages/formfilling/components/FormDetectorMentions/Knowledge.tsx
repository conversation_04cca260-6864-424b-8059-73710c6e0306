import React, { useRef, useState, useEffect } from "react";
import type { CheckboxProps, InputRef } from "antd";
import { Checkbox, Flex, Input, Modal, Spin, Empty, message } from "antd";
import "./Knowledge.less";
import { getContainer } from "@/utils";
import { DatabaseTwoTone, SearchOutlined } from "@ant-design/icons";
import classNames from "classnames";

interface CardData {
  id: number | string;
  libName: string;
  libDesc: string;
  checked: boolean;
}

interface Props {
  cardData: CardData[];
  knowledModel: boolean;
  knowledLoading?: boolean;
  keywordValue: string;
  closeKnowledModel: (boolean, string) => void;
  keywordSearch: (keyword) => void;
  onCheckboxChange: (string, boolean) => void;
  closeKnowledModelFalg: (boolean) => void;
  container?: () => HTMLElement | null; // 可选的容器函数
  knowledgeContents: Record<string, any[]>;
  loadingKnowledgeIds: Set<string>;
  expandedKnowledgeIds: Set<string>;
  fetchKnowledgeContent: (baseId: string) => void;
  setKnowledgeContents: React.Dispatch<React.SetStateAction<Record<string, any[]>>>;
  setExpandedKnowledgeIds: React.Dispatch<React.SetStateAction<Set<string>>>;
  selectedKnowledgeItem: any;
  setSelectedKnowledgeItem: any;
}

const Knowledge: React.FC<Props> = ({
  cardData,
  knowledModel,
  knowledLoading = false,
  closeKnowledModel,
  keywordSearch,
  keywordValue,
  onCheckboxChange,
  closeKnowledModelFalg,
  container,
  knowledgeContents,
  loadingKnowledgeIds,
  expandedKnowledgeIds,
  fetchKnowledgeContent,
  setKnowledgeContents,
  setExpandedKnowledgeIds,
  selectedKnowledgeItem,
  setSelectedKnowledgeItem,
}) => {
  const [selectKnowdgeId, setSelectKnowdgeId] = useState<any>(
    cardData.filter((item) => item.checked).map((item) => item.id),
  );
  // 选中的知识项
  const inputRef = useRef<InputRef>(null);

  const handlerSubmit = () => {
    closeKnowledModel(false, selectKnowdgeId);
  };
  const closeMoal = () => {
    closeKnowledModelFalg(false);
  };
  const onChange = (e: any, item: any) => {
    if (e.target.checked) {
      // 单选模式：清除之前选择的知识库
      if (selectKnowdgeId.length > 0) {
        // 清除之前选择的知识库内容和展开状态
        selectKnowdgeId.forEach((prevId: any) => {
          setKnowledgeContents((prev) => {
            const newContents = { ...prev };
            delete newContents[String(prevId)];
            return newContents;
          });
          setExpandedKnowledgeIds((prev) => {
            const newSet = new Set(prev);
            newSet.delete(String(prevId));
            return newSet;
          });
          // 通知父组件取消之前的选择
          onCheckboxChange(prevId, false);
        });
      }

      // 设置新选择的知识库
      setSelectKnowdgeId([item.id]);
      // 勾选时获取知识内容
      fetchKnowledgeContent(String(item.id));
      setExpandedKnowledgeIds((prev) => new Set(prev).add(String(item.id)));
    } else {
      // 取消勾选
      setSelectKnowdgeId([]);
      // 取消勾选时清除知识内容和展开状态
      setKnowledgeContents((prev) => {
        const newContents = { ...prev };
        delete newContents[String(item.id)];
        return newContents;
      });
      setExpandedKnowledgeIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(String(item.id));
        return newSet;
      });
    }

    onCheckboxChange(item.id, e.target.checked);
  };
  const inputChange = (e) => {
    keywordSearch(e.target.value.trim());
  };

  return (
    <div className="pagesider-form-detector-char-knowledge-content">
      {/* 知识库详情 */}
      <div className="pagesider-form-detector-content-box">
        {/* 文件知识库 */}
        <Modal
          title="选择知识库"
          centered={false}
          width={300}
          getContainer={getContainer}
          open={knowledModel}
          onOk={handlerSubmit}
          onCancel={closeMoal}
          okText="确认"
          cancelText="取消"
          transitionName={null}
          maskTransitionName={null}
        >
          <>
            <Flex className="pagesider-chat-search-input ">
              <Input
                ref={inputRef}
                value={keywordValue}
                onChange={inputChange}
                allowClear
                prefix={<SearchOutlined />}
                placeholder="搜索知识库"
                style={{ width: "100%" }}
              />
            </Flex>
            <Spin spinning={knowledLoading} size="default">
              <Flex className="pagesider-modal-know-wcl" vertical>
                {cardData &&
                  cardData.map((item, index) => {
                    const isExpanded = expandedKnowledgeIds.has(String(item.id));
                    const isLoading = loadingKnowledgeIds.has(String(item.id));
                    const knowledgeList = knowledgeContents[String(item.id)] || [];
                    console.log("knowledgeList", knowledgeList);
                    return (
                      <Flex
                        key={index}
                        vertical
                        className={classNames("pagesider-cardBox-wcl", item.checked ? "pagesider-active" : "")}
                      >
                        <Checkbox checked={item.checked} onChange={(e) => onChange(e, item)}>
                          <Flex className="pagesider-top" justify="center" align="center">
                            <Flex className="pagesider-icon-card">
                              <DatabaseTwoTone />
                            </Flex>
                            <Flex className="pagesider-left-gas" vertical>
                              <Flex>
                                <Flex className="pagesider-first-title">{item.libName}</Flex>
                              </Flex>
                              <Flex className="pagesider-two-title">{item.libDesc}</Flex>
                            </Flex>
                          </Flex>
                        </Checkbox>

                        {/* 显示知识内容 */}
                        {item.checked && isExpanded && (
                          <Flex
                            vertical
                            style={{
                              marginTop: 8,
                              marginLeft: 24,
                              padding: 12,
                              backgroundColor: "#fafafa",
                              borderRadius: 6,
                              border: "1px solid #e8e8e8",
                            }}
                          >
                            {isLoading ? (
                              <Spin size="small" />
                            ) : knowledgeList.length > 0 ? (
                              <Flex vertical gap={6}>
                                <div style={{ fontSize: 13, color: "#666", marginBottom: 6, fontWeight: 500 }}>
                                  知识内容：
                                </div>
                                <div style={{ maxHeight: "300px", overflowY: "auto", paddingRight: "4px" }}>
                                  {knowledgeList.map((knowledge, idx) => (
                                    <Flex
                                      key={idx}
                                      style={{
                                        padding: "8px 12px",
                                        backgroundColor: "#fff",
                                        borderRadius: 4,
                                        border: "1px solid #f0f0f0",
                                        cursor: "pointer",
                                        marginBottom: "6px",
                                      }}
                                    >
                                      <Checkbox
                                        style={{ marginRight: 8 }}
                                        checked={selectedKnowledgeItem?.id === knowledge.id}
                                        onChange={(e) => {
                                          // 处理知识项的单选逻辑
                                          if (e.target.checked) {
                                            setSelectedKnowledgeItem(knowledge);
                                            console.log("knowledge", knowledge);
                                          } else {
                                            setSelectedKnowledgeItem(null);
                                          }
                                        }}
                                      >
                                        <Flex vertical>
                                          <div style={{ fontSize: 13, color: "#333", fontWeight: 500 }}>
                                            {knowledge.title || knowledge.name || "未命名知识"}
                                          </div>
                                          {knowledge.description && (
                                            <div style={{ fontSize: 11, color: "#999", marginTop: 2 }}>
                                              {knowledge.description}
                                            </div>
                                          )}
                                        </Flex>
                                      </Checkbox>
                                    </Flex>
                                  ))}
                                </div>
                              </Flex>
                            ) : (
                              <Empty
                                image={Empty.PRESENTED_IMAGE_SIMPLE}
                                description="暂无知识内容"
                                style={{ margin: 0 }}
                              />
                            )}
                          </Flex>
                        )}
                      </Flex>
                    );
                  })}
              </Flex>
            </Spin>
          </>
        </Modal>
      </div>
    </div>
  );
};

export default React.memo(Knowledge);
