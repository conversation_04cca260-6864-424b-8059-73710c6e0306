/** 我的提示词收藏页面 */
import React, { useEffect, useState } from "react";
import EmptyData from "@/components/EmptyData";
import PromptCard from "@/entrypoints/sidepanel/pages/prompt/components/PromptCard";
import { Prompt, PromptSearchParam } from "@/types/prompt";
import { useFetchRequest } from "@/hooks/useFetchRequest.ts";
import "./index.less";

// 列表查询的初始化参数
const searchParamsInit: PageAPIRequest<PromptSearchParam> = {
  pageNum: 1,
  pageSize: 10,
  entity: {
    title: "",
    status: 0,
    collection: true,
    type: "prompt_type_created",
    agentId: 1,
  },
};
// 列表数据的初始化数据
const pageDataInit: PageAPIResponse<Prompt> = {
  current: 1,
  size: 10,
  total: 0,
  pages: 0,
  records: [],
  count: "",
};

const PersonalCollectionPage: React.FC<{ context }> = ({ context }) => {
  // 父组件传来的搜索参数
  const outletContext: string = context;
  // 查询数据的请求参数
  const [searchParams, setSearchParams] = useState<PageAPIRequest<PromptSearchParam>>(searchParamsInit);
  // 当前显示的提示词分页数据
  const [promptPageData, setPromptPageData] = useState<PageAPIResponse<Prompt>>(pageDataInit);
  const fetchRequest = useFetchRequest();
  // 监听搜索参数变化
  // useEffect(() => {
  //   setSearchParams({
  //     ...searchParams,
  //     entity: {
  //       ...searchParams.entity,
  //       title: outletContext,
  //     },
  //   });
  // }, [outletContext]);

  // 组件初次渲染或者请求参数变更时，调用接口更新数据
  useEffect(() => {
    handlePromptList();
  }, [outletContext]);

  /** 分页查询我的提示词 */
  const handlePromptList = async () => {
    let params = {
      ...searchParams,
      entity: {
        ...searchParams.entity,
        title: outletContext,
      },
    };
    await fetchRequest({
      api: "pagePrompts",
      params: searchParams,
      callback: (res) => {
        if (res.code === 200) {
          setPromptPageData(res.data);
        }
      },
    });
  };

  return (
    <div className="personal-collection-list prompt-list">
      {(!promptPageData || promptPageData.total === 0) && <EmptyData description={"没有找到收藏的提示词"} />}
      {promptPageData?.records.map((item: Prompt, index: number) => {
        return (
          <PromptCard
            key={index}
            type="collection"
            prompt={item}
            editable={true}
            releasable={true}
            onSubmitSuccess={handlePromptList}
          />
        );
      })}
    </div>
  );
};

export default PersonalCollectionPage;
