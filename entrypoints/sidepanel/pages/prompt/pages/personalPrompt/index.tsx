/** 我的提示词页面 */
import React, { useEffect, useRef, useState } from "react";
import EmptyData from "@/components/EmptyData";
import PromptCard from "@/entrypoints/sidepanel/pages/prompt/components/PromptCard";
import { Prompt, PromptSearchParam } from "@/types/prompt";
import { useFetchRequest } from "@/hooks/useFetchRequest.ts";
import { Button, Flex, Image, Spin, Tag, Typography, message } from "antd";
import { formatDate } from "@/utils/dateFormat";
import { useDebounceFn } from "ahooks";
import { PlusOutlined } from "@ant-design/icons";
import "./index.less";
import { pagePrompts } from "@/api/prompt.ts";
import PromptModal from "../../components/PromptModal";
// import InfiniteScroll from "react-infinite-scroll-component";
// 列表查询的初始化参数
const searchParamsInit: PageAPIRequest<PromptSearchParam> = {
  pageNum: 1,
  pageSize: 500,
  entity: {
    title: "",
    status: 0,
    collection: true,
    type: "prompt_type_created_coll",
    agentId: "",
    // createBy: JSON.parse(localStorage.getItem("user"))?.id,
  },
};

// 列表数据的初始化数据
const pageDataInit: PageAPIResponse<Prompt> = {
  current: 1,
  size: 10,
  total: null,
  pages: 0,
  records: [],
  count: 0,
};

const PersonalPromptPage: React.FC<{ context }> = ({ context }) => {
  const scrollableRef = useRef(null);
  console.log("context", context);
  // 父组件传来的搜索参数
  const outletContext: string = context.searchText;
  const promptType: string = context.promptType;
  const agentId: string = context.selectedAgent?.id;
  const agentList: Array<Prompt> = context.agentList || []; // 代理列表
  // 查询数据的请求参数
  const [searchParams, setSearchParams] = useState<PageAPIRequest<PromptSearchParam>>(searchParamsInit);
  // 当前显示的提示词分页数据
  const [promptPageData, setPromptPageData] = useState<PageAPIResponse<Prompt>>(pageDataInit);
  const [loading, setLoading] = useState(false);
  const fetchRequest = useFetchRequest();

  // 弹窗显隐状态
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  // 当前选中的提示词
  const [currentPrompt, setCurrentPrompt] = useState<Prompt>(null);
  // 监听搜索参数变化
  // useEffect(() => {
  //   setSearchParams({
  //     ...searchParams,
  //     entity: {
  //       ...searchParams.entity,
  //       title: outletContext,
  //     },
  //   });
  // }, [outletContext]);

  // 组件初次渲染或者请求参数变更时，调用接口更新数据
  useEffect(() => {
    setSearchParams({
      pageNum: 1,
      pageSize: 500,
      entity: {
        ...searchParams.entity,
        title: outletContext,
        type: promptType,
        agentId: agentId,
      },
    });
    handlePromptList();
  }, [outletContext, promptType, agentId]);

  // useEffect(() => {
  //   handlePromptList();
  // }, []);

  const { run: handlePromptList } = useDebounceFn(
    () => {
      let params = {
        ...searchParams,
        entity: {
          ...searchParams.entity,
          query: outletContext,
          type: promptType,
          agentId: agentId,
        },
      };

      setLoading(true);
      fetchRequest({
        api: "pagePrompts",
        params,
        callback: (res) => {
          setLoading(false);
          if (res.code === 200) {
            setPromptPageData(res.data);
          }
        },
      });
    },
    { wait: 500 },
  );

  /** 创建提示词 */
  const createPrompt = () => {
    console.log("createPrompt");
    setCurrentPrompt(null);
    setModalOpen(true);
  };
  /** 卡片编辑提示词 */
  const handleEditClick = (prompt: Prompt) => {
    setModalOpen(true);
    setCurrentPrompt(prompt);
  };
  return (
    <>
      <div className="personal-prompt-list">
        <Spin spinning={loading} size="default">
          {!promptPageData.records || promptPageData.records.length === 0 ? (
            <EmptyData description={"没有找到提示词"} />
          ) : (
            <>
              <Flex className="card-content" vertical>
                {promptPageData.records.map((item: Prompt, index: number) => {
                  return (
                    <PromptCard
                      key={index}
                      type={promptType}
                      prompt={item}
                      deletable={true}
                      editable={true}
                      releasable={true}
                      onEdit={handleEditClick}
                      onSubmitSuccess={handlePromptList}
                      // collHandler={collHandlerLoading}
                    ></PromptCard>
                  );
                })}
              </Flex>
              {/*  </InfiniteScroll> */}
            </>
            // </div>
          )}
          <Flex className="btn">
            <Button block size="large" icon={<PlusOutlined />} className="submit-btn" onClick={createPrompt}>
              创建提示词
            </Button>
          </Flex>
        </Spin>
        {/* 新增/编辑提示词的模态框表单 */}
        <PromptModal
          open={modalOpen}
          setOpen={setModalOpen}
          agentPar={agentList || []}
          prompt={currentPrompt}
          onSubmitSuccess={handlePromptList}
        />
      </div>
    </>
  );
};

export default React.memo(PersonalPromptPage);
