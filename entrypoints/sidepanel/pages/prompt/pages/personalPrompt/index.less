.personal-prompt-list {
  overflow-y: auto;
  height: calc(100vh - 260px);
  display: flex;
  // position: absolute;
  // top: 150px;
  flex-direction: column;
  .ant-card .ant-card-head {
    padding: 0 24px !important;
    .ant-card-head-wrapper {
      height: 38px !important;
    }
    .ant-card-head-title {
      padding: 10px 0px;
    }
  }
  .card-content {
    width: 100%;
    &:first-child {
      margin-top: 0 !important;
    }

    .card-content-box {
      margin-bottom: var(--ant-margin);
      background-color: var(--ant-color-fill-quaternary);
      padding: var(--ant-padding);
      border-radius: var(--ant-border-radius);
    }
    .title {
      font-size: var(--ant-font-size-lg);
      font-weight: var(--ant-font-weight-strong);
      color: var(--ant-color-text-base);
      height: 28px;
      line-height: 28px;
      padding-left: var(--ant-padding-xs);
      flex-grow: 1;
      display: inline-block; /* 确保文本内容在同一行 */
      white-space: nowrap; /* 不换行 */
      overflow: hidden; /* 隐藏溢出内容 */
      text-overflow: ellipsis; /* 显示省略号 */
      margin-right: 8px;
    }

    .prompt-card-title {
      overflow: hidden; /* 隐藏溢出内容 */
    }
    .prompt-card-tag {
      padding: var(--ant-padding-xxs) var(--ant-padding-sm);
      margin-right: 0;
    }
  }
  .prompt-card-content {
    margin-top: var(--ant-margin-xxs);
    max-height: 66px;
    line-height: var(--ant-line-height);
  }
  .ant-typography {
    margin-bottom: 0 !important;
  }
  .prompt-card-text {
    margin-top: var(--ant-margin-sm);
    .prompt-card-text-left {
      color: var(--ant-color-text);
      font-size: var(--ant-font-size-sm);
      margin-left: var(--ant-margin-sm);
    }
    .prompt-card-text-right {
      color: var(--ant-color-text-quaternary);
      font-size: var(--ant-font-size-sm);
    }
    .prompt-img {
      border-radius: 50%;
    }
  }
  .medal-img {
    height: 32px;
  }
  .btn {
    position: fixed;
    bottom: var(--ant-padding);
    left: var(--ant-padding);
    right: var(--ant-padding);
    z-index: 100;
    .submit-btn {
      // border-radius: var(--ant-border-radius);
    }
  }
}
.ant-spin-nested-loading {
  height: 100%;
}
