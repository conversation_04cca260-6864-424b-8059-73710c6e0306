/** 提示词页面 */
import React, { useEffect, useState, useRef, useCallback } from "react";
import { TabsProps, Input, Flex, message, Tag, Tabs, Segmented } from "antd";
// import Search from "antd/es/input/Search";
import PersonalPromptPage from "@/entrypoints/sidepanel/pages/prompt/pages/personalPrompt";
import { SearchOutlined, DownOutlined, UpOutlined } from "@ant-design/icons";
import TopTitle from "../../components/TopTitle";
import "./index.less";
import { useFetchRequest } from "@/hooks/useFetchRequest";

const PromptPage: React.FC = () => {
  const fetchRequest = useFetchRequest();
  // 路由hook
  const [currentIndex, setCurrentIndex] = useState("1");
  let userId = JSON.parse(localStorage.getItem("user"))?.id || "";
  // 组件加载默认去第一个页签
  useEffect(() => {
    userId = JSON.parse(localStorage.getItem("user"))?.id;
  }, []);
  const [searchIcon, setSearchIcon] = useState(true);
  // 搜索参数
  const [searchText, setSearchText] = useState<string>("");

  // 当前选中的 agent 标签
  // agent 列表数据
  const [agentList, setAgentList] = useState<Array<Prompt>>([]);
  const [selectedTags, setSelectedTags] = React.useState<string[]>([]);
  const selectedAgent = agentList.find((item) => item.agentName === selectedTags[0]);

  const [expanded, setExpanded] = useState(false); // 控制是否展开

  useEffect(() => {
    getAllAgentPageHandler();
  }, []);
  /** 获取 agent 列表 */
  const getAllAgentPageHandler = () => {
    // 获取代理ID
    fetchRequest({
      api: "listAgents",
      params: {},
      callback: async (res) => {
        if (res.data && res.data.length > 0) {
          setAgentList(res.data);
          setSelectedTags(["通用"]); // 默认选中第一个
        }
      },
    });
  };
  const handleChange = (item) => {
    setSelectedTags([item.agentName]);
    setExpanded(false);
  };

  const inputChange = (e) => {
    const value = e.target.value.trim();
    if (!value) {
      setSearchIcon(true);
    }
    setSearchText(value);
  };

  const handleTabChange = (targetKey: string) => {
    setCurrentIndex(targetKey);
  };
  const visibleCount = 2;
  const visibleAgents = expanded
    ? [{ id: new Date().getTime(), agentName: "通用" }, ...agentList]
    : [{ id: new Date().getTime(), agentName: "通用" }, ...agentList.slice(0, visibleCount)];
  return (
    <Flex className="prompt-page-container" vertical>
      <TopTitle title="提示词"></TopTitle>
      {/* 搜索框 */}
      <Input
        size="large"
        placeholder="搜索提示词"
        prefix={<SearchOutlined />}
        value={searchText}
        onChange={inputChange}
        allowClear
      />
      <Segmented
        style={{ marginTop: "10px" }}
        options={[
          { label: "我创建", value: "1" },
          { label: "收藏", value: "2" },
          { label: "广场", value: "3" },
        ]}
        value={currentIndex}
        onChange={(value) => handleTabChange(value as string)}
      />
      {/* Agent 标签选择 */}
      <Flex className="agent-list-cue" wrap="wrap" gap={8} justify="space-between" align="center">
        <Flex wrap="wrap" gap={8}>
          {Array.isArray(visibleAgents) &&
            visibleAgents.map((item, index) => (
              <Tag.CheckableTag
                key={index}
                checked={selectedTags.includes(item.agentName)}
                onChange={() => handleChange(item)}
              >
                {item.agentName}
              </Tag.CheckableTag>
            ))}
        </Flex>

        {/* 展开/收起按钮 */}
        {agentList.length > visibleCount && (
          <div
            className="expand-toggle"
            style={{ marginLeft: "auto", cursor: "pointer" }}
            onClick={() => setExpanded(!expanded)}
          >
            {expanded ? <UpOutlined /> : <DownOutlined />}
            {/* 你可以替换成图标组件，例如 Ant Design 的 DownOutlined / UpOutlined */}
          </div>
        )}
      </Flex>

      {/* 提示词列表 */}
      <PersonalPromptPage
        context={{
          searchText: searchText,
          selectedAgent,
          promptType:
            currentIndex === "1"
              ? "prompt_type_created"
              : currentIndex === "2"
                ? "prompt_type_coll"
                : "prompt_type_square",
          agentList: agentList,
        }}
      />
    </Flex>
  );
};

export default PromptPage;
