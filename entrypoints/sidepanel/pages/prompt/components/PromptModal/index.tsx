/** 新增/编辑提示词弹窗组件 */
import { Button, Checkbox, Flex, Form, Input, message, Modal, Select, Tooltip } from "antd";
import React, { Dispatch, useEffect, useState } from "react";
import { Prompt } from "@/types/prompt";
import { getContainer } from "@/utils";
import "./index.less";

/** 提示词弹窗组件属性 */
interface agentObj {
  label: string;
  value: string | number;

  [key: string]: any;
}

export type PromptModalProps = {
  /** 非受控弹窗开启标识 */
  open: boolean;
  /** 非受控弹窗开启状态设置方法 */
  setOpen: Dispatch<boolean>;
  /** 提示词数据，为空则是新增场景 */
  prompt?: Prompt;
  /** 提交成功后的回调 */
  onSubmitSuccess: () => void;
  agentPar: Array<agentObj>;
};

/** 表单字段类型 */
export type PromptFormFieldType = {
  title?: string;
  content?: string;
  id: string;
  agentId: string;
  remark: string;
  type: string;
};

const PromptModal: React.FC<PromptModalProps> = ({ open, setOpen, prompt, onSubmitSuccess, agentPar }) => {
  // 新增/修改提示词表单的引用
  const [form] = Form.useForm();
  // 帮助弹窗显隐状态
  console.log("open", open);
  const [tipOpen, setTipOpen] = useState<boolean>(false);
  const [agentTipOpen, setAgentTipOpen] = useState<boolean>(false);
  const fetchRequest = useFetchRequest();
  const [loading, setLoading] = useState(false);
  const [promptType, setPromptType] = useState("prompt_type_private");
  useEffect(() => {
    if (prompt && open) {
      form.setFieldsValue(prompt);
      if (typeof prompt.type === "string") {
        setPromptType(prompt.type);
      }
    } else {
      // chrome.storage.local.get(["current_ai_appKey"], (result) => {
      //   let agentId = null;
      //   agentPar?.find((item) => {
      //     if (item.appKey == result.current_ai_appKey) {
      //       agentId = item.id;
      //     }
      //   }),
      //     form.setFieldsValue({
      //       content: "",
      //       title: "",
      //       type: "",
      //       remark: "",
      //       agentId: agentId,
      //     });
      // });
      form.setFieldsValue({
        content: "",
        title: "",
        type: "",
        remark: "",
        agentId: null,
      });
    }
  }, [prompt, open]);

  /** 处理提示弹窗点击 */
  const handleTipClick = (event: React.MouseEvent<HTMLImageElement>) => {
    event.stopPropagation();
    setTipOpen(true);
  };

  /** 处理提示弹窗取消 */
  const handleTipCancel = () => {
    setTipOpen(false);
  };

  /** 处理agent提示弹窗点击 */
  const handleAgentTipClick = (event: React.MouseEvent<HTMLImageElement>) => {
    event.stopPropagation();
    setAgentTipOpen(true);
  };

  /** 处理agent提示弹窗取消 */
  const handleAgentTipCancel = () => {
    setAgentTipOpen(false);
  };

  /** 处理新增/编辑提示词弹窗取消 */
  const handleAddModelCancel = () => {
    setOpen(false);
    form.setFieldsValue({
      content: "",
      title: "",
    });
  };

  /** 处理点击内置变量选项 */
  const handleAddContent = (content: string) => {
    const text = form.getFieldValue("content") || "";
    if (text && text.indexOf(content) != -1) {
      message.warning(`内置变量'${content}'已存在！`);
    } else {
      form.setFieldsValue({
        content: text + content,
      });
    }
  };

  /** 新增/修改提示词 */
  const handlePromptSave = (values: any) => {
    setLoading(true);
    const params = values;
    params.type = promptType;
    if (!prompt) {
      fetchRequest({
        api: "addPrompt",
        params,
        callback: (res) => {
          if (res.code == "200") {
            message.open({
              type: "success",
              content: "新增成功！",
            });
            setOpen(false);
            onSubmitSuccess?.();
          } else {
            message.open({
              type: "error",
              content: res.msg,
            });
          }
          setLoading(false);
        },
      });
    } else {
      params.id = prompt.id;
      fetchRequest({
        api: "editPrompt",
        params,
        callback: (res) => {
          setLoading(false);
          if (res.code == "200") {
            message.open({
              type: "success",
              content: "修改成功！",
            });
            setOpen(false);
            onSubmitSuccess?.();
          } else {
            message.open({
              type: "error",
              content: res.msg,
            });
          }
        },
      });
    }
  };
  // const handleChange = (e) => {
  //   const isChecked = e.target.checked;
  // };

  return (
    <>
      <Modal
        title={!prompt ? "新建提示词" : "编辑提示词"}
        getContainer={getContainer}
        open={open}
        centered={true}
        width={"84%"}
        onCancel={handleAddModelCancel}
        footer={null}
        className="setup-model"
        okText="确认"
        cancelText="取消"
      >
        <Form
          name="basic"
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
          style={{ maxWidth: 700 }}
          onFinish={handlePromptSave}
          autoComplete="off"
          form={form}
        >
          <Form.Item<PromptFormFieldType>
            label="提示词名称"
            name="title"
            rules={[
              {
                required: true,
                message: "必填项",
                validator: (_, value) => {
                  if (!value || value.trim() === "") {
                    return Promise.reject(new Error("必填项"));
                  }
                  if (value.trim().length > 10) {
                    return Promise.reject(new Error("标题不能超过10个字"));
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <Input placeholder="请输入提示词名称" showCount maxLength={10} />
          </Form.Item>
          <div className="setup-model-item-tip">
            {/* <img
              src={browser.runtime.getURL("/images/setup/explain.png")}
              onClick={(event) => handleTipClick(event)}
              className="setup-model-btn-explain"
            ></img> */}
            <Flex vertical className="content">
              <Form.Item<PromptFormFieldType>
                label="内容"
                name="content"
                rules={[
                  {
                    required: true,
                    message: "必填项",
                    validator: (_, value) => {
                      if (!value || value.trim() === "") {
                        return Promise.reject(new Error("必填项"));
                      }
                      if (value.trim().length > 500) {
                        return Promise.reject(new Error("不能超过500个字符"));
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <Input.TextArea
                  showCount
                  placeholder="在此处粘贴输入内容"
                  autoSize={{ minRows: 3, maxRows: 5 }}
                  maxLength={500}
                />
              </Form.Item>
              <Form.Item<PromptFormFieldType> label="" labelCol={{ span: 0 }} className="global-btn">
                {/* <Flex className="global-detail-tipBtn">
                  <Tooltip
                    placement="top"
                    title="${content}"
                    color="#4096ff"
                    className="item"
                    getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                  >
                    <Flex className="global-detail-tipBtn-content" onClick={() => handleAddContent("【】")}>
                      点击插入
                    </Flex>
                  </Tooltip>
                </Flex> */}
                示例：请把【】这句话翻译成【】
              </Form.Item>
            </Flex>
          </div>

          <div className="setup-model-item-tip">
            <img
              src={browser.runtime.getURL("/images/setup/explain.png")}
              onClick={(event) => handleAgentTipClick(event)}
              className="setup-model-btn-explain"
              style={{ left: "80px" }}
            ></img>
            <Flex vertical className="content">
              <Form.Item<PromptFormFieldType> label="Agent选择" name="agentId">
                <Select
                  style={{ width: "100%" }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  options={(agentPar || []).map((agent) => ({
                    label: agent.agentName,
                    value: agent.id,
                  }))}
                  allowClear
                  placeholder="请选择"
                />
              </Form.Item>
            </Flex>
          </div>
          <Form.Item<PromptFormFieldType> label="备注" name="remark">
            <Input.TextArea
              placeholder="您可以在此提供提示词的来源说明，以及该提示词的其他语言版本。此外，如果您有任何关于该提示词的拓展想法和需求，请在此进行说明"
              autoSize={{ minRows: 3, maxRows: 5 }}
              showCount
              maxLength={500}
            />
          </Form.Item>
          <Form.Item<PromptFormFieldType> label="" name="type" className="check-tips">
            <Checkbox
              checked={promptType === "prompt_type_public"}
              onChange={(e) => {
                const value = e.target.checked ? "prompt_type_public" : "prompt_type_private";
                e.target.value = value; // 直接改动事件的value
                setPromptType(value); // 更新状态
              }}
            >
              将该提示词发到提示词广场
            </Checkbox>
          </Form.Item>
          <Flex justify="flex-end" className="footer">
            <Button htmlType="button" onClick={handleAddModelCancel} className="setup-model-btn-cancel">
              取消
            </Button>
            <Button type="primary" htmlType="submit" loading={loading} className="setup-model-btn-submit">
              保存
            </Button>
          </Flex>
        </Form>
      </Modal>
      <Modal
        title="如何编写提示词"
        centered={true}
        open={tipOpen}
        getContainer={getContainer}
        onCancel={handleTipCancel}
        footer={null}
        width={"80%"}
        className="setup-model"
      >
        <div className="setup-model-tip-title">提示词中可以包含哪些变量？</div>
        <div className="setup-model-tip-content">
          <span>{`"$${"\u007B"}content${"\u007d"}"`}</span>
          你可以使用这个变量来表示输入或者选中的内容，如果模板中没有包含该变量，输入或选中的内容会被三引号包围并放在模板的最后面
          <br />
        </div>
        <div className="setup-model-tip-title1">例子:</div>
        <div className="setup-model-tip-content">
          请使用以下主题或关键字生成包含标题、章节和小节的大纲。
          <br />
          以 Markdown 格式输出。 只给我输出，别无其他。
          <br />
          主题或关键字：
          <span>{`"$${"\u007B"}content${"\u007d"}"`}</span>
        </div>
      </Modal>
      <Modal
        title="提示"
        centered={true}
        open={agentTipOpen}
        getContainer={getContainer}
        onCancel={handleAgentTipCancel}
        footer={null}
        width={"80%"}
        className="setup-model"
      >
        <div className="setup-model-tip-title">不配置Agent系统将自动分配通用助手，配置Agent后提示词无法发送到广场</div>
      </Modal>
    </>
  );
};

export default PromptModal;
