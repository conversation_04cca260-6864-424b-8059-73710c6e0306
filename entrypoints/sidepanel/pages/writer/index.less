@import "@/assets/styles/variables";

.writer {
  width: 100%;
  overflow: auto;
  scroll-behavior: smooth;
  margin-top: var(--ant-margin-sm);
  .agent-output-container{
    overflow-y: auto;
  }
  .input {
    flex: 1;
    min-width: @side-panel-route-min-width;
  }

  .result {
    flex: 1;
    margin-bottom: var(--ant-margin-sm);
  }

  .side-write-tabs {
    height: 48px;

    cursor: pointer;
    position: relative;
    user-select: none;

    margin-bottom: var(--ant-margin-lg);
    .side-write-tab {
      margin-right: var(--ant-margin-md);
      color: #454545;
      line-height: 48px;
      font-size: var(--ant-font-size-lg);
    }

    .ant-tabs-tab-active {
      color: var(--ant-color-text-base);
      font-weight: var(--ant-font-weight-strong);
      font-size: var(--ant-font-size-xl);
      font-family: @side-panel-font-family-bold;
      font-size: var(--ant-font-size-xl);
      font-weight: var(--ant-font-weight-strong);
      cursor: pointer;
    }
    .ant-tabs-tab-active-bottom {
      width: 40px;
      height: 2px;
      display: block;
      background: @gradientPrimary;
      transition: left 0.3s ease;
    }
  }

  .side-write-reply {
    margin-bottom: var(--ant-margin-sm);
  }

  .side-write-input {
    margin-bottom: var(--ant-margin-sm);

    .input-textarea,
    .reply-textarea {
      flex: 1;
      width: 100%;
      border-radius: var(--ant-border-radius);
    }
  }

  .side-write-title {
    margin-bottom: var(--ant-margin-sm);

    .side-title-icon {
      width: 15px;
      height: 15px;
      margin-right: var(--ant-margin-xxs);
    }

    .side-title-text {
      color: var(--ant-color-text-base);
      font-size: var(--ant-font-size-LG);
      font-weight: var(--ant-font-weight-strong);
      line-height: 20px;
    }
  }
  .side-write-tags {
    font-size: var(--ant-font-size);
    position: relative;
    .absolute-tag{
      position:absolute;
      right:-40px;
      top:0px;
      margin-right: 0px;
    }
    .ant-select-single {
      width: 100%;
    }
    .tag-name {
      background-color: var(--ant-color-fill-quaternary);
      padding: var(--ant-padding-xxs) var(--ant-padding-xs);
      border:none;
    }

    .tag-name.active {
      background-color: var(--ant-color-primary-bg);
      color: var(--ant-color-bg-base);
    }
    .tag-name-item.active-item {
      font-weight: var(--ant-font-weight-strong);
    }
  }
  .side-write-template{
    width: calc(100% - 40px);
  }

  .side-write-format,
  .side-write-tone,
  .side-write-length,
  .side-write-language {
    margin: var(--ant-margin-sm) 0;
  }

  .side-write-submitBtn {
    width: 100%;
    margin-top: var(--ant-margin-sm);
    .side-sub-btn {
      width: 100%;
      font-size: var(--ant-font-size);

      .button-content {
        display: flex;
        align-items: center;
        justify-content: center;
        // width: 100%;

        .point-display-button {
          margin-left: 5px;
          font-size: 12px;

          .icon {
            font-size: 12px;
          }
        }
      }
    }
  }

  .result {
    .result-header {
      margin-bottom: var(--ant-margin-sm);
      .title {
        user-select: none;
        font-family: @side-panel-font-family-bold;
        height: 22px;
        line-height: 22px;
      }
      .title-text {
        font-size: var(--ant-font-size-lg);
        color: var(--ant-color-text);
        font-weight: var(--ant-font-weight-strong);
      }
      .res-pagination {
        font-size: var(--ant-font-size);
        // color: rgba(0, 0, 0, 0.75);
        color: var(--ant-color-text);
        /* 火狐 */
        -moz-user-select: none;
        /* Safari 和 欧朋 */
        -webkit-user-select: none;
        /* IE10+ and Edge */
        -ms-user-select: none;
        /* Standard syntax 标准语法(谷歌) */
        user-select: none;

        .res-pagination-btn {
          // margin: 0 4px;
          cursor: pointer;
          border-radius: var(--ant-border-radius-xs);
          padding: var(--ant-padding-xxs);

          &:hover {
            background-color: #e8e9fb;
          }
        }

        .res-pagination-text {
          line-height: 16px;
        }
      }
      .right {
        z-index:1;
        position: fixed;
        left: 50%;
        bottom: 10%;
        transform: translate(-50%);
        z-index:1;
        .side-sub-btn-stop {
          // border-radius: var(--ant-border-radius-lg);
          border: 1px solid var(--ant-color-border);
          // padding:0 var(--ant-padding-xxs);
          border-radius: 50px;
        }
      }
    }
  }

  .side-write-result {
    overflow-y: auto;
    height: auto;
    margin-bottom: 40px;
  }
  .side-result-btn-icon {
    width: calc(100% - 24px);
    position: fixed;
    bottom: 0;
    background-color: var(--ant-color-bg-container);
    height: 64px;

    .side-result-icon-copy,
    .side-result-icon-afresh {
      width: 20px;
      height: 20px;
      cursor: pointer;
    }

    .side-result-icon-afresh {
      margin-right: 9px;
    }
  }
  .side-result-btn-left {
    position: fixed;
    bottom: var(--ant-margin-lg);
    left: var(--ant-margin-sm);
  }
  .side-sub-btn-agin {
    border-radius: var(--ant-border-radius-sm);
    border: 1px solid var(--ant-color-border);
    background: var(--ant-color-bg-container);

    position: fixed;
    bottom: var(--ant-margin-lg);
    right: var(--ant-margin-sm);
  }
  .write-base-icon {
    font-size: var(--ant-font-size-xl);
    color: var(--ant-color-text);
    margin-right: var(--ant-margin-xs);
  }
  .btn-icon {
    color: var(--ant-color-text);
    font-size: var(--ant-font-size-heading-3);
  }
  .write-rule {
    font-size: var(--ant-font-size-xl);
  }
  .ruler-line {
    border: none;
    min-width: 20px !important;
    width: 20px !important;
  }
}

/* 隐藏垂直滚动条 */
.side-panel-content::-webkit-scrollbar {
  // width: 0;
}
