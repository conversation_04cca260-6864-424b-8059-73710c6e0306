/** 写作助手页面 */
import React, { useCallback, useEffect, useRef, useState } from "react";
import "./index.less";
import {
  Button,
  Divider,
  Drawer,
  Flex,
  Input,
  Select,
  TabsProps,
  Tag,
  theme,
  Tooltip,
  Typography,
  message,
} from "antd";
import { copyText } from "@/utils/clipboard.ts";
import { formatList, langList, lengthList, toneList } from "@/config/options/ai.ts";
import AgentOutput from "@/components/AgentOutput";
import { useGetState } from "ahooks";
import { getUserInfo, UserInfo, getToken, getTenantId } from "@/utils/auth.ts";
import NoteKnowledgeModal from "@/components/NoteGroupModal/noteKnowledge";
import useSSEChat from "@/hooks/useSSEChat.ts";
import classNames from "classnames";
import IconFont from "@/components/IconFont";
import MentionsComponent, { MentionsComponentRef } from "@/components/Mentions";
import {
  UpOutlined,
  CopyOutlined,
  DownOutlined,
  PauseOutlined,
  ProfileOutlined,
  RedoOutlined,
  SendOutlined,
  SettingOutlined,
  SmileOutlined,
  TranslationOutlined,
  LeftOutlined,
  RightOutlined,
} from "@ant-design/icons";
import TopTitle from "../../components/TopTitle";
import { ShortcutKeyEnum } from "@/config/enums/ShortcutKeyEnum";

const { useToken } = theme;
const { TextArea } = Input;

type StateType = {
  formatList: Array<string>;
  toneList: Array<string>;
  lengthList: Array<string>;
  langList: Array<string>;
  customFormat: string;
  customTone: string;
  customLength: string;
  customLang: string;
  selectedTab: string;
  content: string | undefined;
  format: Array<string>;
  tone: Array<string>;
  length: Array<string>;
  lang: Array<string>;
  reply: string | undefined;
  bearer: string;
};

// secret列表
// const bearerList: [string, string] = [
//   import.meta.env["VITE_AI_WRITER_SECRET"],
//   import.meta.env["VITE_AI_REPLY_SECRET"],
// ];

const Writer: React.FC = () => {
  // 当前登录人信息
  const [userInfo, setUserInfo] = useState<UserInfo>({});
  // 文本的复制状态
  const [hasCopy, setHasCopy] = useState<boolean>(false);
  const [selectedTags, setSelectedTags] = React.useState<string[]>(["Movies"]);
  const [contentHeight, setContentHeight] = useState("100px");
  const [bearerList, setBearerList] = useState([]); // 写作，回复的agentId
  const [scrolling, setScrolling] = useState(false);
  const [sendMessageKey, setSendMessageKey] = useState("");
  const [visible, setVisible] = useState(false); // 历史记录弹框是否展示
  const [isLoading, setIsLoading] = useState(false); // 历史记录数据是否记载完成
  const [historyList, setHistoryList] = useState([]); // 历史记录数据
  const [historySearchText, setHistorySearchText] = useState(""); // 历史记录搜索条件
  const listRef = useRef(null); // 滚动区域
  const [init, setInit] = useState(false);
  const [writerPoint, setWriterPoint] = useState(null); // 写作积分
  const [replyPoint, setReplyPoint] = useState(null); // 回复积分
  const stopRequestList = useRef(false);
  const [modalVisible, setModalVisible] = useState(false); // 知识库
  const [noteInfoData, setNoteInfoData] = useState<any>(); // 知识库弹框数据
  const [langDisabled, setlangDisabled] = useState<any>(false); // 语言是否可选
  const mentionsRef = useRef<MentionsComponentRef>(null);
  // 该页面的初始状态
  const [state, setState] = useState<StateType>({
    // 当前选择的tab：1-撰写，2-回复
    selectedTab: "1",
    content: "",
    // 数组
    formatList: [],
    toneList: toneList,
    lengthList: lengthList,
    langList: langList.map((item) => {
      return item.label;
    }),
    // 默认值
    format: "",
    tone: [toneList[0]],
    length: [lengthList[0]],
    lang: [langList[0].label],
    // 自定义
    customFormat: "",
    customTone: "",
    customLength: "",
    customLang: "",
    reply: "",
    bearer: bearerList[0],
  });
  const searchParams = useRef({
    apiKey: "",
    user: "anonymous",
    query: "",
    last_id: "",
  });
  const [showAllFormat, setShowAllFormat] = useState(false);
  const displayedTags = showAllFormat ? state.formatList : state.formatList.slice(0, 3);
  // 多次生成时当前显示的条目
  const [number, setNumber] = useGetState<number>(0);
  const { token: csstoken } = useToken();
  const fetchRequest = useFetchRequest();
  useEffect(() => {
    // 获取写作回复id
    fetchRequest({
      api: "getConfig",
      params: {},
      callback: async (res) => {
        if (res.code == 200) {
          setBearerList([res.data["ai-assistant"]["writing-agent-id"], res.data["ai-assistant"]["reply-agent-id"]]);
          setState({
            ...state,
            bearer: res.data["ai-assistant"]["writing-agent-id"],
          });
        }
      },
    });
    cacheGet(ShortcutKeyEnum.SENDMESSAGEKEY).then((res) => {
      setSendMessageKey(res || "Enter");
    });
    fetchRequest({
      api: "templateList",
      params: { agentType: "writTool" },
      callback: async (res) => {
        if (res.code == "200") {
          let arrShow = [];
          formatList.forEach((item) => {
            arrShow.push({
              templateName: item,
            });
          });
          res.data.forEach((item) => {
            if (item.showFlag != 0) {
              arrShow.push(item);
            }
          });
          setState((prev) => ({
            ...prev,
            formatList: arrShow,
            format: [arrShow[0]?.templateName] || [], // 默认选中第一个
          }));
        }
      },
    });
  }, []);

  useEffect(() => {
    getAgentList();
    const handleSendMessageKeyChanged = (changes) => {
      if (changes[ShortcutKeyEnum.SENDMESSAGEKEY]) {
        setSendMessageKey(changes[ShortcutKeyEnum.SENDMESSAGEKEY].newValue);
      }
    };
    browser.storage.local.onChanged.addListener(handleSendMessageKeyChanged);
    return () => {
      browser.storage.local.onChanged.removeListener(handleSendMessageKeyChanged);
    };
  }, []);

  const resultDomRef = useRef<HTMLDivElement>(null);
  const inputDomRef = useRef<HTMLDivElement>(null);
  const downDomRef = useRef<HTMLDivElement>(null);
  // SSE会话Hook
  const sseChat = useSSEChat();
  useEffect(() => {
    if (resultDomRef.current && sseChat.displayedText) {
      resultDomRef.current.querySelector(".agent-output-container").scrollTop =
        resultDomRef.current?.querySelector(".agent-output-container").scrollHeight -
        resultDomRef.current?.querySelector(".agent-output-container").clientHeight;
    }
  }, [sseChat.displayedText, sseChat.isRendering]);

  // 获取agent
  const getAgentList = () => {
    fetchRequest({
      api: "listAgents",
      params: {},
      callback: async (res) => {
        if (res.code === 200) {
          const data = [...res.data];
          data.forEach((item) => {
            if (item.id === bearerList[0]) {
              setWriterPoint(item.agentScore);
            }
            if (item.id === bearerList[1]) {
              setReplyPoint(item.agentScore);
            }
          });
        }
      },
    });
  };

  const handleChange = (value: string, type: string, item?: any) => {
    setState({ ...state, [type]: [value] });
    if (type == "format" && item?.fileUrl) {
      // handleChange(state.langList[0], "lang");
      setlangDisabled(true);
    } else {
      setlangDisabled(false);
    }
  };

  const blurHandle = (type) => {
    handleChange(state[type][0], type);
  };

  /** 搜索条件配置项 */
  const filterOption = (input: string, option?: { label: string; value: string }) =>
    (option?.label ?? "").toLowerCase().includes(input.toLowerCase());

  /** select搜索事件 */
  const onSearch = (value: string, type: string) => {
    if (value) {
      setState({ ...state, [type]: [value] });
    }
  };
  useEffect(() => {
    getUserInfo().then((res) => {
      setUserInfo(res);
    });
  }, []);

  /** 切换【撰写】与【回复】Tab */
  const handleSelectedTab = (item: string) => {
    // const result = document.getElementById("side-result")! as HTMLDivElement; // 获取要滚动的DIV元素
    // result.style.minHeight = "0px";
    setState({
      ...state,
      content: "",
      reply: "",
      selectedTab: item,
      bearer: bearerList[item === "1" ? 0 : 1],
    });
    sseChat.setCurrentConversation("");
    // 清空
    mentionsRef.current?.setMentionsData({
      localWebPageFile: [],
      selectKnowledgeArr: [],
      imageList: [],
      localFile: [],
      query: [],
      ocrCon: "",
    });
    if (sseChat.progress !== GenerationProgress.INITIALIZED) {
      handleStop();
    }
  };
  const handleScrollToTop = () => {
    setScrolling(true); // 开始滚动
    const duration = 500; // 滚动时长
    const start = window.scrollY;
    const startTime = performance.now();

    const scrollStep = (currentTime) => {
      const elapsed = currentTime - startTime; // 计算已过去时间
      const progress = Math.min(elapsed / duration, 1); // 计算进度
      const newScrollY = start - start * progress; // 计算新的 scrollY 值
      window.scrollTo(0, newScrollY); // 更新页面滚动

      if (progress < 1) {
        requestAnimationFrame(scrollStep); // 继续循环
      } else {
        setScrolling(false); // 滚动结束
      }
    };

    requestAnimationFrame(scrollStep);
  };
  const scrollToBottom = () => {
    if (resultDomRef.current) resultDomRef.current.scrollTop = inputDomRef.current.clientHeight + 20;
  };
  const submit = () => {
    if (state.selectedTab == "1") {
      if (!/^[\s]*$/.test(String(state.content))) {
        handleSubmit();
      }
    } else {
      if (!/^[\s]*$/.test(String(state.content)) && !/^[\s]*$/.test(String(state.reply))) {
        handleSubmit();
      }
    }
    //
  };
  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      if (e.ctrlKey) {
        if (/^[\s]*$/.test(String(state.content))) return;
        sendMessageKey === "ctrlEnter" ? submit() : handleChange(state.content + "\n", "content");
      } else {
        if (sendMessageKey === "Enter") {
          e.preventDefault();
          e.stopPropagation();
          if (
            !/^[\s]*$/.test(String(state.content)) &&
            (state.selectedTab == "1" || (state.selectedTab == "2" && !/^[\s]*$/.test(String(state.reply))))
          ) {
            submit();
          }
        }
      }
    }
  };
  const handleReplyKeyDown = (e) => {
    if (e.key === "Enter") {
      if (e.ctrlKey) {
        if (/^[\s]*$/.test(String(state.reply))) return;
        sendMessageKey === "ctrlEnter" ? submit() : handleChange(state.reply + "\n", "reply");
      } else {
        if (sendMessageKey === "Enter") {
          e.preventDefault();
          e.stopPropagation();
          if (
            !/^[\s]*$/.test(String(state.content)) &&
            (state.selectedTab == "1" || (state.selectedTab == "2" && !/^[\s]*$/.test(String(state.reply))))
          ) {
            submit();
          }
        }
      }
    }
  };
  // 将提交信息提取出来
  const getSubmitInfo = async (templateFile?: any) => {
    const token = await getToken();
    const tenantId = await getTenantId();
    // 文件信息
    const data: any = mentionsRef.current?.getMentionsData();
    let fileData = [];
    let imageFiles = [];
    data.localWebPageFile.forEach((item) => {
      // 选择的标签页
      let obj = {
        type: "document",
        transfer_method: "local_file",
        upload_file_id: item.uid,
      };
      fileData.push(obj);
    });
    data.localFile.forEach((item) => {
      // 文件
      let obj = {
        type: item.fileType ? item.fileType : "document",
        transfer_method: "local_file",
        upload_file_id: item.id,
      };
      fileData.push(obj);
    });

    data.imageList.forEach((item) => {
      // 图片
      let obj = {
        type: "image",
        transfer_method: "local_file",
        upload_file_id: item.id,
      };
      imageFiles.push(obj);
    });
    const selectKnowledgeId = data.selectKnowledgeArr.map((item) => item.id); // 知识库
    setNumber(sseChat.chatList.length + 1);
    sseChat.start({
      url: "/dify/broker/agent/stream",
      headers: {
        // Authorization: `Bearer ${state.bearer}`,
        "Content-Type": "application/json",
        Token: token || "",
      },
      body: {
        insId: "1",
        bizType: "app:agent",
        bizId: state.bearer, // 取agentId
        agentId: state.bearer, // 取agentId
        path: "/chat-messages",
        query: data.query || "",
        difyJson: {
          inputs: {
            reply: state.reply,
            docFiles: fileData,
            imageFiles: imageFiles,
            personalLibs: selectKnowledgeId.join(","),
            Token: token || "",
            tenantid: tenantId || "",
            outputTemplate: templateFile?.id
              ? {
                  // 模板信息
                  type: "document",
                  transfer_method: "local_file",
                  upload_file_id: templateFile.id,
                }
              : null,
            format: state.format[0],
            length: state.length[0],
            lang: state.lang[0],
            tone: state.tone[0],
          },
          response_mode: "streaming",
          user: userInfo?.id || "anonymous",
          conversation_id: "",
          query: data.query || "",
        },
      },
      query: {
        conversation_id: "",
      },
      message: data.query,
    });
  };

  /** 处理提交事件，生成草稿 */
  const handleSubmit = async () => {
    if (state.format[0] == "" || state.format[0] == undefined || state.format[0].length < 1) {
      message.error("请先添加模板");
      return;
    }
    setContentHeight("calc(100vh - 100px)");
    setTimeout(() => {
      scrollToBottom();
    }, 700);
    setHasCopy(false);
    const matchedItem: any = state.formatList.find((item: any) => item.templateName === state.format[0]);
    const fileUrl = matchedItem?.fileUrl;
    if (fileUrl) {
      // 调用上传API  先将模板上传到dify 然后再调用回复接口
      fetchRequest({
        api: "uploadChatFile",
        params: {
          path: "/files/upload",
          agentId: state.bearer,
          user: userInfo?.id,
          remoteFileUrl: fileUrl,
          remoteFileName: matchedItem?.fileName,
        },
        file: true,
        callback: (response) => {
          if (response && response.data) {
            const templateFile = response.data;
            getSubmitInfo(templateFile);
          } else {
            message.error("文件上传失败");
          }
        },
      });
    } else {
      getSubmitInfo();
    }
  };
  const gotoTop = () => {
    if (resultDomRef.current) resultDomRef.current.scrollTop = 0;
  };
  const handlerRefresh = () => {
    if ((state.selectedTab == "1" && state.content) || (state.selectedTab == "2" && state.content && state.reply)) {
      handleSubmit();
    }
  };

  /** 处理点击复制按钮事件 */
  const handleCopyBtnClick = () => {
    copyText(sseChat.chatList[number - 1][1].content).then((res) => {
      res && setHasCopy(true);
    });
  };

  // 上一个结果
  const handlePre = () => {
    setNumber(number > 1 ? number - 1 : sseChat.chatList.length);
  };
  // 下一个结果
  const handleNext = () => {
    setNumber(number < sseChat.chatList.length ? number + 1 : 1);
  };

  /** 处理停止 */
  const handleStop = () => {
    sseChat.stop(state.bearer, state.bearer, userInfo?.id || "anonymous", "completion");
  };

  // Tab页签数据配置
  const tabItems: TabsProps["items"] = [
    {
      key: "1",
      label: "撰写",
    },
    {
      key: "2",
      label: "回复",
    },
  ];

  const onDropdownVisibleChange = (open: boolean) => {
    if (open) {
      resultDomRef.current.style.overflow = "visible";
    } else {
      setTimeout(() => {
        resultDomRef.current.style.overflow = "auto";
      }, 200);
    }
  };
  // 关闭历史记录
  const historyDrawerClose = () => {
    setVisible(false);
  };
  // 搜索框
  const historyInputChange = (e) => {
    const value = e.target.value.trim();
    setHistorySearchText(value);
    const updateSearchParams = {
      ...searchParams.current,
      query: value,
    };
    searchParams.current = updateSearchParams;
    debounceHistorySearch(updateSearchParams);
  };
  // 历史记录单个点击
  const historyClick = (item) => {};

  const handleHistorySearch = (params = searchParams.current, first = false) => {
    fetchRequest({
      api: "getConversationHistoryList",
      params,
      callback: (res) => {
        const newList = res.data.data;
        if (newList.length == 0) {
          setIsLoading(false);
          return;
        }

        if (first) {
          setInit(true);
        }
        if (
          historyList.length == 0 ||
          (historyList.length > 0 && historyList[historyList.length - 1].id !== newList[newList.length - 1].id)
        ) {
          const list = [...historyList, ...newList];
          setHistoryList(list);
          const updateSearchParams = {
            ...searchParams.current,
            last_id: list[list.length - 1].id,
          };
          // setSearchParams(updateSearchParams);
          searchParams.current = updateSearchParams;
        }
        if (historyList.length > 0 && historyList[historyList.length - 1].id == newList[newList.length - 1].id) {
          stopRequestList.current = true;
        }
        setIsLoading(false);
      },
    });
  };

  const debounceHistorySearch = useCallback(debounce(handleHistorySearch, 1000), []);
  useEffect(() => {
    if (!init) return;
    if (!listRef.current) return; // 如果列表容器不存在，则不再监听

    const handleScroll = () => {
      if (isLoading) return; // 如果当前正在加载，则不再监听
      if (stopRequestList.current) return;

      const { scrollTop, clientHeight, scrollHeight } = listRef.current;
      if (scrollTop + clientHeight >= scrollHeight) {
        // 当列表滚动到底部时，加载更多
        setIsLoading(true);
        handleHistorySearch();
      }
    };

    // 监听滚动事件
    listRef.current.addEventListener("scroll", handleScroll);
    return () => {
      // 清理函数，组件卸载时移除事件监听
      listRef.current.removeEventListener("scroll", handleScroll);
    };
  }, [init]);

  // 跳转至设置模块的 模板管理
  const jumpTemplate = () => {
    window.open(browser.runtime.getURL("/options.html?message=11"), "_blank");
  };
  return (
    <>
      <TopTitle title="写作"></TopTitle>
      <div className="writer" ref={resultDomRef}>
        {/* 表单选择区域 */}
        <Flex className="input" vertical ref={inputDomRef}>
          <Flex className="side-write-tabs" vertical={false}>
            {tabItems.map((item, index) => (
              <Typography.Text
                className={classNames("side-write-tab", {
                  "ant-tabs-tab-active": Number(state.selectedTab) == index + 1,
                })}
                key={index}
                onClick={() => handleSelectedTab(item.key)}
              >
                {item.label}
                <Typography.Text
                  className={classNames({
                    "ant-tabs-tab-active-bottom": Number(state.selectedTab) == index + 1,
                  })}
                ></Typography.Text>
              </Typography.Text>
            ))}
          </Flex>
          {state.selectedTab == "1" ? null : (
            <Flex className="side-write-reply" vertical>
              <TextArea
                allowClear
                value={state.reply}
                onKeyDown={handleReplyKeyDown}
                onInput={(e) => {
                  let value = (e.target as HTMLInputElement).value;
                  // 检查内容是否只包含空格或回车符
                  if (/^[\s]*$/.test(value)) {
                    setTimeout(() => {
                      handleChange("", "reply");
                    }, 1);
                  } else {
                    setState({
                      ...state,
                      reply: value,
                    });
                  }
                }}
                onClear={() => handleChange("", "reply")}
                autoSize={{ minRows: 4, maxRows: 6 }}
                maxLength={2000}
                className="reply-textarea"
                placeholder="要回复的原文"
              />
            </Flex>
          )}
          <Flex className="side-write-compose" vertical>
            <Flex className="side-write-input" vertical>
              {/* <TextArea
                allowClear
                maxLength={500}
                value={state.content}
                autoSize={{ minRows: 4, maxRows: 6 }}
                onKeyDown={handleKeyDown}
                onInput={(e) => {
                  let value = (e.target as HTMLInputElement).value;
                  // 检查内容是否只包含空格或回车符
                  if (/^[\s]*$/.test(value)) {
                    setTimeout(() => {
                      handleChange("", "content");
                    }, 1);
                  } else {
                    setState({
                      ...state,
                      content: value,
                    });
                  }
                }}
                onClear={() => handleChange("", "content")}
                className="input-textarea"
                placeholder={state.selectedTab == "1" ? `您要撰写的主题` : "以上内容回复的大致内容"}
              /> */}
              <MentionsComponent
                agentId={state.bearer}
                ref={mentionsRef}
                onQueryChange={(val) => {
                  handleChange(val, "content");
                }}
              />
            </Flex>
            <Flex style={{ color: csstoken.colorTextTertiary, fontSize: csstoken.fontSizeSM }}>
              你可以选择多种类型信息作为参考输入
            </Flex>
            <Divider orientation="left" style={{ color: csstoken.colorText, fontSize: csstoken.fontSizeLG }}>
              内容配置
            </Divider>
            <Flex className="side-write-tone" vertical>
              <Flex className="side-write-title">
                <SmileOutlined className="write-base-icon" />
                <Flex className="side-title-text">语气</Flex>
              </Flex>
              <Flex className="side-write-tags" wrap gap={8}>
                {state.toneList.map<React.ReactNode>((tag, index) => (
                  <Tag.CheckableTag
                    key={index}
                    className={classNames("tag-name", { active: state.tone.includes(tag) })}
                    checked={state.tone.includes(tag)}
                    onChange={(checked) => handleChange(tag, "tone")}
                  >
                    <Typography.Text
                      className={classNames("tag-name-item", { "active-item": state.tone.includes(tag) })}
                    >
                      {tag}
                    </Typography.Text>
                  </Tag.CheckableTag>
                ))}
              </Flex>
            </Flex>
            <Flex className="side-write-length" vertical>
              <Flex className="side-write-title" align="center">
                <Button
                  shape="circle"
                  icon={<IconFont type="RulerOutlined" className="write-rule" />}
                  className="ruler-line write-base-icon"
                />
                <Flex className="side-title-text">长度</Flex>
              </Flex>
              <Flex className="side-write-tags" wrap gap={8}>
                {state.lengthList.map<React.ReactNode>((tag, index) => (
                  <Tag.CheckableTag
                    key={index}
                    className={classNames("tag-name", { active: state.length.includes(tag) })}
                    checked={state.length.includes(tag)}
                    onChange={(checked) => handleChange(tag, "length")}
                  >
                    <Typography.Text
                      className={classNames("tag-name-item", { "active-item": state.length.includes(tag) })}
                    >
                      {tag}
                    </Typography.Text>
                  </Tag.CheckableTag>
                ))}
              </Flex>
            </Flex>
            <Flex className="side-write-language" vertical>
              <Flex className="side-write-title">
                <TranslationOutlined className="write-base-icon" />
                <Flex className="side-title-text">语言</Flex>
              </Flex>
              <Flex className="side-write-tags" wrap gap={8}>
                <Select
                  // showSearch
                  style={{ width: "50%" }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  onDropdownVisibleChange={onDropdownVisibleChange}
                  optionFilterProp="children"
                  disabled={langDisabled}
                  onBlur={() => blurHandle("lang")}
                  onChange={(val) => handleChange(val, "lang")}
                  onSearch={(val) => onSearch(val, "lang")}
                  // filterOption={filterOption}
                  value={state.lang[0]}
                  options={state.langList.map((item) => {
                    return {
                      label: item,
                      value: item,
                    };
                  })}
                />
              </Flex>
            </Flex>
            <Flex className="side-write-format" vertical>
              <Flex className="side-write-title" align="center" justify="space-between">
                <Flex align="center">
                  <ProfileOutlined className="write-base-icon" />
                  <Flex className="side-title-text" vertical>
                    模板
                  </Flex>
                </Flex>
                <Tooltip
                  placement="top"
                  title="模板管理"
                  getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                >
                  <Button type="text" icon={<SettingOutlined />} onClick={jumpTemplate} />
                </Tooltip>
              </Flex>
              <Flex className="side-write-tags side-write-template" wrap gap={8}>
                {displayedTags.map<React.ReactNode>((tag: any, index) => {
                  return (
                    <Tag.CheckableTag
                      key={index}
                      className={classNames("tag-name", {
                        active: state.format.includes(tag.templateName),
                      })}
                      checked={state.format.includes(tag.templateName)}
                      onChange={(checked) => handleChange(tag.templateName, "format", tag)}
                    >
                      <Typography.Text
                        className={classNames("tag-name-item", {
                          "active-item": state.format.includes(tag.templateName),
                        })}
                      >
                        {tag.templateName}
                      </Typography.Text>
                    </Tag.CheckableTag>
                  );
                })}
                {/* 展开/收起按钮 */}
                {state.formatList.length > 3 && (
                  <Tag
                    className="tag-name absolute-tag"
                    onClick={() => setShowAllFormat(!showAllFormat)}
                    style={{ cursor: "pointer" }}
                  >
                    <Typography.Text className="tag-name-item">
                      {showAllFormat ? <UpOutlined /> : <DownOutlined />}
                    </Typography.Text>
                  </Tag>
                )}
              </Flex>
            </Flex>
            <Flex id="side-result">
              <Flex className="side-write-submitBtn">
                <Button
                  className="side-sub-btn"
                  disabled={
                    sseChat.progress == GenerationProgress.RESPONDING ||
                    (state.selectedTab == "1" && /^[\s]*$/.test(String(state.content))) ||
                    (state.selectedTab == "2" &&
                      (/^[\s]*$/.test(String(state.content)) || /^[\s]*$/.test(String(state.reply))))
                  }
                  type="primary"
                  onClick={handleSubmit}
                  icon={<SendOutlined />}
                >
                  <span className="button-content">
                    开始写作
                    {state.selectedTab == "1" && writerPoint && writerPoint !== "0" && (
                      <span className="point-display-button">
                        <span style={{ color: csstoken.blue }}>{writerPoint}</span>{" "}
                        <IconFont type="PointsCost" className="icon" />
                      </span>
                    )}
                    {state.selectedTab == "2" && replyPoint && replyPoint !== "0" && (
                      <span className="point-display-button">
                        <span style={{ color: csstoken.blue }}>{replyPoint}</span>{" "}
                        <IconFont type="PointsCost" className="icon" />
                      </span>
                    )}
                  </span>
                </Button>
              </Flex>
            </Flex>
          </Flex>
        </Flex>
        {/* AI输出区域 */}
        <Flex
          className="result"
          vertical
          ref={downDomRef}
          style={{ height: contentHeight, transition: "height 0.2s ease" }}
        >
          <Flex className="result-header" vertical>
            <Divider className="title" orientation="left">
              <Typography.Text className="title-text"> 结果</Typography.Text>
            </Divider>
            {sseChat.progress != 0 && sseChat.isRendering == false ? (
              <Flex className="right" onClick={handleStop}>
                <Tooltip
                  placement="top"
                  title="停止生成"
                  getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                >
                  <Button className="side-sub-btn-stop" icon={<PauseOutlined />}>
                    停止生成
                  </Button>
                </Tooltip>
              </Flex>
            ) : null}
            {/* // 分页 */}
            {sseChat.chatList.length > 1 ? (
              <Flex className="res-pagination" justify="end" align="center">
                <LeftOutlined className="res-pagination-btn" onClick={handlePre} />
                <Flex className="res-pagination-text">
                  {number}/{sseChat.chatList.length}
                </Flex>
                <RightOutlined className="res-pagination-btn" onClick={handleNext} />
              </Flex>
            ) : null}
          </Flex>
          <Flex className="side-write-result">
            {sseChat.progress !== GenerationProgress.INITIALIZED && (
              <AgentOutput
                content={
                  sseChat.isRendering == false
                    ? sseChat.displayedText
                    : number > 0 && sseChat.chatList[number - 1].length > 0
                      ? sseChat.chatList[number - 1][1].content
                      : ""
                }
                finished={GenerationProgress.INITIALIZED == sseChat.progress || sseChat.isRendering}
                role="assistant"
              />
            )}
          </Flex>

          {sseChat.isRendering && (
            <Flex className="side-result-btn-icon" vertical={false} justify="space-between" align="center">
              <Flex vertical={false} className="side-result-btn-left" align="center">
                <Tooltip
                  placement="top"
                  title="知识库"
                  getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                >
                  <Flex
                    align="center"
                    style={{ cursor: "pointer" }}
                    onClick={(event) => {
                      event.stopPropagation();
                      const str = sseChat.chatList[number - 1][1]?.content;
                      const match = str.match(/\((.*?)\)/);
                      const url = match ? match[1] : null;
                      setModalVisible(true);
                      let title =
                        sseChat.chatList[number - 1][0]?.content.length > 15
                          ? sseChat.chatList[number - 1][0]?.content.slice(0, 15) + "..."
                          : sseChat.chatList[number - 1][0]?.content;
                      if (url) {
                        setNoteInfoData({
                          title: title,
                          url: url,
                        });
                      } else {
                        setNoteInfoData({
                          title: title,
                          content: str,
                        });
                      }
                    }}
                  >
                    <Button
                      icon={<IconFont type={"knowledgeBaseOutlined"} />}
                      style={{ height: "24px", width: "24px" }}
                      type="text"
                    ></Button>
                    <Flex style={{ color: csstoken.colorText, fontSize: csstoken.fontSizeSM }}>存为知识库</Flex>
                  </Flex>
                </Tooltip>
                <Divider type="vertical" style={{ margin: "0px 8px" }} />
                <Tooltip
                  placement="top"
                  title={hasCopy ? "已复制" : "复制"}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                >
                  <Button
                    onClick={handleCopyBtnClick}
                    icon={<CopyOutlined className=" btn-icon" />}
                    type="text"
                    size="small"
                  ></Button>
                </Tooltip>
                <Tooltip
                  placement="top"
                  title="重新生成"
                  getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                >
                  <Button
                    onClick={handlerRefresh}
                    icon={<RedoOutlined className=" btn-icon" />}
                    type="text"
                    size="small"
                  ></Button>
                </Tooltip>
                {/* <Tooltip
                  placement="top"
                  title="写作历史"
                  getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                >
                  <Button
                    onClick={handlerRefresh}
                    icon={<HistoryOutlined className="btn-icon" />}
                    type="text"
                    size="small"
                  ></Button>
                </Tooltip> */}
              </Flex>
              <Tooltip
                placement="top"
                title="继续写作"
                getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
              >
                <Button className="side-sub-btn-agin" onClick={gotoTop}>
                  继续写作
                </Button>
              </Tooltip>
            </Flex>
          )}
        </Flex>
      </div>
      <Drawer
        title={<Flex className="history-drawer-title">写作历史</Flex>}
        placement="bottom"
        className="chat-history-drawer"
        closable={true}
        maskClosable={false}
        getContainer={() => {
          if (document.getElementById("shadow-side-panel")) {
            const shadowDom = document.getElementById("shadow-side-panel").shadowRoot;
            return shadowDom.querySelector(".side-panel-content");
          }
        }}
        onClose={historyDrawerClose}
        open={visible}
      >
        <Flex className="history-panel" vertical>
          {init && (
            <Flex className="search-area">
              <Flex className="search-input">
                <img
                  src={browser.runtime.getURL(`/images/note/searchDefaultIcon.png`)}
                  alt=""
                  className="search-icon"
                />
                <Input
                  allowClear
                  placeholder="搜索"
                  style={{ width: "100%" }}
                  value={historySearchText}
                  onChange={historyInputChange}
                />
              </Flex>
            </Flex>
          )}

          <Flex className="history-box" ref={listRef} vertical>
            {historyList.length === 0 && <Flex className="no-data">暂无写作历史记录呢</Flex>}
            {historyList.map((item) => (
              <Flex className="history-item" key={item.id} onClick={() => historyClick(item)} vertical>
                <Flex className="history-item-head" align="center" justify="space-between">
                  <Flex className="title">{item.name}</Flex>
                  <Flex className="time">{formatDate(getDateInfo(item.createdAt * 1000))}</Flex>
                </Flex>
                <Flex className="info-bottom" align="center" justify="space-between">
                  <div className="desc">{item.conversations}</div>
                </Flex>
              </Flex>
            ))}
            {isLoading && <div className="loading">Loading...</div>}
          </Flex>
        </Flex>
      </Drawer>
      <NoteKnowledgeModal
        visible={modalVisible}
        noteInfo={noteInfoData}
        isFile={noteInfoData?.url ? true : false}
        position="sider"
        onClose={() => {
          setModalVisible(false);
        }}
        onConfirm={() => {
          setModalVisible(false);
        }}
      />
    </>
  );
};

export default React.memo(Writer);
