/* 提示词侧边栏页面内容容器 */
.tool-page-container {
  width: 100%;
}
.tool-group-con {
  margin-top: var(--ant-margin);
  flex: 1 1 100%;
}
.tool-group-title {
  box-sizing: border-box;
  color: var(--ant-color-text-secondary);
  font-size: var(--ant-font-size);
  // font-weight: bold;
  line-height: var(--ant-line-height-lg);
  margin-bottom: var(--ant-margin-sm);
}
.sino-gutter-text{
  margin-left: var(--ant-margin-xs);
  flex: 1;
  min-width: 0px;
  span{
    // white-space: nowrap;
    overflow-wrap: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
  span:nth-child(1){
    color: var(--ant-color-text-base);
    font-size: var(--ant-font-size-lg);
    font-weight: bold;
    line-height: var(--ant-line-height-lg);
  }
  span:nth-child(2){
    color: var(--ant-color-text-tertiary);
    font-size: var(--ant-font-size-sm);
    line-height: var(--ant-line-height-sm);
  }
}
.sino-gutter-point {
  position: absolute;
  bottom: 5px;
  right: 5px;
  font-size: 12px;
  color: #333;
  .icon {
    width: 12px;
    height: 12px
  }
}
.tool-group-sign{
  margin-bottom: var(--ant-margin-md);
  .sino-gutter-row{
    margin-bottom: var(--ant-margin-sm)
  }
}

.tool-item {
  position: relative;
  height: 100%;
  box-sizing: border-box;
  cursor: pointer;
  align-items: flex-start;
  background: var(--ant-color-fill-quaternary);
  padding: var(--ant-padding);
  border-radius: var(--ant-border-radius);
  overflow-wrap: break-word;
  .icon-img{
    width: 24px;
    height: 24px;
  }
  .anticon{
    font-size: var(--ant-font-size-heading-3);
    color: var(--ant-color-text-tertiary);
    flex-shrink: 0;
  }
  &:hover {
    background-color: var(--ant-color-primary-bg);
    color: var(--ant-color-primary-text-hover);
    .sino-gutter-text{
      span{
        color: var(--ant-color-primary-text-hover);
      }
    }
    .anticon{
      color: var(--ant-color-primary-text-hover);
    }
  }
}
