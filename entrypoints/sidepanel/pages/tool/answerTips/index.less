@import "@/assets/styles/variables";
/* 提示词侧边栏页面内容容器 */
.answerTips-page-container {
  width: 100%;
  margin: var(--ant-margin) 0;
}
.card-content {
  width: 100%;
  margin-top: 20px;

  .card-content-box {
    margin-bottom: var(--ant-margin);
    background-color: var(--ant-color-fill-quaternary);
    padding: var(--ant-padding);
    border-radius: var(--ant-border-radius);
  }
  .prompt-card-title {
    font-size: var(--ant-font-size-lg);
    font-weight: var(--ant-font-weight-strong);
    color: var(--ant-color-text-base);
  }
  .ant-typography {
    margin-bottom: 0 !important;
  }  
}
.prompt-card-content {
  margin-top: var(--ant-margin-xxs);
  line-height: var(--ant-line-height);
}