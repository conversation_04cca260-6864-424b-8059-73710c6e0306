import { Flex, Input, List, message, Radio, RadioChangeEvent, Spin, Typography } from "antd";
import React, { useCallback, useEffect, useRef, useState } from "react";
import debounce from "lodash.debounce";
import { useGetState } from "ahooks";
import { getUrlNoQuery } from "@/utils/notes";
import EmptyData from "@/components/EmptyData";
import { SearchOutlined } from "@ant-design/icons";
import { cacheGet, cacheSet } from "@/utils/browserStorage";
import TopTitle from "../../../components/TopTitle";
import "./index.less";

const AnswerTips: React.FC<{ handleBack }> = ({ handleBack }) => {
  const [questionTitle, setQuestionTitle] = useGetState("");
  const [questionOptions, setQuestionOptions] = useGetState([]);
  const currentUrl = useRef(getUrlNoQuery());
  const topicScore = useRef([]);
  const currentQuestionNumber = useRef(0);
  // 搜索参数
  const [searchText, setSearchText] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [isStart, setIsStart] = useState(false);
  const [questionLoading, setQuestionLoading] = useState(false);
  const [promptPageData, setPromptPageData] = useState([]);
  const fetchRequest = useFetchRequest();

  useEffect(() => {
    if (!getUrlNoQuery().includes("exam-doing")) {
      cacheSet("topicScore", null);
    }
    return () => {
      end();
    };
  }, []);

  // 1. 使用 useRef 来创建一个持久化的 observer 引用
  const observerUrlRef = useRef<MutationObserver | null>(null);

  // 监听主页面 URL 变化
  useEffect(() => {
    // 创建一个 MutationObserver 实例来监视指定节点的子节点变化
    observerUrlRef.current = new MutationObserver(async () => {
      if (currentUrl.current !== getUrlNoQuery()) {
        // 在这里可以执行一些操作，处理 URL 变化事件
        currentUrl.current = getUrlNoQuery();

        if (currentUrl.current.includes("exam-result")) {
          cacheSet("topicScore", null);
          extractExamResultInfo();
          end();
          setQuestionTitle("");
          setIsStart(false);
        } else if (currentUrl.current.includes("exam-doing")) {
          message.open({
            type: "success",
            content: "开始答题",
          });
          setIsStart(true);
          start();
        } else {
          cacheSet("topicScore", null);
          setQuestionTitle("");
          setIsStart(false);
        }
      }
    });

    // 选择要监视变化的节点，可以根据具体情况选择合适的节点
    const targetNode = document.body;

    // 配置 MutationObserver 实例以监视子节点的变化
    const observerConfig = { childList: true, subtree: true };

    observerUrlRef.current.observe(targetNode, observerConfig);

    return () => {
      observerUrlRef.current.disconnect();
    };
  }, []);

  useEffect(() => {
    if (!observerQuestionRef.current && getUrlNoQuery().includes("exam-doing")) {
      message.open({
        type: "success",
        content: "开始答题",
      });
      start();
    }
  }, []);

  const observerQuestionRef = useRef<MutationObserver | null>(null);
  const start = () => {
    let maxAttempts = 50;
    let attemptCount = 0; // 初始化计数器
    const interval = setInterval(() => {
      attemptCount++; // 每次执行时增加计数
      // 选择要监听的 div 元素，这里我们监听题号的容器
      const targetNode = document.querySelector(".question-content"); // 或者其他包含题号的父元素

      if (targetNode) {
        cacheGet("topicScore").then((res) => {
          if (res) {
            topicScore.current = res;
          } else {
            getTopicScoreList();
          }

          // 配置观察选项
          const config = {
            childList: true, // 监听子节点的变化
            subtree: true, // 监听所有子节点的变化
            characterData: true, // 监听文本内容的变化
            attributes: true,
            attributeFilter: ["class"],
          };

          // 创建 MutationObserver 实例
          observerQuestionRef.current = new MutationObserver((mutationsList, observer) => {
            if (currentQuestionNumber.current !== extractQuestionInfo().questionNumber) {
              currentQuestionNumber.current = extractQuestionInfo().questionNumber;
              handleQuestionChange(extractQuestionInfo().questionNumber);
            } else {
              // 点击选项更新选中数据
              setTimeout(() => {
                const answerInfo = extractQuestionInfo();
                if (answerInfo) {
                  addPropertiesToQuestion({
                    questionNumber: answerInfo.questionNumber,
                    newProperties: answerInfo,
                  });
                }
              }, 100);
            }
          });

          // 启动观察器
          if (targetNode) {
            observerQuestionRef.current.observe(targetNode, config);
          } else {
            message.open({
              type: "error",
              content: "答题失败，请检查是否在答题页面",
            });
          }

          const answerInfo = extractQuestionInfo();
          currentQuestionNumber.current = answerInfo.questionNumber;
          addPropertiesToQuestion({
            questionNumber: answerInfo.questionNumber,
            newProperties: answerInfo,
          });
          setQuestionTitle(answerInfo.questionTitle);
          recommendQuestion(answerInfo);
        });

        clearInterval(interval); // 停止定时器
      } else if (attemptCount >= maxAttempts) {
        clearInterval(interval); // 达到最大尝试次数，停止定时器
      } else {
        console.error("无法找到question-content");
        return;
      }
    }, 500);
  };

  // 当题号变化时，执行其他程序
  const handleQuestionChange = (questionNumber) => {
    setTimeout(() => {
      const answerInfo = extractQuestionInfo();
      if (answerInfo) {
        addPropertiesToQuestion({
          questionNumber: answerInfo.questionNumber,
          newProperties: answerInfo,
        });
        setQuestionTitle(answerInfo.questionTitle);
        recommendQuestion(answerInfo);
      }
    }, 200);
  };

  const end = () => {
    if (observerQuestionRef.current) {
      observerQuestionRef.current?.disconnect();
    }
  };

  const extractQuestionInfo = () => {
    const questionContent: any = document.querySelector(".question-content");

    if (!questionContent) {
      console.error("无法找到问题内容");
      return;
    }

    // 题号
    const questionNumber = questionContent.querySelector(".sort")
      ? questionContent.querySelector(".sort").innerText
      : "未找到题号";

    // 题目类型：检查是单选题还是多选题
    const questionTypeText = questionContent.querySelector(".crumb span")
      ? questionContent.querySelector(".crumb span").innerText
      : "未找到题目类型";
    const isMultipleChoice = questionTypeText.includes("多选");

    // 题目标题：从exam-item中获取题目内容，考虑到标题可能包含img标签
    const questionTitlePElement = questionContent.querySelector(".exam-item .item-content p");

    const textContent = questionTitlePElement
      ? questionTitlePElement.textContent || questionTitlePElement.innerText
      : "";

    // 提取文本内容并保留img标签
    let imgHtml = "";
    if (questionTitlePElement) {
      // 获取p标签内的文本部分

      // 获取所有img标签，并保留它们
      const imgTags = questionTitlePElement.querySelectorAll("img");
      imgHtml = Array.from(imgTags)
        .map((img: any) => img.outerHTML)
        .join("");
    }

    // 选项内容
    const options = [];
    const selectedOptions = []; // 存储选中的选项
    let optionElements;

    if (isMultipleChoice) {
      // 多选题：选择所有checkbox元素
      optionElements = questionContent.querySelectorAll(".ant-checkbox-wrapper");
    } else {
      // 单选题：选择所有radio元素
      optionElements = questionContent.querySelectorAll(".ant-radio-wrapper");
    }

    optionElements.forEach((option, index) => {
      const optionText = option.querySelector("span.flex") ? option.querySelector("span.flex").innerText.trim() : "";
      const isSelected = option.classList.contains(
        isMultipleChoice ? "ant-checkbox-wrapper-checked" : "ant-radio-wrapper-checked",
      );

      options.push(optionText);

      if (isSelected) {
        selectedOptions.push({
          option: optionText,
          index: index + 1, // 记录选项的索引，方便显示
        });
      }
    });

    // 获取题号索引
    let arr = questionNumber.match(/第(\d+)\//);
    const index = arr ? arr[1] : -1;
    const result = topicScore.current.find((item) => item.questionNumber === index.toString());

    return {
      questionNumber: index,
      questionType: isMultipleChoice ? "多选题" : "单选题",
      questionTitle: textContent + imgHtml || "未找到题目标题", // 返回包含img的题目标题
      options,
      selectedOptions, // 返回选中的选项
      questionScore: result ? result.questionScore : null,
    };
  };

  const addPropertiesToQuestion = ({ questionNumber, newProperties }) => {
    // Create a new array with the updated object
    const updatedTopicScore = topicScore.current.map(
      (item) =>
        item.questionNumber === questionNumber.toString()
          ? { ...item, ...newProperties } // Add new properties to the matched item
          : item, // Keep the other items unchanged
    );
    cacheSet("topicScore", updatedTopicScore);
    // Set the updated state
    topicScore.current = updatedTopicScore;
  };

  const extractExamResultInfo = () => {
    let maxAttempts = 50;
    let attemptCount = 0; // 初始化计数器
    const interval = setInterval(() => {
      attemptCount++; // 每次执行时增加计数
      const examResultContent: any = document.querySelector(".exam-result-content");

      if (examResultContent) {
        // 考试结果（通过或失败）
        const resultImage = examResultContent.querySelector("img");
        const resultText = examResultContent.querySelector("h3.message");
        const examResult = resultImage && resultImage.src.includes("exam-failed.png") ? "未通过" : "通过";

        const message = resultText ? resultText.innerText.trim() : "未找到结果消息";

        // 考试分数
        const scoreElement = examResultContent.querySelector(".scoreShow span");
        const score = scoreElement ? scoreElement.innerText.trim() : "0";

        // 考试用时
        const timeElement = examResultContent.querySelector(".exam-data .data-item:nth-child(1) span");
        const examTime = timeElement ? timeElement.innerText.trim() : "未找到考试用时";

        // 答对题数
        const correctAnswersElement = examResultContent.querySelector(".exam-data .data-item:nth-child(2) span");
        const correctAnswers = correctAnswersElement ? correctAnswersElement.innerText.trim() : "未找到答对题数";

        // 答错题数
        const incorrectAnswersElement = examResultContent.querySelector(".exam-data .data-item:nth-child(3) span");
        const incorrectAnswers = incorrectAnswersElement ? incorrectAnswersElement.innerText.trim() : "未找到答错题数";

        // 准确率
        const accuracyElement = examResultContent.querySelector(".exam-data .data-item:nth-child(4) span");
        const accuracy = accuracyElement ? accuracyElement.innerText.trim() : "未找到准确率";
        let params = {
          questions: topicScore.current.filter((x) => x.selectedOptions && x.selectedOptions.length > 0),
          score: Number(score.replace(/分/g, "")),
          correctAmount: Number(correctAnswers),
        };
        fetchRequest({
          api: "submittQuestion",
          params,
          callback: (res) => {
            if (res.code === 200) {
              message.open({
                type: "success",
                content: "成功",
              });
            }
          },
        });
        clearInterval(interval); // 停止定时器
      } else if (attemptCount >= maxAttempts) {
        clearInterval(interval); // 达到最大尝试次数，停止定时器
      } else {
        console.error("无法找到exam-result-content");
        return;
      }
    }, 500);
  };

  const getTopicScoreList = () => {
    let maxAttempts = 50;
    let attemptCount = 0; // 初始化计数器
    const interval = setInterval(() => {
      attemptCount++; // 每次执行时增加计数
      const menuItems = document.querySelectorAll(".menu-item");

      if (menuItems.length > 0) {
        let questionScores = [];

        menuItems.forEach((item) => {
          const questionText = item.querySelector("div").textContent.trim();
          const questionNumberMatch = questionText.match(/第(\d+)题/); // Match "第X题"

          if (questionNumberMatch) {
            const questionNumber = questionNumberMatch[1];

            const scoreText = item.querySelector(".small-text").textContent.trim();
            const scoreMatch = scoreText.match(/\((\d+)分\)/); // Match "(X分)"

            if (scoreMatch) {
              const score = scoreMatch[1];
              questionScores.push({ questionNumber, questionScore: score });
            }
          }
        });
        topicScore.current = questionScores;
        clearInterval(interval); // 停止定时器
      } else if (attemptCount >= maxAttempts) {
        clearInterval(interval); // 达到最大尝试次数，停止定时器
      } else {
        console.error("无法找到menu-item");
        return;
      }
    }, 500);
  };

  const inputChange = (e) => {
    const value = e.target.value.trim();
    setSearchText(value);
    debounceSearch(value);
  };

  const handleSearch = (value) => {
    let params = {
      questionTitle: value,
    };
    setLoading(true);
    fetchRequest({
      api: "getQuestionList",
      params,
      callback: (res) => {
        setLoading(false);
        if (res.code === 200) {
          setPromptPageData(res.data || []);
        }
      },
    });
  };
  const debounceSearch = useCallback(debounce(handleSearch, 500), []);

  const recommendQuestion = (answerInfo) => {
    let params = {
      questionType: answerInfo.questionType,
      questionTitle: answerInfo.questionTitle,
      options: answerInfo.options,
    };

    setQuestionLoading(true);
    fetchRequest({
      api: "recommendQuestion",
      params,
      callback: (res) => {
        if (res.code === 200) {
          setQuestionOptions(res.data || []);
        }
        setQuestionLoading(false);
      },
    });
  };

  const Percentage = ({ value }) => {
    return <span>正确概率：{value ? (value * 1).toFixed(2) + "%" : "暂无"}</span>;
  };

  const [currnetTag, setCurrnetTag] = useState("1");
  const radioChange = (e: RadioChangeEvent) => {
    setCurrnetTag(e.target.value);
  };

  return (
    <Flex vertical className="answerTips-page-container">
      <TopTitle title="题库" titleDetail handleBackPar={handleBack}></TopTitle>
      <Flex
        style={{
          position: "fixed",
          background: "#fff",
          width: "calc(100% - 1px)",
          top: "45px",
          left: 0,
          padding: "30px 0 10px 16px",
        }}
      >
        <Radio.Group value={currnetTag} onChange={radioChange}>
          <Radio.Button value="1">当前考试</Radio.Button>
          <Radio.Button value="2">搜索题目</Radio.Button>
        </Radio.Group>
      </Flex>
      <Flex style={{ marginTop: 45 }} vertical>
        {currnetTag === "1" &&
          (questionTitle ? (
            <>
              <Flex vertical>
                <Typography.Title level={5} style={{ marginTop: 0 }}>
                  当前题目：
                </Typography.Title>
                <span
                  dangerouslySetInnerHTML={{
                    __html: questionTitle,
                  }}
                />
                <Typography.Title level={5} style={{ marginTop: 8 }}>
                  类似题目：
                </Typography.Title>
                <div className="personal-prompt-list" style={{ height: "calc(100vh - 250px)" }}>
                  <Spin spinning={questionLoading} size="default">
                    {questionOptions.length === 0 ? (
                      <EmptyData description={"暂无数据"} />
                    ) : (
                      <>
                        <Flex className="card-content" vertical>
                          {questionOptions.map((prompt, index: number) => {
                            return (
                              <Flex key={index} vertical className="card-content-box">
                                <Flex align="flex-start" className="prompt-card-title" vertical>
                                  题目{index + 1}：
                                  <span
                                    dangerouslySetInnerHTML={{
                                      __html: prompt.questionTitle,
                                    }}
                                  />
                                </Flex>

                                <List
                                  itemLayout="horizontal"
                                  dataSource={prompt.options}
                                  locale={{ emptyText: "暂无数据" }}
                                  renderItem={(item: any, index) => (
                                    <List.Item>
                                      <List.Item.Meta
                                        title={
                                          <>
                                            {index + 1 + "."}
                                            <span
                                              dangerouslySetInnerHTML={{
                                                __html: item.options,
                                              }}
                                            />
                                          </>
                                        }
                                        description={<Percentage value={item.weight} />}
                                      />
                                    </List.Item>
                                  )}
                                />
                              </Flex>
                            );
                          })}
                        </Flex>
                      </>
                    )}
                  </Spin>
                </div>
              </Flex>
            </>
          ) : isStart ? (
            <EmptyData description={"还未找到题目"} />
          ) : (
            <EmptyData description={"请先开始考试"} />
          ))}

        {currnetTag === "2" && (
          <>
            <Input
              allowClear
              size="large"
              prefix={<SearchOutlined />}
              placeholder="搜索题目"
              style={{ width: "100%" }}
              value={searchText}
              onChange={inputChange}
            />
            <div className="personal-prompt-list" style={{ height: "calc(100vh - 190px)" }}>
              <Spin spinning={loading} size="default">
                {promptPageData.length === 0 ? (
                  <EmptyData description={"暂无数据"} />
                ) : (
                  <>
                    <Flex className="card-content" vertical>
                      {promptPageData.map((prompt, index: number) => {
                        return (
                          <Flex key={index} vertical className="card-content-box">
                            <Flex align="flex-start" className="prompt-card-title" vertical>
                              题目{index + 1}：
                              <span
                                dangerouslySetInnerHTML={{
                                  __html: prompt.questionTitle,
                                }}
                              />
                            </Flex>

                            <List
                              itemLayout="horizontal"
                              dataSource={prompt.questionLibraryOptionInfoList}
                              locale={{ emptyText: "暂无数据" }}
                              renderItem={(item: any, index) => (
                                <List.Item>
                                  <List.Item.Meta
                                    title={
                                      <>
                                        {index + 1 + "."}
                                        <span
                                          dangerouslySetInnerHTML={{
                                            __html: item.options,
                                          }}
                                        />
                                      </>
                                    }
                                    description={<Percentage value={item.weight} />}
                                  />
                                </List.Item>
                              )}
                            />
                          </Flex>
                        );
                      })}
                    </Flex>
                  </>
                )}
              </Spin>
            </div>
          </>
        )}
      </Flex>
    </Flex>
  );
};

export default React.memo(AnswerTips);
