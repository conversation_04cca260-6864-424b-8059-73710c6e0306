@import "@/assets/styles/variables";

.question-wrapper {
  margin-bottom: 55px;
  scroll-behavior: smooth;
  font-size: @side-panel-font-size;
  align-content: flex-start;
  overflow: auto;
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  width: 100%;
  margin-top: var(--ant-margin);
  .question-container {
    width: 100%;

    .side-question-title {
      margin-bottom: var(--ant-margin-md);
      .chat-container-assistant {
        position: relative;
        img {
          position: absolute;
          z-index: 99;
          top: 7px;
          left: 8px;
          width: 14px;
        }

        .ant-select-selector {
          padding: 0 11px 0 24px;
          background: var(--ant-color-bg-layout);
          border-radius: var(--ant-border-radius);
          border: none;
        }
        .ant-select {
          --ant-select-active-outline-color: none !important;
        }
        .ant-select-focused {
          box-shadow: none !important;
        }
        .ant-select-open {
          box-shadow: none !important;
        }
        .agent-select-name {
          display: inline-block;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 150px;
        }
        .ant-select-dropdown {
          min-width: 200px !important;
        }
      }
      // .side-question-menu {
      //   height: 40px;
      //   font-weight: var(--ant-font-weight-strong);

      //   .side-question-menu-item {
      //     width: 100%;

      //     .side-question-menu-item-text {
      //       height: 20px;
      //       font-size: 14px;
      //       color: #121212;
      //       line-height: 20px;
      //     }
      //   }
      // }

      // .side-question-menu {
      //   height: 32px;
      //   background: var(--ant-color-bg-base);
      //   border-radius: var(--ant-border-radius);
      // }
    }

    .side-question-content {
      margin-bottom: var(--ant-margin-md);
    }

    .side-question-submit-btn {
      margin-top: var(--ant-margin-md);
      height: 32px;

      .side-sub-btn {
        flex: 1;
        font-weight: var(--ant-font-weight-strong);
        // font-family: PingFangSC;
      }
    }

    .result {
      .result-header {
        .title {
          user-select: none;
          font-family: @side-panel-font-family-bold;
          height: 22px;
          line-height: 22px;
        }
        .title-text {
          font-size: var(--ant-font-size-lg);
          color: var(--ant-color-text);
          font-weight: var(--ant-font-weight-strong);
        }
      }
      .agent-name {
        margin-bottom: var(--ant-margin-xs);
        img {
          width: 20px;
          height: 17.11px;
          vertical-align: text-top;
          padding-right: var(--ant-padding-xxs);
        }
      }
    }

    .side-question-result {
      margin-bottom: var(--ant-margin-xl);
      padding-bottom: var(--ant-padding-xl);
      text-align: justify;
      .side-question-result-content {
        height: 100%;
        overflow: auto;
      }
    }
    .side-result-btn-icon {
      width: 92%;
      position: fixed;
      bottom: 0;
      background-color: var(--ant-color-bg-container);
      height: 64px;
      .side-result-icon-copy,
      .side-result-icon-afresh {
        width: 20px;
        height: 20px;
        cursor: pointer;
      }
    }
    .right {
      z-index: 200;
      position: fixed;
      left: 50%;
      bottom: 10%;
      transform: translate(-50%);
      .side-sub-btn-stop {
        // border-radius: var(--ant-border-radius-lg);
        border-radius: 50px;
        border: 1px solid var(--ant-color-border);
        // padding:0 var(--ant-padding-xxs);
      }
    }
  }
  .side-result-btn-left {
    position: fixed;
    bottom: var(--ant-margin-lg);
    left: var(--ant-margin-sm);
  }
  .side-sub-btn-agin {
    border-radius: var(--ant-border-radius-sm);
    border: 1px solid var(--ant-color-border);
    background: var(--ant-color-bg-container);

    position: fixed;
    bottom: var(--ant-margin-lg);
    right: var(--ant-margin-sm);
  }
  /* 隐藏垂直滚动条 */
  .side-panel-content::-webkit-scrollbar {
    width: 2px;
  }

  .question-base-icon {
    font-size: var(--ant-font-size);
    color: var(--ant-color-text);
  }
  .btn-icon {
    color: var(--ant-color-text);
    font-size: var(--ant-font-size-heading-3);
  }
}
