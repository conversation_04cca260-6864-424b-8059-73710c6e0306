/** 提问页面 */
import AgentOutput from "@/components/AgentOutput";
import { ShortcutKeyEnum } from "@/config/enums/ShortcutKeyEnum";
import { AIType } from "@/config/options/agent";
import { langList } from "@/config/options/ai.ts";
import { useFetchRequest } from "@/hooks/useFetchRequest.ts";
import useSSEChat from "@/hooks/useSSEChat.ts";
import { Prompt } from "@/types/prompt";
import { getUserInfo, UserInfo } from "@/utils/auth.ts";
import { copyText } from "@/utils/clipboard.ts";
import { CopyOutlined, LeftOutlined, PauseOutlined, RedoOutlined, RightOutlined } from "@ant-design/icons";
import { useGetState } from "ahooks";
import { Button, Divider, Flex, Input, MenuProps, Select, theme, Tooltip, Typography } from "antd";
import React, { useEffect, useRef, useState } from "react";
import TopTitle from "../../components/TopTitle";
import IconFont from "@/components/IconFont";
import "./index.less";
import { getToken } from "@/utils/auth.ts";

const { useToken } = theme;

const { TextArea } = Input;

const Question: React.FC = () => {
  const buttonRef = useRef(null);
  // 当前登录人
  const [userInfo, setUserInfo] = useState<UserInfo>({});
  const [selectedMenu, setSelectedMenu] = useState<string>("");
  const [selectedLang, setSelectedLang] = useState<string>("0");
  // 查询得到的提示词列表
  const [promptList, setPromptList] = useState<Array<Prompt>>([]);
  const [tip, setTip] = useState<string>("");
  const [content, setContent] = useState<string>("");
  const [hasCopy, setHasCopy] = useState<boolean>(false);
  const [agnetNameFlag, setAgnetNameFlag] = useState<boolean>(false);
  const [number, setNumber, getNumber] = useGetState<number>(0);
  const [aiList, setAiList] = useState<Array<AIType>>([]);
  const [currentAi, setCurrentAi] = useState<string>();
  const [agentId, setAgentId] = useState<string>();
  const [agentName, setAgentName] = useState<string>();
  const resultDomRef = useRef<HTMLDivElement>(null);
  const inputDomRef = useRef<HTMLDivElement>(null);
  const [promptContent, setPromptContent] = useState<string>("");
  const [contentHeight, setContentHeight] = useState("100px");
  const [copyNumber, setCopeNumber] = useState<number[]>([]);
  const [sendMessageKey, setSendMessageKey] = useState("");
  const { token } = useToken();
  const fetchRequest = useFetchRequest();
  // SSE会话Hook
  const sseChat = useSSEChat();

  useEffect(() => {
    cacheGet(ShortcutKeyEnum.SENDMESSAGEKEY).then((res) => {
      setSendMessageKey(res || "Enter");
    });
  }, []);

  useEffect(() => {
    const handleSendMessageKeyChanged = (changes) => {
      if (changes[ShortcutKeyEnum.SENDMESSAGEKEY]) {
        setSendMessageKey(changes[ShortcutKeyEnum.SENDMESSAGEKEY].newValue);
      }
    };
    browser.storage.local.onChanged.addListener(handleSendMessageKeyChanged);
    return () => {
      browser.storage.local.onChanged.removeListener(handleSendMessageKeyChanged);
    };
  }, []);

  useEffect(() => {
    // 获取用户信息
    getUserInfo().then((res) => {
      setUserInfo(res);
    });

    getAgentList();
  }, []);

  useEffect(() => {
    console.log(sseChat.chatList, 88888);
    console.log(number);
  }, [number, sseChat]);

  useEffect(() => {
    findAgentId(currentAi);
  }, [currentAi]);
  // AI助手选中后根据appkey找到id
  const findAgentId = (value) => {
    aiList.find((item) => {
      if (item.appKey === value) {
        setAgentId(item.id);
      }
    });
  };
  // 获取agent
  const getAgentList = () => {
    fetchRequest({
      api: "listAgents",
      params: {},
      callback: async (res) => {
        if (res.code === 200) {
          let data = [...res.data];
          setAiList(data);
          const currentAIKey = await browser.storage.local.get("current_ai_appKey");
          const currentAI = currentAIKey?.current_ai_appKey || data![0].appKey;
          let selectAgent = data.find((item) => item.appKey === currentAI);
          setAgentName(selectAgent.agentName);
          browser.storage.local.set({ current_ai_appKey: currentAI });
          setCurrentAi(currentAI);

          handlePromptSearch(selectAgent);
        }
      },
    });
  };
  /** 处理提示词搜索 */
  const handlePromptSearch = (queryText) => {
    fetchRequest({
      api: "pagePrompts",
      params: {
        entity: {
          type: "prompt_type_created_coll",
          status: 0,
          agentId: queryText.id,
          collection: false,
        },
        pageNum: 1,
        pageSize: 100,
      },
      callback: (res) => {
        if (res.code === 200) {
          setPromptList(res.data.records);
          if (res.data.records.length > 0) {
            setPromptContent(res.data.records[0].content);
            setSelectedMenu(res.data.records[0].id);
          } else {
            setPromptContent("");
            setSelectedMenu(null);
          }
        }
      },
    });
  };

  const handleMenuClick: MenuProps["onClick"] = (e: any) => {
    setTip(promptList[Number(e.key)].content);
    setPromptContent(promptList[Number(e.key)].content);
    setSelectedMenu(e.key);
  };

  const menuProps = {
    items: promptList.map((item, index) => {
      return { key: index, label: item.title };
    }),
    onClick: handleMenuClick,
  };

  /** 处理点击复制按钮事件 */
  const handleCopyBtnClick = () => {
    copyText(sseChat.chatList[number - 1][1].content).then((res) => {
      res && setHasCopy(true);
      setCopeNumber([...copyNumber, number]);
    });
  };

  const handleKeyDownContent = (e) => {
    if (e.key === "Enter") {
      if (e.ctrlKey) {
        if (/^[\s]*$/.test(String(content))) return;
        sendMessageKey === "ctrlEnter" ? handleSubmit() : setContent(content + "\n");
      } else {
        if (sendMessageKey === "Enter") {
          e.preventDefault();
          e.stopPropagation();
          if (!/^[\s]*$/.test(String(content))) {
            handleSubmit();
          }
        }
      }
    }
  };

  const handlePromptplyKeyDown = (e) => {
    if (e.key === "Enter") {
      if (e.ctrlKey) {
        sendMessageKey === "ctrlEnter"
          ? !/^[\s]*$/.test(String(content)) && handleSubmit()
          : !/^[\s]*$/.test(String(promptContent)) && setPromptContent(promptContent + "\n");
      } else {
        if (sendMessageKey === "Enter") {
          e.preventDefault();
          e.stopPropagation();
          if (!/^[\s]*$/.test(String(content))) {
            handleSubmit();
          }
        }
      }
    }
  };

  const scrollToBottom = () => {
    if (resultDomRef.current) resultDomRef.current.scrollTop = inputDomRef.current.clientHeight;
  };
  const onDropdownVisibleChange = (open: boolean) => {
    if (open) {
      resultDomRef.current.style.overflow = "visible";
    } else {
      setTimeout(() => {
        resultDomRef.current.style.overflow = "auto";
      }, 200);
    }
  };
  /** 提交并生成问题回复 */
  const handleSubmit = async () => {
    setContentHeight("calc(100vh - 170px)");
    setTimeout(() => {
      scrollToBottom();
      setAgnetNameFlag(true);
    }, 700);
    console.log(111);
    if (content === "") return;

    let query = "";
    if (promptContent) {
      let str = promptContent;
      str = str.replace("${lang}", langList[Number(selectedLang)].label);
      if (str.includes("${content}")) {
        str = str.replace("${content}", content);
        query = str;
      } else {
        query = `${str}\n${content}`;
      }
    } else {
      query = content;
    }
    // if (tip) {
    //   let str = tip;
    //   str = str.replace("${lang}", langList[Number(selectedLang)].label);
    //   str = str.replace("${content}", content);
    //   query = str;
    // } else {
    //   query = content;
    // }
    setHasCopy(false);
    console.log(sseChat.chatList.length, 9);
    console.log(sseChat.chatList, 10);
    const token = await getToken();
    setNumber(sseChat.chatList.length + 1);
    sseChat.start({
      url: "/dify/broker/agent/stream",
      headers: {
        // Authorization: `Bearer ${currentAi}`,
        "Content-Type": "application/json",
        Token: token || "",
      },
      // 其他的参数先不传，
      body: {
        insId: "1",
        bizType: "app:agent",
        bizId: agentId, // 取agentId
        agentId: agentId, // 取agentId
        path: "/chat-messages",
        difyJson: {
          conversation_id: "",
          inputs: {},
          response_mode: "streaming",
          user: userInfo?.id || "anonymous",
          query,
        },
      },
      query: {},
    });
  };
  /** 重新生成草稿 */
  const handlerRefresh = () => {
    handleSubmit();
  };

  // 上一个结果
  const handlePre = () => {
    setNumber(number > 1 ? number - 1 : sseChat.chatList.length);
  };

  // 下一个结果
  const handleNext = () => {
    setNumber(number < sseChat.chatList.length ? number + 1 : 1);
  };

  /** 处理停止 */
  const handleStop = () => {
    sseChat.stop(agentId, "", userInfo?.id || "anonymous");
  };

  // AI助手切换
  const handleAgentChange = (value) => {
    let selectItem = aiList.find((item) => item.appKey === value);

    setAgentName(selectItem.agentName);
    browser.storage.local.set({ current_ai_appKey: value }).then(async () => {
      setCurrentAi(value);
      if (!(sseChat.progress == GenerationProgress.RESPONDING || !content.trim())) {
        handleStop();
      }
    });
    handlePromptSearch(selectItem);
  };
  const gotoTop = () => {
    if (resultDomRef.current) resultDomRef.current.scrollTop = 0;
  };
  // const { run: throttledUpdateButtonPosition } = useThrottleFn(
  //   () => {
  //     if (buttonRef.current) {
  //       const scrollTop = buttonRef.current.scrollTop || document.body.scrollTop;
  //       const buttonTop = buttonRef.current.offsetTop + scrollTop;
  //     }
  //   },
  //   { wait: 100 },
  // ); // 间隔时间为 100 毫秒

  // useEffect(() => {
  //   if (!resultDomRef.current) return;
  //   const scrollableDiv = resultDomRef.current;
  //   const updateButtonPosition = () => {
  //     throttledUpdateButtonPosition(); // 调用节流后的函数
  //   };

  //   // 初始加载时获取位置
  //   updateButtonPosition();

  //   // 监听窗口尺寸变化
  //   window.addEventListener("resize", updateButtonPosition);

  //   // 监听滚动事件
  //   scrollableDiv.addEventListener("scroll", updateButtonPosition);

  //   // 清理函数
  //   return () => {
  //     window.removeEventListener("resize", updateButtonPosition);
  //     scrollableDiv.removeEventListener("scroll", updateButtonPosition);
  //   };
  // }, [throttledUpdateButtonPosition]);
  const promptHandleChange = (id) => {
    setSelectedMenu(id);

    setPromptContent(promptList.find((item) => item.id === id).content);
  };

  return (
    <>
      <TopTitle title="提问"></TopTitle>
      <div className="question-wrapper" ref={resultDomRef}>
        <Flex className="question-container" vertical flex={1}>
          <Flex className="input" vertical ref={inputDomRef}>
            <Flex className="side-question-title" justify="space-between" vertical={false} gap={token.marginSM}>
              <Flex className="chat-container-assistant">
                <img src={browser.runtime.getURL("/images/logo.png")} alt="" />
                <Select
                  style={{ width: 150 }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  onChange={handleAgentChange}
                  onDropdownVisibleChange={onDropdownVisibleChange}
                  value={currentAi}
                  options={aiList?.map((item: AIType, index: number) => {
                    return {
                      value: `${item.appKey}`,
                      key: `${index}`,
                      label: (
                        <Tooltip title={`${item.agentName}`} placement="rightBottom" color="#999">
                          <Flex justify="space-between" align="center">
                            <span className="agent-select-name">{`${item.agentName}`}&nbsp;&nbsp;</span>
                            {/* agent请求一次消耗积分显示 */}
                            {!!item.agentScore && item.agentScore !== "0" && (
                              <Flex align="center">
                                <span style={{ color: token.blue }}>{item.agentScore}&nbsp;</span>
                                <IconFont type="PointsCost" className="icon" />
                              </Flex>
                            )}
                          </Flex>
                        </Tooltip>
                      ),
                    };
                  })}
                />
              </Flex>
              <div style={{ width: "calc(100% - 132px)" }}>
                {promptList && (
                  <Select
                    placeholder="请选择"
                    notFoundContent="暂无数据"
                    value={selectedMenu}
                    style={{ width: "100%" }}
                    getPopupContainer={(triggerNode) => triggerNode.parentNode}
                    onChange={promptHandleChange}
                    options={promptList.map((item) => {
                      return {
                        label: item.title,
                        value: item.id,
                      };
                    })}
                  />
                )}
              </div>
              {/* <Dropdown
                menu={menuProps}
                className="side-question-menu"
                getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
              >
                <Button block>
                  {promptList.map((item: Prompt, index) => {
                    if (index === +selectedMenu) {
                      return (
                        <Flex key={index} className="side-question-menu-item" justify="space-between" align="center">
                          <Flex className="side-question-menu-item-text">{item.title}</Flex>
                          <DownOutlined />
                        </Flex>
                      );
                    }
                  })}
                </Button>
              </Dropdown> */}
            </Flex>

            <Flex className="side-question-content" vertical>
              <Flex className="side-question-content" vertical>
                <TextArea
                  value={promptContent}
                  autoSize={{ minRows: 4, maxRows: 8 }}
                  onKeyDown={handlePromptplyKeyDown}
                  onInput={(e) => {
                    let value = (e.target as HTMLInputElement).value;
                    // 检查内容是否只包含空格或回车符
                    if (/^[\s]*$/.test(value)) {
                      setTimeout(() => {
                        setPromptContent("");
                      }, 1);
                    } else {
                      setPromptContent(value);
                    }
                  }}
                  onClear={() => setPromptContent("")}
                  placeholder={tip}
                />
              </Flex>
              <TextArea
                allowClear
                value={content}
                onKeyDown={handleKeyDownContent}
                autoSize={{ minRows: 3, maxRows: 6 }}
                onInput={(e) => {
                  let value = (e.target as HTMLInputElement).value;
                  // 检查内容是否只包含空格或回车符
                  if (/^[\s]*$/.test(value)) {
                    setTimeout(() => {
                      setContent("");
                    }, 1);
                  } else {
                    setContent(value);
                  }
                }}
                onClear={() => setContent("")}
                placeholder="在此处粘贴或输入内容"
              />

              <Flex className="side-question-submit-btn" vertical={false}>
                {/* disabled={sseChat.progress == GenerationProgress.RESPONDING || !content} */}
                <Button
                  disabled={sseChat.progress == GenerationProgress.RESPONDING || !content.trim()}
                  className="side-sub-btn"
                  type="primary"
                  block
                  onClick={handleSubmit}
                >
                  提 交
                </Button>
              </Flex>
            </Flex>
          </Flex>
          <Flex className="result" vertical>
            <Flex className="result-header" vertical>
              <Divider className="title" orientation="left">
                <Typography.Text className="title-text" ref={buttonRef}>
                  结果
                </Typography.Text>
              </Divider>
              <Flex className="agent-name" justify="space-between" align="center">
                {agnetNameFlag && (
                  <Typography.Text>
                    <img src={browser.runtime.getURL("/images/logo.png")} alt="" />
                    {agentName}
                  </Typography.Text>
                )}
                {sseChat.chatList.length > 1 ? (
                  <Flex className="res-pagination">
                    <LeftOutlined className="res-pagination-btn" onClick={handlePre} />
                    <Flex className="res-pagination-text">
                      {number}/{sseChat.chatList.length}
                    </Flex>
                    <RightOutlined className="res-pagination-btn" onClick={handleNext} />
                  </Flex>
                ) : null}
              </Flex>
              {sseChat.progress != 0 && sseChat.isRendering == false && (
                <Flex className="right" onClick={handleStop}>
                  <Tooltip
                    placement="top"
                    title="停止生成"
                    getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                  >
                    <Button className="side-sub-btn-stop" icon={<PauseOutlined></PauseOutlined>}>
                      停止生成
                    </Button>
                  </Tooltip>
                </Flex>
              )}
            </Flex>
            <Flex className="side-question-result" style={{ height: contentHeight }}>
              <Flex className="side-question-result-content">
                {sseChat.progress !== GenerationProgress.INITIALIZED && sseChat.isRendering == false && (
                  <AgentOutput
                    content={sseChat.displayedText}
                    finished={sseChat.progress === GenerationProgress.RENDER_FINISHED}
                    role="assistant"
                  />
                )}
              </Flex>
              {sseChat.isRendering == true && (
                <AgentOutput
                  role="assistant"
                  content={
                    sseChat.progress === GenerationProgress.RESPONDING
                      ? sseChat.displayedText
                      : sseChat.chatList[number - 1][1].content
                  }
                  finished={true}
                />
              )}
            </Flex>

            {(sseChat.progress === GenerationProgress.RENDER_FINISHED ||
              sseChat.progress === GenerationProgress.USER_CANCELED) && (
              <Flex className="side-result-btn-icon">
                <Flex vertical={false} className="side-result-btn-left">
                  <Tooltip
                    placement="top"
                    title={copyNumber.includes(number) ? "已复制" : "复制"}
                    getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                  >
                    <Button
                      onClick={handleCopyBtnClick}
                      icon={<CopyOutlined className=" btn-icon" />}
                      type="text"
                      size="small"
                    ></Button>
                  </Tooltip>
                  <Tooltip
                    placement="top"
                    title="重新生成"
                    getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                  >
                    <Button
                      onClick={handlerRefresh}
                      icon={<RedoOutlined className=" btn-icon" />}
                      type="text"
                      size="small"
                    ></Button>
                  </Tooltip>
                </Flex>
                {/* <Tooltip placement="top" title="继续写作" getPopupContainer={(triggerNode) => triggerNode.parentNode}> */}
                <Button className="side-sub-btn-agin" onClick={gotoTop}>
                  继续提问
                </Button>
                {/* </Tooltip> */}
              </Flex>
            )}
          </Flex>
        </Flex>
      </div>
    </>
  );
};

export default Question;
