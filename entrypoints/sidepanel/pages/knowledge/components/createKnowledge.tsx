import React, { useState, useEffect } from "react";
import { Button, Modal, Form, Input, Space } from "antd";
import type { FormInstance } from "antd";
interface CreateKnowledgeProps {
  visibilyFlag: boolean;
}
interface SubmitButtonProps {
  form: FormInstance;
}
const createKnowledge: React.FC<CreateKnowledgeProps> = ({ visibilyFlag }) => {
  const [form] = Form.useForm();

  const SubmitButton: React.FC<React.PropsWithChildren<SubmitButtonProps>> = ({ form, children }) => {
    const [submittable, setSubmittable] = useState<boolean>(false);

    // Watch all values
    const values = Form.useWatch([], form);

    useEffect(() => {
      form
        .validateFields({ validateOnly: true })
        .then(() => setSubmittable(true))
        .catch(() => setSubmittable(false));
    }, [form, values]);

    return (
      <Button type="primary" htmlType="submit" disabled={!submittable}>
        {children}
      </Button>
    );
  };
  return (
    <div>
      {visibilyFlag}
      <Modal
        title="Vertically centered modal dialog"
        centered
        open={visibilyFlag}
        // onOk={() => setModal2Open(false)}
        // onCancel={() => setModal2Open(false)}
      >
        <Form form={form} name="validateOnly" layout="vertical" autoComplete="off">
          <Form.Item name="name" label="Name" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item name="age" label="Age" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item>
            <Space>
              <SubmitButton form={form}>确定</SubmitButton>
              <Button htmlType="reset">取消</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default createKnowledge;
