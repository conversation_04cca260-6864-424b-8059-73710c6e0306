@import "@/assets/styles/variables";

.chat-history-drawer-1 {
  overflow: hidden;
  border-radius: 8px;
  all: initial;
  .ant-drawer-content {
    overflow: hidden;
    border-radius: 8px;
  }
  .ant-drawer-mask {
    bottom: 7px;
  }
  .ant-drawer-content-wrapper {
    height: 75vh !important;
  }
  .ant-drawer-header {
    border-bottom: 0;
    padding: 16px 16px 12px;
    line-height: 32px;
    font-family: AlibabaPuHuiTi_2_85_Bold;
    font-size: 24px;
    color: #121212;
    .ant-drawer-header-title {
      justify-content: space-between;
      flex-direction: row-reverse;
      .history-drawer-title {
        font-weight: 700;
        span {
          color: #b8afaa;
        }
      }
      .ant-drawer-close {
        margin-right: 0;
        margin-left: 12px;
        font-size: 20px;
      }
    }
  }
  .ant-drawer-body {
    padding: 16px;
  }
  .history-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    /* 自定义 header */
    .topbar {
      padding: 12px;
      .topbar-title {
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .title {
          flex: 1;
          width: 0;
        }
        .close {
          margin-left: 12px;
        }
      }
    }
    .search-area {
      display: flex;
      gap: 10px;
      padding-bottom: 16px;
      .search-input {
        flex: 1 1 0%;
        position: relative;
        .search-icon {
          position: absolute;
          z-index: 99;
          top: 12px;
          left: 13px;
          width: 18px;
        }
        .ant-input-affix-wrapper {
          border: 1px solid #eeeeee !important;
          box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08) !important;
          &:hover {
            border-color: @primary-color !important;
          }
          .ant-input {
            padding: 8px 12px 8px 44px !important;
          }
          .ant-input-clear-icon {
            margin: 0 6px;
          }
        }
      }
      .delete {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08);
        border-radius: 10px;
        border: 1px solid #eeeeee;
        box-sizing: border-box;
        cursor: pointer;
        &:hover {
          border-color: @primary-color;
          color: @primary-color;
        }
      }
    }
    .history-box {
      flex: 1 1 0%;
      padding-bottom: 12px;
      overflow-y: auto;
      .no-data {
        margin: 50px auto;
        text-align: center;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
      }
      .history-item {
        padding: 12px;
        min-height: 66px;
        margin-bottom: 8px;
        background: #f7f7f7;
        border-radius: 12px;
        cursor: pointer;
        .history-item-head {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 20px;
          .title {
            flex: 1 1 0%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-family: AlibabaPuHuiTi_2_85_Bold;
            font-size: 14px;
            color: #121212;
          }
          .time {
            margin-left: 8px;
            white-space: nowrap;
            font-family: AlibabaPuHuiTi_2_55_Regular;
            font-size: 12px;
            color: #ababab;
          }
        }
        .info-bottom {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 8px;
          height: 32px;
          .desc {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-family: AlibabaPuHuiTi_2_55_Regular;
            font-size: 14px;
            color: #787878;
          }
          .action {
            /*display: flex;*/
            display: none;
            align-items: center;
            .round {
              width: 32px;
              height: 32px;
              border-radius: 8px;
              display: flex;
              align-items: center;
              justify-content: center;
              &:hover {
                background: #eeeeee;
              }
              svg {
                width: 14px !important;
                height: 14px !important;
              }
            }
          }
        }
        &:hover {
          .info-bottom {
            .action {
              display: flex;
            }
          }
        }
      }
    }
  }
}

.chat-history-modal-1 {
  .ant-input-affix-wrapper {
    .ant-input-suffix {
      margin-right: 4px;
    }
  }
}
