import { useEffect, useState } from "react";
import { Avatar, Button, Flex, Input, List, Select, Tag, theme } from "antd";
import { SearchOutlined, UserOutlined } from "@ant-design/icons";
import debounce from "lodash.debounce";
import "./index.less";
const { useToken } = theme;
interface UserSelectorProps {
  addedMembers: []; // 用户列表
  onAddUser: (user) => void; // 点击添加的回调
  onRemoveUser: (userId: string) => void; // 移除成员回调
}

const UserSelector: React.FC<UserSelectorProps> = ({ addedMembers, onAddUser, onRemoveUser }) => {
  console.log(addedMembers, 434234);
  const fetchRequest = useFetchRequest();
  const [searchText, setSearchText] = useState("");
  const { token } = useToken();
  const [allUsers, setAllUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchText(e.target.value);
    debouncedFilterUsers(e.target.value, allUsers);
  };
  // 防抖的搜索函数
  const debouncedFilterUsers = debounce((value: string, allUsers: []) => {
    const filtered = allUsers.filter((user: any) => user?.realName.includes(value));
    setFilteredUsers(filtered);
  }, 300); // 300毫秒防抖

  // 获取用户信息
  const getUserList = () => {
    fetchRequest({
      api: "getBaseEmployee",
      params: {},
      callback: (res) => {
        if (res.code === 200) {
          setAllUsers(res.data);
          setFilteredUsers(res.data);
        }
      },
    });
  };
  useEffect(() => {
    getUserList();
  }, []);

  return (
    <Flex style={{ marginBottom: token.margin }} vertical>
      {/* 选择人员 */}
      <Flex vertical>
        <Select
          placeholder="请选择"
          style={{ width: "100%" }}
          className="user-selector-know"
          getPopupContainer={(triggerNode) => triggerNode.parentNode as HTMLElement}
          dropdownRender={(menu) => (
            <Flex vertical>
              {/* 搜索框 */}
              <Flex style={{ padding: token.paddingXS }}>
                <Input
                  placeholder="搜索姓名"
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={handleSearchChange}
                  allowClear
                />
              </Flex>
              {/* 人员列表 */}
              <List
                style={{ padding: `0px ${token.paddingXXS}` }}
                dataSource={filteredUsers}
                renderItem={(user) => {
                  const isAdded = addedMembers.some((member) => member.userId === user.userId); // 判断是否已添加

                  return (
                    <List.Item key={user.userId} style={{ padding: `0px ${token.paddingXXS}` }}>
                      <List.Item.Meta avatar={<Avatar icon={<UserOutlined />} />} title={user.realName} />
                      {isAdded ? (
                        <Button type="link" danger onClick={() => onRemoveUser(user.userId)}>
                          剔除
                        </Button>
                      ) : (
                        <Button type="link" onClick={() => onAddUser(user)}>
                          添加
                        </Button>
                      )}
                    </List.Item>
                  );
                }}
              />
            </Flex>
          )}
        />
      </Flex>

      {/* 已添加成员 */}
      <Flex style={{ marginBottom: token.margin }} vertical>
        <Flex justify="space-between" align="center">
          <h4>已添加成员</h4>
          <Flex style={{ color: token.colorTextDescription }}>共{addedMembers.length}人</Flex>
        </Flex>
        <Flex
          wrap="wrap"
          gap="small"
          style={{
            maxWidth: "100%",
            marginTop: token.marginXS,
          }}
        >
          {addedMembers.map((member: any) => (
            <Tag
              key={member.userId}
              bordered={false}
              closable
              onClose={() => onRemoveUser(member.userId)}
              style={{
                marginRight: 0,
                flexShrink: 0,
                whiteSpace: "nowrap",
              }}
            >
              {member.realName}
            </Tag>
          ))}
        </Flex>
      </Flex>
    </Flex>
  );
};

export default UserSelector;
