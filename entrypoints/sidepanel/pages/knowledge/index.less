@import "@/assets/styles/variables";

.knowledge-content {
  ::-webkit-scrollbar {
    display: none;
  }
  width: 100%;
  .content-box {
    height: calc(100vh - 170px);
    // overflow-y: auto;
    padding-top: var(--ant-margin-sm);
    .back-arrow {
      position: fixed;
      top: 16px;
      // left: 20px;
      // width: 30px;
      // height: 30px;
      // color: #000000;
      z-index: 999;
    }
    .back-width {
      // width: 30px;
      font-size: var(--ant-font-size-heading-3);
      color: var(--ant-color-text);
      cursor: pointer;
    }
  }
  .dropdown-delete {
    .ant-dropdown-menu-vertical {
      overflow: visible;
    }
  }
  .btn {
    position: fixed;
    bottom: var(--ant-padding);
    left: var(--ant-padding);
    right: var(--ant-padding);
    z-index: 1000;
    .submit-btn {
      border-radius: var(--ant-border-radius);
    }
  }
  .btn .css-dev-only-do-not-override-1gwfwyx,
  .btn .ant-upload-select {
    width: 100%;
    display: inline-block;
  }

  .left-gas {
    margin-left: var(--ant-margin-sm);
    cursor: pointer;
    font-size: 0px;
    width: 90%;
    // width: 80%;
  }

  .card-box {
    padding: var(--ant-padding) var(--ant-padding-sm) var(--ant-padding-xs);
    box-sizing: border-box;
    border-radius: var(--ant-border-radius);
    margin-bottom: var(--ant-margin-sm);
    background: var(--ant-color-bg-base);
    border: 1px solid var(--ant-color-border);
    transition: border 0.3s;
    position: relative;
    .edit-margin {
      margin: 0 var(--ant-margin-xxs);
    }

    .first-title {
      color: var(--ant-color-text-base);
      font-size: var(--ant-font-size-lg);
      font-weight: var(--ant-font-weight-strong);
    }
    .two-title {
      color: var(--ant-color-text);
      font-size: var(--ant-font-size);
      height: 22px;
    }

    .tools {
      margin-top: var(--ant-margin-xs);
      font-size: var(--ant-font-size-sm);
      .left {
        width: 78%;
        .left-name {
          color: var(--ant-color-text-tertiary);
          font-size: var(--ant-font-size-sm);
        }
      }
      .konw-left span {
        line-height: 0;
      }
      .right:first-child {
        margin-right: 4px;
      }
      .right {
        cursor: pointer;
        opacity: 0;
        transition: opacity 0.3s ease;
      }
    }

    .ai-qa{
      position: absolute;
      right: var(--ant-margin);
      top: var(--ant-margin-xs);
      opacity: 0;
      z-index: 1;
    }
  }

  .card-box:hover .right {
    opacity: 1;
  }
  .card-box:hover .ai-qa {
    opacity: 1;
  }
  //上传

  .juzhong {
    display: flex;
    align-items: center;
  }

  // 网页知识库详情
  .cardInter {
    background: var(--ant-color-bg-base);
    border: 1px solid var(--ant-color-border);
    // position: relative;
    // .cardInter-title {
    //   position: absolute;
    //   font-style: italic;
    //   top: 0;
    //   left: 0;
    //   width: 43px;
    //   height: 15px;
    //   line-height: 15px;
    //   text-align: center;
    //   font-size: 12px;
    //   color: #ff7800;
    //   background: linear-gradient(180deg, #fff1a9 0%, #ffd85b 100%);
    //   border-radius: 8px 0px 8px 0px;
    //   text-shadow: 0px 0px 2px #ffd133;
    // }
  }
  .sino-relation-icon {
    margin-left: var(--ant-margin-xs);
  }
  // 详情
  .card-box-detail {
    padding: var(--ant-padding-sm);
    box-sizing: border-box;
    border: 1px solid transparent;
    border-radius: var(--ant-border-radius);
    background: var(--ant-color-fill-tertiary);
    margin-bottom: var(--ant-margin-sm);

    .top {
      .extend-icon {
        width: 24px;
        height: 24px;
      }
      .top-left {
        // width: 90%;
        width: 100%;
      }
    }
    .first-title {
      width: 80%;
      font-size: var(--ant-font-size);
      color: var(--ant-color-text-base);
      margin-bottom: var(--ant-margin-xxs);
    }
    .progress-num {
      color: var(--ant-color-text-tertiary);
      font-size: var(--ant-font-size-sm);
      margin-left: var(--ant-margin-xxs);
      span {
        margin-left: var(--ant-margin-xxs);
      }
    }
    .two-title {
      font-size: var(--ant-font-size-sm);
      color: var(--ant-color-text-tertiary);
    }
    .know-detail-time {
      text-align: right;
      color: var(--ant-color-text-quaternary);
      font-size: var(--ant-font-size-sm);
    }
  }
  .card-box-detail-active {
    border: 1px solid var(--ant-color-primary-border);
  }
  .right-gas {
    opacity: 0;
    .ant-dropdown-menu-vertical {
      overflow: visible;
    }
    .ant-dropdown-menu-title-content {
      label {
        display: inline-block;
        width: 100%;
      }
    }
  }
  .card-box-detail:hover .right-gas {
    opacity: 1;
  }
  .knowle-flex {
    .knowle-person {
      margin: 0 var(--ant-margin-sm) 0 var(--ant-margin-xxs);
    }
  }

  .ant-dropdown-trigger {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
  }
  .item-detail {
    margin-bottom: var(--ant-margin-sm);
  }

  .first-title,
  .two-title {
    display: inline-block;
    white-space: nowrap;
    width: 96%;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .add-knowledge-form {
    margin-top: var(--ant-margin);
  }
  .know-file-icon {
    color: var(--ant-color-primary);
    font-size: var(--ant-font-size-heading-3);
  }
  .know-inter-icon {
    color: var(--ant-cyan-6);
    font-size: var(--ant-font-size-heading-3);
  }
  .know-base-icon {
    color: var(--ant-color-text-description);
    font-size: var(--ant-font-size-sm);
    margin-right: var(--ant-margin-xxs);
  }
  .know-edit-icon {
    color: var(--ant-color-text);
    font-size: var(--ant-font-size);
  }

  .know-upload-icon {
    // margin: 0 var(--ant-margin);
  }
  .know-more-icon {
    color: var(--ant-color-text-tertiary);
    font-size: var(--ant-font-size);
    cursor: pointer;
  }
  .know-lock-icon {
    color: var(----ant-color-text-tertiary);
    font-size: var(--ant-font-size-sm);
  }
  .setup-model-btn-cancel {
    margin-right: var(--ant-margin-xs);
  }
  .knowledge-content-input {
    margin-top: var(--ant-margin);
  }
  .radio-group-options{
    width: 166px;
    margin-bottom: var(--ant-margin) !important;
  }
  .knowle-badge{
    margin-right:20px;
    cursor: pointer;
    .ant-scroll-number{
      color: #000;
      position: absolute;
      right: -10px;
      top: 0px;
      background: none !important;
      box-shadow: none !important;
    }
  }
}
.ant-spin-nested-loading {
  width: 100%;
}
.radio-button-per{
  .radio-button{
    width: 140px;
  }
  .active{
    background: "#f0f7ff";
    border-color: var(--ant-color-primary);
  }
}
.only-user-see{
  .ant-space-item{
    width:100%;
  }
}
