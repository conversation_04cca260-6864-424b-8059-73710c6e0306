@import "@/assets/styles/variables";

.knowledge-content {
  ::-webkit-scrollbar {
    display: none;
  }
  width: 100%;
  .title-count{
    background: radial-gradient(50% 50% at 50% 50%, #BDE1FF 0%, #F4FAFF 100%) !important;
    padding: 1px 8px !important;
    color: var(--ant-color-info) !important;
    border: none !important;
  }
  .ant-segmented{
    width: calc(100% - 40px);
    padding: 2px!important;
    height: 32px;
    .ant-segmented-group{
      label{
        flex: 1;
        height: 28px;
        
        .ant-segmented-item-label{
          font-size: var(--ant-font-size) !important;
          color: var(--ant-color-text)!important;
        }
      }
    }
  }
  .search-icon{
    border: none;
    background: var(--ant-color-fill-tertiary);
    &:hover{
      background: var(--ant-color-fill-tertiary) !important;
    }
  }
  .knowledge-content-main {
    height: calc(100vh - 80px);
    padding-top: var(--ant-padding);
  }
  .card-content-info{
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(147px, 1fr)); 
    max-height: calc(100vh - 100px);
    overflow-y: auto;
    padding-top: var(--ant-padding);
    .con-tit{
      color: var(--ant-color-text-tertiary);
      font-size: var(--ant-font-size);
      line-height: var(--ant-line-height);
      margin-bottom: var(--ant-margin-xs);
    }
    .web-page-card{
      cursor: pointer;
      background: var(--ant-color-bg-base);
      padding: 20px var(--ant-padding-xs);
      border: 1px solid var(--ant-color-border-secondary);
      border-radius: var(--ant-border-radius);
      flex: 1;
      &:hover{
        border: 1px solid var(--ant-color-primary-border-hover);
      }
      .page-card-icon{
        color: #BFBFBF;
        font-size: 18px;
      }
      .name{
        margin-left: var(--ant-margin-xs);
        font-size: var(--ant-font-size-lg);
        font-weight: bold;
        line-height: 24px;
      }
      .web-page-bottom{
        margin-top: var(--ant-margin-xxs);
        .page-length{
          color: var(--ant-color-text-quaternary);
          font-size: var(--ant-font-size-sm);
          .ant-typography{
            color: var(--ant-color-text-quaternary);
            margin-left: 2px
          }
        }
      }
    }
  }
  .card-recently{
    display: Flex;
  }
  // 搜索
  .know-search, .web-knowledge{
    height:calc(100vh - 80px);
    .knowledge-content-input{
      margin-bottom: var(--ant-margin);
    }
    .know-search-con{
      max-height: calc(100vh - 150px);
      overflow-y: auto;
      .con-tit{
        color: var(--ant-color-text-tertiary);
        font-size: var(--ant-font-size);
        line-height: var(--ant-line-height);
        margin-bottom: var(--ant-margin-xs);
      }
    }
  }
  .web-know-tag{
    .ant-tag{
     margin-inline-end: 0px;
    }
  }
 
  .web-con{
    max-height: calc(100vh - 186px);
    margin-bottom: 50px;
    margin-top: var(--ant-margin-sm);
    overflow-y: auto
  }
  .page-con{
    max-height: calc(100vh - 186px);
    margin-top: var(--ant-margin-sm);
    overflow-y: auto;
  }
  .page-con-knowledge{
    margin-bottom: 50px;
  }
  .btn {
    position: fixed;
    bottom: var(--ant-padding);
    left: var(--ant-padding);
    right: var(--ant-padding);
    z-index: 10;
    .submit-btn {
      border-radius: var(--ant-border-radius);
    }
  }
  .btn .css-dev-only-do-not-override-1gwfwyx,
  .btn .ant-upload-select {
    width: 100%;
    display: inline-block;
  }
  .fixed-bottom{
    position: fixed;
    background: var(--ant-color-bg-elevated);
    padding: var(--ant-padding-xs);
    right: 16px;
    bottom: 16px;
    z-index: 20;
    border-radius: var(--ant-border-radius-lg);
    box-shadow: 0px 6px 16px 0px rgba(0, 0, 0, 0.08),0px 3px 6px -4px rgba(0, 0, 0, 0.12),0px 9px 28px 8px rgba(0, 0, 0, 0.05);
    .fixed-bottom-operate{
      padding: 5px 8px;
      height: 44px;
      margin-top:2px;
      span{
        color: var(--ant-color-text);
        font-size: var(--ant-font-size);
        line-height: var(--ant-font-size);
        text-align: center;
        .anticon{
          font-size: var(--ant-font-size-lg)
        }
      }
      span:nth-child(2){
        margin-top:4px;
      }
    }
    .operate-hover{
      cursor: pointer;
      &:hover{
        border-radius: var(--ant-border-radius-sm);
        background: var(--ant-color-bg-text-hover)
      }
    }
    .fixed-bottom-line{
      margin-top: 5px;
      width: 1px;
      height: 44px;
      background: rgba(0, 0, 0, 0.06);
    }
    .fixed-bottom-close{
      cursor: pointer;
      margin-top:2px;
      font-size: var(--ant-font-size-xl);
      color: var(--ant-color-icon)
    }
  }
  .ant-empty {
    grid-column: 1 / -1;
    .ant-empty-image {
      margin-top: 200px;
      height: 70px !important;
      svg{
        width: 100%;
      }
    }
  }
  .grid-size{
    width: 100%;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(147px, 1fr));
  }
}  
.ant-spin-nested-loading {
  width: 100%;
}