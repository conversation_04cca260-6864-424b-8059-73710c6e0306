.learn-card {
  background: var(--ant-color-fill-tertiary);
  padding: var(--ant-padding-sm) var(--ant-padding-xs) var(--ant-padding-sm) var(--ant-padding-sm);
  border: 1px solid var(--ant-color-border-secondary);
  border-radius: var(--ant-border-radius);
  flex: 1;
  cursor: pointer;
  .custom-popover{
    max-width: 150%;
    max-height: 150px;
  }
  .extend-icon {
    width: 24px;
    height: 24px;
  }
  .knowledge-name{
    font-weight: bold;
    font-size: var(--ant-font-size);
    line-height: 24px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    word-break: break-all;
    -webkit-box-orient: vertical;
  }
  .learn-card-tag{
    font-size: var(--ant-font-size-sm);
    color: var(--ant-color-text-quaternary);
    font-weight: normal;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    word-break: break-all;
    -webkit-box-orient: vertical;
    line-height: 24px;
  }
  .knowledge-checkbox{
    opacity: 0
  }
  .knowledge-opeate{
    justify-content: flex-end;
    display: none;
  }
  &:hover{
    border: 1px solid var(--ant-color-primary-border-hover);
    // .knowledge-name{
    //   display: none;
    // }
    .learn-card-tag{
      display: none;
    }
    .knowledge-checkbox{
      opacity: 1 !important;
    }
    .knowledge-opeate{
      display: flex;
    }
  }
  .show-always {
    display: -webkit-box !important;
  }
}