import React, { <PERSON> } from "react";
import "./index.less";
import { Prompt } from "@/types/prompt";
import { Flex } from "antd";
import { CloseOutlined } from "@ant-design/icons";

type SelectedPromptProps = {
  prompt: Prompt;
  onClear: () => void;
};

const Index: FC<SelectedPromptProps> = ({ prompt, onClear }) => {
  return (
    <Flex className="selected-prompt">
      <Flex className="selected-prompt-ul">
        <Flex className="selected-prompt-li">
          <span className="selected-prompt-li-tit">{prompt.title}</span>
          <CloseOutlined className="img" />
        </Flex>
      </Flex>
    </Flex>
  );
};

export default Index;
