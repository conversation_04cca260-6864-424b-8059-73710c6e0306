@import "@/assets/styles/variables";
// ::-webkit-scrollbar {
//   display: none;
// }
.modal-know-wcl {
  max-height: 50vh;
  min-height: 30vh;
  overflow-y: auto;
  width: 100%;
  gap: var(--ant-margin-sm);
  .cardBox-wcl {
    padding: var(--ant-padding-xs);
    width: 100%;
    box-sizing: border-box;
    border-radius: var(--ant-border-radius);
    background: var(--ant-color-fill-tertiary);
    border: 1px solid var(--ant-color-fill-tertiary);
    .top {
      width: 100%;
      gap: var(--ant-margin-xxs);
      .icon-card {
        font-size: var(--ant-font-size-xl);
      }
    }
    .left-gas {
      cursor: pointer;
      width: 100%;
    }
    .first-title {
      color: var(--ant-color-text-base);
      font-size: var(--ant-font-size);
      font-weight: bold;
    }
    .two-title {
      color: var(--ant-color-text-quaternary);
      font-size: var(--ant-font-size);
    }
    .first-title,
    .two-title {
      display: inline-block;
      white-space: nowrap;
      width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .acitve {
    border: 1px solid var(--ant-color-primary-border);
    background: var(--ant-control-item-bg-active);
  }
}

.chat-search-input {
  width: 100%;
  height: 100%;
  position: relative;
  margin-bottom: var(--ant-margin-sm);
}
