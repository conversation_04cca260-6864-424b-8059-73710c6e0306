import React, { useEffect, useRef, useState } from "react";
import type { CheckboxProps } from "antd";
import { Checkbox, Flex, Modal } from "antd";
import "./index.less";
import { getContainer } from "@/utils";
import classNames from "classnames";

interface CardData {
  id: number | string;
  libName: string;
  libDesc: string;
  checked: boolean;
  active: boolean;
  url: string;
  favIconUrl: string;
}

interface Props {
  webCardData: CardData[];
  webPageFileModal: boolean;
  closeWebPageModel: (boolean, string) => void;
  onCheckboxWebPageChange: (string, boolean) => void;
  closeWebPageFalg: (boolean) => void;
}

const webPageFilechild: React.FC<Props> = ({
  webCardData,
  webPageFileModal,
  closeWebPageModel,
  onCheckboxWebPageChange,
  closeWebPageFalg,
}) => {
  const [selectWebPageId, setselectWebPageId] = useState<any>(
    webCardData.filter((item) => item.checked).map((item) => item.id),
  );

  const listRef = useRef(null);
  useEffect(() => {
    // 查找第一个具有 active: true 的项
    const activeItem = webCardData.find((item) => item.active === true);

    if (activeItem) {
      // 获取活动项的 DOM 元素
      const activeElement = listRef.current.querySelector(`[data-key="${activeItem.id}"]`);
      if (activeElement) {
        // 滚动到活动项
        activeElement.scrollIntoView({ behavior: "smooth", block: "nearest", inline: "start" });
      }
    }
  }, []);
  const handlerSubmit = () => {
    closeWebPageModel(false, selectWebPageId);
  };
  const closeMoal = () => {
    closeWebPageFalg(false);
  };

  const onChange: CheckboxProps["onChange"] = (e, item) => {
    if (e.target.checked) {
      setselectWebPageId([...selectWebPageId, item.id]);
    } else {
      let selectId = [];
      for (let i = 0; i < selectWebPageId.length; i++) {
        if (selectWebPageId[i] != item.id) {
          selectId.push(selectWebPageId[i]);
        }
      }
      setselectWebPageId(selectId);
    }

    onCheckboxWebPageChange(item.id, e.target.checked);
  };

  return (
    <div>
      <div className="content-box">
        <Modal
          title="标签页"
          centered
          width={300}
          getContainer={getContainer}
          open={webPageFileModal}
          onOk={handlerSubmit}
          onCancel={closeMoal}
          okText="确认"
          cancelText="取消"
        >
          <>
            <Flex className="modal-webpage-wcl" ref={listRef} vertical>
              {webCardData &&
                webCardData.map((item, index) => {
                  return (
                    <Flex key={index} className={classNames("cardBox-wcl", item.checked ? "acitve" : "")}>
                      <Checkbox checked={item.checked} onChange={(e) => onChange(e, item)}>
                        <Flex className="top" data-key={item.id} align="center">
                          <Flex></Flex>
                          <Flex className="left-gas" align="center">
                            <Flex className="sino-relation-icon">
                              <img src={item.favIconUrl} alt="" />
                            </Flex>
                            <Flex className="content" justify="space-between" align="center">
                              <Flex vertical>
                                <div className="first-title">{item.libName} </div>
                                <div className="url">{item.url} </div>
                              </Flex>
                              <Flex style={{ color: "#813CE0" }}>{item.active && "当前"}</Flex>
                            </Flex>

                            {/* <p className="two-title">{item.libDesc}</p> */}
                          </Flex>
                        </Flex>
                      </Checkbox>
                    </Flex>
                  );
                })}
            </Flex>
          </>
        </Modal>
      </div>
    </div>
  );
};

export default webPageFilechild;
