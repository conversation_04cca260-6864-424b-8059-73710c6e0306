@import "@/assets/styles/variables";
::-webkit-scrollbar {
  display: none;
}

.modal-webpage-wcl {
  max-height: 50vh;
  min-height: 30vh;
  overflow-y: auto;
  width: 100%;
  gap: var(--ant-margin-sm);
  .cardBox-wcl {
    padding: var(--ant-padding-sm);
    width: 100%;
    box-sizing: border-box;
    border-radius: var(--ant-border-radius);
    background: var(--ant-color-fill-tertiary);
    border: 1px solid var(--ant-color-fill-tertiary);
    .sino-relation-icon {
      width: 30px;
      height: 30px;
      border-radius: var(--ant-border-radius-xs);
      overflow: hidden;
      img {
        width: 100%;
      }
    }
    .top {
      width: 100%;
      gap: var(--ant-margin-sm);
    }

    .left-gas {
      cursor: pointer;
      width: 100%;
      gap: var(--ant-margin-sm);
      .content {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .first-title,
      .url {
        color: var(--ant-color-text-base);
        font-size: var(--ant-font-size);
        white-space: nowrap;
        width: 133px;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .url {
        color: var(--ant-color-text-tertiary);
        font-size: var(--ant-font-size-sm);
      }
    }
  }
  .acitve {
    border: 1px solid var(--ant-color-primary-border);
    background: var(--ant-control-item-bg-active);
  }
}
