@import "@/assets/styles/variables";

.chat-history-drawer {
  overflow: hidden;
  border-radius: var(--ant-border-radius) var(--ant-border-radius) 0 0 !important;
  all: initial;
  .ant-drawer-content {
    overflow: hidden;
    border-radius: var(--ant-border-radius);
  }
  .ant-drawer-mask {
    bottom: 7px;
  }
  .ant-drawer-content-wrapper {
    height: 394px !important;
  }
  .ant-drawer-header {
    padding: var(--ant-padding) var(--ant-padding-lg) var(--ant-padding-xs) !important;
    line-height: var(--ant-line-height-lg);
    font-family: @side-panel-font-family-bold;
    border-bottom: 0px !important;
    font-size: var(--ant-font-size-lg);
    color: var(--ant-color-text);
    .ant-drawer-header-title {
      justify-content: space-between;
      flex-direction: row-reverse;
      .history-drawer-title {
        font-weight: 700;
        span {
          color: #b8afaa;
        }
      }
      .ant-drawer-close {
        margin-right: 0;
        margin-left: var(--ant-margin-sm);
        color: var(--ant-color-text-tertiary);
        font-size: var(--ant-font-size);
      }
    }
  }
  .ant-drawer-body {
    padding: var(--ant-padding) !important;
  }
  .history-panel {
    height: 100%;
    /* 自定义 header */
    .topbar {
      padding: 12px;
      .topbar-title {
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .title {
          flex: 1;
          width: 0;
        }
        .close {
          margin-left: var(--ant-margin-sm);
        }
      }
    }
    .search-area {
      gap: var(--ant-margin-sm);
      padding-bottom: var(--ant-padding-sm);
      .search-input {
        flex: 1 1 0%;
        position: relative;
        .search-icon {
          position: absolute;
          z-index: 99;
          top: 12px;
          left: 13px;
          width: 18px;
        }
        .ant-input-affix-wrapper {
          padding: 0px !important;
          border: 1px solid var(--ant-color-border);
          box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.08) !important;
          &:hover {
            border-color: @primary-color !important;
          }
          .ant-input {
            padding: 8px 12px 8px 44px !important;
          }
          .ant-input-clear-icon {
            margin: 0 var(--ant-margin-xxs);
          }
        }
      }
    }
    .history-box {
      flex: 1 1 0%;
      padding-bottom: var(--ant-padding-sm);
      overflow-y: auto;
      .loading {
        text-align: center;
        font-size: var(--ant-font-size);
        color: var(--ant-color-text-tertiary);
      }
      .no-data {
        margin: 50px auto;
        text-align: center;
        font-size: var(--ant-font-size);
        color: var(--ant-color-text-tertiary);
      }
      .history-item {
        padding: var(--ant-padding-sm);
        min-height: 66px;
        margin-bottom: var(--ant-margin-sm);
        background: var(--ant-color-fill-tertiary);
        border-radius: var(--ant-border-radius-lg);
        cursor: pointer;
        .history-item-head {
          height: 20px;
          .title {
            flex: 1 1 0%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-family: @side-panel-font-family-bold;
            font-size: var(--ant-font-size);
            color: var(--ant-color-text);
          }
          .time {
            margin-left: var(--ant-margin-xs);
            white-space: nowrap;
            font-family: @side-panel-font-family;
            font-size: var(--ant-font-size-sm);
            color: var(--ant-color-text-quaternary);
          }
        }
        .info-bottom {
          gap: var(--ant-margin-xs);
          height: 32px;
          .desc {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-family: @side-panel-font-family;
            font-size: var(--ant-font-size);
            color: var(--ant-color-text-tertiary);
          }
          .action {
            /*display: flex;*/
            display: none;
            align-items: center;
            .round {
              font-size: var(--ant-font-size);
              margin-left: var(--ant-margin-xs);
              color: var(--ant-color-text-tertiary);
            }
          }
        }
        &:hover {
          .info-bottom {
            .action {
              display: flex;
            }
          }
        }
      }
    }
  }
}

.chat-history-modal {
  .ant-input-affix-wrapper {
    .ant-input-suffix {
      margin-right: var(--ant-margin-xxs);
    }
  }
}
