import React, { useCallback, useEffect, useState, useRef } from "react";
import { debounce } from "@/utils/debounce.ts";
import { UserInfo } from "@/utils/auth.ts";
import { Drawer, Flex, Input, message, Modal } from "antd";
import { formatDate } from "@/utils/dateFormat";
import "./index.less";

const HistoryDrawer: React.FC<{
  agentId: string;
  userInfo: UserInfo;
  visible: boolean;
  handleClose: () => void;
  handleBack: (info: { id: string; name: string; introduction: string }) => void;
}> = ({ agentId, userInfo, visible, handleClose, handleBack }) => {
  const [historySearchText, setHistorySearchText] = useState<string>("");
  // const [searchParams, setSearchParams] = useState({
  //   apiKey: "",
  //   user: "anonymous",
  //   query: "",
  //   last_id: "",
  // });
  const searchParams = useRef({
    page: "1",
    size: "20",
    title: "",
    agentId: agentId,
  });
  const [init, setInit] = useState(false);
  const [historyList, setHistoryList] = useState([]);
  const [totalPages, setTotalPages] = useState(0);
  const [totalElements, setTotalElements] = useState(0);
  const [isLastPage, setIsLastPage] = useState(false);
  const stopRequestList = useRef(false);

  const [openRename, setOpenRename] = useState(false);
  const [conversationName, setConversationName] = useState("");
  const [conversationId, setConversationId] = useState("");

  const [isLoading, setIsLoading] = useState(false);
  const listRef = useRef(null);

  /** 处理普通Web请求的hook */
  const fetchRequest = useFetchRequest();

  // 当前正在请求的页码
  const currentRequestPage = useRef<string | null>(null);

  const handleHistorySearch = (params = searchParams.current, first = false) => {
    // 防止重复请求同一页
    if (currentRequestPage.current === params.page && !first) {
      setIsLoading(false);
      return;
    }

    // 记录当前请求页码
    currentRequestPage.current = params.page;

    fetchRequest({
      api: "getConversationHistoryList",
      params,
      callback: (res) => {
        // 获取分页信息
        const { content, totalElements: total, totalPages: pages, last } = res.data;
        const newList = content || [];

        // 更新分页状态
        setTotalPages(pages || 0);
        setTotalElements(total || 0);
        setIsLastPage(last || false);

        if (newList.length === 0) {
          setIsLoading(false);
          currentRequestPage.current = null; // 重置请求页码
          return;
        }

        if (first) {
          setInit(true);
          setHistoryList(newList);
        } else {
          // 合并列表
          setHistoryList(prev => [...prev, ...newList]);
        }

        // 如果是最后一页，或者没有数据了，停止加载
        if (last || newList.length === 0) {
          stopRequestList.current = true;
        } else {
          // 更新页码，准备加载下一页
          const nextPage = (parseInt(params.page) || 1) + 1;
          searchParams.current = {
            ...searchParams.current,
            page: nextPage.toString()
          };
        }

        setIsLoading(false);
        // 请求完成后重置当前请求页码
        currentRequestPage.current = null;
      },
    });
  };

  const debounceHistorySearch = useCallback(debounce(handleHistorySearch, 1000), []);
  // input change - 搜索输入框变化处理
  const historyInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.trim();
    setHistorySearchText(value);

    // 重置所有状态
    setIsLoading(true); // 设置加载状态，防止滚动触发新请求
    currentRequestPage.current = null; // 重置当前请求页码
    stopRequestList.current = false; // 重置停止加载标志
    setIsLastPage(false); // 重置最后一页标志

    // 更新搜索参数
    const updateSearchParams = {
      ...searchParams.current,
      title: value,
      page: "1", // 搜索时重置页码
    };
    searchParams.current = updateSearchParams;

    // 清空列表，准备显示新的搜索结果
    setHistoryList([]);

    // 使用防抖函数进行搜索，避免频繁请求
    debounceHistorySearch(updateSearchParams);
  };

  // delete 接口方法
  const deleteHistoryRequest = (id: string, search = false) => {
    fetchRequest({
      api: "deleteConversationById",
      params: {
        agentId: agentId,
        user: userInfo?.id || "anonymous",
        id,
      },
      callback: () => {
        message.success("删除成功");
        search && handleHistorySearch();
      },
    });
  };

  const deleteHistoryChat = (e: React.MouseEvent, id: string) => {
    e.stopPropagation();
    deleteHistoryRequest(id, true);
  };

  const renameHistoryChat = (e: React.MouseEvent, row: { id: string; name: string }) => {
    e.stopPropagation();

    setConversationId(row.id);
    setConversationName(row.name);
    setOpenRename(true);
  };

  const handleChangeConversationName = (e: React.ChangeEvent<HTMLInputElement>) => {
    setConversationName(e.target.value.trim());
  };

  const handleCancelConversation = () => {
    setOpenRename(false);
    setConversationName("");
  };

  // 重命名提交
  const handleSubmitConversation = () => {
    fetchRequest({
      api: "conversationRename",
      params: {
        agentId: agentId,
        user: userInfo?.id || "anonymous",
        id: conversationId,
        name: conversationName,
      },
      callback: () => {
        handleCancelConversation();
        handleHistorySearch();
      },
    });
  };

  // 批量删除
  const handleBatchDelete = () => {
    if (!historyList.length) return;

    for (const history of historyList) {
      try {
        deleteHistoryRequest(history.id);
      } catch (error) {
        console.error(error.message);
      }
    }

    setTimeout(() => {
      handleHistorySearch();
    }, 100);
  };

  // 滚动加载的防抖函数
  const debouncedLoadMore = useCallback(
    debounce(() => {
      if (!isLoading && !stopRequestList.current && !isLastPage) {
        setIsLoading(true);
        handleHistorySearch();
      }
    }, 300),
    [isLoading, stopRequestList.current, isLastPage]
  );

  useEffect(() => {
    if (visible) {
      // 重置搜索文本
      setHistorySearchText("");

      // 初始化参数
      const updateSearchParams = {
        page: "1",
        size: "20",
        title: "",
        agentId: agentId,
      };
      searchParams.current = updateSearchParams;

      // 重置分页状态
      setTotalPages(0);
      setTotalElements(0);
      setIsLastPage(false);
      stopRequestList.current = false;
      currentRequestPage.current = null; // 重置当前请求页码

      // 加载数据
      handleHistorySearch(updateSearchParams, true);
    } else {
      // 关闭时重置状态
      setInit(false);
      setIsLoading(false);
      setHistoryList([]);
      setHistorySearchText("");
      stopRequestList.current = false;
      currentRequestPage.current = null;
    }
  }, [visible]);

  // 监听滚动事件
  useEffect(() => {
    if (!init) return;
    if (!listRef.current) return; // 如果列表容器不存在，则不再监听

    // 滚动事件处理函数
    const handleScroll = () => {
      // 如果已经是最后一页或者正在加载，则不再监听
      if (isLoading || stopRequestList.current || isLastPage) return;

      const { scrollTop, clientHeight, scrollHeight } = listRef.current;
      // 当滚动到距离底部近的位置时，提前加载下一页
      if (scrollTop + clientHeight >= scrollHeight - 100) {
        // 使用防抖函数加载更多
        debouncedLoadMore();
      }
    };

    // 监听滚动事件
    listRef.current.addEventListener("scroll", handleScroll);
    return () => {
      // 清理函数，组件卸载时移除事件监听
      listRef.current?.removeEventListener("scroll", handleScroll);
    };
  }, [init, isLastPage, isLoading, debouncedLoadMore]); // 添加相关依赖

  return (
    <>
      <Drawer
        title={
          <Flex className="history-drawer-title">
            问答历史
            {/* <span>({historyList.length})</span> */}
          </Flex>
        }
        placement="bottom"
        className="chat-history-drawer"
        closable={true}
        maskClosable={false}
        getContainer={() => {
          if (document.getElementById("shadow-side-panel")) {
            const shadowDom = document.getElementById("shadow-side-panel").shadowRoot;
            return shadowDom.querySelector(".side-panel-content");
          }
        }}
        onClose={handleClose}
        open={visible}
      >
        <Flex className="history-panel" vertical>
          {/* {init && (
            <Flex className="search-area">
              <Flex className="search-input">
                <Input
                  allowClear
                  placeholder="搜索"
                  style={{ width: "100%" }}
                  value={historySearchText}
                  onChange={historyInputChange}
                />
              </Flex>
            </Flex>
          )} */}

          <Flex className="history-box" ref={listRef} vertical>
            {historyList.length === 0 && <Flex className="no-data">暂无会话历史记录呢</Flex>}
            {historyList.map((item) => (
              <Flex className="history-item" key={item.id} onClick={() => handleBack(item)} vertical>
                <Flex className="history-item-head" align="center" justify="space-between">
                  <Flex className="title">{item.title}</Flex>
                  <Flex className="time">{formatDate(item.createdAt)}</Flex>
                </Flex>
                <Flex className="info-bottom" align="center" justify="space-between">
                  <div className="desc">{item.conversations}</div>
                  {/* <Flex className="action">
                    <Flex>
                      <Tooltip
                        placement="top"
                        title="编辑标题"
                        getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                      >
                        <Flex
                          className="round"
                          onClick={(e) => renameHistoryChat(e, item)}
                          justify="center"
                          align="center"
                        >
                          <EditOutlined />
                        </Flex>
                      </Tooltip>
                    </Flex>
                    <Flex>
                      <Tooltip
                        placement="top"
                        title="删除"
                        getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                      >
                        <Flex
                          className="round"
                          onClick={(e) => deleteHistoryChat(e, item.id)}
                          justify="center"
                          align="center"
                        >
                          <DeleteOutlined />
                        </Flex>
                      </Tooltip>
                    </Flex>
                  </Flex> */}
                </Flex>
              </Flex>
            ))}
            {isLoading && <div className="loading">Loading...</div>}
          </Flex>
        </Flex>
      </Drawer>
      <Modal
        title="编辑标题"
        open={openRename}
        className="chat-history-modal"
        width={"90%"}
        centered={true}
        okText="确认"
        cancelText="取消"
        onOk={handleSubmitConversation}
        onCancel={handleCancelConversation}
        getContainer={() => {
          if (document.getElementById("shadow-side-panel")) {
            const shadowDom = document.getElementById("shadow-side-panel").shadowRoot;
            return shadowDom.querySelector(".side-panel-content");
          }
        }}
      >
        <Flex>
          <Input maxLength={200} showCount={true} value={conversationName} onChange={handleChangeConversationName} />
        </Flex>
      </Modal>
    </>
  );
};

export default HistoryDrawer;
