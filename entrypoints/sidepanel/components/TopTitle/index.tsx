import { Flex, Layout } from "antd";
import "./index.less";
import { LeftOutlined } from "@ant-design/icons";
import React, { ReactNode } from "react";

interface TopTitleProps {
  title: string;
  titleDetail?: boolean;
  handleBackPar?: () => void;
  /**
   * 右侧插槽内容
   */
  rightSlot?: ReactNode;
}
const TopTitle: React.FC<TopTitleProps> = ({ title, titleDetail, handleBackPar, rightSlot }) => {
  return (
    <>
      <Flex className="side-panel-title">
        <Flex
          align="center"
          className="title-container"
          style={{ flex: rightSlot ? "1" : "auto", marginRight: rightSlot ? "16px" : "0" }}
        >
          {titleDetail ? (
            <Flex className="back-arrow" style={{ cursor: "pointer" }} onClick={handleBackPar}>
              <LeftOutlined className="back-width " />
              <div className="title-text-detail common title-ellipsis">{title}</div>
            </Flex>
          ) : (
            <div className="title-text common title-ellipsis">{title}</div>
          )}
        </Flex>
        {rightSlot && (
          <Flex align="center" className="right-slot-container">
            {rightSlot}
          </Flex>
        )}
      </Flex>
    </>
  );
};

export default TopTitle;
