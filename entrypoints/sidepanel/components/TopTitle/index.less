@import "@/assets/styles/variables";
.side-panel-title {
  z-index: 100;
  padding: 16px 16px;
  width: 100%;
  position: fixed;
  top: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  // background-color: #fff;

  .title-container {
    min-width: 0; // 允许容器收缩到比内容更小
    overflow: hidden; // 确保溢出内容被隐藏
  }

  .right-slot-container {
    flex-shrink: 0; // 防止右侧插槽被压缩
  }

  .title-ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .common {
    line-height: var(--ant-line-height-heading-2);
    font-family: @side-panel-font-family-bold;
    font-size: var(--ant-font-size-heading-4);
  }
  .title-text {
    color: transparent;
    background: linear-gradient(116deg, #1888ff 16%, #2f54eb 88%);
    -webkit-background-clip: text;
    user-select: none;
  }
  .back-arrow {
    width: 100%;
  }
  .title-text-detail {
    width: calc(100% - 30px);
    color: var(--ant-color-text-base);
    display: inline-block;
  }
  .back-width {
    font-size: var(--ant-font-size-heading-4);
    color: var(--ant-color-text);
    cursor: pointer;
    margin-right: 6px;
  }
}
