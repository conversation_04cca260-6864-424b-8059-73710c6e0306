import { createContext, useContext, useEffect, useState } from "react";
import { getUserInfo } from "@/utils/auth";
interface PermissionContextType {
  permissions: string[];
  setPermissions: React.Dispatch<React.SetStateAction<string[]>>;
  userInfo: UserInfo;
  setUserInfo: React.Dispatch<React.SetStateAction<UserInfo>>;
  point: { effectivePoint?: string; totalPoint?: string };
  setPoint: React.Dispatch<React.SetStateAction<{ effectivePoint: string; totalPoint: string }>>;
}

const PermissionContext = createContext<PermissionContextType>({
  permissions: [],
  setPermissions: () => {},
  userInfo: {},
  setUserInfo: () => {},
  point: {},
  setPoint: () => {},
});

export const PermissionProvider = ({ children }) => {
  const [permissions, setPermissions] = useState([]);
  const [userInfo, setUserInfo] = useState<UserInfo>({});
  const [point, setPoint] = useState<UserInfo>({});

  useEffect(() => {
    cacheGet("permissions").then((res) => {
      if (res) {
        setPermissions(JSON.parse(res) || []);
      }
    });
    cacheGet("userInfo").then((res) => {
      if (res) {
        setUserInfo(JSON.parse(res) || {});
      }
    });
    cacheGet("point").then((res) => {
      if (res) {
        setPoint(JSON.parse(res) || {});
      }
    });
  }, []);

  useEffect(() => {
    // 监听userInfo变化
    const handleNoteListChanged = (changes) => {
      if (changes["userInfo"]) {
        if (changes["userInfo"].newValue) {
          setUserInfo(JSON.parse(changes["userInfo"].newValue) || {});
        }
        if (changes["userInfo"].oldValue) {
          setUserInfo({});
        }
      }
      if (changes["permissions"]) {
          // console.log('changes["permissions"].newValue', changes["permissions"].newValue)
          setPermissions(JSON.parse(changes["permissions"].newValue) || []);
      }
      if (changes["point"]) {
        setPoint(JSON.parse(changes["point"].newValue) || {});
    }
    };
    browser.storage.local.onChanged.addListener(handleNoteListChanged);
    return () => {
      browser.storage.local.onChanged.removeListener(handleNoteListChanged);
    };
  }, []);

  return (
    <PermissionContext.Provider value={{ permissions, setPermissions, userInfo, setUserInfo, point, setPoint }}>
      {children}
    </PermissionContext.Provider>
  );
};

export const usePermissions = () => {
  return useContext(PermissionContext);
};
