@import "@/assets/styles/variables";
.betweenCenter() {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.horCenter() {
  display: flex;
  align-items: center;
}

.center() {
  display: flex;
  justify-content: center;
  align-items: center;
}

.sino-cursor-not-allowed {
  cursor: not-allowed !important;
}

.highlight-menu-box {
  display: inline-block;
  /*cursor: pointer;*/
  border: 0;
  background: #fff;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.25);
  border-radius: 10px;

  .action-wrapper {
    position: relative;

    .action-wrapper-container {
      .horCenter();
      white-space: nowrap;
      padding-left: 8px;
      height: 35px;

      & > span,
      & > b {
        .horCenter();
        padding: 0 8px;
        height: 100%;

        &:hover {
          background: #f8f8f8;
        }
      }

      & > span {
        font-size: 14px;
        font-family: Inter, Inter;
        font-weight: 400;
        color: #333333;
      }
    }

    .action-item {
      font-size: 14px;
      font-family: Inter, Inter;
      font-weight: 400;
      color: #333333;
      line-height: 22px;
      cursor: pointer;

      img {
        margin-left: 10px;
      }
    }

    .action-trigger {
      width: 30px;
      box-sizing: border-box;
    }

    .icon {
      font-size: 14px;
      font-family: Inter, Inter;
      font-weight: 400;
      color: #666666;
      background: #f8f8f8;
      border-radius: 0px 10px 10px 0px;

      img {
        margin-right: 3px;
      }
    }

    .close {
      position: absolute;
      top: -4px;
      right: -4px;
      cursor: pointer;
    }
  }

  .explain-wrapper {
    width: 450px;
    padding: 10px 16px 9px 16px;

    .explain-header {
      .betweenCenter();
      margin-bottom: 10px;

      & > div {
        .horCenter();
        font-size: 16px;
        font-family: Inter, Inter;
        font-family: @side-panel-font-family-bold;
        color: #000000;

        img {
          margin-left: 8px;
          width: 13px;
        }
      }

      & > img {
        width: 15px;
        cursor: pointer;
      }

      .lang-drop-box {
        .betweenCenter();
        padding: 0 10px;

        .lang-drop {
          height: 40px;
          font-size: 14px;
          color: #333;
          font-weight: Medium;
          line-height: 22px;
          padding: 8px 7px 8px 20px;
          border-radius: 10px;
        }

        .lang-drop-menu-item {
          .betweenCenter();

          & > span:first-of-type {
            margin-right: 10px;
          }
        }
      }
    }

    .explain-question {
      margin-bottom: 10px;
      position: relative;
      background: #f8f8f8;
      border-radius: 10px;

      &.view {
        padding-right: 30px;
      }

      &.edit {
        padding-bottom: 42px;
      }

      .question-input {
        padding: 4px 10px 3px;
        min-height: 30px;
        background: #f8f8f8;
        border-radius: 10px;
        border: none;
        box-shadow: none;
        line-height: 25px;
        font-size: 14px;
        font-family: Inter, Inter;
        font-weight: 400;
        color: #333333;
      }

      .edit-icon {
        position: absolute;
        top: 5px;
        right: 5px;
        cursor: pointer;
      }

      .bottom {
        position: absolute;
        bottom: 0;
        right: 0;
        padding: 6px;

        button {
          border-radius: 30px;
        }

        button + button {
          margin-left: 6px;
        }
      }
    }

    .result-wrapper {
      .result-header {
        .betweenCenter();
        margin-bottom: 10px;
        font-size: 14px;
        font-family: Inter, Inter;
        font-weight: 400;
        color: #333333;

        img {
          cursor: pointer;
        }

        img + img {
          margin-left: 10px;
        }

        .result-icon {
          .horCenter();
        }
      }

      .sino-result {
        margin-bottom: 10px;

        .result-info {
          padding: 12px;
          height: 170px;
          font-size: 14px;
          font-family: Inter, Inter;
          font-weight: 400;
          color: #333333;
          line-height: 22px;
          border: 1px solid #d7d7d7;
          border-radius: 10px;
          box-shadow: none;

          &.result-overflow-y {
            overflow-y: auto;
          }
        }

        .result-info-cursor {
          display: inline-block;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: #000000;
          animation: blink 600ms infinite;
        }

        @keyframes blink {
          0% {
            opacity: 0;
          }
          100% {
            opacity: 1;
          }
        }
      }

      .result-bottom {
        .betweenCenter();

        .sino-feedback {
          display: flex;

          span {
            .center();
            margin-right: 10px;
            width: 28px;
            height: 28px;
            border-radius: 10px;
            cursor: pointer;

            &:hover {
              background: #d7d7d7;
            }

            img {
              width: 18px;
            }
          }

          .rating {
            width: 28px;
          }
        }

        .chat-btn {
          .horCenter();
          padding: 0 12px;
          height: 32px;
          font-size: 14px;
          font-family: Inter, Inter;
          font-weight: 500;
          color: @primary-color;
          background: #ffffff;
          border-radius: 30px;
          border: 1px solid @primary-color;
          cursor: pointer;

          img {
            width: 19px;
            margin-right: 7px;
          }
        }
      }
    }
  }
}

.sino-drop-menu {
  .ant-dropdown-menu .ant-dropdown-menu-item {
    border-radius: 10px !important;
  }

  .ant-dropdown-menu-item {
    &:hover {
      background: #f8f8f8;
    }

    &:first-of-type {
      padding-left: 10px;

      &:hover {
        background: transparent;
      }
    }
  }

  .sino-drop-title,
  .sino-drop-item {
    .betweenCenter();
    min-width: 160px;
  }

  .sino-drop-title {
  }

  .sino-drop-item {
    span {
      & > img {
        width: 5px;
        margin-right: 13px;
      }
    }

    & > img {
      width: 12px;
    }
  }
}
