/** 侧边栏主组件 */
import React, { useEffect, useRef, useState } from "react";
import { Button, Dropdown, Tooltip } from "antd";
import { DownOutlined } from "@ant-design/icons";
import { LANGDEFAULTKEY, SELECTEDDEFAULTKEY, TIPLANGKEY } from "@/config/options/ai";
import "./index.less";
import { copyText } from "@/utils/clipboard.ts";
import { imgBase } from "@/config/env.ts";
import { langList } from "@/config/options/ai.ts";
import { Prompt } from "@/types/prompt";
import { listPrompts } from "@/api/prompt.ts";

const HighlightMenu: React.FC = (props) => {
  const resultDomRef = useRef<HTMLDivElement>(null);

  const [value, setValue] = useState(props.text);
  const [action, setAction] = useState<string>("");
  const [top, setTop] = useState(SELECTEDDEFAULTKEY);
  const [generating, setGenerating] = useState(false);
  const [result, setResult] = useState("");
  const [messageId, setMessageId] = useState("");
  const [rating, setRating] = useState("");
  const [lang, setLang] = useState("0");
  const [promptList, setPromptList] = useState<Array<Prompt>>();

  useEffect(() => {
    const fetchPrompt = async () => {
      const promptList: Array<Prompt> = await listPrompts(null);
      setPromptList(promptList);
    };
    fetchPrompt();
  }, []);

  // 关闭弹窗
  const closeTooltip = () => {
    setGenerating(false);
    setResult("");
    setAction("");
    props.close();
  };

  // 消息队列，始终位于滚动条最底部
  const scrollToBottom = () => {
    if (resultDomRef.current) resultDomRef.current.scrollTop = resultDomRef.current.scrollHeight;
  };

  useEffect(() => {
    browser.storage.onChanged.addListener((changes, storageName) => {
      if (Object.prototype.hasOwnProperty.call(changes, "result") && changes.result.newValue) {
        const res = changes.result.newValue;

        if (Object.prototype.hasOwnProperty.call(res, "generating")) {
          setGenerating(res.generating);
        }
        if (res.result) {
          setResult(res.result);
        }
        if (res.messageId && !messageId) {
          setMessageId(res.messageId);
        }
      } else if (Object.prototype.hasOwnProperty.call(changes, "feedback") && changes.feedback.newValue) {
        const res = changes.feedback.newValue;
        res.status === 200 && setRating(res.rating);
      }
    });
  }, []);

  useEffect(() => {
    setAction(props.text ? "-1" : "");
    setGenerating(false);
    setResult("");
    setValue(props.text);
  }, [props.text]);

  useEffect(() => {
    if (action == "") return;

    changeAction();
  }, [action]);

  useEffect(() => {
    scrollToBottom();
  }, [result]);

  // 重新生成
  useEffect(() => {
    if (!generating) return;

    regeneration();
  }, [generating]);

  const changeAction = () => {
    // const dom = document.querySelector('.HighlightMenu-box')
    const popup = {
      popupWidth: action == "-1" ? 300 : 500,
      popupHeight: action == "-1" ? 35 : 380,
    };
    props.changeConent(popup);
  };

  // 点击排列的菜单
  const explainClick = (key) => {
    setAction(key);
    changeAction();
    // 生成
    updateExplain();
  };

  // 点击 dropdown 里面的菜单
  const menuClick = (event) => {
    if (event.key === "1") return;

    const key = (Number(event.key) - 2).toString();
    setAction(key);
    // 生成
    updateExplain();
  };

  // 是否置顶
  const topToggle = (event, key) => {
    event.stopPropagation();

    const _top = JSON.parse(JSON.stringify(top));
    if (_top.includes(key)) {
      const _index = _top.indexOf(key);
      _top.splice(_index, 1);
    } else {
      _top.push(key);
    }

    setTop(_top);
  };

  // 重新生成点击事件
  const updateExplain = () => {
    // 重新生成
    setResult("");
    setRating("");

    setGenerating(true);
  };
  // 重新生成请求处理
  const regeneration = async () => {
    const langInfo = action == TIPLANGKEY ? langList[lang].label : LANGDEFAULTKEY;

    let query = promptList[action].tip;
    query = query.replace("${lang}", langInfo);
    query = query.replace("${content}", value + "");

    // fixme 添加
  };

  // 复制
  const copyResult = () => {
    if (generating) return;
    copyText(result);
  };

  const handleLangClick = (e) => {
    if (generating) return;
    setLang(e.key);
    updateExplain();
  };

  // dropDown 渲染
  const dropList = promptList.filter(({ key }) => key !== "0" && key !== "1" && key !== "2");
  const items = dropList.map(({ key, label }) => {
    return {
      key: (Number(key) + 2).toString(),
      label: (
        <div className="sino-drop-item">
          <span>
            <img src={`${imgBase}adorn_icon.svg`} alt="" />
            {label}
          </span>
          <img
            src={`${imgBase}${top.includes(key) ? "top_checked" : "top_default"}.svg`}
            alt=""
            onClick={(event) => topToggle(event, key)}
          />
        </div>
      ),
    };
  });

  return (
    <div className="highlight-menu-box">
      {action && action == "-1" && (
        <div className="action-wrapper">
          <div className="action-wrapper-container">
            {top.length > 0 &&
              top.map((t) => (
                <span className="action-item" key={t} onClick={() => explainClick(t)}>
                  {promptList[t].label}
                </span>
              ))}
            <Dropdown
              overlayClassName={"sino-drop-menu"}
              getPopupContainer={(triggerNode) => triggerNode.parentNode}
              menu={{
                items: [...items],
                onClick: menuClick,
              }}
            >
              <span className="action-trigger">
                <img src={`${imgBase}top_arrow_icon.svg`} alt="" />
              </span>
            </Dropdown>
            <b className="icon">
              <img src={`${imgBase}logo%20.svg`} alt="" />
            </b>
            <img src={`${imgBase}circle_close_icon.svg`} alt="" className="close" onClick={closeTooltip} />
          </div>
        </div>
      )}
      {action && action != "-1" && (
        <div className="explain-wrapper">
          <div className="explain-header">
            <div>
              {promptList[action].label}
              {action === TIPLANGKEY && (
                <div className="lang-drop-box">
                  <Dropdown
                    getPopupContainer={(triggerNode) => triggerNode.parentNode}
                    menu={{
                      items: langList,
                      onClick: handleLangClick,
                    }}
                    className="lang-drop"
                  >
                    <Button>
                      {langList.map((item: any) => {
                        if (item.key == lang) {
                          return (
                            <div key={item.key} className="lang-drop-menu-item">
                              <span>{item.label}</span>
                              <DownOutlined />
                            </div>
                          );
                        }
                      })}
                    </Button>
                  </Dropdown>
                </div>
              )}
            </div>
            <img src={`${imgBase}close_icon.svg`} alt="" onClick={closeTooltip} />
          </div>
          <div className={`explain-question view`}>
            <div className="question-input">{value}</div>
          </div>
          <div className="result-wrapper">
            <div className="result-header">
              <span>{generating ? "正在回答..." : "回答"}</span>
              <span className="result-icon">
                <Tooltip
                  title="重新生成"
                  className={`${generating ? "sino-cursor-not-allowed" : ""}`}
                  onClick={updateExplain}
                >
                  <img src={`${imgBase}regen_icon.svg`} alt="" />
                </Tooltip>
                <Tooltip title="复制" className={`${generating ? "sino-cursor-not-allowed" : ""}`} onClick={copyResult}>
                  <img src={`${imgBase}copy_icon.svg`} alt="" />
                </Tooltip>
              </span>
            </div>
            <div className="sino-result">
              <div className="result-info result-overflow-y" ref={resultDomRef}>
                {result}
                {generating && <span className="result-info-cursor"></span>}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HighlightMenu;
