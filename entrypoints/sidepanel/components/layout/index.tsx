/** 侧边栏布局组件 */
import React, { useEffect, useState } from "react";
import "./index.less";
import { Outlet, useNavigate } from "react-router-dom";
import { navButtons, OCR_MENU_ID, ReturnIcon } from "@/config/menu";
import { Tooltip } from "antd";
import setModifyItem, { MENU_NAME_STORAGE_KEY } from "@/utils/browserStorageCurrentPage";

const SidePanelLayout: React.FC = () => {
  const navigate = useNavigate();
  const [selected, setSelected] = useState(navButtons[0]);
  const [detail, setDetail] = useState("");

  useEffect(() => {
    chrome.storage.local.get(["isImgType"], function (result) {
      if (result.isImgType) {
        setSelected(navButtons[OCR_MENU_ID]);
        navigate(navButtons[OCR_MENU_ID].url!);
        chrome.storage.local.remove("isImgType");
      } else {
        navigate(selected.url!);
      }
    });
    chrome.runtime.onMessage.addListener(openListen);
    return () => {
      browser.runtime.onMessage.removeListener(openListen);
    };
  }, []);

  // 组件注册时，便开始监听用户自身便签数据的变化，一旦变化，重新渲染列表
  useEffect(() => {
    const handleNoteListChanged = (changes) => {
      let sinoKey = sessionStorage.getItem("sino-tap-key");
      const menuNameChange = changes[MENU_NAME_STORAGE_KEY + sinoKey];
      if (menuNameChange) {
        setDetail(menuNameChange.newValue.value);
      }
    };
    browser.storage.local.onChanged.addListener(handleNoteListChanged);
    return () => {
      browser.storage.local.onChanged.removeListener(handleNoteListChanged);
    };
  }, []);

  const openListen = (message) => {
    if (message.extractImg64) {
      setSelected(navButtons[OCR_MENU_ID]);
      navigate(navButtons[OCR_MENU_ID].url!);
    }
  };
  const handleClickTool = (item: any) => {
    setModifyItem(MENU_NAME_STORAGE_KEY, {
      key: new Date().getTime(),
      value: "",
    });

    if (item.id == "7") {
      browser.runtime.openOptionsPage();
    } else {
      setSelected(item);
      navigate(item.url!);
    }
  };
  const handleNavigate = (e) => {
    setSelected(navButtons[e]);
  };
  return (
    <div className="side-panel-container">
      <div className="side-panel-content">
        <div className="side-panel-title">
          <div className="title-text">
            {detail ? (
              <div>
                {ReturnIcon} <span>{detail} </span>
              </div>
            ) : selected ? (
              selected.title
            ) : (
              ""
            )}
            {/*{selected ? selected.title : ""}*/}
          </div>
        </div>
        <div className="side-panel-route">
          <Outlet />
        </div>
      </div>
      <div className="side-panel-menu">
        <div className="menu-list">
          {navButtons.map((tool) => {
            return (
              <Tooltip placement="left" title={tool.title} key={tool.id}>
                <div key={tool.id} className="icon-tool" onClick={() => handleClickTool(tool)}>
                  {tool.id == selected.id ? tool.selectedImg : tool.unselectedImg}
                </div>
              </Tooltip>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default SidePanelLayout;
