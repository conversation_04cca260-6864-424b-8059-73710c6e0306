@import "/assets/styles/variables";

html,
body {
  /* 默认样式 */
  height: 100vh;
  overflow: hidden;
}

.side-panel-container {
  background: #f7f7f7;
  padding: 4px 4px 0px 4px;
  /* 侧边栏容器 */
  height: 100vh;
  width: 100vw;
  max-width: 100%;
  min-width: @side-panel-min-width;
  overflow-y: auto;
  // background: #f2f2f2;
  display: flex;
  font-family: @side-panel-font-family;

  .side-panel-content {
    /* 侧边栏主区域 */
    border-radius: 8px;
    backdrop-filter: blur(10px);
    margin-bottom: 10px;
    flex: 1;
    width: calc(100vw - @side-panel-menu-width);
    display: flex;
    flex-direction: column;
    // min-width: @side-panel-content-min-width;
    position: relative;
    background: @side-panel-background-color;

    .side-panel-title {
      /* 标题区域 */
      z-index: 100;
      padding: 8px 0;
      // height: 56px;
      // line-height: 56px;
      width: 100%;
      display: flex;
      align-items: center;

      .title-sign {
        width: 32px;
        height: 32px;
        object-fit: cover;
        margin-left: 18px;
      }

      .title-text {
        user-select: none;
        margin-left: 16px;
        font-family: @side-panel-font-family-bold;
        height: 16px;
        font-size: 28px;
        color: #121212;
        line-height: 40px;

        // span{
        //   white-space: nowrap;
        //   overflow: hidden;
        //   text-overflow: ellipsis;
        //   width: 261px;
        //   display: flex;
        //   align-items: center;
        // }
      }
    }

    .side-panel-route {
      /* 侧边栏内容区域 */
      // background-color: @side-panel-background-color;
      flex: 1;
      padding: @side-panel-route-padding;
    }
  }

  .side-panel-menu {
    /* 侧边栏菜单 */
    width: @side-panel-menu-width;
    z-index: 999;
    flex-shrink: 0;
    background-color: @side-panel-menu-background-color;

    .menu-list {
      user-select: none;
      padding-top: 10px;
      width: @side-panel-menu-width;
      // background-color: #F5F5F5;
      position: fixed;
      top: 0;
      right: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 19px;
    }

    .icon-tool {
      border-radius: 5px;
      width: 30px;
      height: 30px;
      cursor: pointer;
    }

    .icon-tool:hover {
      background-color: #fff;
    }
  }
}

.side-panel-container::-webkit-scrollbar {
  width: 0;
}
