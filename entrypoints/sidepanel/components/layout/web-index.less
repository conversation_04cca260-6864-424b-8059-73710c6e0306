//@import "/assets/styles/variables";
@import "/assets/styles/index.less";
html,
body {
  /* 默认样式 */
  // height: 100vh;
  // position: relative;
  // overflow: hidden;
}

.side-panel-container {
  /* 侧边栏容器 */
  text-align: left;
  background: var(--ant-color-bg-container);
  // padding: var(--ant-padding-xxs);
  // padding-bottom: 0px;
  height: 100%;
  width: 100%;
  max-width: 100%;
  min-width: @side-panel-min-width;
  overflow-y: auto;
  display: flex;
  font-family: @side-panel-font-family;
  position: relative;
  .c-resize {
    position: absolute;
    left: 0px;
    width: 4px;
    height: 100%;
    z-index: 10;
    cursor: ew-resize;
    .arrow {
      pointer-events: none;
      display: none;
      opacity: 0;
    }
    &:hover {
      .arrow {
        display: inline-block;
      }
    }
  }
  .content-info {
    background: var(--ant-color-bg-container);
  }
  .side-panel-content {
    /* 侧边栏主区域 */
    border-radius: var(--ant-border-radius-lg);
    backdrop-filter: blur(10px);
    margin-bottom: 10px;
    height: calc(100% - 4px);
    flex: 1;
    width: calc(100% - 64px);
    border: 1px solid var(--ant-color-border);
    background: var(--ant-color-bg-container);
    position: relative;
    // .side-panel-title {
    //   /* 标题区域 */
    //   z-index: 100;
    //   padding: 8px 0;
    //   width: 100%;
    //   position: fixed;
    //   top: 0;
    //   .title-sign {
    //     width: 32px;
    //     height: 32px;
    //     object-fit: cover;
    //     margin-left: 18px;
    //   }

    //   .title-text {
    //     background: linear-gradient(116deg, #1888ff 16%, #2f54eb 88%);
    //     -webkit-background-clip: text;
    //     user-select: none;
    //     margin-left: var(--ant-margin);
    //     font-family: @side-panel-font-family-bold;
    //     font-size: var(--ant-font-size-heading-2);
    //     color: transparent;
    //     line-height: var(--ant-line-height-heading-2);

    //     .title-text-detail{

    //       span{
    //         overflow: hidden;
    //         white-space: nowrap;
    //         text-overflow: ellipsis;
    //         color:var(--ant-color-text-base);
    //         width: 290px;
    //         padding-left:26px;
    //         display: inline-block;
    //       }
    //     }
    //   }
    // }
    .side-panel-route {
      width: 100%;
      margin-top: 56px;
      text-align: left;
      /* 侧边栏内容区域 */
      overflow-y: auto;
      flex: 1 1 100%;
      padding: 0 var(--ant-padding) var(--ant-padding);
    }
  }
  .layout-menu-func {
    padding-top: var(--ant-padding);
    gap: var(--ant-margin);
    .layout-menu-div {
      margin: 0px auto;
    }
    .layout-text {
      font-size: var(--ant-font-size-sm);
      text-align: center;
      cursor: pointer;
    }
    .layout-text-active {
      color: var(--ant-color-primary);
    }
  }
  .layout-menu-func-web{
    padding-top: var(--ant-margin-xxs);
    >div:nth-child(2){
      margin-top: -12px;
    }
  }
  .layout-icon {
    margin: 0px auto;
    border-radius: var(--ant-border-radius);
    .btn-icon {
      .anticon {
        font-size: var(--ant-font-size-xl);
        .icon {
          width: var(--ant-font-size-xl);
          height: var(--ant-font-size-xl);
        }
      }
      &:hover {
        color: var(--ant-color-primary-bg-hover);
      }
    }
    .btn-icon-active {
      background: var(--ant-blue-1);
    }
  }
  .layout-icon-seting {
    margin-bottom: var(--ant-margin);
  }
}

.side-panel-container::-webkit-scrollbar {
  width: 0;
}
