import useEventSource from "@/hooks/useEventSource.ts";
import { IFetchSSERequestMessage } from "@/types/message";
import { FETCH_REQUEST_TYPE, FETCH_RESPONSE_TYPE } from "@/entrypoints/background/handlers/fetchMessageHandler.ts";
import { getTenantId } from "@/utils/auth.ts";

/** 普通网络请求端口的名称 */
export const FETCH_SSE_PORT_NAME = "fetchSSE";

import { swManager } from "@/utils/serviceWorkerManager";

const registerFetchSSEMessageHandler = () => {
  // 使用状态管理器检查是否已注册
  if (swManager.isInitialized("sseHandlerRegistered")) {
    console.debug("SSE消息处理器已经注册，跳过重复注册");
    return;
  }

  console.debug("开始注册SSE消息处理器...");

  const handleFetchSSEConnected = () => {
    browser.runtime.onConnect.addListener((port) => {
      if (port.name !== FETCH_SSE_PORT_NAME) return;
      console.debug(`${FETCH_SSE_PORT_NAME} 端口已建立连接`);
      let eventSourceInstance: ReturnType<typeof useEventSource> | null = null; // To store the event source instance

      port.onMessage.addListener(async (msg: IFetchSSERequestMessage) => {
        console.debug(`background ${FETCH_SSE_PORT_NAME} 收到了消息`, msg);

        // 处理停止指令
        if (msg.instruct == "stop") {
          eventSourceInstance?.stop();
          eventSourceInstance = null;
          port.postMessage({
            process: "cancel",
            type: FETCH_RESPONSE_TYPE,
            msg: "已经取消加载了哦！",
          });
          return;
        }

        if (msg.type === FETCH_REQUEST_TYPE) {
          // 检查网络连接
          if (!navigator.onLine) {
            port.postMessage({
              process: "error",
              type: FETCH_RESPONSE_TYPE,
              msg: "网络连接已断开，请检查网络连接",
            });
            return;
          }

          try {
            const tenantId = await getTenantId();

            // 如果已有活跃的EventSource，先停止它
            if (eventSourceInstance) {
              eventSourceInstance.stop();
              eventSourceInstance = null;
            }

            const eventSource = useEventSource({
              url: msg.body.insId
                ? `${import.meta.env["VITE_API_BASE"]}${import.meta.env["VITE_API_BASE_INS"]}${msg.url}`
                : // `${"https://copilot.sino-bridge.com/api"}${import.meta.env["VITE_API_BASE_INS"]}${msg.url}`
                  `${import.meta.env["VITE_AI_API_BASE"]}${msg.url}`,
              method: "POST",
              headers: { ...msg.headers, ...{ tenantId: tenantId } },
              body: msg.body,
              onMessage: (message) => {
                try {
                  port.postMessage({
                    url: msg.url,
                    process: "onmessage",
                    type: FETCH_RESPONSE_TYPE,
                    ...message,
                  });
                } catch (error) {
                  console.debug("发送SSE消息失败:", error);
                }
              },
              onAgentLog: (log) => {
                try {
                  port.postMessage({
                    url: msg.url,
                    process: "onagentlog",
                    type: FETCH_RESPONSE_TYPE,
                    ...log,
                  });
                } catch (error) {
                  console.debug("发送Agent日志消息失败:", error);
                }
              },
              onMessageFile: (message) => {
                try {
                  port.postMessage({
                    url: msg.url,
                    process: "onmessagefile",
                    type: FETCH_RESPONSE_TYPE,
                    ...message,
                  });
                } catch (error) {
                  console.debug("发送SSE文件消息失败:", error);
                }
              },
              onMessageEnd: (data) => {
                try {
                  port.postMessage({
                    url: msg.url,
                    process: "onmessage",
                    type: FETCH_RESPONSE_TYPE,
                    ...data,
                  });
                } catch (error) {
                  console.debug("发送SSE结束消息失败:", error);
                }
              },
              onClose: () => {
                try {
                  port.postMessage({
                    url: msg.url,
                    process: "finished",
                    type: FETCH_RESPONSE_TYPE,
                  });
                } catch (error) {
                  console.debug("发送SSE关闭消息失败:", error);
                }
                eventSourceInstance = null;
              },
              onError: () => {
                try {
                  port.postMessage({
                    url: msg.url,
                    process: "error",
                    type: FETCH_RESPONSE_TYPE,
                  });
                } catch (error) {
                  console.debug("发送SSE错误消息失败:", error);
                }
                eventSourceInstance = null;
              },
            });

            eventSourceInstance = eventSource;

            if (msg.instruct === "start") {
              eventSource.start(msg.query);
            } else if (msg.instruct === "stop") {
              port.postMessage({
                url: msg.url,
                instruct: "stop",
                process: "finished",
                type: FETCH_RESPONSE_TYPE,
              });
              eventSource.stop();
              eventSourceInstance = null;
              console.log("Event stream stopped"); // 更清晰的日志
            }
          } catch (error) {
            console.error("处理SSE请求时发生错误:", error);
            try {
              port.postMessage({
                url: msg.url,
                process: "error",
                type: FETCH_RESPONSE_TYPE,
                msg: "处理请求时发生错误",
              });
            } catch (postError) {
              console.debug("发送错误响应失败:", postError);
            }
          }
        }
      });

      port.onDisconnect.addListener((port) => {
        console.debug(`content主动断开了 ${FETCH_SSE_PORT_NAME} 端口的连接`);
        // 清理EventSource实例
        if (eventSourceInstance) {
          eventSourceInstance.stop();
          eventSourceInstance = null;
        }
      });
    });
  };

  // 在安装时与启动时注册消息响应事件，确保浏览器进程全部关闭再打开后连接依然可以打开
  handleFetchSSEConnected();

  // 使用状态管理器标记已注册
  swManager.markInitialized("sseHandlerRegistered", true);
  console.debug("SSE消息处理器注册完成");
};

export default registerFetchSSEMessageHandler;
