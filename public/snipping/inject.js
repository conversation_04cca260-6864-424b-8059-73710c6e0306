"use strict";

// 定义全局变量
var monitor = window.monitor;
var capture = window.capture;
var guide = window.guide;

// 尝试移除现有的 guide、capture 和 monitor 元素
try {
  guide.remove();
  capture.remove();
  monitor.remove();
} catch (e) {}

// 定义 capture 模块，用于捕获屏幕区域
capture = (function () {
  const rect = {}; // 存储矩形区域的坐标
  let box; // 用于显示捕获区域的 div 元素

  // 计算矩形区域的左、上、宽、高
  const calc = () => ({
    left: Math.min(rect.lt.x, rect.rb.x),
    top: Math.min(rect.lt.y, rect.rb.y),
    width: Math.abs(rect.rb.x - rect.lt.x),
    height: Math.abs(rect.rb.y - rect.lt.y),
  });
  const getTrueCalc = () => ({
    left: Math.min(rect.lt.x, rect.rb.x) * window.devicePixelRatio,
    top: Math.min(rect.lt.y, rect.rb.y) * window.devicePixelRatio,
    width: Math.abs(rect.rb.x - rect.lt.x) * window.devicePixelRatio,
    height: Math.abs(rect.rb.y - rect.lt.y) * window.devicePixelRatio,
  });

  // 更新捕获区域的大小和位置
  function update(e) {
    rect.rb.x = e.clientX;
    rect.rb.y = e.clientY;

    for (const [key, value] of Object.entries(calc())) {
      box.style[key] = value + "px";
    }
  }

  // 移除捕获区域并发送消息
  function remove() {
    chrome.runtime.sendMessage({
      type: "captured",
      ...getTrueCalc(),
      title: document.title,
      service: window.service, // 用于反向图像搜索扩展
    });
    guide.remove();
    capture.remove();
    monitor.remove();
  }

  // 鼠标按下事件处理函数，开始捕获区域
  function mousedown(e) {
    // 防止在 Firefox 中选择内容
    e.stopPropagation();
    e.preventDefault();
    box = document.createElement("div");
    box.setAttribute("class", "itrisearch-box");

    rect.lt = {
      x: e.clientX,
      y: e.clientY,
    };
    rect.rb = {
      x: e.clientX,
      y: e.clientY,
    };

    document.addEventListener("mousemove", update);
    document.addEventListener("mouseup", remove);
    document.documentElement.appendChild(box);
  }

  return {
    install: function () {
      document.addEventListener("mousedown", mousedown);
    },
    remove: function () {
      document.removeEventListener("mousedown", mousedown);
      document.removeEventListener("mousemove", update);
      document.removeEventListener("mouseup", remove);
      for (const e of [...document.querySelectorAll(".itrisearch-box")]) {
        e.remove();
      }
    },
  };
})();

// 定义 guide 模块，用于显示捕获区域的辅助线
guide = (function () {
  let guide1;
  let guide2;
  let guide3;

  // 更新辅助线的位置
  function position(left, top) {
    guide1.style.width = left + "px";
    guide2.style.height = top + "px";
  }

  // 鼠标移动事件处理函数，更新辅助线的位置
  function update(e) {
    position(e.clientX, e.clientY);
  }

  return {
    install() {
      guide1 = document.createElement("div");
      guide2 = document.createElement("div");
      guide3 = document.createElement("div");
      guide1.setAttribute("class", "itrisearch-guide-1");
      guide2.setAttribute("class", "itrisearch-guide-2");
      guide3.setAttribute("class", "itrisearch-guide-3");
      document.documentElement.append(guide3, guide1, guide2);
      document.addEventListener("mousemove", update, false);
    },
    remove() {
      document.removeEventListener("mousemove", update, false);
      for (const e of [...document.querySelectorAll(".itrisearch-guide-1, .itrisearch-guide-2, .itrisearch-guide-3")]) {
        e.remove();
      }
      capture.remove();
    },
  };
})();

// 定义 monitor 模块，用于监听键盘事件
monitor = (function () {
  // 键盘按下事件处理函数，按下 Escape 键时取消捕获
  const keydown = (e) => {
    if (e.code === "Escape") {
      guide.remove();
      capture.remove();
      monitor.remove();
      chrome.runtime.sendMessage({
        method: "aborted",
      });
    }
  };

  return {
    install() {
      window.addEventListener("keydown", keydown);
    },
    remove() {
      window.removeEventListener("keydown", keydown);
    },
  };
})();

// 安装 guide、capture 和 monitor 模块
guide.install();
capture.install();
monitor.install();
