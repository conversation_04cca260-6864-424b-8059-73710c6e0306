.itrisearch-box,
.itrisearch-guide-1,
.itrisearch-guide-2,
.itrisearch-guide-3 {
  all: initial;
}

.itrisearch-box {
  box-sizing: border-box;
  position: fixed;
  z-index: 2147483646;
  border: gray 1px dotted;
  box-shadow: 0 0 0 50000px rgba(0, 0, 0, 0.2);
}
.itrisearch-box::before {
  content: "";
  display: block;
  width: calc(100% + 20px);
  height: calc(100% + 20px);
  margin-left: -10px;
  margin-top: -10px;
  cursor: crosshair;
}

.itrisearch-guide-1,
.itrisearch-guide-2 {
  box-sizing: border-box;
  position: fixed;
  z-index: 2147483646;
}
.itrisearch-guide-1 {
  border-right: dotted 2px #ee14cd;
  top: 0;
  left: 0;
  height: 100%;
}
.itrisearch-guide-2 {
  border-bottom: dotted 2px #0c1ae5;
  top: 0;
  left: 0;
  width: 100%;
}
.itrisearch-guide-3 {
  z-index: 2147483645;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.test {
  background-color: rgb(88, 105, 129);
  /* input */
  background-color: rgb(45, 212, 215);
  border-color: rgb(75, 85, 99);
}
