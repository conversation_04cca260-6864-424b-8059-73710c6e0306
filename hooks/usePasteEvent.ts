import { useCallback, useEffect, useState } from "react";
import { getImageDataURL } from "@/utils/image";
import setModifyItem from "@/utils/browserStorageCurrentPage";
import { message } from "antd";

const usePasteEvent = (divRef, initialNote) => {
  const [noteInfo, setNoteInfo] = useState(initialNote);

  useEffect(() => {
    divRef.current.addEventListener("paste", pasteListener);
    return () => {
      divRef.current.removeEventListener("paste", pasteListener);
    };
  }, []);

  const fetchRequest = useFetchRequest();
  // 便签修改接口请求，重新存储 位置信息
  const editNoteRequest = (noteElement: CopilotNote) => {
    const note = noteElement;
    fetchRequest({
      api: "editNote",
      params: note,
      callback: (res) => {
        if (res.code === 200) {
          setModifyItem(NOTE_MODIFY_STORAGE_KEY, { ...note, key: new Date().getTime(), updateType: "edit" });
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };

  // 粘贴图片到图片占位位置
  const pasteImageFile = async (imageFile: File) => {
    try {
      const dataURL = await getImageDataURL(imageFile);

      const obj = {
        url: dataURL,
        name: imageFile.name,
      };
      const updatedNoteInfo = noteInfo?.filesInfo ? [...noteInfo.filesInfo, obj] : [obj];
      setNoteInfo(updatedNoteInfo);
      // editNoteRequest(updatedNoteInfo);
    } catch (error) {
      console.error("粘贴图片文件失败:", error);
    }
  };

  /**
   * 粘贴事件监听
   * @param e ClipboardEvent
   */
  const pasteListener = useCallback((e: ClipboardEvent) => {
    if (!e.clipboardData) return;

    const clipboardDataItems = e.clipboardData.items;
    const clipboardDataFirstItem = clipboardDataItems[0];

    if (!clipboardDataFirstItem) return;

    // 如果剪贴板内有图片，优先尝试读取图片
    for (const item of clipboardDataItems) {
      if (item.kind === "file" && item.type.indexOf("image") !== -1) {
        const imageFile = item.getAsFile();
        if (imageFile) pasteImageFile(imageFile);
        return;
      }
    }
  }, []);

  return noteInfo;
};

export default usePasteEvent;
