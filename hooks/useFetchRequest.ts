import { useCallback, useEffect, useRef, useState } from "react";
import {
  FETCH_PORT_NAME,
  FETCH_REQUEST_TYPE,
  FETCH_RESPONSE_TYPE,
} from "@/entrypoints/background/handlers/fetchMessageHandler.ts";
import { IFetchArguments, IFetchResponseMessage } from "@/types/message";
import { Runtime } from "webextension-polyfill";
import Port = Runtime.Port;

export const useFetchRequest = () => {
  const portRef = useRef<Port | null>();
  const [isPortManuallyClosed, setIsPortManuallyClosed] = useState(false);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isConnectingRef = useRef(false);
  const storageListenerRef = useRef<((changes: any) => void) | null>(null);

  const handlePort = useCallback(() => {
    // 防止重复连接
    if (isConnectingRef.current || portRef.current) {
      return;
    }

    isConnectingRef.current = true;

    try {
      const newPort = browser.runtime.connect(browser.runtime.id, { name: FETCH_PORT_NAME });

      newPort.onDisconnect.addListener((port) => {
        console.debug(`background中断了当前端口 ${FETCH_PORT_NAME} 主动销毁：${isPortManuallyClosed}`);
        isConnectingRef.current = false;

        // 清理当前端口引用
        if (portRef.current === newPort) {
          portRef.current = null;
        }

        // 如果不是手动关闭且组件未卸载，则重连
        if (!isPortManuallyClosed) {
          // 清除之前的重连定时器
          if (reconnectTimeoutRef.current) {
            clearTimeout(reconnectTimeoutRef.current);
          }

          reconnectTimeoutRef.current = setTimeout(() => {
            console.debug("尝试重新连接...");
            handlePort();
          }, 1000); // 增加重连间隔到1秒，避免频繁重连
        }
      });

      portRef.current = newPort;
      isConnectingRef.current = false;
    } catch (error) {
      console.error("创建端口连接失败:", error);
      isConnectingRef.current = false;
    }
  }, [isPortManuallyClosed]);

  useEffect(() => {
    handlePort();

    // 监听用户退出登录，清理连接
    const handleStorageChange = (changes: any) => {
      if (changes.userInfo && changes.userInfo.oldValue && !changes.userInfo.newValue) {
        console.debug("检测到用户退出登录，清理fetch连接");
        // 用户退出登录，清理连接
        if (reconnectTimeoutRef.current) {
          clearTimeout(reconnectTimeoutRef.current);
          reconnectTimeoutRef.current = null;
        }

        if (portRef.current) {
          portRef.current.disconnect();
          portRef.current = null;
        }

        setIsPortManuallyClosed(true);
        isConnectingRef.current = false;
      }
    };

    browser.storage.local.onChanged.addListener(handleStorageChange);
    storageListenerRef.current = handleStorageChange;

    return () => {
      // 清理存储监听器
      if (storageListenerRef.current) {
        browser.storage.local.onChanged.removeListener(storageListenerRef.current);
        storageListenerRef.current = null;
      }

      // 清理重连定时器
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }

      // 断开端口连接
      if (portRef.current) {
        portRef.current.disconnect();
        portRef.current = null;
      }

      setIsPortManuallyClosed(true);
      isConnectingRef.current = false;
    };
  }, [handlePort]);

  // 检查端口连接状态
  const checkPortConnection = useCallback(() => {
    if (!portRef.current) {
      return false;
    }

    // 检查端口是否仍然有效
    try {
      // 在 Manifest V3 中，可以通过检查 port.name 来验证连接状态
      if (!portRef.current.name) {
        portRef.current = null;
        return false;
      }
      return true;
    } catch (error) {
      console.debug("端口连接已断开:", error);
      portRef.current = null;
      return false;
    }
  }, []);

  // 提取消息发送逻辑 - 先定义，避免提升问题
  const sendMessage = useCallback((args: IFetchArguments) => {
    if (!portRef.current) {
      console.error("端口连接不存在");
      args.callback({ code: 500, msg: "连接失败" });
      return;
    }

    const messageHandler = (res: IFetchResponseMessage) => {
      if (res.type === FETCH_RESPONSE_TYPE && args.api === res.api) {
        // console.debug("content接受到了background的消息：", res);
        args.callback(res.response);
        portRef.current?.onMessage.removeListener(messageHandler);
      }
    };

    try {
      portRef.current.onMessage.addListener(messageHandler);
      portRef.current.postMessage({
        api: args.api,
        type: FETCH_REQUEST_TYPE,
        params: args.params,
        file: args.file || false,
      });
    } catch (error) {
      console.error("发送消息失败:", error);
      portRef.current?.onMessage.removeListener(messageHandler);
      args.callback({ code: 500, msg: "发送失败" });
    }
  }, []);

  return useCallback((args: IFetchArguments) => {
    // 检查并确保端口连接正常
    if (!checkPortConnection()) {
      console.debug("端口连接不可用，尝试重新连接...");
      handlePort();

      // 等待连接建立后再发送消息
      const waitForConnection = () => {
        if (portRef.current) {
          sendMessage(args);
        } else {
          // 如果连接仍未建立，延迟重试
          setTimeout(() => {
            if (portRef.current) {
              sendMessage(args);
            } else {
              console.error("端口连接失败，无法发送消息");
              args.callback({ code: 500, msg: "连接失败" });
            }
          }, 200);
        }
      };

      setTimeout(waitForConnection, 100);
      return;
    }

    sendMessage(args);
  }, [checkPortConnection, handlePort, sendMessage]);
};
