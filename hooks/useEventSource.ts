/** SSE公共Hook */
import { fetchEventSource } from "@microsoft/fetch-event-source";
import { AgentError, AgentThoughtItem, VisibleFileItem } from "@/types/chat";

declare type SSEArguments = {
  url: string;
  method: string;
  params?: Record<string, object>;
  body?: Record<string, string | object>;
  headers?: Record<string, string>;
  onStart?: OnSSEStart;
  onOpen?: object;
  onMessage?: OnSSEMessage;
  onAgentLog?: OnSSEAgentLog;
  onAgentThought?: OnSSEAgentThought;
  onMessageFile?: OnSSEMessageFile;
  onMessageEnd?: OnSSEMessageEnd;
  onClose?: OnSSEClose;
  onError?: OnSSEError;
};
declare type AgentLogItem = {
  conversation_id: string;
  message_id: string;
  created_at: number;
  task_id: string;
  data: {
    node_execution_id: string;
    id: string;
    label: string;
    parent_id: string;
    error: null | string;
    status: string;
    data: {
      output: string;
      tool_call_args: {
        code: string;
        language: string;
      };
      tool_name: string;
    };
    metadata: {
      elapsed_time: number;
      finished_at: number;
      provider: string;
      started_at: number;
    };
    node_id: string;
  };
};
declare type OnSSEAgentLog = (log: AgentLogItem) => void;

declare type OnSSEStart = () => void;
declare type OnSSEMessage = (message: MessageItem) => void;
declare type OnSSEAgentThought = (thought: AgentThoughtItem) => void;
declare type OnSSEMessageFile = (file: VisibleFileItem) => void;
declare type OnSSEClose = () => void;
declare type OnSSEError = (err: AgentError) => void;
declare type OnSSEMessageEnd = (message: MessageItem) => void;

declare type MessageItem = {
  id: string;
  answer?: string;
  create_at: number;
  event: string;
  conversation_id?: string;
  message_id?: string;
  status?: number;
  code?: string;
};
const useEventSource = ({
  url,
  method,
  body,
  headers,
  onStart,
  onMessage,
  onAgentLog,
  onAgentThought,
  onMessageFile,
  onMessageEnd,
  onClose,
  onError,
}: SSEArguments) => {
  let connected = false;
  // const [connected, setConnected] = useState<boolean>(false);
  let abortController = null;
  // const [abortController, setAbortController] = useState<AbortController>();
  const start = (customBody?: Record<string, string | object>) => {
    // setConnected(true);
    connected = true;
    const abort = new AbortController();
    const signal = abort.signal;
    abortController = abort;
    // setAbortController(abort);
    fetchEventSource(url, {
      method: method,
      headers: headers,
      body: JSON.stringify(Object.assign({}, body, customBody)),
      fetch,
      signal,
      openWhenHidden: true,
      onopen(response: Response): Promise<void> {
        if (!response.ok) {
          abortController?.abort();
          throw `${response.status}: ${response.statusText}`;
        }
        onStart?.();
        return Promise.resolve();
      },
      onmessage(event): void {
        if (!event.data || event.data === "ping") {
          return;
        }
        const parsedData: Record<string, any> = JSON.parse(event.data);
        // console.log("SSE接收到消息-------：", parsedData);
        switch (parsedData.event) {
          case "message":
          case "agent_message":
            onMessage?.(parsedData as MessageItem);
            break;
          case "agent_log":
            onAgentLog?.(JSON.parse(event.data) as AgentLogItem);
            break;
          case "agent_thought":
            onAgentThought?.(JSON.parse(event.data) as AgentThoughtItem);
            break;
          case "message_file":
            onMessageFile?.(JSON.parse(event.data) as VisibleFileItem);
            break;
          case "message_end":
            onMessageEnd?.(JSON.parse(event.data));
            connected = false;
            // setConnected(false);
            onClose?.();
            break;
          case "error":
            // setConnected(false);
            connected = false;
            // eslint-disable-next-line no-case-declarations
            const err = JSON.parse(event.data) as AgentError;
            onError?.(err);
            throw `网络请求出现错误：${err.code}`;
        }
      },
      onerror(err): void {
        console.debug(err);
        // setConnected(false);
        connected = false;
        onError?.(err);
        throw err;
      },
      onclose(): void {
        // setConnected(false);
        connected = false;
        onClose?.();
      },
    });
  };
  // [url, method, body, headers],
  // };

  const stop = () => {
    abortController?.abort();
    // setConnected(false);
    connected = false;
    onClose?.();
  };

  // useEffect(() => {
  //   // 组件卸载时自动关闭连接
  //   return () => {
  //     abortController?.abort();
  //     connected = false
  //   };
  // }, []);
  return { start, stop, connected };
};

export default useEventSource;
