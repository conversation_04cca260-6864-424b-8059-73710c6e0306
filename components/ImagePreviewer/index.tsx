/** 图片预览组件 */
import React, { useRef } from "react";
import { createPortal } from "react-dom";
import "./index.less";

/**
 * 图片预览组件属性类型声明
 */
type ImagePreviewerProps = {
  url: string;
  onCancel: () => void;
};

const ImagePreviewer: React.FC<ImagePreviewerProps> = ({ url, onCancel }) => {
  const previewerRef = useRef(null);
  return createPortal(
    <div
      className={"image-previewer-container"}
      ref={previewerRef}
      onClick={(e) => {
        e.stopPropagation();
        if (e.target === previewerRef.current) {
          onCancel();
        }
      }}
    >
      <img className="preview-image" src={url} alt="" />
      <div className="close" onClick={onCancel}>
        <img src="/images/chat/close.svg" alt="" />
      </div>
    </div>,
    document.body,
  );
};

export default ImagePreviewer;
