import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Typography } from "antd";
import { DownloadOutlined, CloseOutlined } from "@ant-design/icons";
import { base64ToBlob } from "@/utils/screenshot";
import packageJson from "../../package.json";

const { Text, Title } = Typography;
interface AttachmentsType {
  attachmentName: string;
  attachmentId: string;
  attachmentType: "install:package" | "operate:manual";
}
const UpdateMask = () => {
  const [describe, setDescribe] = useState("");
  const [version] = useState(`v${packageJson.version}`);
  const [editionName, setEditionName] = useState("");
  const [visible, setVisible] = useState(false);
  const [editionForce, setEditionForce] = useState(1);
  const [attachments, setAttachments] = useState<AttachmentsType[]>([]);
  const fetchRequest = useFetchRequest();

  const getLatestVersion = () => {
    fetchRequest({
      api: "getLatestVersion",
      params: {
        clientType: "browser:plugin",
        versionNumber: version,
      },
      callback: (res) => {
        if (res.code === 200) {
          if (!res.data || res.data.versionNumber === version) {
            setVisible(false);
            return;
          }
          setVisible(true);
          setDescribe(res.data.content);
          setEditionName(res.data.versionNumber);
          // prohibitOld 禁止使用旧版本 0-否，1-是
          setEditionForce(res.data.prohibitOld);
          setAttachments((res.data.attachmentList || []).filter((item) => item.attachmentType === "install:package"));
        }
      },
    });
  };

  useEffect(() => {
    getLatestVersion();
  }, []);

  const show = () => {
    setVisible(true);
  };

  const cancel = () => {
    if (editionForce === 0) {
      setVisible(false);
    }
  };

  const getType = (fileName) => {
    const typeMap = {
      ".pdf": "application/pdf",
      ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      ".pptx": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
      ".xls": "application/vnd.ms-excel",
      ".xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      ".csv": "text/csv",
      ".txt": "text/plain",
      ".ppt": "application/vnd.ms-powerpoint",
      ".doc": "application/msword",
      ".zip": "application/zip",
      ".rar": "application/x-rar-compressed",
      ".exe": "application/x-msdownload",
      ".dmg": "application/octet-stream",
    };
    const type = typeMap[fileName.split(".").pop().toLowerCase()];
    return type;
  };

  const confirm = (item: AttachmentsType) => {
    fetchRequest({
      api: "downLoadFile",
      params: {
        id: item.attachmentId,
      },
      callback: (res) => {
        let type = getType(item.attachmentName);
        const blob = base64ToBlob(res, type);
        const aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = URL.createObjectURL(blob);
        aLink.setAttribute("download", item.attachmentName); // 设置下载文件名称
        document.body.appendChild(aLink);
        aLink.click();
        URL.revokeObjectURL(aLink.href); // 清除引用
        document.body.removeChild(aLink);
      },
    });
  };

  const modalStyles = {
    mask: { backgroundColor: "rgba(0, 0, 0, 0.65)" },
    content: {
      boxShadow: "none",
      backgroundColor: "transparent",
      padding: "0",
      border: "none",
    },
  };

  return (
    <Modal
      open={visible}
      onCancel={cancel}
      footer={null}
      closable={false}
      keyboard={false}
      maskClosable={false}
      centered
      width={300}
      styles={modalStyles}
      getContainer={() => {
        if (document.getElementById("shadow-side-panel")) {
          const shadowDom = document.getElementById("shadow-side-panel").shadowRoot;
          return shadowDom.querySelector("#sino-assistant-crx-web-app");
        }
      }}
    >
      <Card
        bordered={false}
        style={{ borderRadius: "15px" }}
        cover={
          <div style={{ position: "relative", height: 80, top: -36, left: 0 }}>
            <img src={browser.runtime.getURL("/images/update/bg_top.png")} style={{ width: "100%", height: 135 }} />

            <div style={{ position: "absolute", top: 35, left: 25 }}>
              <Title level={4} style={{ color: "#f8f8fa" }}>
                发现新版本 {editionName}
              </Title>
              <Text style={{ color: "#eeeeee" }}>当前版本：{version}</Text>
            </div>
          </div>
        }
      >
        <div style={{ padding: "0 15px" }}>
          <Title level={5} style={{ color: "#3da7ff", margin: "0 0 10px 0" }}>
            更新内容
          </Title>
          <div
            style={{
              height: 120,
              overflowY: "auto",
            }}
            dangerouslySetInnerHTML={{ __html: describe }}
          />

          <div style={{ textAlign: "center", marginTop: 10 }}>
            {attachments.map((item, index) => (
              <Button
                type="primary"
                icon={<DownloadOutlined />}
                onClick={() => {
                  confirm(item);
                }}
                key={index}
                style={{
                  width: "100%",
                  height: 40,
                  borderRadius: 20,
                  background: "linear-gradient(to right, #1785ff, #3da7ff)",
                }}
              >
                立即下载
              </Button>
            ))}
          </div>
        </div>
      </Card>

      {editionForce !== 1 && (
        <div style={{ textAlign: "center", marginTop: 10 }}>
          <Button
            type="text"
            icon={<CloseOutlined />}
            onClick={cancel}
            style={{ width: 35, height: 35, color: "#fff", border: "1px solid #fff", borderRadius: "50%" }}
          />
        </div>
      )}
    </Modal>
  );
};

export default UpdateMask;
