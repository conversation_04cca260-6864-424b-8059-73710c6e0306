// Knowledge组件样式，通过正确的导入链确保样式加载

.form-detector-modal-know-wcl {
  max-height: 50vh;
  min-height: 30vh;
  overflow-y: auto;
  width: 100%;
  gap: 8px;
  display: flex;
  flex-direction: column;
}

.form-detector-cardBox-wcl {
  padding: 4px;
  width: 100%;
  box-sizing: border-box;
  border-radius: 6px;
  background: #fafafa;
  border: 1px solid #fafafa;
  display: block;
  visibility: visible;
  opacity: 1;

  &.form-detector-acitve {
    border: 1px solid #1677ff;
    background: #e6f4ff;
  }
}

.form-detector-top {
  width: 100%;
  gap: 2px;
  display: flex;
  align-items: center;
}

.form-detector-icon-card {
  font-size: 20px;
  display: inline-block;
}

.form-detector-left-gas {
  cursor: pointer;
  width: 100%;
  display: block;
}

.form-detector-first-title {
  color: #000000d9;
  font-size: 14px;
  font-weight: bold;
  display: inline-block;
  white-space: nowrap;
  width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.form-detector-two-title {
  color: #00000040;
  font-size: 14px;
  display: inline-block;
  white-space: nowrap;
  width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
}
