/** 数据空状态组件 */
import React from "react";
import { Empty } from "antd";
import "./index.less";

/** 数据空状态组件参数 */
export type EmptyDataProps = {
  image?: string;
  description?: string;
};

const EmptyData: React.FC<EmptyDataProps> = (props) => {
  // image={props.image || browser.runtime.getURL("/images/public/empty-data.svg")}
  return <Empty description={props.description} />;
};

export default EmptyData;
