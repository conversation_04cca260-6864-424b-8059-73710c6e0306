.think-block {
  .think-collapse .ant-collapse-header {
    transition: all 0.3s;
    cursor: pointer;
  }

  .think-collapse .ant-collapse-header:hover {
    background-color: #fffbe6;
  }

  .think-content {
    // 确保 think 内容有正确的样式
    p, ul, ol, pre, blockquote {
      margin-bottom: 12px;
    }

    // 确保代码块正确显示
    pre {
      background-color: #f6f8fa;
      border-radius: 6px;
      padding: 16px;
      overflow: auto;
    }

    // 确保列表正确显示
    ul, ol {
      padding-left: 20px;
    }
  }

  /* 移除流式输出时的小黑点 */
  /*
  .think-content.markdown-blink > :not(ol):not(ul):not(pre):last-child:after,
  .think-content.markdown-blink > ol:last-child > li:last-child:after,
  .think-content.markdown-blink > pre:last-child code:after,
  .think-content.markdown-blink > ul:last-child > li:last-child:after {
    animation: blink 1s steps(5, start) infinite;
    content: "\25cf";
    font-size: 16px;
    font-family: <PERSON>o <PERSON>;
    margin-inline-start: 0.25em;
    vertical-align: baseline;
  }
  */
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}