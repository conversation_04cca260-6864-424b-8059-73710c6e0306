import React, { useEffect, useState } from "react";
import { Collapse } from "antd";
import { BulbOutlined } from "@ant-design/icons";
import ReactMarkdown from "react-markdown";
import RemarkMath from "remark-math";
import RemarkGfm from "remark-gfm";
import RemarkBreaks from "remark-breaks";
import rehypeRaw from "rehype-raw";

const ThinkBlock: React.FC<{ children: React.ReactNode; finished: boolean }> = ({ children, finished }) => {
  const [activeKey, setActiveKey] = useState<string[] | undefined>(["1"]);

  useEffect(() => {
    // 当内容完成时，延迟折叠面板
    if (finished) {
      const timer = setTimeout(() => {
        setActiveKey([]); // 折叠
      }, 300); // 可以加点延迟让过渡更自然
      return () => clearTimeout(timer);
    } else {
      // 未完成时保持展开状态
      setActiveKey(["1"]);
    }
  }, [finished]);

  const items = [
    {
      key: "1",
      label: (
        <span className="think-header">
          <BulbOutlined style={{ marginRight: 8, color: "#faad14" }} />
          思考提示
        </span>
      ),
      children: (
        <div className="think-content">
          {typeof children === "string" ? (
            <ReactMarkdown
              remarkPlugins={[[RemarkMath], RemarkGfm, RemarkBreaks]}
              rehypePlugins={[rehypeRaw]}
              components={{
                // 自定义代码块渲染
                code({ node, inline, className, children, ...props }: any) {
                  const match = /language-(\w+)/.exec(className || "");
                  return !inline && match ? (
                    <pre style={{ background: "#f6f8fa", padding: "16px", borderRadius: "6px", overflow: "auto" }}>
                      <code className={className} {...props}>
                        {String(children).replace(/\n$/, "")}
                      </code>
                    </pre>
                  ) : (
                    <code className={className} {...props}>
                      {children}
                    </code>
                  );
                },
                // 确保段落正确换行
                p({ children }) {
                  return <p style={{ marginBottom: "12px" }}>{children}</p>;
                },
                // 确保列表正确显示
                ul({ children }) {
                  return <ul style={{ paddingLeft: "20px", marginBottom: "12px" }}>{children}</ul>;
                },
                ol({ children }) {
                  return <ol style={{ paddingLeft: "20px", marginBottom: "12px" }}>{children}</ol>;
                },
              }}
            >
              {children}
            </ReactMarkdown>
          ) : children ? (
            children
          ) : null}
        </div>
      ),
    },
  ];

  return (
    <div className="think-block">
      <Collapse
        bordered={false}
        expandIconPosition="end"
        className="think-collapse"
        activeKey={activeKey}
        onChange={(key) => setActiveKey(key as string[])}
        items={items}
      />
    </div>
  );
};

export default ThinkBlock;
