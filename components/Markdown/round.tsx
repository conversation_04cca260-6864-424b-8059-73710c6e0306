import React, { useState } from "react";
import { Flex } from "antd";
import { UpOutlined, DownOutlined } from "@ant-design/icons";
import ReactMarkdown from "react-markdown";
import RemarkMath from "remark-math";
import RemarkGfm from "remark-gfm";
import RemarkBreaks from "remark-breaks";
import rehypeRaw from "rehype-raw";
import SyntaxHighLighter from "react-syntax-highlighter";
import { solarizedLight } from "react-syntax-highlighter/dist/esm/styles/hljs";
import "katex/dist/katex.min.css";
import "./round.less";

interface RoundProps {
  children?: string;
  finished: boolean;
}

const Round: React.FC<RoundProps> = ({ children = "", finished }) => {
  const [isExpanded, setIsExpanded] = useState(true);

  // 只处理字符串类型的 children
  const processContent = (content: any): { title: string; body: string } => {
    const contentStr = typeof content === "string" ? content : "";
    const lines = contentStr.split("\n");
    const titleLine = lines.find((line) => line.includes("🔄") || line.startsWith("### "));
    const title = titleLine ? titleLine.replace(/^### /, "").replace("🔄", "").trim() : "思考回合";
    const body = titleLine ? lines.filter((line) => line !== titleLine).join("\n") : contentStr;
    return { title, body };
  };
  const { title, body } = React.useMemo(() => processContent(children), [children]);

  const markdownComponents = {
    code({ node, inline, className, children, ...props }: any) {
      const match = /language-(\w+)/.exec(className || "");
      const codeString = String(children).replace(/\n$/, "");
      return !inline && match ? (
        <div className="code-block">
          <div className="code-header">
            <span className="language-label">{match[1]}</span>
          </div>
          <SyntaxHighLighter
            {...props}
            style={solarizedLight}
            language={match[1]}
            showLineNumbers
            customStyle={{ margin: 0, background: "#fafafa" }}
          >
            {codeString}
          </SyntaxHighLighter>
        </div>
      ) : (
        <code className={className} {...props}>
          {children}
        </code>
      );
    },
  };

  return (
    <div className={`round-block ${finished ? "" : "round-loading"}`}>
      <Flex className="round-header" justify="space-between" align="center" onClick={() => setIsExpanded(!isExpanded)}>
        <span className="round-title">{title}</span>
        {isExpanded ? <UpOutlined /> : <DownOutlined />}
      </Flex>
      {isExpanded && (
        <div className="round-content">
          <ReactMarkdown
            remarkPlugins={[RemarkMath, RemarkGfm, RemarkBreaks]}
            rehypePlugins={[rehypeRaw]}
            components={markdownComponents}
          >
            {body}
          </ReactMarkdown>
        </div>
      )}
    </div>
  );
};

export default Round;
