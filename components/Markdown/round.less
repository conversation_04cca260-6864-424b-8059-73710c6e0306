.round-block {
  margin: 12px 0;
  border: 1px solid var(--ant-color-border);
  border-radius: 8px;
  background: var(--ant-color-bg-container);

  .round-header {
    margin: 0 5px -1px 5px;
    padding: 12px 16px;
    border-bottom: 1px solid var(--ant-color-border);
    cursor: pointer;
    transition: background-color 0.3s;

    &:hover {
      background-color: var(--ant-color-bg-container-hover);
    }

    .round-title {
      font-size: 14px;
      font-weight: 500;
      color: var(--ant-color-text);
    }
  }

  .round-content {
    padding: 16px;
    
    p {
      margin-bottom: 8px;
    }

    pre {
      margin: 12px 0;
    }
  }

  &.round-loading {
    opacity: 0.8;
    .round-content {
      position: relative;
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, 
          transparent 25%, 
          rgba(0, 0, 0, 0.05) 50%,
          transparent 75%
        );
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
      }
    }
  }

  
  
}

.round-code-block {
  margin: 16px 0;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--ant-color-border);
  background: var(--ant-color-bg-container);

  .code-header {
    padding: 8px 16px;
    background: var(--ant-color-bg-container-hover);
    border-bottom: 1px solid var(--ant-color-border);
    display: flex;
    align-items: center;

    .language-label {
      font-size: 13px;
      color: var(--ant-color-text-secondary);
      text-transform: lowercase;
      font-family: Monaco, Consolas, monospace;
    }
  }

  pre {
    margin: 0 !important;
    padding: 16px !important;

    code {
      font-family: Monaco, Consolas, monospace;
      font-size: 13px;
      line-height: 1.6;
    }
  }

  // 语法高亮主题覆盖
  .hljs {
    background: var(--ant-color-bg-container) !important;
    padding: 0 !important;
  }
}

@keyframes loading {
  from { background-position: 200% 0; }
  to { background-position: -200% 0; }
}