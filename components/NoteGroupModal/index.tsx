import React, { useEffect, useState } from "react";
import { Modal, Form, Input, Select, Button, Flex, Col, theme, message } from "antd";
import ParentGroupSelect from "./noteGroupList";
import "./index.less";
const { TextArea } = Input;
const { useToken } = theme;
interface NoteGroupModalProps {
  visible: boolean;
  onClose: () => void;
  id?: string;
  position?: string;
  onConfirm: () => void;
}

const NoteGroupModal: React.FC<NoteGroupModalProps> = ({ visible, id, position, onClose, onConfirm }) => {
  const { token } = useToken();
  const fetchRequest = useFetchRequest();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [selectedParent, setSelectedParent] = useState<string | undefined>(undefined); // 选中的值
  const handleParentChange = (value: string) => {
    setSelectedParent(value);
  };
  const getDetails = () => {
    if (id) {
      fetchRequest({
        api: "getGroupInfo",
        params: {
          id,
        },
        callback: (res) => {
          if (res.code === 200) {
            const parentId = res.data.parentId !== "0" ? res.data.parentId : undefined;

            // 先更新状态
            setSelectedParent(parentId);

            // 确保状态更新后，再设置表单值
            setTimeout(() => {
              form.setFieldsValue({
                name: res.data.name,
                description: res.data.description,
                parentId: parentId,
              });
            }, 0);
          }
        },
      });
    }
  };

  useEffect(() => {
    if (visible && id) {
      getDetails();
    }
  }, [visible]);
  const onSubmit = (values: any) => {
    setLoading(true);
    const formValues = form.getFieldsValue();
    let api = "";
    if (id) {
      api = "editNotesGroup";
    } else {
      api = "addNotesGroup";
    }
    fetchRequest({
      api: api,
      params: {
        name: formValues.name,
        description: formValues.description,
        parentId: selectedParent,
        id: id || undefined,
      },
      callback: (res) => {
        if (res.code === 200) {
          setLoading(false);
          message.open({
            type: "success",
            content: id ? "编辑成功" : "新建成功",
          });
          browser.storage.local.set({
            uploadGroupData: {
              date: Date.now(),
              type: id ? "edit" : "add",
            },
          });
          onConfirm();
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
        form.resetFields(); // 重置表单
        setSelectedParent(undefined); // 清空父级便签组
      },
    });
  };
  const onCancel = () => {
    form.resetFields(); // 重置表单
    onClose();
    setSelectedParent(undefined); // 清空父级便签组
  };
  return (
    <Modal
      title={id ? "编辑便签组" : "新建便签组"}
      open={visible}
      wrapClassName="note-custom-modal"
      onCancel={onCancel}
      footer={null}
      className="modal-group-note"
      getContainer={() => {
        if (position == "window") {
          const shadowDom = document.getElementById("shadow-dom-note").shadowRoot;
          return shadowDom;
        } else {
          const shadowPanel = document.getElementById("shadow-side-panel");
          return shadowPanel?.shadowRoot?.querySelector(".side-panel-content") || document.body;
        }
      }}
    >
      <Form form={form} onFinish={onSubmit} layout="vertical">
        <Form.Item label="便签组名称" name="name" rules={[{ required: true, message: "请输入便签组名称" }]}>
          <Input placeholder="请输入便签组名称" maxLength={15} />
        </Form.Item>

        <Form.Item label="父级便签组" name="parentId">
          <ParentGroupSelect value={selectedParent} currentId={id} onChange={handleParentChange} />
        </Form.Item>

        <Form.Item label="描述" name="description">
          <TextArea rows={4} placeholder="请输入描述" showCount maxLength={100} />
        </Form.Item>

        <Flex justify="end" gap={token.marginXS}>
          <Col>
            <Button type="default" onClick={onCancel}>
              取消
            </Button>
          </Col>
          <Col>
            <Button type="primary" htmlType="submit" loading={loading}>
              {id ? "修改" : "新建"}
            </Button>
          </Col>
        </Flex>
      </Form>
    </Modal>
  );
};

export default NoteGroupModal;
