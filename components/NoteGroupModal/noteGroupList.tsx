import React, { useEffect, useState } from "react";
import { TreeSelect, message, Flex } from "antd";

interface ParentGroupSelectProps {
  value?: string;
  onChange?: (value: string) => void;
  currentId?: string;
}

const ParentGroupSelect: React.FC<ParentGroupSelectProps> = ({ value, onChange, currentId }) => {
  const fetchRequest = useFetchRequest();
  const [treeData, setTreeData] = useState([]);
  const getCommentPage = () => {
    fetchRequest({
      api: "notesGroupTree",
      params: {},
      callback: (res) => {
        if (res.code === 200) {
          setTreeData(res.data);
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };
  const disableCurrentNode = (nodes, currentId) => {
    return nodes.map((node) => ({
      ...node,
      disabled: node.id === currentId, // 禁用自己
      children: node.children ? disableCurrentNode(node.children, currentId) : undefined, // 递归处理子节点
    }));
  };
  useEffect(() => {
    getCommentPage();
  }, []);
  return (
    <>
      <TreeSelect
        showSearch
        style={{ width: "100%" }}
        fieldNames={{ label: "name", value: "id", children: "children" }}
        value={value}
        dropdownStyle={{ maxHeight: 400, overflow: "auto" }}
        placeholder="请选择父级便签组"
        allowClear
        treeDefaultExpandAll
        treeData={disableCurrentNode(treeData, currentId)}
        onChange={onChange}
        getPopupContainer={(triggerNode) => triggerNode.parentNode}
      />
    </>
  );
};

export default ParentGroupSelect;
