import React, { useEffect, useState } from "react";
import { Modal, Form, Input, Select, Button, Flex, Col, theme, message } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import ParentGroupSelect from "./noteGroupList";
import NoteGroupModal from "@/components/NoteGroupModal";
const { useToken } = theme;
interface NoteGroupModalProps {
  visible: boolean;
  noteId?: string;
  onClose: () => void;
  onConfirm: () => void;
}

const NoteSelectGroupModal: React.FC<NoteGroupModalProps> = ({ visible, noteId, onClose, onConfirm }) => {
  const { token } = useToken();
  const fetchRequest = useFetchRequest();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [groupModalvisible, setGroupModalvisible] = useState(false);
  const onSubmit = (values: any) => {
    setLoading(true);
    fetchRequest({
      api: "addrelation",
      params: {
        groupId: form.getFieldValue("parentId"),
        noteIds: [noteId],
      },
      callback: (res) => {
        setLoading(false);
        if (res.code === 200) {
          message.success("添加成功");
          form.resetFields(); // 重置表单
          onConfirm();
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };
  const onCancel = () => {
    form.resetFields(); // 重置表单
    onClose();
  };
  // 新增组
  const handleAddGroup = () => {
    setGroupModalvisible(true);
  };

  return (
    <>
      <NoteGroupModal
        visible={groupModalvisible}
        position="window"
        onClose={() => setGroupModalvisible(false)}
        onConfirm={() => {
          setGroupModalvisible(false);
        }}
      />
      <Modal
        title="保存到便签组"
        open={visible}
        onCancel={onCancel}
        footer={null}
        wrapClassName="note-custom-modal"
        width={350}
        getContainer={() => {
          const shadowDom = document.getElementById("shadow-dom-note").shadowRoot;
          return shadowDom;
        }}
      >
        <Form form={form} onFinish={onSubmit} layout="vertical">
          <Form.Item label="" name="parentId" rules={[{ required: true, message: "请选择便签组" }]}>
            <Flex>
              <ParentGroupSelect
                value={form.getFieldValue("parentId")}
                onChange={(value) => form.setFieldsValue({ parentId: value })}
              />
              {/* <Button
                style={{ width: "40%", marginLeft: token.marginLG }}
                icon={<PlusOutlined />}
                type="text"
                onClick={handleAddGroup}
              >
                新增组
              </Button> */}
            </Flex>
          </Form.Item>

          <Flex justify="end" gap={token.marginXS}>
            <Col>
              <Button type="default" onClick={onCancel}>
                取消
              </Button>
            </Col>
            <Col>
              <Button type="primary" htmlType="submit" loading={loading}>
                保存
              </Button>
            </Col>
          </Flex>
        </Form>
      </Modal>
    </>
  );
};

export default NoteSelectGroupModal;
