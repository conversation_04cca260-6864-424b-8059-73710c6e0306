import React, { useEffect, useState } from "react";
import { Modal, Form, Input, Select, Button, Flex, Col, theme, message } from "antd";
const { useToken } = theme;
interface NoteGroupModalProps {
  visible: boolean;
  noteInfo?: any; //便签信息
  position?: any; // 渲染的位置
  isFile?: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

const NoteKnowledgeModal: React.FC<NoteGroupModalProps> = ({
  visible,
  noteInfo,
  onClose,
  onConfirm,
  position,
  isFile = false,
}) => {
  const { token } = useToken();
  const fetchRequest = useFetchRequest();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState([]);
  const onSubmit = (values: any) => {
    setLoading(true);
    const formValues = form.getFieldsValue();
    if (isFile) {
      fetchRequest({
        api: "ocrAdd",
        params: {
          libId: form.getFieldValue("baseId"),
          title: noteInfo.title || "",
          ocrUrl: noteInfo.url || "",
        },
        callback: (res) => {
          setLoading(false);
          if (res.code === 200) {
            message.success("知识库添加成功！");
            form.resetFields(); // 重置表单
            onConfirm();
          } else {
            message.open({
              type: "error",
              content: res.msg,
            });
          }
        },
      });
    } else {
      fetchRequest({
        api: "addNoteKnowledge",
        params: {
          libId: form.getFieldValue("baseId"),
          title: noteInfo.title || "",
          content: noteInfo.content || "",
        },
        callback: (res) => {
          setLoading(false);
          if (res.code === 200) {
            message.success("知识库添加成功！");
            form.resetFields(); // 重置表单
            onConfirm();
          } else {
            message.open({
              type: "error",
              content: res.msg,
            });
          }
        },
      });
    }
  };
  const onCancel = () => {
    form.resetFields(); // 重置表单
    onClose();
  };
  // 获取个人库的列表
  const getList = () => {
    fetchRequest({
      api: "getTeamList",
      params: {},
      callback: (res) => {
        if (res.code === 200) {
          setList(res.data);
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };
  useEffect(() => {
    if (visible) {
      getList();
    }
  }, [visible]);
  return (
    <Modal
      title="存入知识库"
      open={visible}
      onCancel={onCancel}
      footer={null}
      className="modal-knowledge-note"
      wrapClassName="note-custom-modal"
      getContainer={() => {
        if (position == "sider") {
          const shadowPanel = document.getElementById("shadow-side-panel");
          return shadowPanel?.shadowRoot?.querySelector(".side-panel-content") || document.body;
        } else {
          const shadowDom = document.getElementById("shadow-dom-note").shadowRoot;
          return shadowDom;
        }
      }}
    >
      <Form form={form} onFinish={onSubmit} layout="vertical">
        <Form.Item label="" name="baseId" rules={[{ required: true, message: "请选择知识库" }]}>
          <Select
            placeholder="请选择知识库"
            getPopupContainer={(triggerNode) => triggerNode.parentNode}
            options={(list || []).map((agent) => ({
              label: agent.libName,
              value: agent.id,
            }))}
          />
        </Form.Item>

        <Flex justify="end" gap={token.marginXS}>
          <Col>
            <Button type="default" onClick={onCancel}>
              取消
            </Button>
          </Col>
          <Col>
            <Button type="primary" htmlType="submit" loading={loading}>
              保存
            </Button>
          </Col>
        </Flex>
      </Form>
    </Modal>
  );
};

export default NoteKnowledgeModal;
