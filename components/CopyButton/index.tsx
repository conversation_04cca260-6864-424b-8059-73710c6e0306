/** 公共复制文本组件 */
import { Tooltip, Button } from "antd";
import React from "react";
import { copyText } from "@/utils/clipboard.ts";
import IconFont from "@/components/IconFont";
import { CopyOutlined } from "@ant-design/icons";
import "./index.less";

const CopyButton: React.FC<{ text?: string; copied: boolean; setCopied: React.Dispatch<boolean> }> = ({
  text,
  copied,
  setCopied,
}) => {
  return (
    <Tooltip
      getPopupContainer={(triggerNode) => triggerNode.parentNode}
      title={<span>{copied ? "已复制" : "复制"}</span>}
    >
      <div className="copy-btn" onClick={() => text && copyText(text).then(() => setCopied(true))}>
        {/* <IconFont type={copied ? "icon-clipboard-copied" : "icon-clipboard-uncopied"} /> */}
        {/* <div>
          <img src={browser.runtime.getURL("/images/write/copy.png")} className="side-result-icon-copy" alt="" />
        </div> */}
        <Button icon={<CopyOutlined className=" btn-icon" />} type="text" size="small"></Button>
      </div>
    </Tooltip>

    // <Tooltip
    //                 placement="top"
    //                 title={hasCopy ? "已复制" : "复制"}
    //                 getPopupContainer={(triggerNode) => triggerNode.parentNode}
    //               >
    //                 <img
    //                   src={browser.runtime.getURL("/images/write/copy.png")}
    //                   onClick={handleCopyBtnClick}
    //                   className="side-result-icon-copy"
    //                   alt=""
    //                 />
    //               </Tooltip>
  );
};

export default CopyButton;
