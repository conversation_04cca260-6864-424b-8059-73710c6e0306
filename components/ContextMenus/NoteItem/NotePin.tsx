/** 便签图钉组件 */
import React, { useEffect, useState } from "react";
import type { MenuProps } from "antd";
import { Dropdown, Flex, Tooltip } from "antd";

type NotePinProps = {
  /**
   * 便签信息
   */
  note?: CopilotNote;
  /**
   * 是否打开 tooltip
   */
  isDropdownOpen: boolean;
  /**
   * 展开状态
   */
  // expanded?: boolean;
  onExpanded?: () => void;
  onFixed?: (boolean) => void;
};

const NotePin: React.FC<NotePinProps> = ({ note, isDropdownOpen, onExpanded, onFixed }) => {
  const [selfFixed, setSelfFixed] = useState<boolean>(note?.noteStyle?.noteType === "fixed");
  const [dropdownOpen, setDropdownOpen] = useState<boolean>(false);
  const [showTooltip, setShowTooltip] = useState<boolean>(false);

  useEffect(() => {
    setDropdownOpen(isDropdownOpen);
    setShowTooltip(false);

    const shadowDom = document.getElementById("shadow-dom-note").shadowRoot;
    setTimeout(() => {
      // 获取目标容器
      const dropdownElement = shadowDom.getElementById(`note${note.id}`)?.querySelector(".ant-dropdown ul");

      // 如果获取到元素，绑定点击事件
      if (dropdownElement) {
        const liElements = dropdownElement.querySelectorAll("li");
        const handleClick = (e) => {
          const clickedLi = e.currentTarget; // 获取点击的li元素
          const index = clickedLi.dataset.index; // 获取存储在dataset中的index
          handleAgentClick(index);
        };
        if (isDropdownOpen) {
          // 为每个li元素绑定点击事件
          liElements.forEach((li, index) => {
            li.dataset.index = String(index);
            li.addEventListener("mousedown", handleClick);
          });
        } else {
          liElements.forEach((li) => {
            li.removeEventListener("mousedown", handleClick);
          });
        }
      }
    }, 100);
  }, [isDropdownOpen]);

  const handleExpandedToggle = () => {
    onExpanded && onExpanded();
  };

  const handleFixedToggle = () => {
    setSelfFixed(!selfFixed);
    onFixed && onFixed(selfFixed);
  };

  const handleAgentClick = (key) => {
    switch (key) {
      case "0":
        handleExpandedToggle();
        break;
      case "1":
        handleFixedToggle();
        break;
      default:
        break;
    }
    setDropdownOpen(false);
  };

  const ItemLabel1 = () => <a style={{ textAlign: "left", width: "48px", display: "inline-block" }}>收起</a>;
  const ItemLabel2 = () => (
    <a style={{ textAlign: "left", width: "48px", display: "inline-block" }}>{selfFixed ? "释放" : "固定"}</a>
  );

  const items: MenuProps["items"] = [
    {
      key: "1",
      label: <ItemLabel1 />,
    },
    {
      key: "2",
      label: <ItemLabel2 />,
    },
  ];
  return (
    <Dropdown
      menu={{ items: items.filter((x) => !(x.key === (note.editable ? "" : "2"))) }}
      placement="bottomRight"
      open={dropdownOpen}
      getPopupContainer={(triggerNode) => triggerNode.parentNode.parentNode as any}
    >
      <Tooltip
        placement="topLeft"
        title="移动便签"
        open={showTooltip}
        getPopupContainer={() => {
          const shadowDom = document.getElementById("shadow-dom-note").shadowRoot;
          return shadowDom.getElementById(`sino-click-img-${note.id}`);
        }}
      >
        <Flex
          id={`sino-click-img-${note.id}`}
          onMouseEnter={() => setShowTooltip(true)}
          onMouseLeave={() => setShowTooltip(false)}
        >
          <img
            style={{ width: "28px", userSelect: "none", pointerEvents: "none" }}
            src={browser.runtime.getURL(`/images/note/pin-${selfFixed ? "select" : "default"}.png`)}
          />
        </Flex>
      </Tooltip>
    </Dropdown>
  );
};

export default React.memo(NotePin);
