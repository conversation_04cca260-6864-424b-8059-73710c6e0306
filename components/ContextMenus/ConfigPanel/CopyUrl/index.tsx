import React from "react";
import Coworkers from "./Coworkers";
import { Flex, theme, Typography } from "antd";

const { useToken } = theme;

const CopyUrl: React.FC<{ note: CopilotNote }> = ({ note }) => {
  const { token } = useToken();
  return (
    <>
      <Flex
        vertical
        justify="space-between"
        style={{
          width: "200px",
          height: "100px",
          marginLeft: "4px",
          padding: "18px 12px 16px",
          backgroundColor: token.colorBgContainer,
          boxShadow:
            "0px 8px 10px -5px rgba(0, 0, 0, 0.08),0px 16px 24px 2px rgba(0, 0, 0, 0.04),0px 6px 30px 5px rgba(0, 0, 0, 0.05)",
          borderRadius: token.borderRadiusLG,
          boxSizing: "border-box",
        }}
      >
        <Typography.Title level={5} style={{ margin: 0 }}>
          分享链接
        </Typography.Title>
        <Coworkers note={note} />
      </Flex>
    </>
  );
};

export default CopyUrl;
