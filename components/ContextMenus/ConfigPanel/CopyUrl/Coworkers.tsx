import React, { useState } from "react";
import { Button, Flex, Select, theme, Tooltip } from "antd";
import { LinkOutlined } from "@ant-design/icons";
import { copyText } from "@/utils/clipboard.ts";
const { useToken } = theme;

const Coworkers: React.FC<{ note: CopilotNote }> = ({ note }) => {
  const { token } = useToken();
  const [isCopy, setIsCopy] = useState(false); // 是否已复制
  const [selectType, setSelectType] = useState("expire_time_week");
  const fetchRequest = useFetchRequest();
  const handleChange = (value: string) => {
    setIsCopy(false);
    setSelectType(value);
  };
  // 分享
  const copyClick = () => {
    let obj = {
      noteId: note.id,
      url: getUrlNoQuery(),
      expireTime: selectType,
    };
    fetchRequest({
      api: "shareNote",
      params: obj,
      callback: (res) => {
        if (res.code == "200") {
          copyText(res.data);
          setIsCopy(true);
          setTimeout(() => {
            setIsCopy(false);
          }, 2000);
        }
      },
    });
  };
  return (
    <>
      <Flex>
        <Select
          defaultValue={selectType}
          style={{ width: "136px", marginRight: "8px" }}
          getPopupContainer={(triggerNode) => triggerNode.parentNode}
          onChange={handleChange}
          options={[
            {
              label: <span>链接有效期</span>,
              title: "链接有效期",
              options: [
                { label: <span>7天</span>, value: "expire_time_week" },
                { label: <span>30天</span>, value: "expire_time_month" },
                { label: <span>永久有效</span>, value: "expire_time_perpetual" },
              ],
            },
          ]}
        />
        <Tooltip
          placement="top"
          title="已复制"
          open={isCopy}
          getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
        >
          <Button
            style={{ padding: token.paddingXXS, width: "32px" }}
            icon={<LinkOutlined style={{ color: token.colorText }} />}
            onClick={copyClick}
          ></Button>
        </Tooltip>
      </Flex>
    </>
  );
};

export default Coworkers;
