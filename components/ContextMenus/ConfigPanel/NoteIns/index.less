.sino-mentions-box {
  .rc-textarea {
    background: none;
  }
}

.note-ins-note-noteAiResult {
  height: calc(100% - 100px);
  margin-bottom: 30px;
  overflow-y: auto;
  .side-panel-note-noteAiResult-title {
    width: calc(100% - 32px);
    bottom: 12px;
    left: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 24px;
    .side-panel-note-noteAiResult-text {
      color: var(--ant-color-text-tertiary);
      font-size: var(--ant-font-size-sm);
    }
  }
  .side-panel-note-noteAiResult-result {
    color: rgba(18, 18, 18, 0.85);
    line-height: 20px;
    padding-bottom: var(--ant-padding-xxs);
  }
}

.note-ins-processing-tag {
  display: flex;
  background: var(--ant-color-bg-layout) !important;
  align-items: center;
  align-self: flex-start;
  span {
    vertical-align: middle;
    font-weight:bold;
  }
}
.sino-his-content * {
  color: var(--ant-color-text-tertiary) !important;
  font-size: var(--ant-font-size-sm) !important;
}
