import React, { useCallback, useEffect, useState, useRef } from "react";
import classNames from "classnames";
import { useGetState } from "ahooks";
import { QUOTE, saveDetailJumpInfo, ONQUIZ } from "@/utils/notes";
import { Button, ConfigProvider, Flex, Input, message, theme, Typography } from "antd";
import ExpandableText from "@/components/ExpandableText/index";
import ReactDOM from "react-dom/client";
import { StyleProvider } from "@ant-design/cssinjs";
import themeToken from "@/theme.json";
import "./index.less";
import { getFirstTextNode } from "@/utils/node";
import MentionReact from "@/utils/prosemirror/plugins/mentionReact";
import IconFont from "@/components/IconFont";
import ReactMde from "react-mde";
import ReactMarkdown from "react-markdown";
import "react-mde/lib/styles/css/react-mde-all.css";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import remarkBreaks from "remark-breaks";
import setModifyItem, { NOTE_MODIFY_STORAGE_KEY } from "@/utils/browserStorageCurrentPage";
const { useToken } = theme;

type updateNoteType = { value: string; key: string }[];

const PlainNote: React.FC<{
  note: CopilotNote;
  updateNoteValue: (updateNoteType) => void;
  isThumbnail: boolean;
  acitve: boolean;
  onExpanded?: () => void;
}> = ({ note, updateNoteValue, isThumbnail, acitve, onExpanded }) => {
  const { token } = useToken();
  const [noteInfo, setNoteInfo] = useState(note);
  const [noteTitle, setNoteTitle, getNoteTitle] = useGetState(note.title || "");
  const [selectedTab, setSelectedTab] = useState<"write" | "preview">("write");
  const [markdownValue, setMarkdownValue] = useState(noteInfo.content);
  const textareaRef = useRef(null);
  const mdeRef = useRef(null);
  const [mentionPopup, setMentionPopup] = useState({ left: 0, top: 0 });
  const [userList, setUserList] = useState<any[]>([]);
  const autocompleteRef = useRef<any>(null); // 存储自动补全实例
  const [filteredNoteList, setFilteredNoteList] = useState<any[]>([]);
  const fetchRequest = useFetchRequest();

  useEffect(() => {
    setNoteInfo(note);
    setNoteTitle(note.title || "");
  }, [note]);

  const debounceHandleInput = useCallback(debounce(updateNoteValue, 1000), []);

  const updateValue = (value) => {
    if (getNoteTitle()) {
      updateNoteValue([{ value: value, key: "content" }]);
    } else {
      const parser = new DOMParser();
      const doc = parser.parseFromString(value, "text/html");
      let text = getFirstTextNode(doc.body);
      if (!text) return;
      let inputTitle = text.length > 15 ? text.slice(0, 15) + "..." : text;
      setNoteTitle(inputTitle);
      updateNoteValue([
        { value: value, key: "content" },
        { value: inputTitle, key: "title" },
      ]);
    }
  };

  const noteClick = (event) => {
    // 获取点击的元素
    const clickedElement = event?.target;
    // 检查是否有 data-data 属性
    if (event && clickedElement.hasAttribute("data-data")) {
      // 获取 data-data 属性的值
      const dataValue = clickedElement.getAttribute("data-data");
      // 将值解析为对象（假设它是一个 JSON 字符串）
      try {
        const obj = JSON.parse(dataValue);
        if (obj.type === "note_rel_note") {
          const params = { id: obj.objId };
          fetchRequest({
            api: "queryNote",
            params,
            callback: (res) => {
              if (res.code === 200) {
                saveDetailJumpInfo(res.data);
              } else {
                message.open({
                  type: "error",
                  content: res.msg,
                });
              }
            },
          });
        }
      } catch (e) {
        console.error("无法解析 data-data 的值:", e);
      }
    }
  };

  // const [width, setWidth] = useState(isThumbnail ? 200 : 348); // 初始化宽度
  const [width, setWidth] = useState(600); // 初始化宽度
  const [height, setHeight] = useState(260); // 初始化高度

  // 最大和最小尺寸限制
  const minWidth = 600;
  const minHeight = 260;
  const maxWidth = 800;
  const maxHeight = 600;

  // 鼠标摁下
  const onMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    if (isThumbnail) return;
    // 记录鼠标按下时的位置以及当前盒子的尺寸
    const initialMouseX = e.clientX;
    const initialMouseY = e.clientY;
    const initialWidth = width;
    const initialHeight = height;
    const shadowDom = document.getElementById("shadow-dom-note").shadowRoot;
    let isDragging = true; // 是否正在拖拽
    const onMouseMove = (e) => {
      if (isDragging) {
        // 计算鼠标的移动距离
        const dx = e.clientX - initialMouseX;
        const dy = e.clientY - initialMouseY;

        // 计算新的宽度和高度，加入最大最小限制
        const newWidth = Math.max(minWidth, Math.min(initialWidth + dx, maxWidth));
        const newHeight = Math.max(minHeight, Math.min(initialHeight + dy, maxHeight));
        setWidth(newWidth); // 更新宽度
        setHeight(newHeight); // 更新高度
      }
    };

    const onMouseUp = () => {
      isDragging = false;
      document.removeEventListener("mousemove", onMouseMove);
      document.removeEventListener("mouseup", onMouseUp);
      shadowDom.removeEventListener("mouseup", onMouseUp);
    };
    document.addEventListener("mousemove", onMouseMove);
    document.addEventListener("mouseup", onMouseUp);
    shadowDom.addEventListener("mouseup", onMouseUp);
  };

  const sendMessageToIframe = (type, data) => {
    const shadowDom = document.getElementById("shadow-dom-note").shadowRoot;
    const iframe = shadowDom.getElementById(`iframe${note.id}`) as any;
    if (iframe) {
      iframe.contentWindow.postMessage({ type, data }, "*");
    }
  };
  // 获取@ 后的人员信息
  const getInfoList = () => {
    getUserInfo().then((userInfo) => {
      fetchRequest({
        api: "listNoteRela",
        params: {
          type: "note_rel_all",
          query: "",
        },
        callback: (res) => {
          if (res.code === 200) {
            fetchRequest({
              api: "flatList",
              params: {
                keyword: "",
              },
              callback: (flat) => {
                if (flat.code == 200) {
                  let arr = [];
                  flat.data.map((val) => {
                    let obj = {
                      name: val.name,
                      objId: val.id,
                      type: "note_rel_group",
                    };
                    arr.push(obj);
                  });

                  let noteList = (res.data || []).concat(arr).filter((item, index, self) => {
                    return (
                      self.findIndex((t) => t.objId === item.objId) === index &&
                      item.objId !== userInfo.id && // 不是当前登录人
                      !(res.data?.noteObjs || []).map((x) => x.objId).includes(item.objId) // 去重
                    );
                  });
                  noteList.forEach((ele) => {
                    if (ele.type === "note_rel_contact") {
                      ele.icon = browser.runtime.getURL("/images/textEditor/userOutlined.png");
                    } else if (ele.type === "note_rel_note") {
                      ele.icon = browser.runtime.getURL("/images/textEditor/noteOutlined.png");
                    } else {
                      ele.icon = browser.runtime.getURL("/images/textEditor/medicineBoxOutlined.png");
                    }
                  });
                  setUserList(noteList);
                }
              },
            });
          } else {
            // 创建报错了给出提示，便签不渲染
            message.open({
              type: "error",
              content: res.msg,
            });
          }
        },
      });
    });
  };
  // 修改便签
  const editNoteRequest = (noteElement: CopilotNote) => {
    fetchRequest({
      api: "editNote",
      params: noteElement,
      callback: (res) => {
        if (res.code === 200) {
          setModifyItem(NOTE_MODIFY_STORAGE_KEY, { ...noteElement, key: new Date().getTime(), updateType: "edit" });
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };

  useEffect(() => {
    getInfoList();
  }, []);

  useEffect(() => {
    const handleNoteListChanged = (changes) => {
      if (changes["uploadGroupData"]) {
        getInfoList();
      }
    };
    browser.storage.local.onChanged.addListener(handleNoteListChanged);
    return () => {
      browser.storage.local.onChanged.removeListener(handleNoteListChanged);
    };
  }, []);
  var el = document.createElement("div"); // 创建一个 DOM 容器
  let root = null;
  const showList = ({ offset, count, state, noteId, textDOMOffsetHeight }) => {
    const shadowDom = document.getElementById("shadow-dom-note").shadowRoot;
    const noteDom = shadowDom.getElementById(`note${note.id}`);
    const iframeDom = shadowDom.getElementById(`iframe${note.id}`);
    const divRect = noteDom.getBoundingClientRect();
    const iframeRect = iframeDom.getBoundingClientRect();
    // 如果 root 为空，说明还没有创建 React 根节点，创建它
    if (!root) {
      root = ReactDOM.createRoot(el); // 使用 createRoot 创建根节点
    }

    // 渲染组件
    root.render(
      <StyleProvider hashPriority="high" container={shadowDom}>
        <ConfigProvider theme={themeToken}>
          <MentionReact
            noteId={noteId}
            query={state.queryText}
            suggestions={state.suggestions}
            onSelect={(item) => {
              sendMessageToIframe("onSelect", item);
            }}
            onHide={() => {
              hideList();
              sendMessageToIframe("hide", {});
            }}
            count={count}
          />
        </ConfigProvider>
      </StyleProvider>,
    );

    noteDom.appendChild(el);
    el.classList.add("suggestion-item-container");
    el.style.position = "absolute";
    el.style.left = offset.left + iframeRect.left - divRect.left + "px";

    var top = textDOMOffsetHeight + iframeRect.top - divRect.top + offset.top;
    el.style.top = top + "px";
    el.style.display = "block";
    el.style.zIndex = "999999";
  };
  const hideList = () => {
    if (root) {
      root.unmount();
      root = null;
    }
    el.style.display = "none";
  };

  useEffect(() => {
    // 监听来自 iframe 的消息
    const handleMessage = (event) => {
      // 验证消息来源
      if (event.origin === `chrome-extension://${browser.runtime.id}` && event.data.noteId === note.id) {
        switch (event.data.type) {
          case "showList":
            showList(event.data.data);
            break;
          case "hideList":
            hideList();
            break;
          case "updateValue":
            updateValue(event.data.data);
            break;
          case "prompt":
            message.open({
              type: "info",
              content: event.data.data,
            });
            break;
          default:
            break;
        }
      }
    };

    window.addEventListener("message", handleMessage);

    return () => {
      window.removeEventListener("message", handleMessage); // 清理事件监听
    };
  }, []);
  const [mentionQuery, setMentionQuery] = useState("");

  // 检测 @ 输入
  const handleInputChange = (text) => {
    setMarkdownValue(text);
    // const lastAtPos = text.lastIndexOf("@");
    // if (lastAtPos >= 0) {
    //   const query = text.slice(lastAtPos + 1);
    //   console.log(query, 3244342);
    //   setMentionQuery(query);
    // } else {
    //   setMentionQuery("");
    // }

    const shadowDom = document.getElementById("shadow-dom-note").shadowRoot;
    const noteDom = shadowDom.getElementById(`note${note.id}`);
    const computedStyle = window.getComputedStyle(noteDom);
    const transform = computedStyle.transform || "";
    let translateX = 0,
      translateY = 0;
    const values = transform.match(/matrix\((.+)\)/);
    if (values) {
      const matrix = values[1].split(", ").map(parseFloat);
      translateX = matrix[4];
      translateY = matrix[5];
    }
    const left = parseFloat(computedStyle.left) || 0;
    const top = parseFloat(computedStyle.top) || 0;

    const textarea = noteDom.querySelector("textarea");
    const cursorPos = textarea.selectionStart;
    const textBeforeCursor = text.substring(0, cursorPos);
    const lastAtPos = textBeforeCursor.lastIndexOf("@");
    if (lastAtPos >= 0 && !textBeforeCursor.substring(lastAtPos).includes(" ")) {
      const query = textBeforeCursor.slice(lastAtPos + 1);
      const coords = getCursorCoordinates(textarea, cursorPos);
      setMentionQuery(query);
      const list = userList.filter((item) => {
        item.nameStyle = item.name; // 修改 nameStyle
        return item.name.includes(query); // 根据 query 过滤
      });
      setFilteredNoteList(list);
      const positionLeft = coords.left - (translateX + left);
      const positionTop = coords.top - (translateY + top);
      setMentionPopup({ left: positionLeft, top: positionTop + 50 });
    } else {
      setMentionPopup({ ...mentionPopup });
    }
  };
  // 插入提及
  const insertMention = (user) => {
    if (markdownValue.includes("note_rel_group") && user.type == "note_rel_group") {
      message.open({
        type: "info",
        content: "已经加入组了，一个便签只能加入一个组",
      });
      return;
    }
    const mentionText = `<a data-data='${JSON.stringify(user)}' data-icon='${user.icon}' data-name='${user.name}' data-id='${user.objId}' style="width: 12px; height: 12px; vertical-align: middle; margin: 0 2px 0 0">@${user.name}</a>`;
    const newValue = markdownValue.replace(`@${mentionQuery}`, mentionText);
    setMarkdownValue(newValue);
    const shadowDom = document.getElementById("shadow-dom-note").shadowRoot;
    const noteDom = shadowDom.getElementById(`note${note.id}`);
    const textarea = noteDom.querySelector("textarea");
    if (textarea) {
      textarea.focus();
    }
    setMentionQuery("");
  };
  // 获取光标的屏幕坐标
  const getCursorCoordinates = (textarea, cursorPos) => {
    const rect = textarea.getBoundingClientRect();
    const mirrorDiv = document.createElement("div");
    mirrorDiv.style.whiteSpace = "pre-wrap";
    mirrorDiv.style.position = "absolute";
    mirrorDiv.style.visibility = "hidden";
    mirrorDiv.style.top = `${rect.top + window.scrollY}px`; // 加上滚动偏移量
    mirrorDiv.style.left = `${rect.left + window.scrollX}px`; // 加上滚动偏移量
    mirrorDiv.style.width = `${textarea.clientWidth}px`;
    mirrorDiv.style.font = window.getComputedStyle(textarea).font;
    mirrorDiv.textContent = markdownValue.substring(0, cursorPos);
    document.body.appendChild(mirrorDiv);

    const span = document.createElement("span");
    span.textContent = "."; // 占位符
    mirrorDiv.appendChild(span);
    const { left, top } = span.getBoundingClientRect();

    document.body.removeChild(mirrorDiv);

    // 返回相对于页面的绝对位置
    return { left: left + window.scrollX, top: top + window.scrollY - textarea.scrollTop };
  };
  const continueClick = () => {
    browser.runtime.sendMessage({
      type: "openChat",
      data: "",
    });
  };
  return (
    <Flex
      gap={token.padding}
      vertical
      style={{
        borderRadius: token.borderRadiusLG,
        backgroundColor: note?.color,
        fontSize: "12px",
        height: `${isThumbnail ? "250px" : `${height}px`}`,
        width: `${width}px`,
        position: "relative",
        userSelect: "none",
      }}
      className={classNames("sino-tooltipInstance-sticky", isThumbnail ? "" : acitve ? "active" : "")}
    >
      <div
        onMouseDown={onMouseDown}
        style={{
          position: "absolute",
          right: 0,
          bottom: 0,
          width: "20px",
          height: "20px",
          cursor: "se-resize", // 鼠标样式为右下角调整大小
        }}
      />
      <Flex vertical className="sino-note-content" gap={token.padding}>
        {isThumbnail || !noteInfo.editable ? (
          <Typography.Text strong style={{ marginLeft: "28px", fontSize: token.fontSizeLG }}>
            {noteTitle || "无标题"}
          </Typography.Text>
        ) : (
          <Input
            placeholder="请输入标题"
            maxLength={15}
            variant="borderless"
            style={{
              padding: "1px 0 1px 3px",
              marginLeft: "28px",
              color: token.colorTextBase,
              fontWeight: 600,
              fontSize: token.fontSizeLG,
            }}
            value={noteTitle}
            onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
              const { value: inputValue } = e.target;
              // 如果首个字符不是空格，更新状态
              if (inputValue.length === 0 || inputValue[0] !== " ") {
                setNoteTitle(inputValue);
              }
              debounceHandleInput([{ value: inputValue, key: "title" }]);
            }}
          />
        )}
        {/* {JSON.stringify(noteInfo)} */}
        {/* 引入内容区 div用来消除父组件flex对display: "inline"之后背景不跟随文本长度的影响*/}
        {noteInfo.type === QUOTE && <ExpandableText text={noteInfo.quoteContent} />}
        <div
          className="sino-content"
          style={{ height: `calc(100% - ${noteInfo.type === QUOTE ? "65" : "20"}px)`, overflow: "auto" }}
        >
          {!noteInfo.editable || isThumbnail ? (
            <p
              className="sino-proseMirror-static sino-proseMirror-static-view"
              onClick={noteClick}
              style={{ height: (noteInfo.type === QUOTE ? 138 : 134) + "px" }}
              dangerouslySetInnerHTML={{
                __html: noteInfo.content.replace(
                  /chrome-extension:\/\/[a-zA-Z0-9]+\/images\/textEditor\//g,
                  `chrome-extension://${browser.runtime.id}/images/textEditor/`,
                ),
              }}
            />
          ) : (
            <>
              {noteInfo.type === ONQUIZ ? (
                <div
                  ref={textareaRef}
                  style={{ height: "calc(100% - 10px)" }}
                  className="note-react-mde"
                  onBlur={() => {
                    setNoteInfo((prevState) => {
                      const updatedNoteInfo = {
                        ...prevState,
                        content: markdownValue, // 更新 content 属性
                      };
                      updateNoteValue([{ value: markdownValue, key: "content" }]);
                      // editNoteRequest(updatedNoteInfo);
                      return updatedNoteInfo;
                    });
                  }}
                >
                  <ReactMde
                    ref={mdeRef}
                    value={markdownValue}
                    // onChange={setMarkdownValue}
                    onChange={handleInputChange}
                    selectedTab={selectedTab}
                    onTabChange={(e) => {
                      setSelectedTab(e);
                      setMentionQuery("");
                      setFilteredNoteList([]);
                    }}
                    generateMarkdownPreview={(markdown) =>
                      Promise.resolve(
                        <ReactMarkdown remarkPlugins={[remarkGfm, remarkBreaks]} rehypePlugins={[rehypeRaw]}>
                          {markdown}
                        </ReactMarkdown>,
                      )
                    }
                  />
                  {mentionQuery && (
                    <div
                      style={{
                        position: "absolute",
                        left: `${mentionPopup.left}px`,
                        top: `${mentionPopup.top}px`,
                        background: "white",
                        border: "1px solid #ddd",
                        zIndex: 1000,
                      }}
                    >
                      <MentionReact
                        noteId={noteInfo.id}
                        query={mentionQuery}
                        suggestions={filteredNoteList}
                        noteType={noteInfo.type}
                        onSelect={(item) => {
                          insertMention(item);
                          setFilteredNoteList([]);
                        }}
                        count={mentionQuery.length}
                        onHide={() => {
                          setMentionQuery("");
                          setFilteredNoteList([]);
                        }}
                      />
                    </div>
                  )}
                </div>
              ) : (
                <iframe
                  id={`iframe${noteInfo.id}`}
                  src={`chrome-extension://${browser.runtime.id}/pop.html?noteId=${noteInfo.id}&insertType=${noteInfo.insertType}&sinoKey=${sessionStorage.getItem("sino-tap-key")}`}
                  style={{ border: 0, height: "100%", width: "100%" }}
                />
              )}
            </>
          )}
        </div>
        <Flex>
          <Flex onClick={continueClick} align="center" style={{ cursor: "pointer" }}>
            <IconFont className="icon" type="AIChatOutlined" fill={token.colorInfoText} style={{ fontSize: "14px" }} />
            <Flex style={{ marginLeft: token.marginXS, color: token.colorText, fontSize: token.fontSize }}>问答</Flex>
          </Flex>
        </Flex>
      </Flex>
      {isThumbnail && (
        <Flex style={{ backgroundColor: "#fff", position: "absolute", bottom: 12, width: "calc(100% - 20px)" }}>
          <Button color="primary" type="link" style={{ padding: 0 }} onClick={onExpanded}>
            展开便签
          </Button>
        </Flex>
      )}
    </Flex>
  );
};

export default PlainNote;
