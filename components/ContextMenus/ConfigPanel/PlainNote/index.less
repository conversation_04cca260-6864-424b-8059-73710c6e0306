@import "@/assets/styles/variables";
.sino-relation {
  position: absolute;
  bottom: 0;
  display: flex;
  align-items: center;
  padding: 6px 16px;
  color: @primary-color;
  .sino-relation-icon {
    margin-right: 3px;
  }
  .sino-relation-first {
    margin-right: 16px;
    max-width: 54px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 20px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 12px;
    cursor: pointer;
  }
  .more {
    position: relative;
    cursor: pointer;
    i {
      position: absolute;
      top: 0px;
      right: -2px;
      width: 10px;
      height: 10px;
      line-height: 10px;
      text-align: center;
      font-size: 12px;
      zoom: 0.8;
      color: #ffffff;
      background: #ee0404;
      border-radius: 50%;
    }
  }
}

.sino-tooltipInstance-sticky {
  padding: 16px !important;
  box-sizing: border-box;
  background: var(--ant-color-bg-container);
  border: 1px solid transparent;
  box-shadow:
    0px 8px 10px -5px rgba(0, 0, 0, 0.08),
    0px 16px 24px 2px rgba(0, 0, 0, 0.04),
    0px 6px 30px 5px rgba(0, 0, 0, 0.05);
  height: 100%;
  width: 100%;
  .note-react-mde{
    height: calc(100% - 10px);
    .react-mde{
      height:100%;
      overflow:auto;
      >div:nth-child(2){
        height: calc(100% - 44px);
      }
      .mde-textarea-wrapper{
        height: 100%;
        textarea{
          height:100% !important;
        }
      }
    }
  }
}
.sino-tooltipInstance-sticky.active {
  border-color: var(--ant-color-primary-hover);
}
.sino-note-content {
  height: 100%;
  p {
    margin: 0 !important;
  }

  .sino-content {
    margin-top: 4px;
    overflow: hidden;
  }
}
