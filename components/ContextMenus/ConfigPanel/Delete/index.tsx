import React from "react";
import { But<PERSON>, message, Popconfirm, theme, Tooltip } from "antd";
import { DelSVGIcon } from "@/config/menu/note";
import setModifyItem, { NOTE_MODIFY_STORAGE_KEY } from "@/utils/browserStorageCurrentPage";

const { useToken } = theme;

const Delete: React.FC<{
  note;
  onSuccess?: () => void;
  svgWidth?: number;
  width?: number;
  type?: "link" | "text" | "default" | "primary" | "dashed";
}> = ({ note, onSuccess, width = 20, svgWidth = 14, type = "text" }) => {
  const { token } = useToken();
  const fetchRequest = useFetchRequest();

  /** 处理删除便签 */
  const handleDelete = (event, note: CopilotNote) => {
    event.stopPropagation();
    fetchRequest({
      api: "delNote",
      params: { id: note.id },
      callback: (res) => {
        if (res.code == 200) {
          setModifyItem(NOTE_MODIFY_STORAGE_KEY, {
            key: new Date().getTime(),
            updateType: "del",
            id: note.id,
          });
          message.open({
            type: "success",
            duration: 1,
            content: "删除成功！",
          });
          onSuccess?.();
          window.postMessage({ type: "delNoteNotice", note }, "*");
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };

  const stopPropagation = (event) => event.stopPropagation();

  return (
    <>
      <Popconfirm
        title="确认删除该便签？"
        onConfirm={(e) => handleDelete(e, note)}
        onCancel={stopPropagation}
        getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
        okText="确认"
        cancelText="取消"
      >
        <Tooltip placement="top" title="删除" getPopupContainer={(triggerNode) => triggerNode.parentNode as any}>
          <Button
            icon={<DelSVGIcon width={svgWidth} height={svgWidth} />}
            type={type}
            style={{ padding: token.paddingXXS, height: `${width}px`, width: `${width}px` }}
            onClick={stopPropagation}
            disabled={!note.editable}
          ></Button>
        </Tooltip>
      </Popconfirm>
    </>
  );
};

export default Delete;
