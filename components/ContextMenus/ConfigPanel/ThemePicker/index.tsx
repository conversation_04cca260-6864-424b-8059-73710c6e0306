import React, { useEffect, useState } from "react";
import { NOTE_THEME } from "@/utils/notes";
import { Flex } from "antd";
import "./index.less";

const ThemePicker: React.FC<{ note; onChange?: (note: CopilotNote) => void }> = ({ note, onChange }) => {
  const [noteInfo, setNoteInfo] = useState(note);
  const themeColors = [NOTE_THEME, "#FEFFE6", "#E6FFFB"];

  useEffect(() => {
    setNoteInfo(note);
  }, [note]);

  const changeTheme = (colorVal) => {
    let value = { color: colorVal };
    onChange(value as CopilotNote);
  };

  return (
    <>
      <Flex className="theme-dropdown">
        <ul>
          {themeColors.map((item) => (
            <li
              key={item}
              className={`${(noteInfo?.color ?? NOTE_THEME) === item && "current"}`}
              style={{ background: `${item}` }}
              onClick={() => changeTheme(item)}
            ></li>
          ))}
        </ul>
      </Flex>
    </>
  );
};

export default ThemePicker;
