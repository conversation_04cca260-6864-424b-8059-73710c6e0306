.theme-dropdown {
  position: relative;
  background: #ffffff;
  border-radius: 8px;
  box-shadow:
    0px 9px 28px 8px rgba(0, 0, 0, 0.05),
    0px 6px 16px 0px rgba(0, 0, 0, 0.08),
    0px 3px 6px -4px rgba(0, 0, 0, 0.12);
  &::before {
    content: "";
    position: absolute;
    z-index: 9;
    right: 57px;
    bottom: -6px; /* 是边框宽度的一半 */
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid #ffffff;
  }
  ul {
    padding: 12px;
    display: flex;
    align-items: center;
    list-style: none;
    margin: 0;
    li {
      width: 24px;
      height: 24px;
      margin-right: 16px;
      border-radius: 50%;
      box-sizing: border-box;
      cursor: pointer;
      border: 1px solid var(--ant-color-border);
      font-style: normal;
      &:hover,
      &.current {
        border: 2px solid var(--ant-color-primary-hover);
      }
      &:last-of-type {
        margin-right: 0;
      }
    }
  }
}

.pos-top {
  .theme-dropdown {
    &::before {
      bottom: auto;
      top: -6px;
      transform: rotateX(180deg);
    }
  }
}
