import React, { useRef, useState } from "react";
import { Avatar, Button, Flex, Input, message, Popconfirm, Space, Tag, Tooltip, Typography } from "antd";
import { formatDate } from "@/utils/dateFormat.ts";
import { DelSVGIcon, InfoSVGIcon } from "@/config/menu/note";
import { usePermissions } from "@/entrypoints/sidepanel/components/PermissionProvider";
const { TextArea } = Input;

const DiffComment: React.FC<{
  commentKey;
  commentList;
  getCommentPage;
  noteInfo;
}> = ({ commentKey, commentList, getCommentPage, noteInfo }) => {
  const [hoveredIndex, setHoveredIndex] = useState("");
  const [commentValue, setCommentValue] = useState("");
  const [textAreaPlaceholder, setTextAreaPlaceholder] = useState("请输入评论");
  const { userInfo } = usePermissions();
  const timeoutRef = useRef(null); // 用于存储定时器 ID
  const fetchRequest = useFetchRequest();

  const handleMouseEnter = (index) => {
    // 清除之前的定时器
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null; // 清空引用
    }
    setHoveredIndex(index);
  };

  const handleMouseLeave = () => {
    // 设置一个新的定时器
    timeoutRef.current = setTimeout(() => {
      setHoveredIndex("");
    }, 200); // 根据需要调整延迟时间
  };

  const submit = () => {
    if (commentValue == "") return;
    let param: AddCommentRequest = {
      busiId: noteInfo.id.toString(), // 业务id
      cmtContent: commentValue, // 评论内容
    };
    if (replyInfo.userId) {
      param.replyCmtId = replyInfo.contentId; // 回复评论id
      param.replyUser = replyInfo.userId; // 回复人
    }
    fetchRequest({
      api: "addComment",
      params: param,
      callback: (res) => {
        if (res.code === 200) {
          message.open({
            type: "success",
            content: "评论成功",
          });
          setReplyInfo({
            contentId: "",
            userId: "",
          });
          setCommentValue("");
          getCommentPage();
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };

  // 删除评论
  const delComment = (item: any) => {
    fetchRequest({
      api: "delComment",
      params: {
        id: item.id,
      },
      callback: (res) => {
        if (res.code === 200) {
          message.open({
            type: "success",
            content: "删除成功",
          });
          getCommentPage();
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };

  const [replyInfo, setReplyInfo] = useState<{
    contentId: string;
    userId: string;
  }>({
    contentId: "",
    userId: "",
  }); // 回复人

  // 回复
  const handleReplyInfo = (item: any) => {
    setReplyInfo({
      contentId: item.id,
      userId: item.cmtPerson,
    });
    setTextAreaPlaceholder(`@${item.cmtPersonName}`);
  };

  return (
    <>
      {commentList.map((x, index) => (
        <Flex key={x.id} vertical flex={1}>
          <Flex
            gap={8}
            style={{ marginBottom: "12px", paddingLeft: commentKey ? "40px" : "0" }}
            onMouseEnter={() => handleMouseEnter(index + "" + commentKey)}
            onMouseLeave={handleMouseLeave}
          >
            {x.cmtPersonPhoto ? (
              <Avatar
                size={commentKey ? 20 : 30}
                style={{ flex: `${commentKey ? 20 : 30}px 0 0` }}
                src={<img src={x.cmtPersonPhoto} alt="avatar" />}
              />
            ) : (
              <Avatar size={commentKey ? 20 : 30} style={{ flex: `${commentKey ? 20 : 30}px 0 0` }}>
                {x?.cmtPersonName?.charAt(0)}
              </Avatar>
            )}
            <Flex vertical>
              <Space>
                <Typography.Text strong>{x.cmtPersonName}</Typography.Text>
                <Typography.Text type="secondary">{formatDate(x.cmtTime)}</Typography.Text>
                {hoveredIndex === index + "" + commentKey && (
                  <>
                    <Tooltip
                      placement="top"
                      title="回复"
                      getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                    >
                      <Button
                        icon={<InfoSVGIcon width={12} height={12} />}
                        style={{ padding: 0, height: "12px" }}
                        onClick={() => handleReplyInfo(x)}
                        type="link"
                        block
                      />
                    </Tooltip>
                    {userInfo.id === x.cmtPerson ? (
                      <Popconfirm
                        title="确认删除该评论吗？"
                        onConfirm={() => delComment(x)}
                        onCancel={(event) => event.stopPropagation()}
                        getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                        okText="确认"
                        cancelText="取消"
                      >
                        <Tooltip
                          placement="top"
                          title="删除"
                          getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                        >
                          <Button
                            icon={<DelSVGIcon width={12} height={12} />}
                            style={{ padding: 0, height: "12px" }}
                            type="link"
                            block
                          />
                        </Tooltip>
                      </Popconfirm>
                    ) : null}
                  </>
                )}
              </Space>
              <Typography.Text>
                {x.cmtContent}
                {x.replyCmtId && (
                  <Tag bordered={false} color="processing" style={{ marginLeft: "4px" }}>
                    @{x.replyUserName || x.replyUser}
                  </Tag>
                )}
              </Typography.Text>
            </Flex>
          </Flex>
          {replyInfo.contentId === x.id ? (
            <>
              <TextArea
                placeholder={textAreaPlaceholder}
                value={commentValue}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => {
                  const { value } = e.target;
                  // 更新评论内容
                  setCommentValue(value.trim());
                }}
                maxLength={120}
                rows={3}
              />
              <Flex justify="flex-end" style={{ marginTop: "10px" }}>
                <Space>
                  <Button
                    size="small"
                    onClick={() => {
                      setReplyInfo({
                        contentId: "",
                        userId: "",
                      });
                    }}
                  >
                    取消
                  </Button>
                  <Button size="small" type="primary" onClick={submit}>
                    确定
                  </Button>
                </Space>
              </Flex>
            </>
          ) : null}
          <DiffComment
            commentKey={x.id + ""}
            commentList={x.child}
            getCommentPage={getCommentPage}
            noteInfo={noteInfo}
          />
        </Flex>
      ))}
    </>
  );
};

export default React.memo(DiffComment);
