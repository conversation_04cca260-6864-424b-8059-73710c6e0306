import React, { useState } from "react";
import { Button, Empty, Flex, Input, message, Space, theme, Typography } from "antd";
import { DownOutlined, UpOutlined } from "@ant-design/icons";
import DiffComment from "./DiffComment";

const { useToken } = theme;

const { TextArea } = Input;
const Comment: React.FC<{ noteInfo; handleBack }> = ({ noteInfo, handleBack }) => {
  const { token } = useToken();
  const [rows, setRows] = useState(2);
  const [isCommentOpen, setIsCommentOpen] = useState<boolean>(true);
  const [loading, setLoading] = useState<boolean>(true);
  const [commentList, setCommentList] = useState<Array<CommentType>>([]);
  const [commentValue, setCommentValue] = useState("");
  const fetchRequest = useFetchRequest();
  // 提交评论
  const submit = () => {
    if (commentValue == "") {
      message.open({
        type: "error",
        content: "评论内容不能为空",
      });
      return;
    }
    let param: AddCommentRequest = {
      busiId: noteInfo.id.toString(), // 业务id
      cmtContent: commentValue, // 评论内容
    };
    fetchRequest({
      api: "addComment",
      params: param,
      callback: (res) => {
        if (res.code === 200) {
          message.open({
            type: "success",
            content: "评论成功",
          });
          setCommentValue("");
          getCommentPage();
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };
  // 取消评论
  const clearComment = () => {
    setRows(2);
    setCommentValue("");
  };
  // 展开评论
  const openComment = () => {
    setIsCommentOpen(!isCommentOpen);
    if (isCommentOpen) {
      getCommentPage();
    }
  };
  // 获取评论列表
  const getCommentPage = () => {
    let param = {
      busiId: noteInfo.id.toString(),
    };
    fetchRequest({
      api: "pageComments",
      params: param,
      callback: (res) => {
        setLoading(false);
        if (res.code === 200) {
          setCommentList(res.data);
        } else {
          message.open({
            type: "error",
            content: res.msg,
          });
        }
      },
    });
  };
  return (
    <>
      <Flex
        vertical
        style={{
          width: "300px",
          backgroundColor: "#fff",
          borderRadius: "10px",
          fontSize: "14px",
          marginLeft: "3px",
          cursor: "pointer",
          padding: "13px",
          border: `1px solid ${token.colorInfoBorder}`,
        }}
      >
        <TextArea
          placeholder="请输入评论"
          onFocus={() => setRows(4)}
          value={commentValue}
          onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => {
            const { value } = e.target;
            // 更新评论内容
            setCommentValue(value.trim());
          }}
          maxLength={120}
          rows={rows}
        />
        <Flex justify="flex-end" style={{ marginTop: "10px" }}>
          <Space>
            <Button
              size="small"
              onClick={() => {
                clearComment();
                handleBack();
              }}
            >
              取消
            </Button>
            <Button size="small" type="primary" onClick={submit}>
              确定
            </Button>
          </Space>
        </Flex>
        <Flex justify="space-between">
          <Typography.Title level={5} style={{ margin: "0", padding: "10px 0" }}>
            评论...
          </Typography.Title>
          {isCommentOpen ? (
            <UpOutlined onClick={() => openComment()} />
          ) : (
            <DownOutlined onClick={() => openComment()} />
          )}
        </Flex>
        {!isCommentOpen && !loading && (
          <Flex vertical style={{ maxHeight: "300px", overflowY: "auto" }}>
            {commentList.length > 0 ? (
              <DiffComment
                commentKey={""}
                commentList={commentList}
                getCommentPage={getCommentPage}
                noteInfo={noteInfo}
              />
            ) : (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无数据" />
            )}
          </Flex>
        )}
      </Flex>
    </>
  );
};

export default Comment;
