.sino-inner-element {
  position: relative;
  overflow: hidden;
  color: var(--ant-color-text-secondary);
  font-size: var(--ant-font-size-sm);
  /*必须结合的属性,当内容溢出元素框时发生的事情*/
  text-overflow: ellipsis;
  /*可以用来多行文本的情况下，用省略号“…”隐藏超出范围的文本 。*/
  display: -webkit-box;
  /*必须结合的属性 ，将对象作为弹性伸缩盒子模型显示 。*/
  -webkit-box-orient: vertical;
  /*必须结合的属性 ，设置或检索伸缩盒对象的子元素的排列方式 。*/
  text-align: justify;
}

.sino-line-clamp-three {
  line-clamp: 2;
  -webkit-line-clamp: 2;
  /*用来限制在一个块元素显示的文本的行数。*/
}

.sino-line-clamp-countless {
  line-clamp: 666;
  -webkit-line-clamp: 666;
  /*用来限制在一个块元素显示的文本的行数。*/
}

.sino-float-verticalLine {
  clear: both;
  float: right;
  height: calc(100% - 20px);
}

.sino-float {
  float: right;
  clear: both;
  margin-left: 7px;
  color: var(--ant-color-primary);
}

.sino-float:hover {
  cursor: pointer;
}
