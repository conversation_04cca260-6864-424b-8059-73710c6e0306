import React, { useState, useRef, useEffect } from "react";
import "./index.less"; // 引入样式文件
const { useToken } = theme;
import { CaretDownOutlined, CaretUpOutlined } from "@ant-design/icons";
import { theme } from "antd";

const ExpandableText: React.FC<{ text: string }> = ({ text }) => {
  const { token } = useToken();
  const [isExpanded, setIsExpanded] = useState(true);
  const innerElementRef = useRef(null);
  const floatDomRef = useRef(null);

  useEffect(() => {
    const innerElement = innerElementRef.current;
    const lineHeight = parseFloat(getComputedStyle(innerElement).lineHeight);
    const maxHeight = lineHeight * 2; // 两行的高度
    if (innerElement.offsetHeight > 0 && innerElement.offsetHeight > maxHeight) {
      floatDomRef.current.style.display = "block";
      setIsExpanded(false);
    }
  }, [text]);

  const toggleExpand = () => {
    setIsExpanded((prev) => !prev);
  };

  return (
    <div
      style={{
        display: "flex",
        width: "100%",
        marginTop: "4px",
        paddingLeft: "3px",
        backgroundColor: token.colorFillQuaternary,
        borderRadius: token.borderRadiusSM,
      }}
    >
      <div
        className={`sino-inner-element ${isExpanded ? "sino-line-clamp-countless" : "sino-line-clamp-three"}`}
        style={{
          maxHeight: "60px",
          lineHeight: "20px",
        }}
        ref={innerElementRef}
      >
        <div className="sino-float-verticalLine"></div>
        <div
          className="sino-float"
          style={{
            display: "none",
          }}
          ref={floatDomRef}
          onClick={toggleExpand}
        >
          {isExpanded ? (
            <span
              style={{
                fontSize: token.fontSizeSM,
              }}
            >
              收起 <CaretUpOutlined />
            </span>
          ) : (
            <span
              style={{
                fontSize: token.fontSizeSM,
              }}
            >
              展开 <CaretDownOutlined />
            </span>
          )}
        </div>
        <i
          style={{
            fontSize: token.fontSizeSM,
            fontStyle: "normal",
            color: token.colorTextTertiary,
            lineHeight: token.lineHeightSM,
            padding: `0 ${token.marginXXS}`,
          }}
        >
          {text}
        </i>
      </div>
    </div>
  );
};

export default ExpandableText;
