/** 流式输出组件，用于AI回答的输出渲染 */
import React from "react";
import { AgentThoughtItem, VisibleFileItem } from "@/types/chat";
import Markdown from "@/components/Markdown";
import ImageGallery from "@/components/ImageGallery";
import AgentThought from "@/components/AgentOutput/components/AgentThought";
import { LoadingOutlined } from "@ant-design/icons";
import { Flex, Spin } from "antd";
import "./index.less";

export type AgentOutputProps = {
  /**
   * 本次输入/输出的纯文本内容
   */
  content: string;

  /**
   * 回答是否输出完毕
   */
  finished: boolean;

  /**
   * 类型
   */
  role: "system" | "user" | "assistant";

  /**
   * AI智能助手的思考步骤，当对接应用为智能助手类型时，该字段才可能不为空
   */
  agent_thoughts?: Array<AgentThoughtItem>;

  /**
   * 该输入/输出需要展示的文件
   */
  message_files?: Array<VisibleFileItem>;
};

const AgentOutput: React.FC<AgentOutputProps> = ({ content, finished = false, agent_thoughts, message_files }) => {
  // 判断本次输出否属于一次智能助手输出
  const agentModeFlag: boolean = !!agent_thoughts && agent_thoughts.length > 0;
  const loading =
    !finished &&
    (agentModeFlag
      ? !content && (agent_thoughts || []).filter((item) => !!item.thought || !!item.tool).length === 0
      : !content);
  return (
    <div className={"agent-output-container"}>
      {loading ? (
        // <SyncOutlined spin />
        <Flex className="search-progress">
          <Spin indicator={<LoadingOutlined className="agent-load" />} />
          <span>检索中</span>
        </Flex>
      ) : agentModeFlag ? (
        // 智能模式的输出渲染内容
        <Flex className="agent-thought">
          {agent_thoughts?.map((item, index) => {
            return (
              <Flex className="agent-thought-item" key={index}>
                {/* 渲染思考文本 */}
                {content && index === 0 && <Markdown content={content} finished={finished} />}
                {/* 显示工具调用（或有）*/}
                {!!item.tool && <AgentThought thought={item} finished={!!item.observation || finished} />}
              </Flex>
            );
          })}
          {/* 文件展示（或有，暂时只支持图片）*/}
          {message_files && message_files.length > 0 && (
            <ImageGallery srcList={message_files?.map((item) => item.url)} />
          )}
        </Flex>
      ) : (
        // 普通模式的输出渲染内容
        <Markdown content={content} finished={finished} />
      )}
    </div>
  );
};
export default React.memo(AgentOutput);
