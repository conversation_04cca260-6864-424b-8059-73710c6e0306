@import "@/assets/styles/variables";

.agent-output-container {
  position: relative;
  width: 100%;
  .search-progress {
    .agent-load {
      color: var(--ant-color-primary);
    }
    span {
      color: var(--ant-color-text-tertiary);
      font-size: var(--ant-font-size);
      margin-left: var(--ant-margin-xxs);
    }
  }
}

.agent-thought-item {
  /* 每个思考项的样式 */
}

.output-toolbar {
  /* 功能区 */
  position: absolute;
  right: 0;
  display: none;
  justify-content: flex-end;
  z-index: 1;
}

.chat-answer {
  &:hover .output-toolbar {
    display: flex;
  }
}
