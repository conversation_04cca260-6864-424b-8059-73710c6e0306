/** 工具详情面板组件 */
import React from "react";
import "./index.less";

/**
 * 工具详情面板属性类型声明
 */
export type AgentToolPanelProps = {
  /**
   * 类型：request-请求信息，response-响应信息
   */
  type: "request" | "response";
  /**
   * 工具名称
   */
  toolName: string;
  /**
   * 输出内容
   */
  content: string;
};

const AgentToolPanel: React.FC<AgentToolPanelProps> = ({ type, toolName, content }) => {
  return (
    <div className="agent-tool-panel-container">
      <div className="panel request">
        {type === "request" ? "请求来自" : "响应来自"} {toolName}
      </div>
      <div className="panel response">{content}</div>
    </div>
  );
};
export default React.memo(AgentToolPanel);
