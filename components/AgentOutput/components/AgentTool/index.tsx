/** 智能助手工具调用渲染结果 */
import React, { useState } from "react";
import { Spin } from "antd";
import AgentToolPanel from "@/components/AgentOutput/components/AgentToolPanel";
import IconFont from "@/components/IconFont";
import { LoadingOutlined } from "@ant-design/icons";
import "./index.less";

export type AgentToolProps = {
  toolInfo: {
    name: string;
    input: string;
    output: string;
    finished: boolean;
  };
};

const AgentTool: React.FC<AgentToolProps> = ({ toolInfo }) => {
  // 是否展示工具详情
  const [showDetailFlag, setShowDetailFlag] = useState<boolean>(false);
  return (
    <div className="agent-tool-container" onClick={() => setShowDetailFlag(!showDetailFlag)}>
      <div className="abstract">
        {/* 工具未完成调用时，显示加载 */}
        {!toolInfo.finished && <Spin indicator={<LoadingOutlined />} />}
        {toolInfo.finished && <IconFont type="icon-finished" />}
        <span>{toolInfo.finished ? "已使用" : "正在使用"}</span>
        <span>{toolInfo.name}</span>
        <span className={showDetailFlag ? "show-detail" : ""}>
          <IconFont type="icon-arrow" />
        </span>
      </div>
      {/* 工具调用详情 */}
      {showDetailFlag && (
        <div className="panel">
          <AgentToolPanel type="request" toolName={toolInfo.name} content={toolInfo.input} />
          {toolInfo.output && (
            <AgentToolPanel type="response" toolName={toolInfo.name} content={toolInfo.output as string} />
          )}
        </div>
      )}
    </div>
  );
};

export default React.memo(AgentTool);
