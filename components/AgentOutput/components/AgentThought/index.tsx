/** 智能助手思考内容组件 */
import React from "react";
import { AgentThoughtItem } from "@/types/chat";
import AgentTool from "@/components/AgentOutput/components/AgentTool";
import "./index.less";

/**
 * 思考内容属性类型声明
 */
export type AgentThoughtProps = {
  /**
   * 思考内容
   */
  thought: AgentThoughtItem;
  /**
   * 思考是否结束
   */
  finished: boolean;
};

const AgentThought: React.FC<AgentThoughtProps> = ({ thought, finished }) => {
  // 获取工具名称的数组，以及判断它是否是原先就是以数组形式传递过来的
  const toolNames: string[] = thought.tool.split(";");
  const arrayValueFlag: boolean = toolNames.length >= 2;

  // 组装工具数据列表
  const toolList = toolNames.map((toolName, index) => {
    return {
      name: toolName,
      input: arrayValueFlag ? JSON.parse(thought.tool_input)[index] : thought.tool_input,
      output: arrayValueFlag ? JSON.parse(thought.observation)[index] : thought.observation,
      finished,
    };
  });

  return (
    <div className="agent-thought-container">
      {toolList.map((item, index) => {
        return <AgentTool key={index} toolInfo={item} />;
      })}
    </div>
  );
};

export default React.memo(AgentThought);
