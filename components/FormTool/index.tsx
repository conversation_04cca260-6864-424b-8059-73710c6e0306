import { Button, Form, Input, message, Modal } from "antd";
import { useState } from "react";
import qs from "qs";
import { useFetchRequest } from "@/hooks/useFetchRequest.ts";

const FormTool = (data) => {
  const [form] = Form.useForm();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const showModal = () => {
    setIsModalOpen(true);
  };
  const fetchRequest = useFetchRequest();

  let obj = qs.parse(data.href);
  const onFinish = (e) => {
    fetchRequest({
      api: "formDemo",
      params: {
        ...e,
        user: "谭某",
        type: "holiday",
      },
      callback: (res) => {
        if (res.code === 200) {
          message.success(res.data);
          handleModalClose();
        } else if (res.code === 500) {
          message.error(res.msg);
        }
      },
    });
  };
  const handleModalClose = () => {
    setIsModalOpen(false);
  };
  return (
    <>
      <Button type="primary" onClick={showModal}>
        {data.title}
      </Button>
      <Modal
        title={data.title}
        open={isModalOpen}
        footer={null}
        closable={false}
        width={350}
        getContainer={() => {
          if (document.getElementById("shadow-side-panel")) {
            const shadowDom = document.getElementById("shadow-side-panel").shadowRoot;
            return shadowDom.querySelector("#side-panel-container-web");
          }
        }}
      >
        <Form
          form={form}
          name="leaveForm"
          onFinish={onFinish}
          initialValues={{
            holidayType: obj.holidayType || "",
            backup: obj.backup || "",
            startTime: obj.startTime || "",
            endTime: obj.endTime || "",
            reason: obj.reason || "",
          }}
          layout="vertical"
        >
          <Form.Item name="holidayType" label="请假类型" rules={[{ required: true, message: "请选择请假类型" }]}>
            <Input />
          </Form.Item>

          <Form.Item
            name="startTime"
            label="开始时间"
            rules={[{ type: "string", required: true, message: "请输入开始时间" }]}
          >
            <Input></Input>
          </Form.Item>
          <Form.Item
            name="endTime"
            label="结束时间"
            rules={[{ type: "string", required: true, message: "请输入结束时间" }]}
          >
            <Input></Input>
          </Form.Item>
          <Form.Item
            name="reason"
            label="请假原因"
            rules={[{ type: "string", required: true, message: "请输入请假原因" }]}
          >
            <Input.TextArea rows={4} />
          </Form.Item>
          <Form.Item
            name="backup"
            label="备用联系人"
            rules={[{ type: "string", required: true, message: "请输入备用联系人" }]}
          >
            <Input />
          </Form.Item>
          <Form.Item>
            <Button
              type="default"
              htmlType="button"
              style={{
                marginRight: "10px",
              }}
              onClick={handleModalClose}
            >
              取消
            </Button>
            <Button type="primary" htmlType="submit">
              提交
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default FormTool;
