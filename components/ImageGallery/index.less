.image-gallery-container {
  /* 图片预览容器 */
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  .image-item {
    flex: 1;
    /* 图片项 */
    border-radius: 8px;
    cursor: pointer;
    height: 200px;
    object-fit: cover;
  }
}

// .item {
//   height: 200px;
//   margin-right: 8px;
//   margin-bottom: 8px;
//   object-fit: cover;
//   object-position: center;
// }
//
// .item:nth-child(3n) {
//   margin-right: 0;
// }
//
// .img-2 .item:nth-child(2n),
// .img-4 .item:nth-child(2n) {
//   margin-right: 0;
// }
//
// .img-4 .item:nth-child(3n) {
//   margin-right: 8px;
// }
