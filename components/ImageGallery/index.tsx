/** 图片浏览组件 */
import React, { useState } from "react";
import ImagePreview from "../ImagePreviewer";
import "./index.less";

type ImageGalleryProps = {
  srcList: string[];
};

const getWidthStyle = (imgNum: number) => {
  if (imgNum === 1) {
    return {
      maxWidth: "100%",
    };
  }
  if (imgNum === 2 || imgNum === 4) {
    return {
      width: "calc(50% - 4px)",
    };
  }
  return {
    width: "calc(33.3333% - 5.3333px)",
  };
};

const ImageGallery: React.FC<ImageGalleryProps> = ({ srcList }) => {
  const [imagePreviewUrl, setImagePreviewUrl] = useState("");
  const distinctSrcList = Array.from(new Set(srcList));
  const imgNum = distinctSrcList.length;
  const imgStyle = getWidthStyle(imgNum);
  return (
    <div className="image-gallery-container">
      {distinctSrcList.map((src, index) => (
        <img
          key={index}
          className="image-item"
          // style={imgStyle}
          src={src}
          alt=""
          onClick={() => setImagePreviewUrl(src)}
        />
      ))}
      {imagePreviewUrl && <ImagePreview url={imagePreviewUrl} onCancel={() => setImagePreviewUrl("")} />}
    </div>
  );
};

export default React.memo(ImageGallery);
