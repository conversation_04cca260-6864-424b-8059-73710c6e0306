import React, { useEffect, useRef, useState } from "react";
import { initProsemirrorEditor } from "@/utils/prosemirror";

import { useFetchRequest } from "@/hooks/useFetchRequest";
import { message } from "antd";
import { saveDetailJumpInfo } from "@/utils/notes";
import "./prosemirror.less";
import "./index.less";
import { getMentionsPlugin, updateMentionSuggestions } from "@/utils/prosemirror/plugins/mentionPluginExtended";
import { getUserInfo } from "@/utils/auth";
import { QUIZ } from "@/utils/notes";
import setModifyItem, { SET_PLACE_TOP_ID } from "@/utils/browserStorageCurrentPage";

const TextEditor: React.FC<{
  noteId: string; // 确保唯一性
  insertType?: string; // 刚创建加载即需要获取焦点
  sinoKey?: string; // 当前网页唯一key
  height?: number | string; // 输入框的高度
  value: string; // value 输入框已经输入的内容
  placeholder?: string; // placeholder
  size?: number; // 字数限制
  type?: string; // 类型
  updateValue?: (str: string) => void; // 传进来的方法，用于吐出文本框内容
}> = ({ noteId, insertType, type, sinoKey, height, value, placeholder = "点击输入", size, updateValue }) => {
  const refContainer = useRef(null);
  const editorView = useRef(null);
  const [isEditorInitialized, setIsEditorInitialized] = useState(false);

  useEffect(() => {
    const handleNoteWrapper = () => {
      editorView.current?.focus();
    };
    let dom = document.getElementById("shadow-dom-note");
    let shadowDom, noteWrapper;
    if (dom) {
      shadowDom = dom.shadowRoot;
      noteWrapper = shadowDom.getElementById(`note${noteId}`);
      noteWrapper.addEventListener("mousedown", handleNoteWrapper);
    }
    return () => {
      noteWrapper && noteWrapper.removeEventListener("mousedown", handleNoteWrapper);
    };
  }, []);

  // 粘贴是否限制字符数
  const handlePaste = {
    // 内置 ctrl + v 粘贴段落时改为纯文本粘贴 【自定义插件/处理粘贴事件】
    handlePaste(view, event) {
      const textContentLen = editorView.current.dom.textContent.length;
      if (typeof size === "number" && size <= textContentLen) return true;

      const { state, dispatch } = view;
      const { $from } = state.selection;

      // 检查光标是否在段落中
      if ($from.parent.type.name === "paragraph") {
        event.preventDefault();
        const text = event.clipboardData?.getData("text/plain");
        dispatch(state.tr.insertText(typeof size === "number" ? text.slice(0, size - textContentLen) : text));
        return true;
      }
      return false;
    },
  };

  // 监听点击事件
  const handleClick = (view, event) => {
    // 获取点击的元素
    const clickedElement = event?.target;
    // 检查是否有 data-data 属性
    if (event && clickedElement.hasAttribute("data-data")) {
      // 获取 data-data 属性的值
      const dataValue = clickedElement.getAttribute("data-data");
      // 将值解析为对象（假设它是一个 JSON 字符串）
      try {
        const obj = JSON.parse(dataValue);
        if (obj.type === "note_rel_note") {
          const params = { id: obj.objId };
          fetchRequest({
            api: "queryNote",
            params,
            callback: (res) => {
              if (res.code === 200) {
                saveDetailJumpInfo(res.data);
              } else {
                message.open({
                  type: "error",
                  content: res.msg,
                });
              }
            },
          });
        }
      } catch (e) {
        console.error("无法解析 data-data 的值:", e);
      }
    }
  };

  const handleBlur = (view) => {
    const dom = view.dom;
    dom.scrollTop = 0;
    dom.scrollLeft = 0;
    updateValue && updateValue(view.dom.innerHTML);
  };

  const fetchRequest = useFetchRequest();
  // 创建编辑器实例
  const initEditor = (userInfo) => {
    try {
      fetchRequest({
        api: "listNoteRela",
        params: {
          type: "note_rel_all",
          query: "",
        },
        callback: (res) => {
          try {
            fetchRequest({
              api: "flatList",
              params: {
                keyword: "",
              },
              callback: (flat) => {
                try {
                  if (flat.code == 200) {
                    let arr = [];
                    flat.data.map((val) => {
                      let obj = {
                        name: val.name,
                        objId: val.id,
                        type: "note_rel_group",
                      };
                      arr.push(obj);
                    });

                    let noteList = (res.data || []).concat(arr).filter((item, index, self) => {
                      return (
                        self.findIndex((t) => t.objId === item.objId) === index &&
                        item.objId !== userInfo.id && // 不是当前登录人
                        !(res.data?.noteObjs || []).map((x) => x.objId).includes(item.objId) // 去重
                      );
                    });
                    noteList.forEach((ele) => {
                      try {
                        if (ele.type === "note_rel_contact") {
                          ele.icon = browser.runtime.getURL("/images/textEditor/userOutlined.png");
                        } else if (ele.type === "note_rel_note") {
                          ele.icon = browser.runtime.getURL("/images/textEditor/noteOutlined.png");
                        } else {
                          ele.icon = browser.runtime.getURL("/images/textEditor/medicineBoxOutlined.png");
                        }
                      } catch (error) {
                        console.error("获取图标URL失败:", error);
                        // 设置默认图标或者使用相对路径
                        ele.icon = "/images/textEditor/noteOutlined.png";
                      }
                    });

                    let options = {
                      placeholder,
                      id: noteId,
                      plugins: [],
                    };

                    try {
                      // 使用我们的函数更新提及数据
                      updateMentionSuggestionsDirectly(noteList);

                      console.log("初始化时提及数据已更新，数据条数:", noteList.length);

                      var mentionPlugin = getMentionsPlugin({
                        options,
                        getSuggestions: (done) => {
                          setTimeout(() => {
                            done(noteList);
                          }, 0);
                        },
                      });
                      options.plugins = [mentionPlugin];

                      // 只有在编辑器未初始化时才创建新的编辑器实例
                      if (!isEditorInitialized) {
                        try {
                          editorView.current = initProsemirrorEditor(
                            refContainer.current as Element,
                            value ? value : "",
                            {
                              handleDOMEvents: {
                                beforeinput: (view, event) => {
                                  const currentLength = view.state.doc.textContent.length;
                                  // 检查是否超过最大字符数
                                  if (currentLength >= 20000) {
                                    // 只在输入时阻止默认行为，允许删除
                                    if (
                                      event.inputType !== "deleteContentBackward" &&
                                      event.inputType !== "deleteByCut"
                                    ) {
                                      event.preventDefault(); // 阻止输入
                                    }
                                  }
                                  return false; // 继续传播事件
                                },
                                paste: (view, event) => {
                                  const currentLength = view.state.doc.textContent.length;
                                  // 获取粘贴内容
                                  const pastedText = event.clipboardData.getData("text/plain");
                                  // 检查粘贴后字符数是否超过限制
                                  if (currentLength + pastedText.length > 20000) {
                                    event.preventDefault(); // 阻止粘贴
                                  }
                                  return false; // 继续传播事件
                                },
                                blur: handleBlur,
                                click: handleClick,
                              },
                              ...handlePaste,
                            },
                            options,
                          );
                          if (!value && insertType === "add") {
                            editorView.current?.focus();
                          }
                          setIsEditorInitialized(true);
                        } catch (error) {
                          console.error("初始化编辑器失败:", error);
                        }
                      } else {
                        // 如果编辑器已经初始化，只更新提及插件
                        updateEditorPlugins(noteList, options);
                      }
                    } catch (error) {
                      console.error("创建提及插件失败:", error);
                    }
                  } else {
                    // 创建报错了给出提示，便签不渲染
                    message.open({
                      type: "error",
                      content: res.msg,
                    });
                  }
                } catch (error) {
                  console.error("处理数据失败:", error);
                  message.open({
                    type: "error",
                    content: "初始化编辑器失败",
                  });
                }
              },
            });
          } catch (error) {
            console.error("获取列表失败:", error);
          }
        },
      });
    } catch (error) {
      console.error("初始化编辑器请求失败:", error);
    }
  };

  // 由于提及功能的实现方式，我们不能简单地更新编辑器插件
  // 因此，当数据更新时，我们需要重新创建编辑器
  const updateEditorPlugins = (noteList, options) => {
    try {
      if (!editorView.current) return;

      // 保存当前编辑器的内容和选择状态
      const content = editorView.current.dom.innerHTML;
      const hadFocus = editorView.current.hasFocus();

      try {
        // 销毁当前编辑器实例
        editorView.current.destroy();
      } catch (error) {
        console.error("销毁编辑器实例失败:", error);
      }

      try {
        // 使用我们的函数更新提及数据
        updateMentionSuggestionsDirectly(noteList);

        console.log("提及数据已更新，数据条数:", noteList.length);

        // 创建新的提及插件
        const mentionPlugin = getMentionsPlugin({
          options,
          getSuggestions: (done) => {
            setTimeout(() => {
              done(noteList);
            }, 0);
          },
        });

        options.plugins = [mentionPlugin];

        // 重新创建编辑器
        editorView.current = initProsemirrorEditor(
          refContainer.current as Element,
          content,
          {
            handleDOMEvents: {
              beforeinput: (view, event) => {
                const currentLength = view.state.doc.textContent.length;
                // 检查是否超过最大字符数
                if (currentLength >= 20000) {
                  // 只在输入时阻止默认行为，允许删除
                  if (event.inputType !== "deleteContentBackward" && event.inputType !== "deleteByCut") {
                    event.preventDefault(); // 阻止输入
                  }
                }
                return false; // 继续传播事件
              },
              paste: (view, event) => {
                const currentLength = view.state.doc.textContent.length;
                // 获取粘贴内容
                const pastedText = event.clipboardData.getData("text/plain");
                // 检查粘贴后字符数是否超过限制
                if (currentLength + pastedText.length > 20000) {
                  event.preventDefault(); // 阻止粘贴
                }
                return false; // 继续传播事件
              },
              blur: handleBlur,
              click: handleClick,
            },
            ...handlePaste,
          },
          options,
        );

        // 如果之前有焦点，恢复焦点
        if (hadFocus) {
          editorView.current?.focus();
        }
      } catch (error) {
        console.error("重新创建编辑器失败:", error);
      }
    } catch (error) {
      console.error("更新编辑器插件失败:", error);
    }
  };
  useEffect(() => {
    try {
      getUserInfo()
        .then((userInfo) => {
          initEditor(userInfo);
        })
        .catch((error) => {
          console.error("初始化时获取用户信息失败:", error);
        });
    } catch (error) {
      console.error("初始化编辑器失败:", error);
    }

    return () => {
      if (editorView.current) {
        try {
          editorView.current.destroy();
        } catch (error) {
          console.error("销毁编辑器失败:", error);
        }
        editorView.current = null;
      }
    };
  }, []);

  // 直接更新提及数据而不重新创建编辑器
  const updateMentionSuggestionsDirectly = (noteList) => {
    try {
      if (!noteList || !Array.isArray(noteList)) {
        console.error("提及数据无效:", noteList);
        return;
      }

      // 使用导入的 updateMentionSuggestions 函数更新提及数据
      // 这个函数会更新全局变量并调用所有回调函数
      updateMentionSuggestions(noteList);
      console.log("直接更新提及数据，数据条数:", noteList.length);
    } catch (error) {
      console.error("直接更新提及数据失败:", error);
      // 备用方案：直接设置全局变量
      try {
        window.__mentionSuggestions = noteList;
        console.log("使用备用方案更新提及数据");
      } catch (backupError) {
        console.error("备用方案也失败:", backupError);
      }
    }
  };

  useEffect(() => {
    const handleNoteListChanged = (changes) => {
      if (changes["uploadGroupData"]) {
        try {
          getUserInfo()
            .then((userInfo) => {
              // 只更新提及数据，不重新创建编辑器
              fetchRequest({
                api: "listNoteRela",
                params: {
                  type: "note_rel_all",
                  query: "",
                },
                callback: (res) => {
                  fetchRequest({
                    api: "flatList",
                    params: {
                      keyword: "",
                    },
                    callback: (flat) => {
                      if (flat.code == 200) {
                        let arr = [];
                        flat.data.map((val) => {
                          let obj = {
                            name: val.name,
                            objId: val.id,
                            type: "note_rel_group",
                          };
                          arr.push(obj);
                        });

                        let noteList = (res.data || []).concat(arr).filter((item, index, self) => {
                          return (
                            self.findIndex((t) => t.objId === item.objId) === index &&
                            item.objId !== userInfo.id && // 不是当前登录人
                            !(res.data?.noteObjs || []).map((x) => x.objId).includes(item.objId) // 去重
                          );
                        });
                        noteList.forEach((ele) => {
                          try {
                            if (ele.type === "note_rel_contact") {
                              ele.icon = browser.runtime.getURL("/images/textEditor/userOutlined.png");
                            } else if (ele.type === "note_rel_note") {
                              ele.icon = browser.runtime.getURL("/images/textEditor/noteOutlined.png");
                            } else {
                              ele.icon = browser.runtime.getURL("/images/textEditor/medicineBoxOutlined.png");
                            }
                          } catch (error) {
                            console.error("获取图标URL失败:", error);
                            // 设置默认图标或者使用相对路径
                            ele.icon = "/images/textEditor/noteOutlined.png";
                          }
                        });

                        // 直接更新提及数据
                        updateMentionSuggestionsDirectly(noteList);
                      }
                    },
                  });
                },
              });
            })
            .catch((error) => {
              console.error("获取用户信息失败:", error);
            });
        } catch (error) {
          console.error("处理数据更新失败:", error);
        }
      }
    };

    try {
      browser.storage.local.onChanged.addListener(handleNoteListChanged);

      return () => {
        try {
          browser.storage.local.onChanged.removeListener(handleNoteListChanged);
        } catch (error) {
          console.error("移除事件监听器失败:", error);
        }
      };
    } catch (error) {
      console.error("添加事件监听器失败:", error);
      return () => {}; // 返回空函数以避免清理函数报错
    }
  }, []);
  return (
    <>
      <div
        className={`editor-components`}
        style={{
          height: height ? height + "px" : "100%",
        }}
        onClick={() => {
          setModifyItem(SET_PLACE_TOP_ID + sinoKey, noteId);
          editorView.current?.focus();
        }}
      >
        <div
          className="prosemirror-editor"
          style={{
            fontSize: "14px",
          }}
          ref={refContainer}
        />
      </div>
    </>
  );
};

export default React.memo(TextEditor);
