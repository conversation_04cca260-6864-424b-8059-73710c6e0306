@import "@/assets/styles/variables";

.prosemirror-editor .ProseMirror,
.sino-proseMirror-static {
  outline: 0;
  border: 0;
  color: rgba(18, 18, 18, 0.85);
  word-break: break-word;
  white-space: normal;

  &:not(.sino-proseMirror-static) {
    user-select: text;
  }

  ::selection {
    background-color: rgba(@primary-color, 0.25);
    color: inherit;
  }

  p {
    margin-top: 6px;
    padding: 0 1px;
    position: relative;
    margin: 0;
  }
  p:first-child {
    margin-top: 0;
  }
  i {
    font-style: normal;
  }

  em {
    font-style: italic !important;
    color: initial !important;
    span {
      font-style: italic !important;
    }
  }

  strong {
    font-weight: bold !important;
    span {
      font-weight: bold !important;
    }
  }

  ul {
    list-style-type: disc !important;
    padding-inline-start: 1.25em;

    li {
      list-style-type: inherit;
      padding: 2px 0;
    }
  }

  ol {
    list-style-type: decimal !important;
    padding-inline-start: 1.25em;

    li {
      list-style-type: inherit;
      padding: 2px 0;
    }
  }

  code {
    background-color: #ffe7e9;
    padding: 2px 6px;
    margin: 0 1px;
    border-radius: 4px;
    font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
  }

  sup {
    vertical-align: super;
    font-size: smaller;
  }
  sub {
    vertical-align: sub;
    font-size: smaller;
  }

  blockquote {
    overflow: hidden;
    padding-right: 1.2em;
    padding-left: 1.2em;
    margin-left: 0;
    margin-right: 0;
    font-style: italic;
    border-left: 4px solid #ddd;
  }

  [data-indent="1"] {
    padding-left: 20px;
  }
  [data-indent="2"] {
    padding-left: 40px;
  }
  [data-indent="3"] {
    padding-left: 60px;
  }
  [data-indent="4"] {
    padding-left: 80px;
  }
  [data-indent="5"] {
    padding-left: 100px;
  }
  [data-indent="6"] {
    padding-left: 120px;
  }
  [data-indent="7"] {
    padding-left: 140px;
  }
  [data-indent="8"] {
    padding-left: 160px;
  }
}
.ProseMirror-selectednode {
  outline: none !important;
}
.sino-proseMirror-static {
  font-size: 14px;
}
.sino-proseMirror-static.sino-proseMirror-static-view {
  overflow-y: auto;
}
/* 文本框不允许折行 */
.prevent-overflow {
  .ProseMirror,
  .sino-proseMirror-static {
    display: flex;
    overflow: hidden;
    white-space: nowrap;
  }
}
