@import "@/assets/styles/variables";

.editor-components {
  height: 100%;
  width: 100%;
  position: relative;
  .prosemirror-editor {
    height: 100%;
    overflow-y: auto;
    cursor: text;
    font-size: 14px;

    ::-webkit-scrollbar {
      display: none; /* 隐藏滚动条 */
    }

    .ProseMirror {
      > p {
        text-align: left;
        margin-bottom: 3px;
        line-height: var(--ant-line-height);
        display: flex;
        // align-items: center;
        font-size: var(--ant-font-size);
        flex-wrap: wrap;
        text-indent: 1px;
        a {
          background: var(--ant-color-primary-bg;);
          border-radius: var(--ant-border-radius-xs);
          padding: 0px var(--ant-padding-xxs);
          display: inline-flex;
          align-items: center;
          line-height: var(--ant-line-height);
          font-family: side-panel-font-family;
          font-size: var(--ant-font-size-sm);
          color: var(--ant-color-primary-text);
          margin: 0px 3px 0px 0px;
        }
      }
      & > p[data-placeholder]::before {
        content: attr(data-placeholder);
        pointer-events: none;
        position: absolute;
        color: rgba(#666, 0.5);
      }
    }
  }
}
.ant-popover.ant-color-picker {
  z-index: 12000 !important;
}
.prosemirror-mention-node {
  display: inline-flex;
  align-items: center;
}
