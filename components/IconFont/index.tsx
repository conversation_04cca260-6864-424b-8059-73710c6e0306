/** 自定义Icon组件，基于AntD + iconfont */
import Icon from "@ant-design/icons";
import { CSSProperties, useEffect, useState } from "react";

/** 自定义Icon组件属性 */
export type IconFontProps = {
  // icon的类型，传入值需与.svg的文件名保持一致
  type: string;
  // 以下是AntD Icon组件的component组件接受的属性
  className?: string;
  fill?: string;
  style?: CSSProperties;
  height?: string | number;
  width?: string | number;
  isGradien?: boolean | undefined;
};

const IconFont = (props: IconFontProps) => {
  const [SVGComponent, setSVGComponent] = useState(null);
  useEffect(() => {
    import(`@/assets/icons/${props.type}.svg?react`)
      .then((module) => {
        const SVG = module.default || module.ReactComponent || module;
        const DynamicSVG = (svgProps: IconFontProps) => {
          const { isGradien, ...restProps } = svgProps;
          return <SVG {...restProps} />;
        };
        DynamicSVG.displayName = `${props.type}Icon`; // 设置动态组件的 displayName
        setSVGComponent(() => DynamicSVG);
      })
      .catch((error) => console.error("Error loading SVG:", error));
  }, [props.type]);
  const gradientId = `gradient-${props.type}`;
  const gradientStyle = (
    <defs>
      <linearGradient id={gradientId} gradientTransform="rotate(128)">
        <stop offset="19%" stopColor="#1888FF" />
        <stop offset="87%" stopColor="#2F54EB" />
      </linearGradient>
    </defs>
  );
  // 返回带有渐变色的 SVG 图标
  return (
    <>
      {props.isGradien
        ? SVGComponent && (
            <Icon
              component={() => (
                <svg width={props.width} height={props.height} className={props.className} style={props.style}>
                  {gradientStyle}
                  <SVGComponent fill={`url(#${gradientId})`} />
                </svg>
              )}
            />
          )
        : SVGComponent && <Icon component={() => <SVGComponent {...props} />} />}
    </>
  );
};
IconFont.displayName = "IconFont";
export default IconFont;
