{"env": {"browser": true, "es2021": true}, "settings": {"react": {"version": "detect"}}, "extends": ["eslint:recommended", "plugin:react/jsx-runtime", "plugin:react/recommended", "plugin:@typescript-eslint/recommended", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["react", "@typescript-eslint", "prettier"], "rules": {"react/no-children-prop": "off", "no-var": "off", "prefer-const": "off", "no-irregular-whitespace": "off", "no-inner-declarations": "off", "react/jsx-key": "off", "react/prop-types": "off", "prettier/prettier": ["warn", {"endOfLine": "auto"}], "@typescript-eslint/ban-types": "off", "require-jsdoc": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": "off", "react/react-in-jsx-scope": "off"}}