/* AI聊天相关的类型 */
export = RAGChat;
export as namespace RAGChat;

declare namespace RAGChat {
  /**
   * 智能助手的每一个思考步骤类型声明
   */
  export type AgentThoughtItem = {
    /**
     * 助手思考的内容
     */
    thought: string;

    /**
     * agent思考在整个消息中的位置，比如如果是第一轮迭代则该值为1，用于数据替换
     */
    position: number;

    /**
     * 使用的工具列表，以【;】分割多个工具
     */
    tool: string;

    /**
     * 工具的输入，JSON格式的字符串
     */
    tool_input: string;

    /**
     * 工具调用的返回结果，JSON格式的字符串
     */
    observation: string;
  };

  /**
   * 用户可见的文件类型声明
   */
  export type VisibleFileItem = {
    /**
     * 文件id
     */
    id: string;

    /**
     * 文件类型，暂时只支持image-图片
     */
    type: "image";

    /**
     * 文件归属，暂时只支持assistant-AI输出
     */
    belongs_to: "assistant";

    /**
     * 文件路径
     */
    url: string;
  };

  /**
   * AI输出错误数据类型声明
   */
  export type AgentError = {
    /**
     * 任务id，用于请求跟踪或停止SSE
     */
    task_id: string;
    /**
     * 消息的唯一id
     */
    message_id: string;
    /**
     * http状态码
     */
    status: number;
    /**
     * 错误码
     */
    code: string;
    /**
     * 错误信息
     */
    message: string;
  };
}

/** 会话历史类型 */
export type Chat = {
  /** 提示词id */
  id: number;
  /** 提示词标题 */
  title: string;
  /** 类型：0-后台公共，1-个人创建 */
  type: number | string;
  /** 提示词内容 */
  content: string;
  /** 发布状态 */
  isRelease: number;
  /** 是否收藏 */
  collection: boolean;
  /** 被收藏数 */
  collectionCount: number;
  /** 创建人名称 */
  createName: string;
  /** 创建人id */
  createBy: string;
  /** 创建时间 */
  createTime: string;
  [key: string]: any;
  label: string;
  value: number;
};
