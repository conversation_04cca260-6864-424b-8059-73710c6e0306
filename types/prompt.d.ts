/** 提示词数据类型 */
export type Prompt = {
  /** 提示词id */
  id: number;
  /** Agent名称 */
  agentName: string;
  /** 提示词标题 */
  title: string;
  /** 类型：0-后台公共，1-个人创建 */
  type: number | string;
  /** 提示词内容 */
  content: string;
  /** 发布状态 */
  isRelease: number;
  /** 是否收藏 */
  collection: boolean;
  /** 被收藏数 */
  collectionCount: number;
  /** 创建人名称 */
  createName: string;
  /** 创建人id */
  createBy: string;
  /** 创建时间 */
  createTime: string;
  [key: string]: any;
  label: string;
  value: number;
};

/** 提示词列表查询参数 */
export type PromptSearchParam = {
  title?: string;
  status: number;
  query?: string | number;
  /** 类型：0-后台公共，1-个人创建 */
  type?: number | string;
  collection?: boolean;
  isRelease?: number;
  createBy?: string;
  agentId?: number | string;
  [key: string]: any;
};

export type AddPromptParams = {
  id?: number;
  title: string;
  type: number;
  status: number;
  content: string;
};
