# 后端请求地址
VITE_API_BASE=http://sinobridge-manage.gtjaqh.cloud/api
# 服务前缀
VITE_API_BASE_PUB=/langwell-pub-server
VITE_API_BASE_SYS=/langwell-sys-server
VITE_API_BASE_NOTE=/langwell-notes-server
VITE_API_BASE_AI=/langwell-ai-server
VITE_API_BASE_INS=/langwell-ins-server
VITE_API_BASE_DOC=/langwell-doc-server

# API凭据key
VITE_API_HEADER_KEY=OVERTOKEN
# 授权方式
VITE_AUTHORIZE_MODE=account
# AI后端请求地址
VITE_AI_API_BASE=http://sinobridge-dify.gtjaqh.cloud/v1
VITE_AI_CHAT_SECRET=app-YXviMrwqRjim51DW0tFApWVn
VITE_AI_CHAT_AGENT_ID=289f9316-56bb-481a-927c-b7b9ff7c69ff
VITE_AI_WRITER_SECRET=b02fd64d-d611-42d4-8860-ec89917d2df3
VITE_AI_REPLY_SECRET=9cd5c1a6-6e50-4ff5-8dec-f9968f4d8439

# 租户用户信息
VITE_USERINFO_BASS=http://sinobridge-lamp.gtjaqh.cloud/api
# 官网页面地址
VITE_OFFICIAL_URL=http://sinobridge-www.gtjaqh.cloud

# 文件前缀
VITE_FILE_PREFIX=http://sinobridge-dify.gtjaqh.cloud

# 是否开启实验功能
VITE_ENABLE_LAB_FEATURES=false

# 工具地址
VITE_TOOLBOX_URL=

# mqtt配置
VITE_MQTT_PROTOCOL=ws
VITE_MQTT_HOST=sinobridge-www.gtjaqh.cloud
VITE_MQTT_PORT=80
VITE_MQTT_USERNAME=your-mqtt-username
VITE_MQTT_PASSWORD=your-mqtt-password
VITE_MQTT_PATH=/mqttSocket/mqtt
