.sino-sidebar-container {
  direction: ltr;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 2147483647;
  user-select: none;
  -webkit-user-select: none;
}
.sino-sidebar-container.is-hidden {
  visibility: hidden;
  transition-property: visibility;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.sino-sidebar-container.sidebar-collapsed {
  margin-left: 0px;
}
.sino-bookmark {
  width: 28px;
  height: 24px;
  border-top: 1px solid #fff;
  border-left: 1px solid #fff;
  border-bottom: 1px solid #fff;
  border-radius: 4px 0px 0px 4px;
}
.sino-bookmark:hover {
  background-color: #e6f6ff;
  border-top: 1px solid #40a3ff;
  border-left: 1px solid #40a3ff;
  border-bottom: 1px solid #40a3ff;
}
