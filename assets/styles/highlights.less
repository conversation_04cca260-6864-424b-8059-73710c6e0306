.sino-customize-tooltip {
  position: absolute;
  z-index: 9999;
  max-height: 640px;
  // opacity: 0;
  display: none;
}
.sino-customize-tooltip.show.sino-transition {
  transition:
    left 1s ease,
    top 1s ease,
    opacity 1s ease; /* 指定特定属性的过渡 */
}
.sino-customize-tooltip.show {
  opacity: 1;
  display: block;
}
.sino-customize-tooltip.isThumbnail {
  opacity: 1;
  display: block;
  position: unset;
}
.sino-custom-tooltip.ant-tooltip.ant-tooltip-placement-left {
  max-width: 400px;
}
.sino-custom-tooltip.ant-tooltip.ant-tooltip-placement-left .ant-tooltip-inner {
  padding: 0;
}
