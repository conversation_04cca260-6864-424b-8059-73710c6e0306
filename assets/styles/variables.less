//@import "@/assets/styles/font.less";
/* 侧边栏整体最小宽度 */
@side-panel-min-width: 320px;
/* 侧边栏菜单宽度 */
@side-panel-menu-width: 60px;
/* 侧边栏内容区的最小宽度，由浏览器侧边栏的最小宽度和侧边栏菜单宽度共同决定 */
@side-panel-content-min-width: calc(@side-panel-min-width - @side-panel-menu-width);
/* 侧边栏内容路由区的内边距 */
@side-panel-route-padding: 16px;
/* 侧边栏路由区的最小宽度 留出滚动条的间距 */
@side-panel-route-min-width: calc(@side-panel-content-min-width - (@side-panel-route-padding * 2) - 5px);

// background: linear-gradient(to top, lighten(@primary-color, 30%), transparent);
/* 主色 */
@primary-color: #813ce0;
/* 禁用颜色 */
@disabled-color: #bbb;
/* 禁用颜色 */
@primary-btn-color: #fff;

/* 图标主色 */
@primary-icon-color: @primary-color;
/* 主题文字 */
@primary-text-color: @primary-color;
/* 基础字号 */
@side-panel-font-size: 14px;
/* 侧边栏背景色 */
// @side-panel-background-color: linear-gradient(to right bottom, lighten(@primary-color, 40%) 10%, #fff 50%);
@side-panel-background-color: linear-gradient(180deg, rgba(225, 189, 255, 0.25) 0%, rgba(224, 242, 255, 0) 100%);
/* 侧边栏菜单背景色 */
@side-panel-menu-background-color: #f5f5f5;
// =============================================标注===================================================== */
// 新版主需要的
/* 基础字体 */
@side-panel-font-family: AlibabaPuHuiTi_2_55_Regular;
/* 基础字体-加粗 */
@side-panel-font-family-bold: AlibabaPuHuiTi_2_85_Bold;

// 默认渐变
@gradientPrimary: linear-gradient(116deg, var(--ant-blue) 16%, var(--ant-geekblue) 88%);

@Shadow-1:
  0px 2px 4px -1px rgba(0, 0, 0, 0.12),
  0px 4px 5px 0px rgba(0, 0, 0, 0.08),
  0px 1px 10px 0px rgba(0, 0, 0, 0.05);
