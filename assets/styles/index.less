@import "reset";
@import "@/assets/styles/variables";
// * {
//   // margin: 0;
//   // padding: 0;
//   // box-sizing: border-box;
// }
/* 设置滚动条宽度、颜色和圆角 */
// ::webkit-scrollbar {
//   width: 6px;
// }

// /* 设置滚动条轨道颜色 */
// ::webkit-scrollbar-track {
//   background: #f1f1f1;
// }

// /* 设置滚动条滑块颜色和圆角 */
// ::webkit-scrollbar-thumb {
//   background: #888;
//   border-radius: 5px;
// }

// /* 设置滚动条滑块悬停状态下的颜色 */
// ::webkit-scrollbar-thumb:hover {
//   background: #555;
// }
