import request from "./";

const API_BASE_INS: string = import.meta.env["VITE_API_BASE_INS"] || "";

/** 获取任务列表 */
export function getTaskList(data): Promise<any> {
  return request({
    url: `${API_BASE_INS}/plugin/server/task/list`,
    method: "GET",
    params: data,
  });
}

//网址收集列表查询接口
export function getUrlList(data): Promise<any> {
  return request({
    url: `${API_BASE_INS}/server/task/url/list`,
    method: "GET",
    params: data,
  });
}

/** 获取任务详情 */
export function getTaskDetail(id: string): Promise<any> {
  return request({
    url: `${API_BASE_INS}/server/task/detail/${id}`,
    method: "GET",
  });
}

/** 新增任务 */
export function addTask(data: any): Promise<any> {
  return request({
    url: `${API_BASE_INS}/server/task/add`,
    method: "POST",
    data: data,
  });
}

/** 更新任务 */
export function updateTask(data: any): Promise<any> {
  return request({
    url: `${API_BASE_INS}/server/task/update`,
    method: "PUT",
    data: data,
  });
}

//删除任务
export function deleteTask(id: string): Promise<any> {
  return request({
    url: `${API_BASE_INS}/server/task/delete/${id}`,
    method: "DELETE",
  });
}

// 根据任务ID获取收集信息接口
export function getGatherInfo(id: string): Promise<any> {
  return request({
    url: `${API_BASE_INS}/server/task/gatherInfo/${id}`,
    method: "GET",
  });
}

//触发添加接口/server/task/url/add
export function addUrl(data: any): Promise<any> {
  return request({
    url: `${API_BASE_INS}/server/task/url/add`,
    method: "POST",
    data: data,
  });
}
