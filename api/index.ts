/** API请求根文件，初始化网络请求实例配置、拦截器，*/
import { getToken, getTenantId} from "@/utils/auth.ts";
import { blobToBase64 } from "@/utils/screenshot";
import { sendMessageToPopup } from "@/utils/messagebus.ts";

// 创建fetch配置
const fetchBeforeRequest = async (config) => {
  config.headers = config.headers || {};
  if (!config.status) {
    config.headers["Content-Type"] = "application/json";
  }
  const token = await getToken();
  const tenantId = await getTenantId()
  if (token) {
    // config.headers[import.meta.env["VITE_API_HEADER_KEY"]] = token;
    // 租户信息接口-需要Token
    config.headers["Token"] = `${token}`;
    config.headers["TenantId"] = tenantId;
  }
};

const fetchAfterReceive = () => {};

const requestInstance = async (config: {
  baseUrl?: string;
  type?: string;
  status?: string;
  params?: Object;
  method: string;
  headers?: any;
  header?: any;
  data?: any;
  url: string;
  responseType?: string;
}) => {
  if (!config.type || config.type === "fetch") {
    await fetchBeforeRequest(config);
    const queryArray: Array<string> = [];
    for (const queryKey in config.params) {
      queryArray.push(`${queryKey}=${config.params[queryKey]}`);
    }
    let url: string = (config.baseUrl ? config.baseUrl : import.meta.env["VITE_API_BASE"]) + config["url"];

    if (queryArray.length > 0) {
      url += `?${queryArray.join("&")}`;
    }
    return fetch(url, {
      method: config.method,
      headers: config.headers as HeadersInit,
      body: !config.status ? JSON.stringify(config.data) : config.data,
      // body: config.method.toUpperCase() === "POST" && !config.status ? JSON.stringify(config.data) : config.data,
    })
      .then(async (res) => {
        let data = null;
        const url = res.url;
        const substring = "pub/oss/download";

        if (url.includes(substring)) {
          const blob = await res.blob();
          let base64 = await blobToBase64(blob);
          return new Promise((resolve) => resolve(base64));
        } else {
          data = await res.json();
        }

        // if ((data.code + "").startsWith("2")) {
        //   return new Promise((resolve) => resolve(data));
        // } else {
        //   throw new Error("网络请求错误:" + data.msg);
        // }
        if (data && data?.code === 401) {
          return new Promise((resolve, reject) => reject(data));
        }
        return new Promise((resolve) => resolve(data));
      })
      .catch((err) => {
        if (err.code === 401) {
          sendMessageToPopup({ type: "mqttAndLogout", errMsg: err?.msg });
          return Promise.reject(err?.msg);
        } else {
          return new Promise((resolve) =>
            resolve({
              msg: "网络异常，请稍后再试",
            }),
          );
        }
        
      });
  }
  throw new Error("不支持的请求API类型，仅支持axios、fetch");
};

export default requestInstance;
