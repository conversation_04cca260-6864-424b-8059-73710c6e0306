import request from "./";
import { getToken } from "@/utils/auth.ts";

const API_BASE_AI: string = import.meta.env["VITE_API_BASE_AI"] || "";
const API_BASE_INS: string = import.meta.env["VITE_API_BASE_INS"] || "";

/** 消息反馈 */
export function messageFeedback(url: string, appKey: string, data) {
  return request({
    baseUrl: `${import.meta.env["VITE_AI_API_BASE"]}`,
    url: "/mark/info",
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${appKey}`,
    },
    data,
  });
}

// 上传文件
export async function uploadChatFile(data) {
  // const currentAIKey = await browser.storage.local.get("current_ai_appKey");
  return request({
    // baseUrl: `${import.meta.env["VITE_API_BASE_INS"]}`,
    url: `${API_BASE_INS}/dify/broker/formData`,
    method: "POST",
    status: "1",
    headers: {
      // Authorization: `Bearer ${currentAIKey.current_ai_appKey}`,
      // "Content-Type": "multipart/form-data",
    },
    data,
  });
}

/** 停止响应 */
export function stopMessage(data: any) {
  let path: string = `/chat-messages/${data.taskId}/stop`;
  if (data.type === "completion") {
    path = `/completion-messages/${data.taskId}/stop`;
  }
  return request({
    // baseUrl: `${import.meta.env["VITE_AI_API_BASE"]}`,
    url: `${API_BASE_INS}/dify/broker/json`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      // Authorization: `Bearer ${data.appKey}`,
    },
    data: {
      agentId: data.agentId,
      path: path,
      difyJson: {
        user: data.user,
      },
      method:"POST"
    },
  });
  // return request({
  //   baseURL: import.meta.env["VITE_AI_API_BASE"],
  //   url,
  //   headers: {
  //     "Content-Type": "application/json",
  //     Authorization: `Bearer ${data.appKey}`,
  //   },
  //   method: "POST",
  //   data: {
  //     user: data.user,
  //   },
  // });
}

/** 获取会话历史消息 */
export async function getConversationHistoryList(data) {
  return request({
    url: `${API_BASE_INS}/conversations`,
    method: "GET",
    params: data
  });
}

/** 删除会话 */
export function deleteConversationById(data) {
  const { appKey, id, user } = data || {};
  return request({
    baseUrl: `${import.meta.env["VITE_AI_API_BASE"]}`,
    url: "/conversations/" + id,
    method: "DELETE",
    headers: {
      Authorization: `Bearer ${appKey}`,
    },
    data: {
      // conversation_id: id,
      user,
    },
  });
}

/** 会话重命名 */
export function conversationRename(data) {
  const { appKey, id, name, user } = data;
  return request({
    baseUrl: `${import.meta.env["VITE_AI_API_BASE"]}`,
    url: "/conversations/" + id + "/name",
    method: "POST",
    headers: {
      Authorization: `Bearer ${appKey}`,
    },
    data: {
      name,
      user,
    },
  });
}

/** 获取会话 */
export function getConversation(data) {
  return request({
    url: `${API_BASE_INS}/conversations/${data.conversationId}/messages/cursor`,
    method: "GET",
    params: {
      messageId: data.messageId,
      size: data.size,
    },
  });
}

export function delBatch(data) {
  return request({
    url: `${API_BASE_AI}/conversations/delBatch`,
    method: "POST",
    data,
  });
}
