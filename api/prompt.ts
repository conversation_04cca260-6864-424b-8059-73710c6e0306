import request from "@/api";
import { AddPromptParams, PromptSearchParam } from "@/types/prompt";

const API_BASE_AI: string = import.meta.env["VITE_API_BASE_AI"] || "";

/** 分页查询提示词 */
export function pagePrompts(data: PageAPIRequest<PagePromptsRequest>): Promise<any> {
  return request({
    url: `${API_BASE_AI}/prompt/page`,
    method: "POST",
    data,
  });
}

/** 查询提示词列表 */
export function listPrompts(query: PromptSearchParam): Promise<any> {
  return request({
    url: `${API_BASE_AI}/prompt/user/list`,
    method: "POST",
    data: query,
  });
}

/** 新增个人提示词 */
export function addPrompt(query: AddPromptParams): Promise<any> {
  return request({
    url: `${API_BASE_AI}/prompt/add`,
    method: "POST",
    data: query,
  });
}

/** 修改个人提示词 */
export function editPrompt(query: AddPromptParams): Promise<any> {
  return request({
    url: `${API_BASE_AI}/prompt/edit`,
    method: "POST",
    data: query,
  });
}

/** 删除个人提示词 */
export function delPrompt(data): Promise<any> {
  return request({
    url: `${API_BASE_AI}/prompt/delBatch`,
    method: "POST",
    data: data,
  });
}

/** 修改提示词发布状态 */
export function updatePromptShareStatus(data): Promise<any> {
  return request({
    url: `${API_BASE_AI}/prompt/user/release`,
    method: "POST",
    data: data,
  });
}

/** 收藏提示词 */
export function updatePromptCollectionStatus(params): Promise<any> {
  return request({
    url: `${API_BASE_AI}/prompt/coll`,
    method: "GET",
    params,
  });
}

/** 取消收藏提示词 */
export function cancelPromptCollectionStatus(params): Promise<any> {
  return request({
    url: `${API_BASE_AI}/prompt/noColl`,
    method: "GET",
    params,
  });
}
