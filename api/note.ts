import request from "@/api";

const API_BASE_NOTE: string = import.meta.env["VITE_API_BASE_NOTE"] || "";

/** 分页查询便签 */
export function pageNote(data: PageAPIRequest<PageNotesRequest>): Promise<any> {
  return request({
    url: `${API_BASE_NOTE}/notes/page`,
    method: "POST",
    data,
  });
}

/** 查询便签列表 */
export function listNote(data: ListNotesRequest): Promise<any> {
  return request({
    url: `${API_BASE_NOTE}/notes/list`,
    method: "POST",
    data,
  });
}

/** 查询便签的详情 */
export function queryNote(params: Pick<CopilotNote, "id">): Promise<any> {
  return request({
    url: `${API_BASE_NOTE}/notes/fundById`,
    method: "GET",
    params,
  });
}

/** 新增一个便签 */
export function addNote(data: AddNoteRequest): Promise<any> {
  return request({
    url: `${API_BASE_NOTE}/notes/add`,
    method: "POST",
    data,
  });
}

/** 修改便签 */
export function editNote(data: EditNoteRequest): Promise<any> {
  return request({
    url: `${API_BASE_NOTE}/notes/edit`,
    method: "POST",
    data,
  });
}

/** 删除便签信息（支持批量） */
export function delNote(params: Pick<CopilotNote, "id">): Promise<any> {
  return request({
    url: `${API_BASE_NOTE}/notes/delete`,
    method: "GET",
    params,
  });
}

/** 添加便签与提示词的关联关系 */
export function addNotePromptRela(data: AddNotePromptRelaRequest): Promise<any> {
  return request({
    url: `${API_BASE_NOTE}/notes/sendByPrompt`,
    method: "POST",
    data,
  });
}

/** 查询便签 @ 指令后查询的可关联数据对象列表 */
export function listNoteRela(params: Pick<CopilotNote, "id">): Promise<any> {
  return request({
    url: `${API_BASE_NOTE}/noteObjRela/relaList`,
    method: "GET",
    params,
  });
}

/** 添加一个便签与对象的关联关系 */
export function addNoteObjRela(data: AddNoteObjRelaRequest): Promise<any> {
  return request({
    url: `${API_BASE_NOTE}/noteObjRela/add`,
    method: "POST",
    data,
  });
}

/** 删除便签与对象的关联关系 */
export function delNoteObjRela(params: DeleteNoteObjRelaRequest): Promise<any> {
  return request({
    url: `${API_BASE_NOTE}/noteObjRela/delete`,
    method: "GET",
    params,
  });
}

/** 分享便签 */
export function shareNote(data: ShareNoteRequest): Promise<any> {
  return request({
    url: `${API_BASE_NOTE}/noteShare/share`,
    method: "POST",
    data,
  });
}

/** 标记便签为已读 */
export function markNoteRead(params: Pick<CopilotNote, "id">): Promise<any> {
  return request({
    url: `${API_BASE_NOTE}/notes/noteRead`,
    method: "GET",
    params,
  });
}

/** 标记全部便签为已读 */
export function markNoteAllRead(): Promise<any> {
  return request({
    url: `${API_BASE_NOTE}/notes/allNotesRead`,
    method: "GET",
  });
}

// 获取便签组
export function notesGroupTree(data): Promise<any> {
  return request({
    url: `${API_BASE_NOTE}/notesGroup/tree`,
    method: "POST",
    data,
  });
}

// 新增便签组
export function addNotesGroup(data): Promise<any> {
  return request({
    url: `${API_BASE_NOTE}/notesGroup/add`,
    method: "POST",
    data,
  });
}

// 修改便签组
export function editNotesGroup(data): Promise<any> {
  return request({
    url: `${API_BASE_NOTE}/notesGroup/edit`,
    method: "POST",
    data,
  });
}

// 删除便签组
export function delNotesGroup(data): Promise<any> {
  return request({
    url: `${API_BASE_NOTE}/notesGroup/delete`,
    method: "POST",
    data,
  });
}

// 便签组详情
export function getGroupInfo(data): Promise<any> {
  return request({
    url: `${API_BASE_NOTE}/notesGroup/getById`,
    method: "POST",
    data,
  });
}

// 获取便签组内便签分页列表
export function notesGroupPage(data): Promise<any> {
  return request({
    url: `${API_BASE_NOTE}/notesGroup/pageNotes`,
    method: "POST",
    data,
  });
}

// 便签组平铺
export function flatList(data): Promise<any> {
  return request({
    url: `${API_BASE_NOTE}/notesGroup/flatList`,
    method: "POST",
    data,
  });
}

// 加入便签组
export function addrelation(data): Promise<any> {
  return request({
    url: `${API_BASE_NOTE}/notesGroup/relation/add`,
    method: "POST",
    data,
  });
}

// 移出便签组
export function noteRelationRemove(data): Promise<any> {
  return request({
    url: `${API_BASE_NOTE}/notesGroup/relation/remove`,
    method: "POST",
    data,
  });
}

// 移出便签组
export function notesGroupDissolve(data): Promise<any> {
  return request({
    url: `${API_BASE_NOTE}/notesGroup/dissolve`,
    method: "POST",
    data,
  });
}
