import request from "@/api";

const API_BASE_PUB: string = import.meta.env["VITE_API_BASE_PUB"] || "";

// 根据字典类型查询字典数据信息
export function getDicts(dictType: string): Promise<any> {
  return request({
    url: `${API_BASE_PUB}/pub/dict/getDictItems/` + dictType,
    method: "GET",
  });
}

// 动态获取写作、回复、通用助手的id
export function getConfig(params: any): Promise<any> {
  return request({
    url: `/frontEndConfig/getConfig`,
    method: "GET",
    params,
    baseUrl: import.meta.env["VITE_USERINFO_BASS"],
  });
}
