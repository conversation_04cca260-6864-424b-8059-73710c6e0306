import request from "@/api";

const API_BASE_PUB: string = import.meta.env["VITE_API_BASE_PUB"] || "";

/** 获取我的消息列表 未发送通知的 */
export function myNoticeList(query): Promise<any> {
  return request({
    url: `${API_BASE_PUB}/notice/myNoticeList`,
    method: "GET",
    params: query,
  });
}

/** 桌面端通知发送成功回调 */
export function noticeSuccessCallBack(data): Promise<any> {
  return request({
    url: `${API_BASE_PUB}/notice/noticeSuccessCallBack`,
    method: "POST",
    data,
  });
}

/** 消息分页 */
export function noticePage(data): Promise<any> {
  return request({
    url: `${API_BASE_PUB}/notice/page`,
    method: "POST",
    data,
  });
}

/** 消息查看 */
export function noticeRead(data): Promise<any> {
  return request({
    url: `${API_BASE_PUB}/notice/noticeRead`,
    method: "POST",
    data,
  });
}

/** 消息发送 */
export function noticeSend(data): Promise<any> {
  return request({
    url: `${API_BASE_PUB}/notice/noticeSend`,
    method: "POST",
    data,
  });
}

/** 消息批量删除 */
export function delBatch(data): Promise<any> {
  return request({
    url: `${API_BASE_PUB}/notice/delBatch`,
    method: "POST",
    data,
  });
}

/** 清空用户信息 */
export function noticeClear(data): Promise<any> {
  return request({
    url: `${API_BASE_PUB}/notice/clear`,
    method: "POST",
    data,
  });
}

// 全部已读
export function allRead(query): Promise<any> {
  return request({
    url: `${API_BASE_PUB}/notice/allRead`,
    method: "GET",
    params: query,
  });
}

// 获取未读的数量
export function getNoticeList(data): Promise<any> {
  return request({
    url: `${API_BASE_PUB}/feign/notice/getNoticeList `,
    method: "POST",
    data,
  });
}
// 获取场景协同消息
export function scenarioNotify(query: any): Promise<any> {
  return request({
    url: `${API_BASE_PUB}/pub/scenario/notify `,
    method: "GET",
    params: query,
  });
}
