import request from "@/api";

const API_BASE_INS: string = import.meta.env["VITE_API_BASE_INS"] || "";

/** 添加模板 */
export function addTemplate(data): Promise<any> {
  return request({
    url: `${API_BASE_INS}/server/acct/template/add`,
    method: "POST",
    data,
  });
}

/** 修改模板 */
export function updateTemplate(data): Promise<any> {
  return request({
    url: `${API_BASE_INS}/server/acct/template/update`,
    method: "PUT",
    data,
  });
}

/** 删除模板 */
export function delTemplate(data): Promise<any> {
  return request({
    url: `${API_BASE_INS}/server/acct/template/del`,
    method: "DELETE",
    data,
  });
}

/** 查询模板列表 */
export function templateList(data): Promise<any> {
  return request({
    url: `${API_BASE_INS}/server/acct/template/list`,
    method: "POST",
    data,
  });
}

/** 批量修改模板 */
export function batchUpdateTemplate(data): Promise<any> {
  return request({
    url: `${API_BASE_INS}/server/acct/template/batchUpdate`,
    method: "POST",
    data,
  });
}
