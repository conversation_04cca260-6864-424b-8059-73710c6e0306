import request from "@/api";

export function recommendQuestion(data) {
  return request({
    url: `/question/recommend`,
    method: "POST",
    data,
  });
}
export function getQuestionList(data) {
  return request({
    url: `/question/retrieval`,
    method: "POST",
    data,
  });
}
export function submittQuestion(data) {
  return request({
    url: `/question/submit`,
    method: "POST",
    data,
  });
}
