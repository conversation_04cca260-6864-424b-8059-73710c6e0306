import request from "@/api";

const API_BASE_INS: string = import.meta.env["VITE_API_BASE_INS"] || "";

/** 获取用户配置列表*/
export function wordList(params): Promise<any> {
  return request({
    url: `${API_BASE_INS}/acctconfig/word-menu/listByAcctId`,
    method: "GET",
    params,
  });
}

/** 新增划词菜单配置 */
export function addWord(data): Promise<any> {
  return request({
    url: `${API_BASE_INS}/acctconfig/word-menu/add`,
    method: "POST",
    data,
  });
}

/** 修改划词菜单配置 */
export function editWord(data): Promise<any> {
  return request({
    url: `${API_BASE_INS}/acctconfig/word-menu/update`,
    method: "PUT",
    data,
  });
}

/** 删除划词菜单配置 */
export function delWord(data): Promise<any> {
  return request({
    url: `${API_BASE_INS}/acctconfig/word-menu/delete`,
    method: "DELETE",
    data,
  });
}
