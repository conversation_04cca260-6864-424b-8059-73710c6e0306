/** 评论 API */
import request from "./";

const API_BASE_PUB: string = import.meta.env["VITE_API_BASE_PUB"] || "";

/** 分页查询评论 */
export function pageComments(query: PageCommentsRequest): Promise<any> {
  return request({
    url: `${API_BASE_PUB}/pub/comment/list`,
    method: "GET",
    params: query,
  });
}

/** 新增一条评论 */
export function addComment(query: AddCommentRequest): Promise<any> {
  return request({
    url: `${API_BASE_PUB}/pub/comment/add`,
    method: "POST",
    data: query,
  });
}

/** 删除评论 */
export function delComment(query: DelCommentRequest): Promise<any> {
  return request({
    url: `${API_BASE_PUB}/pub/comment/del`,
    method: "GET",
    params: query,
  });
}
