import react from "@vitejs/plugin-react";
import fs from "fs";
import path from "path";
import eslintPlugin from "vite-plugin-eslint";
import svgr from "vite-plugin-svgr";
import { defineConfig } from "wxt";
import manifest from "./manifest";

const themeFilePath = path.resolve(__dirname, "theme.json");
const themeJson = JSON.parse(fs.readFileSync(themeFilePath, "utf-8"));
// 读取 JSON 文件并生成 Less 变量
const generateLessVars = (json: object) => {
  const lessVars = {};
  Object.entries(json).forEach(([key, value]) => {
    if (typeof value === "object") {
      Object.entries(value).forEach(([subKey, subValue]) => {
        lessVars[`@${key}-${subKey}`] = subValue;
      });
    }
  });
  return lessVars;
};
const lessVars = generateLessVars(themeJson);

// See https://wxt.dev/api/config.html
export default defineConfig({
  vite: () => ({
    build: { sourcemap: false },
    plugins: [
      react(),
      eslintPlugin({
        include: [
          "component/**/*.tsx",
          "component/**/*.ts",
          "entrypoints/**/*.tsx",
          "entrypoints/**/*.ts",
          "hooks/*.ts",
        ],
      }),
      svgr({
        svgrOptions: {
          icon: true,
          exportType: "named",
        },
        include: "**/*.svg?react",
      }),
    ],
    css: {
      preprocessorOptions: {
        less: {
          modifyVars: lessVars,
          javascriptEnabled: true, // 允许在 Less 中使用 JavaScript 表达式
        },
      },
    },
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./"),
      },
    },
    server: {},
  }),
  hooks: {
    "build:before": (wxt) => {
      console.log("\n======== 构建开始 ========");
      console.log(`构建目标环境：\t${wxt.config.mode}`);
      console.log(`构建目标命令：\t${wxt.config.command}`);
      console.log(`构建目标浏览器：${wxt.config.browser}`);
      console.log(`构建目标MV版本：${wxt.config.manifestVersion}`);
      console.log("==========================\n");
    },
    "build:done": (wxt) => {
      console.log("\n======== 构建完成 ========");
    },
  },
  manifest: ({ browser, manifestVersion }) => {
    return manifest[browser][`MV${manifestVersion}`];
  },
});
