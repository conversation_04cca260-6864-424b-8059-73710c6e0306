# sino-copilot-crx

## 1.代码目录结构

```text
sino-copilot-crx
├─ .output/                 // 项目打包目录
│  ├─ chrome-mv3/           // 谷歌V3版本安装包
│  ├─ firefox-mv2/          // 火狐V2版本安装包
│  ├─ edge-mv3/             // edgeV3版本安装包
│  └─ ...
├─ .wxt/                    // 打包时生成的一些wxt框架内部配置与类型声明
├─ api/                     // 服务端接口目录
├─ assets/                  // 资源目录，会被vite处理
├─ components/              // 项目全局通用组件目录
├─ config/                  // 配置或静态数据目录
├─ entrypoints/             // 项目代码目录
│  ├─ background/           // 插件守护进程目录
│  ├─ content/              // 插件植入脚本目录
│  ├─ options/              // 插件选项目录
│  └─ sidepanel/            // 侧边栏代码目录
├─ hooks/                   // 公共hook目录
├─ manifest/                // 插件清单文件目录
├─ node_modules/            // 项目依赖目录
├─ public/                  // 公共资源目录，不会被vite处理
├─ types/                   // 公共的TS类型声明目录
├─ utils/                   // 公共工具方法目录
├─ .env.development         // 开发环境文件
├─ .env.production          // 生产环境文件
├─ .eslintignore            // eslint忽略文件
├─ .eslintrc                // eslint配置文件
├─ .gitignore               // git忽略文件
├─ .prettierrc              // prettier配置文件
├─ package-lock.json        // npm lock文件
├─ package.json             // 工程配置文件
├─ pnpm-lock.yaml           // pnpm lock文件
├─ README.md                // 项目说明文档
├─ theme.json               // 侧边栏主题配置文件
├─ themeOption.json         // 设置页主题配置文件
├─ tsconfig.json            // TypeScript配置文件
├─ wxt.config.ts            // 框架配置文件
└─ yarn.lock                // yarn lock文件
```

## 2.开发环境

### 进入项目根目录下，执行命令安装依赖

```shell
# npm
npm i
# pnpm
pnpm i
# yarn
yarn
```

### 启动开发环境

```shell
# npm
npm run dev
# pnpm
pnpm dev
# yarn
yarn dev
```

## 3.项目打包

### 基础打包命令

```shell
# npm
npm run build
# pnpm
pnpm build
# yarn
yarn build
```

### build可选参数：

1. `-m`或`--mode`：同vite，设置构建环境
   - `-m development`：开发环境；
   - `-m production`：生产环境（**默认**）。
2. `-b`或`--browser`：目标浏览器
   - `-b firefox`：火狐浏览器；
   - `-b chrome`：谷歌浏览器（**默认**）。
3. `--mv3`(**默认**)：以Manifest Version 3的形式构建
4. `--mv2`：以Manifest Version 2的形式构建（新版chromium即将不支持）
