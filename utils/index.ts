import { SIDE_PANEL_CONTAINER_WEB } from "@/entrypoints/sidepanel/components/layout/web-index.tsx";
import { SHADOW_SIDE_PANEL } from "@/entrypoints/content/sidepanel";

export const getContainer = () => {
  if (document.getElementById(SHADOW_SIDE_PANEL)) {
    const shadowDom = document.getElementById(SHADOW_SIDE_PANEL).shadowRoot;
    return shadowDom.querySelector("#" + SIDE_PANEL_CONTAINER_WEB) as HTMLDivElement;
  }
};
