import { extractFrameworkTableData } from "./frameworkTableExtractor";
import { detectFormElementType, isFormElement, getFormElementLabel } from "./frameworkFormDetector";

/**
 * 表单字段数据接口
 */
export interface FormField {
  fieldIndex: number;
  tagName: string;
  type: string;
  framework?: string;
  componentType?: string;
  name: string | null;
  id: string | null;
  placeholder: string | null;
  label: string | null;
  value: any;
  isDisabled?: boolean;
  isReadonly?: boolean;
  isRequired?: boolean;
}

/**
 * 表单信息接口
 */
export interface FormInfo {
  formIndex: number;
  formAction: string | null;
  formMethod: string | null;
  formType: string;
  fields: FormField[];
  tableName?: string;
  tableId?: string;
  tableClass?: string;
  tableMetadata?: any;
  tableData?: any;
}

/**
 * 数据提取配置接口
 */
export interface ExtractorConfig {
  extractionMethod: string;
  includeHiddenElements?: boolean;
  maxDepthForLabelSearch?: number;
}

/**
 * 统一的表单数据提取器类
 */
export class FormDataExtractor {
  private globalFieldIndex = 0;
  private tableProcessedElements = new Set<HTMLElement>();
  private config: ExtractorConfig;

  constructor(config: ExtractorConfig) {
    this.config = config;
  }

  /**
   * 提取页面中的所有表单数据
   */
  public extractFormData(): FormInfo[] {
    const allFormData: FormInfo[] = [];
    this.globalFieldIndex = 0;
    this.tableProcessedElements.clear();

    // 1. 检测UI框架并提取表单数据
    this.extractFrameworkForms(allFormData);

    // 2. 提取传统表单
    this.extractTraditionalForms(allFormData);

    // 3. 提取独立的表单元素
    this.extractStandaloneFormElements(allFormData);

    // 4. 提取iframe中的表单数据
    this.extractIframeForms(allFormData);

    // 5. 最后提取所有表格数据
    this.extractAllTableData(allFormData);

    return allFormData;
  }

  /**
   * 提取所有表格数据
   */
  private extractAllTableData(allFormData: FormInfo[]): void {
    console.log("🔍 开始提取页面中的所有表格数据...");

    const allTablesData = extractFrameworkTableData(document);

    if (allTablesData && allTablesData.length > 0) {
      console.log(`📊 发现 ${allTablesData.length} 个表格`);

      allTablesData.forEach((tableData) => {
        const allRows = tableData.rows;

        if (allRows.length > 0) {
          const tableFormInfo: FormInfo = {
            formIndex: allFormData.length + 1,
            formAction: null,
            formMethod: null,
            formType: "table",
            fields: [],
            tableMetadata: tableData.tableMetadata || {
              tableSelector: `#${tableData.tableId}` || `.${tableData.tableClass}` || "table",
              tableXPath: "",
              tableAttributes: {},
              structuralFingerprint: {
                headerCount: tableData.headers.length,
                rowCount: allRows.length,
                columnCount: tableData.headers.length,
                headerTexts: [...tableData.headers],
                hasFormElements: allRows.some((row) => row.some((cell) => cell.formElement)),
                formElementCount: allRows.flat().filter((cell) => cell.formElement).length,
              },
              frameworkInfo: {
                framework: undefined,
                componentType: undefined,
              },
              positionInfo: {
                boundingRect: { x: 0, y: 0, width: 0, height: 0 },
                isVisible: true,
                parentSelectors: [],
              },
              extractedAt: Date.now(),
              extractionMethod: this.config.extractionMethod,
            },
            tableName: tableData.tableName,
            tableId: tableData.tableId,
            tableClass: tableData.tableClass,
            tableData: {
              headers: tableData.headers,
              rows: allRows.map((row) => {
                const rowObj = {};
                row.forEach((cell) => {
                  // 将表格中的表单元素添加到已处理集合中
                  if (cell.formElement && cell.formElement instanceof HTMLElement) {
                    this.tableProcessedElements.add(cell.formElement);
                    console.log("表格中的表单元素已添加到已处理集合:", {
                      tagName: cell.formElement.tagName,
                      className: cell.formElement.className,
                      id: cell.formElement.id,
                      type: (cell.formElement as HTMLInputElement).type || "unknown",
                    });
                  }

                  // 同时将包含表单元素的单元格也添加到已处理集合中，防止框架组件被重复处理
                  if (cell.formElement && cell.formElement !== cell.formElement.closest("td, th")) {
                    const cellElement = cell.formElement.closest("td, th") as HTMLElement;
                    if (cellElement) {
                      const frameworkComponents = cellElement.querySelectorAll(
                        '[class*="el-"], [class*="ant-"], [class*="v-"], [class*="Mui"]',
                      );
                      frameworkComponents.forEach((component) => {
                        if (component instanceof HTMLElement) {
                          this.tableProcessedElements.add(component);
                          console.log("表格单元格中的框架组件已添加到已处理集合:", {
                            tagName: component.tagName,
                            className: component.className,
                            id: component.id,
                          });
                        }
                      });
                    }
                  }

                  const columnKey = cell.columnHeader || `column_${cell.columnIndex}`;
                  rowObj[columnKey] = {
                    value: cell.value || null,
                    rowIndex: cell.rowIndex,
                    columnIndex: cell.columnIndex,
                    columnHeader: cell.columnHeader,
                    isInTableHeader: cell.isInTableHeader,
                    hasFormElement: !!cell.formElement,
                    formElementType: cell.formElement?.tagName?.toLowerCase() || null,
                  };
                });
                return rowObj;
              }),
              totalRows: allRows.length,
              totalColumns: tableData.headers.length,
            },
          };

          console.log(`✅ 表格 "${tableData.tableName}" 数据提取完成，共 ${allRows.length} 行数据`);
          allFormData.push(tableFormInfo);
        }
      });
    } else {
      console.log("📋 页面中未发现可提取的表格数据");
    }
  }

  /**
   * 检查元素是否可见
   */
  private isElementVisible(element: HTMLElement): boolean {
    if (!element.isConnected) {
      return false;
    }

    const style = window.getComputedStyle(element);
    if (style.display === "none" || style.visibility === "hidden" || style.opacity === "0") {
      return false;
    }

    const rect = element.getBoundingClientRect();
    if (rect.width === 0 && rect.height === 0) {
      return false;
    }

    let parent = element.parentElement;
    while (parent && parent !== document.body) {
      const parentStyle = window.getComputedStyle(parent);
      if (parentStyle.display === "none" || parentStyle.visibility === "hidden") {
        return false;
      }
      parent = parent.parentElement;
    }

    return true;
  }

  /**
   * 将框架组件内部的所有原生表单元素标记为已处理
   */
  private markInternalElementsAsProcessed(frameworkElement: HTMLElement): void {
    const internalElements = frameworkElement.querySelectorAll("input, textarea, select");
    internalElements.forEach((element) => {
      this.tableProcessedElements.add(element as HTMLElement);
      console.log("标记内部原生元素为已处理:", {
        tagName: element.tagName,
        type: (element as HTMLInputElement).type,
        className: element.className,
        frameworkParent: frameworkElement.className,
      });
    });
  }

  /**
   * 从框架组件中提取实际的表单元素
   */
  private extractActualFormElement(
    element: HTMLElement,
  ): HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement | null {
    console.log("extractActualFormElement 输入元素:", {
      tagName: element.tagName,
      className: element.className,
      id: element.id,
      type: (element as HTMLInputElement).type,
    });

    const tagName = element.tagName.toLowerCase();
    if (["input", "textarea", "select"].includes(tagName)) {
      console.log("返回原生表单元素:", element);
      return element as HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement;
    }

    const actualElement = element.querySelector("input, textarea, select") as
      | HTMLInputElement
      | HTMLSelectElement
      | HTMLTextAreaElement;
    if (actualElement) {
      this.markInternalElementsAsProcessed(element);
      console.log("返回框架组件本身，避免重复收集内部元素:", {
        frameworkElement: element.tagName + "." + element.className,
        internalElement: actualElement.tagName + "." + actualElement.className,
      });
      return element as any;
    }

    console.log("未找到内部表单元素");
    return null;
  }

  /**
   * 从UI框架组件中获取值
   */
  private getFrameworkElementValue(
    frameworkElement: HTMLElement,
    fallbackElement: HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement,
  ): any {
    const className = frameworkElement.className || "";
    const classList = className.split(" ");

    // Element UI 值获取
    if (classList.some((cls) => cls.startsWith("el-"))) {
      if (classList.includes("el-select")) {
        const selectedText = frameworkElement.querySelector(".el-select__tags-text, .el-input__inner");
        if (selectedText && selectedText.textContent?.trim()) {
          return selectedText.textContent.trim();
        }
      }

      if (classList.includes("el-checkbox") || classList.includes("el-radio")) {
        const isChecked = frameworkElement.classList.contains("is-checked");
        const input = frameworkElement.querySelector("input");
        return isChecked ? input?.value || "checked" : null;
      }

      if (classList.includes("el-switch")) {
        const isChecked = frameworkElement.classList.contains("is-checked");
        return isChecked ? "true" : "false";
      }

      if (classList.includes("el-date-picker") || classList.includes("el-time-picker")) {
        const input = frameworkElement.querySelector(".el-input__inner") as HTMLInputElement;
        return input?.value || null;
      }
    }

    // Ant Design 值获取
    if (classList.some((cls) => cls.startsWith("ant-"))) {
      if (classList.includes("ant-select")) {
        const selectedText = frameworkElement.querySelector(".ant-select-selection-item");
        if (selectedText && selectedText.textContent?.trim()) {
          return selectedText.textContent.trim();
        }
      }

      if (classList.includes("ant-checkbox") || classList.includes("ant-radio")) {
        const isChecked =
          frameworkElement.classList.contains("ant-checkbox-checked") ||
          frameworkElement.classList.contains("ant-radio-checked");
        const input = frameworkElement.querySelector("input");
        return isChecked ? input?.value || "checked" : null;
      }

      if (classList.includes("ant-switch")) {
        const isChecked = frameworkElement.classList.contains("ant-switch-checked");
        return isChecked ? "true" : "false";
      }

      if (classList.includes("ant-picker")) {
        const input = frameworkElement.querySelector(".ant-picker-input input") as HTMLInputElement;
        return input?.value || null;
      }
    }

    // Vuetify 值获取
    if (classList.some((cls) => cls.startsWith("v-"))) {
      if (classList.includes("v-select")) {
        const selectedText = frameworkElement.querySelector(".v-select__selection-text");
        if (selectedText && selectedText.textContent?.trim()) {
          return selectedText.textContent.trim();
        }
      }

      if (classList.includes("v-checkbox") || classList.includes("v-radio")) {
        const input = frameworkElement.querySelector("input") as HTMLInputElement;
        return input?.checked ? input.value || "checked" : null;
      }

      if (classList.includes("v-switch")) {
        const input = frameworkElement.querySelector("input") as HTMLInputElement;
        return input?.checked ? "true" : "false";
      }
    }

    // Material-UI 值获取
    if (classList.some((cls) => cls.startsWith("Mui"))) {
      if (classList.includes("MuiSelect-root")) {
        const selectedText = frameworkElement.querySelector(".MuiSelect-select");
        if (selectedText && selectedText.textContent?.trim()) {
          return selectedText.textContent.trim();
        }
      }

      if (classList.includes("MuiCheckbox-root") || classList.includes("MuiRadio-root")) {
        const input = frameworkElement.querySelector("input") as HTMLInputElement;
        return input?.checked ? input.value || "checked" : null;
      }

      if (classList.includes("MuiSwitch-root")) {
        const input = frameworkElement.querySelector("input") as HTMLInputElement;
        return input?.checked ? "true" : "false";
      }
    }

    // 通用回退：尝试从内部input获取值
    const innerInput = frameworkElement.querySelector("input, textarea, select");
    if (innerInput) {
      if (innerInput.tagName.toLowerCase() === "input") {
        const inputElement = innerInput as HTMLInputElement;
        if (inputElement.type === "checkbox" || inputElement.type === "radio") {
          return inputElement.checked ? inputElement.value || "checked" : null;
        }
        return inputElement.value?.trim() || null;
      } else if (innerInput.tagName.toLowerCase() === "select") {
        const selectElement = innerInput as HTMLSelectElement;
        const selectedOption = selectElement.selectedOptions[0];
        return selectedOption ? selectedOption.value || selectedOption.textContent?.trim() : null;
      } else if (innerInput.tagName.toLowerCase() === "textarea") {
        return (innerInput as HTMLTextAreaElement).value?.trim() || null;
      }
    }

    // 最终回退到传入的fallback元素
    if (fallbackElement.type === "checkbox" || fallbackElement.type === "radio") {
      return (fallbackElement as HTMLInputElement).checked ? fallbackElement.value || "checked" : null;
    } else if (fallbackElement.tagName.toLowerCase() === "select") {
      const selectElement = fallbackElement as HTMLSelectElement;
      const selectedOption = selectElement.selectedOptions[0];
      return selectedOption ? selectedOption.value || selectedOption.textContent?.trim() : null;
    } else {
      return fallbackElement.value?.trim() || null;
    }
  }

  /**
   * 处理表单元素的通用函数
   */
  private processFormElement(
    element: HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement,
    formInfo: FormInfo,
    context: string,
    originalFrameworkElement?: HTMLElement,
  ): void {
    // 过滤掉一些明显不是表单的输入框
    const excludeSelectors = [
      '[role="search"]',
      ".search-input",
      ".toolbar-input",
      ".filter-input",
      '[data-testid*="search"]',
      '[placeholder*="搜索"]',
      '[placeholder*="search"]',
      '[class*="search"]',
      '[id*="search"]',
    ];

    // 检查是否匹配排除条件
    for (const selector of excludeSelectors) {
      if (element.matches(selector)) {
        return;
      }
    }

    // 检查父元素是否包含搜索相关的类名或属性
    let parent = element.parentElement;
    let level = 0;
    while (parent && level < 3) {
      if (
        parent.className &&
        (parent.className.includes("search") ||
          parent.className.includes("toolbar") ||
          parent.className.includes("filter"))
      ) {
        return;
      }
      parent = parent.parentElement;
      level++;
    }

    // 计算元素在DOM中的真实索引位置 - 使用与填充时相同的选择器
    const allInputs = document.querySelectorAll(
      "input, textarea, select, .el-input, .el-select, .el-textarea, .el-checkbox, .el-radio, .el-switch, .el-date-picker, .el-time-picker, .el-cascader, .el-slider, .el-rate, .el-upload, .ant-input, .ant-select, .ant-checkbox, .ant-radio, .ant-switch, .ant-date-picker, .ant-time-picker, .ant-cascader, .ant-slider, .ant-rate, .ant-upload, .ant-input-number, .v-text-field, .v-select, .v-checkbox, .v-radio, .v-switch, .v-date-picker, .v-time-picker, .v-slider, .v-rating, .MuiTextField-root, .MuiSelect-root, .MuiCheckbox-root, .MuiRadio-root, .MuiSwitch-root, .MuiSlider-root"
    );
    let domIndex = -1;
    for (let i = 0; i < allInputs.length; i++) {
      if (allInputs[i] === element || (originalFrameworkElement && allInputs[i] === originalFrameworkElement)) {
        domIndex = i + 1; // 使用1基索引
        break;
      }
    }

    // 如果没找到，使用全局索引作为后备
    if (domIndex === -1) {
      this.globalFieldIndex++;
      domIndex = this.globalFieldIndex;
    }

    console.log(`${context} element`, element, `DOM索引: ${domIndex}`);

    // 使用框架检测工具获取准确的表单元素类型信息
    let formElementInfo;
    if (originalFrameworkElement) {
      formElementInfo = detectFormElementType(originalFrameworkElement);
      console.log(`使用原始框架元素检测:`, {
        frameworkElement: originalFrameworkElement.tagName + "." + originalFrameworkElement.className,
        actualElement: element.tagName + (element.className ? "." + element.className : ""),
        detectedInfo: formElementInfo,
      });
    } else {
      formElementInfo = detectFormElementType(element);
      console.log(`使用原生元素检测:`, {
        element: element.tagName + (element.className ? "." + element.className : ""),
        detectedInfo: formElementInfo,
      });
    }

    const fieldData: FormField = {
      fieldIndex: domIndex,
      tagName: element.tagName.toLowerCase(),
      type: formElementInfo.type,
      framework: formElementInfo.framework,
      componentType: formElementInfo.componentType,
      name: element.name || null,
      id: element.id || null,
      placeholder:
        formElementInfo.placeholder || (element as HTMLInputElement | HTMLTextAreaElement).placeholder || null,
      label: null,
      value: null,
      isDisabled: formElementInfo.isDisabled,
      isReadonly: formElementInfo.isReadonly,
      isRequired: formElementInfo.isRequired,
    };

    // 检测是否在表格中 - 表格中的表单元素完全由表格处理逻辑负责
    const tableCell = element.closest("td, th");
    if (tableCell) {
      let tableContainer = tableCell.closest(
        ".el-table, .ant-table-wrapper, .ant-table, .v-data-table, .v-table, .MuiTable-root, .MuiTableContainer-root",
      ) as HTMLElement;

      if (!tableContainer) {
        tableContainer = tableCell.closest("table") as HTMLElement;
      }

      if (tableContainer) {
        console.log(
          `⏭️ 跳过表格中的表单元素: ${element.tagName} (${element.type || "unknown"}) - 表格中的表单元素由表格处理逻辑专门负责`,
        );
        return;
      }
    }

    // 获取字段标签 - 使用框架检测工具
    let labelText = getFormElementLabel(element);

    // 如果框架检测工具没有找到标签，使用formElementInfo中的label信息
    if (!labelText && formElementInfo.label) {
      labelText = formElementInfo.label;
    }

    // 查找附近的文本内容
    if (!labelText) {
      let prevSibling = element.previousElementSibling;
      while (prevSibling && !labelText) {
        if (prevSibling.textContent?.trim()) {
          const text = prevSibling.textContent.trim();
          if (text.length > 0 && !/^[\d\s*\-_]+$/.test(text)) {
            labelText = text;
            break;
          }
        }
        prevSibling = prevSibling.previousElementSibling;
      }
    }

    // 查找包含红色星号(*)的标签文本
    if (!labelText) {
      const container = element.closest("div, td, th, li, span") || element.parentElement;
      if (container) {
        const walker = document.createTreeWalker(container, NodeFilter.SHOW_TEXT);

        let textNode;
        while ((textNode = walker.nextNode())) {
          const text = textNode.textContent?.trim();
          if (text && text.includes("*") && text.length > 1) {
            const cleanText = text.replace(/\*/g, "").replace(/[：:]{1,2}/g, "").trim();
            if (cleanText && cleanText.length > 0 && !/^[\d\s\-_]+$/.test(cleanText)) {
              labelText = cleanText;
              break;
            }
          }
        }
      }
    }

    // 查找附近包含冒号的中文标签
    if (!labelText) {
      let currentElement = element.parentElement;
      let level = 0;
      while (currentElement && level < (this.config.maxDepthForLabelSearch || 3) && !labelText) {
        const allText = currentElement.textContent || "";
        const labelMatches = allText.match(/([\u4e00-\u9fa5]+[\u4e00-\u9fa5\s]*?)\s*\*?\s*[：:]{1,2}/g);
        if (labelMatches && labelMatches.length > 0) {
          const match = labelMatches[labelMatches.length - 1];
          const cleanLabel = match.replace(/\s*\*?\s*[：:]{1,2}/g, "").trim();
          if (cleanLabel && cleanLabel.length > 0) {
            labelText = cleanLabel;
            break;
          }
        }
        currentElement = currentElement.parentElement;
        level++;
      }
    }

    // 查找父元素中的文本节点
    if (!labelText && element.parentElement) {
      const parentText = element.parentElement.textContent?.trim();
      if (parentText) {
        const elementValue = element.value || (element as HTMLInputElement | HTMLTextAreaElement).placeholder || "";
        const cleanText = parentText.replace(elementValue, "").trim();
        if (cleanText && cleanText !== parentText && !/^[\d\s*\-_]+$/.test(cleanText)) {
          labelText = cleanText;
        }
      }
    }

    // 查找表格单元格中的标签（td/th）- 作为备选方案
    if (!labelText) {
      const cell = element.closest("td, th");
      if (cell) {
        const prevCell = cell.previousElementSibling;
        if (prevCell && prevCell.textContent?.trim()) {
          const text = prevCell.textContent.trim();
          if (!/^[\d\s*\-_]+$/.test(text)) {
            labelText = text;
          }
        }
      }
    }

    // 使用placeholder作为备选
    if (!labelText && (element as HTMLInputElement | HTMLTextAreaElement).placeholder) {
      labelText = (element as HTMLInputElement | HTMLTextAreaElement).placeholder;
    }

    fieldData.label = labelText;

    // 获取字段值 - 优先从原始框架组件获取值
    if (originalFrameworkElement) {
      fieldData.value = this.getFrameworkElementValue(originalFrameworkElement, element);
      console.log("从框架组件获取的值:", {
        frameworkElement: originalFrameworkElement.className,
        extractedValue: fieldData.value,
        fallbackValue: element.value,
      });
    } else if (formElementInfo.value !== undefined && formElementInfo.value !== null && formElementInfo.value !== "") {
      fieldData.value = formElementInfo.value;
    } else {
      // 后备方案：使用原有的值获取逻辑
      if (element.type === "checkbox" || element.type === "radio") {
        fieldData.value = (element as HTMLInputElement).checked ? element.value || "checked" : null;
      } else if (element.tagName.toLowerCase() === "select") {
        const selectElement = element as HTMLSelectElement;
        const selectedOption = selectElement.selectedOptions[0];
        fieldData.value = selectedOption ? selectedOption.value || selectedOption.textContent?.trim() : null;
      } else {
        fieldData.value = element.value?.trim() || null;
      }
    }

    // 空值和无效字段赋值为null
    if (!fieldData.value || fieldData.value === "") {
      fieldData.value = null;
    }

    formInfo.fields.push(fieldData);
  }

  /**
   * 提取框架表单
   */
  private extractFrameworkForms(allFormData: FormInfo[]): void {
    // 检测页面中是否存在UI框架
    const hasElementUI =
      document.querySelector(
        ".el-form, .el-input, .el-select, .el-textarea, .el-checkbox, .el-radio, .el-switch, .el-date-picker, .el-time-picker, .el-cascader, .el-slider, .el-rate, .el-upload, .el-input-number, .el-autocomplete, .el-color-picker",
      ) !== null;
    const hasAntDesign =
      document.querySelector(
        ".ant-form, .ant-input, .ant-select, .ant-checkbox, .ant-radio, .ant-switch, .ant-date-picker, .ant-time-picker, .ant-cascader, .ant-slider, .ant-rate, .ant-upload, .ant-input-number, .ant-auto-complete, .ant-mentions, .ant-tree-select",
      ) !== null;
    const hasVuetify =
      document.querySelector(
        ".v-form, .v-text-field, .v-select, .v-checkbox, .v-radio, .v-switch, .v-date-picker, .v-time-picker, .v-slider, .v-rating, .v-textarea, .v-autocomplete, .v-combobox, .v-file-input",
      ) !== null;
    const hasMaterialUI =
      document.querySelector(
        ".MuiFormControl-root, .MuiTextField-root, .MuiSelect-root, .MuiCheckbox-root, .MuiRadio-root, .MuiSwitch-root, .MuiSlider-root, .MuiAutocomplete-root, .MuiRating-root",
      ) !== null;

    const hasUIFramework = hasElementUI || hasAntDesign || hasVuetify || hasMaterialUI;
    console.log("🔍 UI框架检测结果:", {
      hasElementUI,
      hasAntDesign,
      hasVuetify,
      hasMaterialUI,
      hasUIFramework,
      strategy: hasUIFramework ? "框架表单提取" : "传统表单提取",
    });

    if (!hasUIFramework) {
      console.log("❌ 未检测到UI框架，跳过框架表单处理");
      return;
    }

    console.log("✅ 使用UI框架表单提取策略");

    // 定义所有UI框架的表单组件选择器
    const frameworkFormSelectors = [".el-form", ".ant-form", ".v-form", ".MuiFormControl-root"];

    // 查找框架表单容器
    const frameworkForms = new Set<HTMLElement>();
    frameworkFormSelectors.forEach((selector) => {
      const elements = document.querySelectorAll(selector);
      elements.forEach((el) => frameworkForms.add(el as HTMLElement));
    });

    console.log("🔍 框架表单容器检测:", {
      foundContainers: frameworkForms.size,
      strategy: frameworkForms.size === 0 ? "直接提取框架组件" : "处理框架表单容器",
    });

    // 如果没有找到框架表单容器，则查找所有框架表单组件
    if (frameworkForms.size === 0) {
      console.log("📦 未找到框架表单容器，直接提取框架组件");
      this.extractFrameworkComponents(allFormData);
    } else {
      // 处理找到的框架表单容器
      console.log("🏗️ 找到框架表单容器:", frameworkForms.size);
      Array.from(frameworkForms).forEach((formContainer, formIndex) => {
        // 完全排除表格中的框架表单容器
        const isInTable = !!formContainer.closest(
          "td, th, .el-table, .ant-table, .v-data-table, .MuiTable-root, table",
        );

        if (isInTable) {
          console.log("⚠️ 跳过表格中的框架表单容器:", {
            tagName: formContainer.tagName,
            className: formContainer.className,
            id: formContainer.id,
            reason: "表格中的表单容器由表格处理逻辑专门负责",
          });
          return;
        }

        const formInfo: FormInfo = {
          formIndex: formIndex + 1,
          formAction: null,
          formMethod: null,
          formType: "framework",
          fields: [],
        };

        // 在框架表单容器内查找所有框架组件
        const componentSelectors = [
          // Element UI 组件
          ".el-input, .el-select, .el-textarea, .el-checkbox, .el-radio, .el-switch, .el-date-picker, .el-time-picker, .el-cascader, .el-slider, .el-rate, .el-upload, .el-input-number, .el-autocomplete, .el-color-picker",
          // Ant Design 组件
          ".ant-input, .ant-select, .ant-checkbox, .ant-radio, .ant-switch, .ant-date-picker, .ant-time-picker, .ant-cascader, .ant-slider, .ant-rate, .ant-upload, .ant-input-number, .ant-auto-complete, .ant-mentions, .ant-tree-select",
          // Vuetify 组件
          ".v-text-field, .v-select, .v-checkbox, .v-radio, .v-switch, .v-date-picker, .v-time-picker, .v-slider, .v-rating, .v-textarea, .v-autocomplete, .v-combobox, .v-file-input",
          // Material-UI 组件
          ".MuiTextField-root, .MuiSelect-root, .MuiCheckbox-root, .MuiRadio-root, .MuiSwitch-root, .MuiSlider-root, .MuiAutocomplete-root, .MuiRating-root",
        ];

        const formComponents = new Set<HTMLElement>();
        componentSelectors.forEach((selector) => {
          const components = formContainer.querySelectorAll(selector);
          components.forEach((comp) => {
            const element = comp as HTMLElement;
            if (isFormElement(element)) {
              // 检查是否已经被处理过
              if (this.tableProcessedElements.has(element)) {
                console.log("⚠️ 跳过已处理的框架组件:", {
                  tagName: element.tagName,
                  className: element.className,
                  id: element.id,
                  reason: "已在其他逻辑中处理",
                });
                return;
              }

              // 确保只选择最外层的框架组件，排除嵌套在其他框架组件内部的元素
              const parentFrameworkElement = element.parentElement?.closest(
                ".el-input, .el-select, .el-textarea, .el-checkbox, .el-radio, .el-switch, .el-date-picker, .el-time-picker, .el-cascader, .el-slider, .el-rate, .el-upload, .el-input-number, .el-autocomplete, .el-color-picker, .ant-input, .ant-select, .ant-checkbox, .ant-radio, .ant-switch, .ant-date-picker, .ant-time-picker, .ant-cascader, .ant-slider, .ant-rate, .ant-upload, .ant-input-number, .ant-auto-complete, .ant-mentions, .ant-tree-select, .v-text-field, .v-select, .v-checkbox, .v-radio, .v-switch, .v-date-picker, .v-time-picker, .v-slider, .v-rating, .v-textarea, .v-autocomplete, .v-combobox, .v-file-input, .MuiTextField-root, .MuiSelect-root, .MuiCheckbox-root, .MuiRadio-root, .MuiSwitch-root, .MuiSlider-root, .MuiAutocomplete-root, .MuiRating-root",
              );
              const isNestedComponent = parentFrameworkElement !== null;

              // 检查是否有子元素已经在formComponents中
              let hasChildInSet = false;
              for (const existingElement of formComponents) {
                if (element.contains(existingElement)) {
                  hasChildInSet = true;
                  break;
                }
              }

              // 检查是否有父元素已经在formComponents中
              let hasParentInSet = false;
              for (const existingElement of formComponents) {
                if (existingElement.contains(element)) {
                  hasParentInSet = true;
                  break;
                }
              }

              if (!isNestedComponent && !hasChildInSet && !hasParentInSet) {
                formComponents.add(element);
                console.log("✅ 添加框架组件:", {
                  tagName: element.tagName,
                  className: element.className,
                  id: element.id,
                });
              } else {
                console.log("⚠️ 跳过嵌套或重复的框架组件:", {
                  tagName: element.tagName,
                  className: element.className,
                  id: element.id,
                  reason: isNestedComponent ? "嵌套组件" : hasChildInSet ? "子元素已存在" : "父元素已存在",
                });
              }
            }
          });
        });

        Array.from(formComponents).forEach((element) => {
          // 再次确认不处理表格中的组件
          const elementIsInTable = !!element.closest(
            "td, th, .el-table, .ant-table, .v-data-table, .MuiTable-root, table",
          );

          if (elementIsInTable) {
            console.log("⚠️ 跳过框架表单容器中的表格组件:", {
              tagName: element.tagName,
              className: element.className,
              id: element.id,
              reason: "表格中的组件由表格处理逻辑专门负责",
            });
            return;
          }

          if (!this.tableProcessedElements.has(element) && this.isElementVisible(element)) {
            const actualElement = this.extractActualFormElement(element);
            if (actualElement) {
              this.processFormElement(actualElement, formInfo, "Framework form", element);
              // 将原始框架元素添加到已处理集合中，避免重复处理
              this.tableProcessedElements.add(element);
              console.log("✅ 在非表格表单容器中添加框架组件:", {
                tagName: element.tagName,
                className: element.className,
                id: element.id,
              });
            }
          } else if (this.tableProcessedElements.has(element)) {
            console.log("⚠️ 跳过已处理的框架元素（表单容器内）:", {
              tagName: element.tagName,
              className: element.className,
              id: element.id,
            });
          }
        });

        if (formInfo.fields.length > 0) {
          allFormData.push(formInfo);
        }
      });
    }
  }

  /**
   * 提取框架组件
   */
  private extractFrameworkComponents(allFormData: FormInfo[]): void {
    const allFrameworkComponents = new Set<HTMLElement>();

    const componentSelectors = [
      // Element UI
      ".el-input, .el-select, .el-textarea, .el-checkbox, .el-radio, .el-switch, .el-date-picker, .el-time-picker, .el-cascader, .el-slider, .el-rate, .el-upload",
      // Ant Design
      ".ant-input, .ant-select, .ant-checkbox, .ant-radio, .ant-switch, .ant-date-picker, .ant-time-picker, .ant-cascader, .ant-slider, .ant-rate, .ant-upload, .ant-input-number",
      // Vuetify
      ".v-text-field, .v-select, .v-checkbox, .v-radio, .v-switch, .v-date-picker, .v-time-picker, .v-slider, .v-rating",
      // Material-UI
      ".MuiTextField-root, .MuiSelect-root, .MuiCheckbox-root, .MuiRadio-root, .MuiSwitch-root, .MuiSlider-root",
    ];

    componentSelectors.forEach((selector) => {
      const components = document.querySelectorAll(selector);
      components.forEach((comp) => {
        const element = comp as HTMLElement;
        if (isFormElement(element)) {
          if (this.tableProcessedElements.has(element)) {
            console.log("跳过已处理的全局框架组件:", {
              tagName: element.tagName,
              className: element.className,
              id: element.id,
              reason: "已在其他逻辑中处理",
            });
            return;
          }

          // 确保只选择最外层的框架组件，排除嵌套组件
          const parentFrameworkElement = element.parentElement?.closest(
            ".el-input, .el-select, .el-textarea, .el-checkbox, .el-radio, .el-switch, .el-date-picker, .el-time-picker, .el-cascader, .el-slider, .el-rate, .el-upload, .el-input-number, .el-autocomplete, .el-color-picker, .ant-input, .ant-select, .ant-checkbox, .ant-radio, .ant-switch, .ant-date-picker, .ant-time-picker, .ant-cascader, .ant-slider, .ant-rate, .ant-upload, .ant-input-number, .ant-auto-complete, .ant-mentions, .ant-tree-select, .v-text-field, .v-select, .v-checkbox, .v-radio, .v-switch, .v-date-picker, .v-time-picker, .v-slider, .v-rating, .v-textarea, .v-autocomplete, .v-combobox, .v-file-input, .MuiTextField-root, .MuiSelect-root, .MuiCheckbox-root, .MuiRadio-root, .MuiSwitch-root, .MuiSlider-root, .MuiAutocomplete-root, .MuiRating-root",
          );
          const isNestedComponent = parentFrameworkElement !== null;

          let hasChildInSet = false;
          for (const existingElement of allFrameworkComponents) {
            if (element.contains(existingElement)) {
              hasChildInSet = true;
              break;
            }
          }

          let hasParentInSet = false;
          for (const existingElement of allFrameworkComponents) {
            if (existingElement.contains(element)) {
              hasParentInSet = true;
              break;
            }
          }

          if (!isNestedComponent && !hasChildInSet && !hasParentInSet) {
            allFrameworkComponents.add(element);
            console.log("添加全局框架组件:", {
              tagName: element.tagName,
              className: element.className,
              id: element.id,
            });
          } else {
            console.log("跳过嵌套或重复的全局框架组件:", {
              tagName: element.tagName,
              className: element.className,
              id: element.id,
              reason: isNestedComponent ? "嵌套组件" : hasChildInSet ? "子元素已存在" : "父元素已存在",
            });
          }
        }
      });
    });

    // 处理收集到的框架组件
    if (allFrameworkComponents.size > 0) {
      const standaloneFrameworkFormInfo: FormInfo = {
        formIndex: allFormData.length + 1,
        formAction: null,
        formMethod: null,
        formType: "standalone_framework",
        fields: [],
      };

      allFrameworkComponents.forEach((frameworkElement) => {
        if (this.isElementVisible(frameworkElement)) {
          const actualElement = this.extractActualFormElement(frameworkElement);
          if (actualElement) {
            this.processFormElement(
              actualElement,
              standaloneFrameworkFormInfo,
              "Standalone framework component",
              frameworkElement,
            );
          }
        }
      });

      if (standaloneFrameworkFormInfo.fields.length > 0) {
        allFormData.push(standaloneFrameworkFormInfo);
      }
    }
  }

  /**
   * 提取传统表单
   */
  private extractTraditionalForms(allFormData: FormInfo[]): void {
    const forms = document.querySelectorAll("form");
    console.log("📋 传统表单检测:", forms.length, "个表单");

    forms.forEach((form, formIndex) => {
      // 完全排除表格中的传统表单
      const isInTable = !!form.closest("td, th, .el-table, .ant-table, .v-data-table, .MuiTable-root, table");

      if (isInTable) {
        console.log("⚠️ 跳过表格中的传统表单:", {
          tagName: form.tagName,
          className: form.className,
          id: form.id,
          action: form.action,
          reason: "表格中的表单由表格处理逻辑专门负责",
        });
        return;
      }

      const formInfo: FormInfo = {
        formIndex: formIndex + 1,
        formAction: form.action || null,
        formMethod: form.method || null,
        formType: "traditional",
        fields: [],
      };

      // 获取所有表单字段（只处理可见的元素）
      const formElements = form.querySelectorAll("input, textarea, select") as NodeListOf<
        HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
      >;

      // 过滤掉不需要的 input 类型
      const filteredElements = Array.from(formElements).filter((element) => {
        if (element.tagName.toLowerCase() === "input") {
          const inputType = (element as HTMLInputElement).type.toLowerCase();
          // 排除按钮类型和提交类型
          return !["button", "submit", "reset", "image"].includes(inputType);
        }
        return true; // textarea 和 select 都保留
      });
      console.log(`📝 传统表单 ${formIndex + 1} 找到的元素数量:`, filteredElements.length, filteredElements);

      filteredElements.forEach((element) => {
        // 再次确认不处理表格中的元素
        const elementIsInTable = !!element.closest(
          "td, th, .el-table, .ant-table, .v-data-table, .MuiTable-root, table",
        );

        if (elementIsInTable) {
          console.log("⚠️ 跳过传统表单中的表格元素:", {
            tagName: element.tagName,
            type: (element as HTMLInputElement).type,
            className: element.className,
            id: element.id,
            reason: "表格中的元素由表格处理逻辑专门负责",
          });
          return;
        }

        // 跳过已在表格中处理的元素，并且只处理可见的元素
        if (!this.tableProcessedElements.has(element) && this.isElementVisible(element)) {
          console.log("✅ 处理非表格传统表单元素:", {
            tagName: element.tagName,
            type: (element as HTMLInputElement).type,
            className: element.className,
            id: element.id,
          });
          this.processFormElement(element, formInfo, "Traditional form");
        } else if (this.tableProcessedElements.has(element)) {
          console.log("⚠️ 跳过已处理的传统表单元素:", {
            tagName: element.tagName,
            className: element.className,
            reason: "已在框架组件或表格中处理",
          });
        }
      });

      if (formInfo.fields.length > 0) {
        allFormData.push(formInfo);
      }
    });
  }

  /**
   * 提取独立的表单元素
   */
  private extractStandaloneFormElements(allFormData: FormInfo[]): void {
    const allStandaloneInputs = document.querySelectorAll(
      "input:not(form input), textarea:not(form textarea), select:not(form select)",
    ) as NodeListOf<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>;

    // 过滤出可见且未在表格中处理的元素，并排除按钮类型
    const standaloneInputs = Array.from(allStandaloneInputs).filter((element) => {
      // 完全排除表格中的独立输入元素
      const isInTable = !!element.closest("td, th, .el-table, .ant-table, .v-data-table, .MuiTable-root, table");

      if (isInTable) {
        console.log("⚠️ 跳过表格中的独立输入元素:", {
          tagName: element.tagName,
          type: (element as HTMLInputElement).type,
          className: element.className,
          id: element.id,
          reason: "表格中的元素由表格处理逻辑专门负责",
        });
        return false;
      }

      if (!this.tableProcessedElements.has(element) && this.isElementVisible(element)) {
        if (element.tagName.toLowerCase() === "input") {
          const inputType = (element as HTMLInputElement).type.toLowerCase();
          // 排除按钮类型和提交类型
          return !["button", "submit", "reset", "image"].includes(inputType);
        }
        return true; // textarea 和 select 都保留
      }
      return false;
    });

    console.log("🔍 独立表单元素检测:", standaloneInputs.length, "个元素");

    if (standaloneInputs.length > 0) {
      const standaloneFormInfo: FormInfo = {
        formIndex: allFormData.length + 1,
        formAction: null,
        formMethod: null,
        formType: "standalone",
        fields: [],
      };

      standaloneInputs.forEach((element) => {
        console.log("✅ 处理独立表单元素:", {
          tagName: element.tagName,
          type: (element as HTMLInputElement).type,
          className: element.className,
          id: element.id,
        });
        this.processFormElement(element, standaloneFormInfo, "Standalone input");
      });

      if (standaloneFormInfo.fields.length > 0) {
        allFormData.push(standaloneFormInfo);
      }
    }
  }

  /**
   * 提取iframe中的表单数据
   */
  private extractIframeForms(allFormData: FormInfo[]): void {
    // 检测页面中是否存在UI框架
    const hasElementUI =
      document.querySelector(
        ".el-form, .el-input, .el-select, .el-textarea, .el-checkbox, .el-radio, .el-switch, .el-date-picker, .el-time-picker, .el-cascader, .el-slider, .el-rate, .el-upload, .el-input-number, .el-autocomplete, .el-color-picker",
      ) !== null;
    const hasAntDesign =
      document.querySelector(
        ".ant-form, .ant-input, .ant-select, .ant-checkbox, .ant-radio, .ant-switch, .ant-date-picker, .ant-time-picker, .ant-cascader, .ant-slider, .ant-rate, .ant-upload, .ant-input-number, .ant-auto-complete, .ant-mentions, .ant-tree-select",
      ) !== null;
    const hasVuetify =
      document.querySelector(
        ".v-form, .v-text-field, .v-select, .v-checkbox, .v-radio, .v-switch, .v-date-picker, .v-time-picker, .v-slider, .v-rating, .v-textarea, .v-autocomplete, .v-combobox, .v-file-input",
      ) !== null;
    const hasMaterialUI =
      document.querySelector(
        ".MuiFormControl-root, .MuiTextField-root, .MuiSelect-root, .MuiCheckbox-root, .MuiRadio-root, .MuiSwitch-root, .MuiSlider-root, .MuiAutocomplete-root, .MuiRating-root",
      ) !== null;

    const hasUIFramework = hasElementUI || hasAntDesign || hasVuetify || hasMaterialUI;

    // 只有在没有检测到UI框架时才处理iframe中的原生表单元素
    if (!hasUIFramework) {
      console.log("🔍 开始检查iframe中的表单元素");

      try {
        const iframes = document.querySelectorAll("iframe");
        console.log(`发现 ${iframes.length} 个iframe`);

        iframes.forEach((iframe, iframeIndex) => {
          try {
            const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
            if (iframeDoc) {
              console.log(`✅ 成功访问iframe ${iframeIndex + 1}`);

              // 处理iframe中的传统表单
              const iframeForms = iframeDoc.querySelectorAll("form");
              iframeForms.forEach((form, formIndex) => {
                const iframeFormInfo: FormInfo = {
                  formIndex: allFormData.length + 1,
                  formAction: form.action || null,
                  formMethod: form.method || "get",
                  formType: "iframe-form",
                  fields: [],
                };

                const formInputs = form.querySelectorAll("input, textarea, select") as NodeListOf<
                  HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
                >;

                formInputs.forEach((element) => {
                  if (this.isElementVisible(element)) {
                    // 排除按钮类型
                    if (element.tagName.toLowerCase() === "input") {
                      const inputType = (element as HTMLInputElement).type.toLowerCase();
                      if (["button", "submit", "reset", "image"].includes(inputType)) {
                        return;
                      }
                    }

                    this.processFormElement(element, iframeFormInfo, `Iframe ${iframeIndex + 1} form ${formIndex + 1}`);
                  }
                });

                if (iframeFormInfo.fields.length > 0) {
                  allFormData.push(iframeFormInfo);
                }
              });

              // 处理iframe中的独立表单元素
              const allIframeStandaloneInputs = iframeDoc.querySelectorAll(
                'input[type="text"]:not(form input), input[type="email"]:not(form input), input[type="password"]:not(form input), input[type="tel"]:not(form input), input[type="url"]:not(form input), input[type="search"]:not(form input), input[type="number"]:not(form input), input[type="date"]:not(form input), input[type="datetime-local"]:not(form input), input[type="time"]:not(form input), input[type="month"]:not(form input), input[type="week"]:not(form input), input[type="color"]:not(form input), input[type="range"]:not(form input), input[type="file"]:not(form input), input[type="hidden"]:not(form input), input[type="radio"]:not(form input), input[type="checkbox"]:not(form input), textarea:not(form textarea), select:not(form select)',
              ) as NodeListOf<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>;

              // 过滤出可见且未在表格中处理的元素
              const iframeStandaloneInputs = Array.from(allIframeStandaloneInputs).filter(
                (element) => !this.tableProcessedElements.has(element) && this.isElementVisible(element),
              );

              if (iframeStandaloneInputs.length > 0) {
                const iframeStandaloneFormInfo: FormInfo = {
                  formIndex: allFormData.length + 1,
                  formAction: null,
                  formMethod: null,
                  formType: "iframe-standalone",
                  fields: [],
                };

                iframeStandaloneInputs.forEach((element) => {
                  this.processFormElement(
                    element,
                    iframeStandaloneFormInfo,
                    `Iframe ${iframeIndex + 1} standalone input`,
                  );
                });

                if (iframeStandaloneFormInfo.fields.length > 0) {
                  allFormData.push(iframeStandaloneFormInfo);
                }
              }
            }
          } catch (e) {
            // 跨域iframe无法访问，忽略错误
            console.debug(`无法访问iframe ${iframeIndex + 1}内容（可能是跨域）:`, e);
          }
        });
      } catch (e) {
        console.debug("检查iframe时出错:", e);
      }
    } else {
      console.log("❌ 检测到UI框架，跳过iframe中的原生表单处理");
    }
  }
}

/**
 * 创建表单数据提取器实例
 */
export function createFormDataExtractor(config: ExtractorConfig): FormDataExtractor {
  return new FormDataExtractor(config);
}

/**
 * 快速提取表单数据的便捷函数
 */
export function extractFormData(extractionMethod: string = "default"): FormInfo[] {
  const extractor = createFormDataExtractor({ extractionMethod });
  return extractor.extractFormData();
}
