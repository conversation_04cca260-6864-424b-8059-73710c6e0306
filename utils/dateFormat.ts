// 时间格式
// 当日日期 显示格式为HH:mm:（24小时制）
// 昨日日期 显示格式为昨天
// 前天日期 显示格式为前天
// 本年过去的时间 显示格式为x月（个位数的月、日省略前面的0）
// 本年往前的时间 显示格式为xxxx年，4位年号需要给满
export const formatDate = (dateString: string) => {
  // 日期格式YYYY-MM-DD HH:mm:ss
  const inputDate = new Date(dateString);
  const today = new Date();
  // 昨天
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);
  // 前天
  const frontday = new Date(today);
  frontday.setDate(today.getDate() - 2);

  // 仅比较日期，不考虑时间
  const isToday = inputDate.toDateString() === today.toDateString();
  const isYesterday = inputDate.toDateString() === yesterday.toDateString();
  const isFrontday = inputDate.toDateString() === frontday.toDateString();
  if (isToday) {
    // 当日
    const hours = String(inputDate.getHours()).padStart(2, "0");
    const minutes = String(inputDate.getMinutes()).padStart(2, "0");
    return `${hours}:${minutes}`;
  } else if (isYesterday) {
    // 昨日
    // const hours = String(inputDate.getHours()).padStart(2, "0");
    // const minutes = String(inputDate.getMinutes()).padStart(2, "0");
    // return `昨天 ${hours}:${minutes}`;
    return "昨天";
  } else if (isFrontday) {
    // 前天
    // const hours = String(inputDate.getHours()).padStart(2, "0");
    // const minutes = String(inputDate.getMinutes()).padStart(2, "0");
    // return `昨天 ${hours}:${minutes}`;
    return "前天";
  } else if (inputDate.getFullYear() === today.getFullYear()) {
    // 今年
    const month = inputDate.getMonth() + 1; // 月份从0开始
    // const day = inputDate.getDate();
    // return `${month}月${day}日`;
    return `${month}月`;
  } else {
    // 往年
    const year = inputDate.getFullYear();
    return `${year}年`;
  }
};

// 时间戳转为日期格式 日期格式YYYY-MM-DD HH:mm:ss
export const getDateInfo = (timestamp: number) => {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // 月份从0开始，需加1
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");

  const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  return formattedDate;
};

// 日期格式化
export const formatDateS = (time, fmt = "yyyy-MM-dd hh:mm:ss") => {
  if (!time) return "";
  let date = new Date(time);
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
  }
  let o = {
    "M+": date.getMonth() + 1,
    "d+": date.getDate(),
    "h+": date.getHours(),
    "m+": date.getMinutes(),
    "s+": date.getSeconds(),
  };
  for (let k in o) {
    if (new RegExp(`(${k})`).test(fmt)) {
      let str = o[k] + "";
      fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? str : ("00" + str).substr(str.length));
    }
  }
  return fmt;
};
