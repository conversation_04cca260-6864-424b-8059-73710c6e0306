/**
 * 通用UI框架表格数据提取工具
 * 支持Element UI、Ant Design、Vuetify、Material-UI等主流框架
 * 适应表头和表体分离的复杂DOM结构
 */

interface TableCellInfo {
  tableName: string;
  tableId: string | null;
  tableClass: string | null;
  rowIndex: number;
  columnIndex: number;
  columnHeader: string | null;
  value: string | null;
  isInTableHeader: boolean;
  // 表单元素相关信息（如果单元格包含表单元素）
  formElement?: HTMLElement | null;
  // 新增：详细的DOM定位信息，用于精确的逆向定位
  cellMetadata?: {
    // 单元格DOM定位信息
    cellSelector: string; // CSS选择器路径
    cellXPath: string; // XPath路径
    cellAttributes: Record<string, string>; // 单元格的所有属性
    cellPosition: { x: number; y: number; width: number; height: number }; // 位置信息

    // 表单元素DOM定位信息（如果存在）
    formElementSelector?: string; // 表单元素的CSS选择器
    formElementXPath?: string; // 表单元素的XPath
    formElementAttributes?: Record<string, string>; // 表单元素的属性
    formElementType?: string; // 表单元素类型（input/select/textarea等）
    formElementName?: string; // 表单元素的name属性
    formElementId?: string; // 表单元素的id属性

    // 表格结构定位信息
    tableSelector: string; // 表格容器的CSS选择器
    tableXPath: string; // 表格容器的XPath
    rowSelector: string; // 行的CSS选择器
    rowXPath: string; // 行的XPath

    // UI框架特定信息
    framework?: string; // 检测到的UI框架（elementUI/antDesign等）
    componentType?: string; // 组件类型（如el-input、ant-select等）

    // 提取时的上下文信息
    extractionTimestamp: number; // 提取时间戳
    extractionMethod: string; // 提取方法（用于调试）
    isVisible: boolean; // 元素是否可见
    hasFormElements: boolean; // 是否包含表单元素
  };
}

interface TableExtractionResult {
  tableName: string;
  tableId: string | null;
  tableClass: string | null;
  headers: string[];
  rows: TableCellInfo[][];
  allCells: TableCellInfo[];
  // 新增：表格级别的元数据，用于流式匹配
  tableMetadata?: {
    // DOM定位信息
    tableSelector: string; // 表格容器的CSS选择器
    tableXPath: string; // 表格容器的XPath
    tableAttributes: Record<string, string>; // 表格容器的所有属性

    // 结构特征
    structuralFingerprint: {
      headerCount: number; // 表头数量
      rowCount: number; // 数据行数量
      columnCount: number; // 列数量
      headerTexts: string[]; // 表头文本数组（用于结构匹配）
      hasFormElements: boolean; // 是否包含表单元素
      formElementCount: number; // 表单元素数量
    };

    // 框架特定信息
    frameworkInfo: {
      framework?: string; // 检测到的UI框架（elementUI/antDesign等）
      componentType?: string; // 表格组件类型（如el-table、ant-table等）
      version?: string; // 框架版本（如果可检测）
    };

    // 位置和可见性信息
    positionInfo: {
      boundingRect: { x: number; y: number; width: number; height: number };
      isVisible: boolean; // 表格是否可见
      parentSelectors: string[]; // 父容器选择器链（用于上下文匹配）
      zIndex?: number; // z-index值
    };

    // 提取上下文信息
    extractedAt: number; // 提取时间戳
    extractionMethod: string; // 提取方法标识
    pageUrl?: string; // 页面URL（用于跨页面匹配验证）
  };
}

/**
 * 生成元素的CSS选择器路径
 */
function generateCSSSelector(element: HTMLElement): string {
  if (!element || element === document.body) return "body";

  const path: string[] = [];
  let current = element;

  while (current && current !== document.body) {
    let selector = current.tagName.toLowerCase();

    // 添加ID（如果存在且唯一）
    if (current.id) {
      const idSelector = `#${current.id}`;
      if (document.querySelectorAll(idSelector).length === 1) {
        path.unshift(idSelector);
        break;
      }
    }

    // 添加类名（选择最具特征性的）
    if (current.className) {
      const classes = current.className.split(" ").filter((cls) => cls.trim());
      if (classes.length > 0) {
        // 优先选择UI框架相关的类名
        const frameworkClasses = classes.filter(
          (cls) => cls.includes("el-") || cls.includes("ant-") || cls.includes("v-") || cls.includes("mui-"),
        );
        const targetClasses = frameworkClasses.length > 0 ? frameworkClasses : classes;
        selector += "." + targetClasses.slice(0, 2).join(".");
      }
    }

    // 添加nth-child（如果需要）
    const siblings = Array.from(current.parentElement?.children || []);
    const sameTagSiblings = siblings.filter((sibling) => sibling.tagName === current.tagName);
    if (sameTagSiblings.length > 1) {
      const index = sameTagSiblings.indexOf(current) + 1;
      selector += `:nth-child(${index})`;
    }

    path.unshift(selector);
    current = current.parentElement as HTMLElement;
  }

  return path.join(" > ");
}

/**
 * 生成元素的XPath路径
 */
function generateXPath(element: HTMLElement): string {
  if (!element || element === document.body) return "/html/body";

  const path: string[] = [];
  let current = element;

  while (current && current !== document.body) {
    let index = 1;
    let sibling = current.previousElementSibling;

    while (sibling) {
      if (sibling.tagName === current.tagName) {
        index++;
      }
      sibling = sibling.previousElementSibling;
    }

    const tagName = current.tagName.toLowerCase();
    path.unshift(`${tagName}[${index}]`);
    current = current.parentElement as HTMLElement;
  }

  return "/html/body/" + path.join("/");
}

/**
 * 获取元素的所有属性
 */
function getElementAttributes(element: HTMLElement): Record<string, string> {
  const attributes: Record<string, string> = {};

  for (let i = 0; i < element.attributes.length; i++) {
    const attr = element.attributes[i];
    attributes[attr.name] = attr.value;
  }

  return attributes;
}

/**
 * 检测UI框架类型
 */
function detectUIFramework(element: HTMLElement): { framework?: string; componentType?: string } {
  const className = element.className || "";
  const parentClassName = element.parentElement?.className || "";
  const containerClassName =
    element.closest('[class*="el-"], [class*="ant-"], [class*="v-"], [class*="mui-"]')?.className || "";

  // Element UI
  if (className.includes("el-") || parentClassName.includes("el-") || containerClassName.includes("el-")) {
    const componentMatch = (className + " " + parentClassName + " " + containerClassName).match(/el-([a-z-]+)/);
    return {
      framework: "elementUI",
      componentType: componentMatch ? componentMatch[0] : "el-unknown",
    };
  }

  // Ant Design
  if (className.includes("ant-") || parentClassName.includes("ant-") || containerClassName.includes("ant-")) {
    const componentMatch = (className + " " + parentClassName + " " + containerClassName).match(/ant-([a-z-]+)/);
    return {
      framework: "antDesign",
      componentType: componentMatch ? componentMatch[0] : "ant-unknown",
    };
  }

  // Vuetify
  if (className.includes("v-") || parentClassName.includes("v-") || containerClassName.includes("v-")) {
    const componentMatch = (className + " " + parentClassName + " " + containerClassName).match(/v-([a-z-]+)/);
    return {
      framework: "vuetify",
      componentType: componentMatch ? componentMatch[0] : "v-unknown",
    };
  }

  // Material-UI
  if (className.includes("Mui") || parentClassName.includes("Mui") || containerClassName.includes("Mui")) {
    const componentMatch = (className + " " + parentClassName + " " + containerClassName).match(/Mui([A-Z][a-z]+)/);
    return {
      framework: "materialUI",
      componentType: componentMatch ? componentMatch[0] : "Mui-unknown",
    };
  }

  return {};
}

/**
 * 从页面中提取由UI框架生成的复杂表格的数据
 * @param tableContainer 表格的最外层容器元素。如果未提供，则会尝试在整个文档中查找
 * @returns 包含所有表格信息的对象数组
 */
export function extractFrameworkTableData(tableContainer: HTMLElement | Document = document): TableExtractionResult[] {
  const extractedTables: TableExtractionResult[] = [];

  // 1. 定义不同框架的表格容器选择器 - 按框架分类
  const tableContainerSelectors = {
    // Element UI - 分离表格结构
    elementUI: {
      container: ".el-table",
      headerWrapper: ".el-table__header-wrapper",
      bodyWrapper: ".el-table__body-wrapper",
      headerTable: ".el-table__header-wrapper table",
      bodyTable: ".el-table__body-wrapper table",
      fixedLeft: ".el-table__fixed",
      fixedRight: ".el-table__fixed-right",
    },

    // Ant Design - 统一表格结构
    antDesign: {
      container: ".ant-table-wrapper, .ant-table",
      table: ".ant-table table, table.ant-table",
      header: ".ant-table-thead",
      body: ".ant-table-tbody",
      scrollBody: ".ant-table-body",
    },

    // Vuetify - 统一表格结构
    vuetify: {
      container: ".v-data-table, .v-table",
      table: ".v-data-table table, .v-table table",
      header: "thead",
      body: "tbody",
    },

    // Material-UI - 可能分离或统一
    materialUI: {
      container: ".MuiTable-root, .MuiTableContainer-root",
      table: "table.MuiTable-root, .MuiTableContainer-root table",
      header: ".MuiTableHead-root, thead",
      body: ".MuiTableBody-root, tbody",
    },

    // 原生HTML - 统一表格结构
    native: {
      container: "table",
      table: "table",
      header: "thead",
      body: "tbody",
    },
  };

  // 2. 定义不同框架的表头选择器（更精确的选择器）
  const headerSelectors = [
    ".el-table .el-table__header-wrapper thead", // Element UI - 完整路径
    ".el-table .el-table__header-wrapper table thead", // Element UI - 备选
    ".el-table thead", // Element UI - 简化版
    ".ant-table-wrapper .ant-table-thead", // Ant Design - 完整路径
    ".ant-table .ant-table-thead", // Ant Design - 直接路径
    ".ant-table-thead", // Ant Design - 简化版
    ".v-data-table thead", // Vuetify
    ".MuiTableHead-root", // Material-UI
    "thead", // 原生表格
  ];

  // 3. 定义不同框架的表体选择器（更精确的选择器）
  const bodySelectors = [
    ".el-table .el-table__body-wrapper tbody", // Element UI - 完整路径
    ".el-table .el-table__body-wrapper table tbody", // Element UI - 备选
    ".el-table tbody", // Element UI - 简化版
    ".ant-table-wrapper .ant-table-tbody", // Ant Design - 完整路径
    ".ant-table .ant-table-tbody", // Ant Design - 直接路径
    ".ant-table-tbody", // Ant Design - 简化版
    ".v-data-table tbody", // Vuetify
    ".MuiTableBody-root", // Material-UI
    "tbody", // 原生表格
  ];

  /**
   * 表格信息接口
   */
  interface DetectedTableInfo {
    framework: string;
    container: HTMLElement;
    headerElement?: HTMLElement | null;
    bodyElement?: HTMLElement | null;
    headerTable?: HTMLElement | null;
    bodyTable?: HTMLElement | null;
    isSeparated: boolean;
  }

  /**
   * 检测并提取所有表格
   */
  const detectAndExtractTables = (rootElement: HTMLElement | Document, selectors: any): DetectedTableInfo[] => {
    const detectedTables: DetectedTableInfo[] = [];

    // 检测Element UI表格（分离结构）
    const elementUIContainers = findAllElements(rootElement, [selectors.elementUI.container]);
    for (const container of elementUIContainers) {
      const headerTable = findElement(container, [selectors.elementUI.headerTable]);
      const bodyTable = findElement(container, [selectors.elementUI.bodyTable]);

      if (headerTable && bodyTable) {
        detectedTables.push({
          framework: "elementUI",
          container,
          headerTable,
          bodyTable,
          isSeparated: true,
        });
      }
    }

    // 检测Ant Design表格（统一结构）
    const antdContainers = findAllElements(rootElement, [selectors.antDesign.container]);
    for (const container of antdContainers) {
      const table = findElement(container, [selectors.antDesign.table]);
      if (table) {
        const header = findElement(table, [selectors.antDesign.header]);
        const body = findElement(table, [selectors.antDesign.body]);

        detectedTables.push({
          framework: "antDesign",
          container,
          headerElement: header,
          bodyElement: body,
          isSeparated: false,
        });
      }
    }

    // 检测Vuetify表格
    const vuetifyContainers = findAllElements(rootElement, [selectors.vuetify.container]);
    for (const container of vuetifyContainers) {
      const table = findElement(container, [selectors.vuetify.table]);
      if (table) {
        const header = findElement(table, [selectors.vuetify.header]);
        const body = findElement(table, [selectors.vuetify.body]);

        detectedTables.push({
          framework: "vuetify",
          container,
          headerElement: header,
          bodyElement: body,
          isSeparated: false,
        });
      }
    }

    // 检测Material-UI表格
    const muiContainers = findAllElements(rootElement, [selectors.materialUI.container]);
    for (const container of muiContainers) {
      const table = findElement(container, [selectors.materialUI.table]);
      if (table) {
        const header = findElement(table, [selectors.materialUI.header]);
        const body = findElement(table, [selectors.materialUI.body]);

        detectedTables.push({
          framework: "materialUI",
          container,
          headerElement: header,
          bodyElement: body,
          isSeparated: false,
        });
      }
    }

    // 检测原生HTML表格
    let nativeTables = findAllElements(rootElement, [selectors.native.container]);

    // 如果传入的就是一个table元素，直接处理它
    if (rootElement instanceof HTMLElement && rootElement.tagName.toLowerCase() === "table") {
      nativeTables = [rootElement];
    }

    for (const table of nativeTables) {
      // 跳过已被框架处理的表格
      if (table.closest(".el-table, .ant-table, .v-data-table, .MuiTable-root")) {
        continue;
      }

      const header = findElement(table, [selectors.native.header]);
      const body = findElement(table, [selectors.native.body]);

      detectedTables.push({
        framework: "native",
        container: table,
        headerElement: header,
        bodyElement: body,
        isSeparated: false,
      });
    }

    return detectedTables;
  };

  /**
   * 获取表格的唯一标识符
   */
  const getTableIdentifiers = (container: HTMLElement, index: number) => {
    // 1. 尝试获取tableId - 多种策略
    let tableId: string | null = null;

    // 策略1: 直接从容器获取id
    if (container.id && container.id.trim()) {
      tableId = container.id.trim();
    }

    // 策略2: 从内部table元素获取id
    if (!tableId) {
      const innerTable = container.querySelector("table");
      if (innerTable && innerTable.id && innerTable.id.trim()) {
        tableId = innerTable.id.trim();
      }
    }

    // 策略3: 从data属性获取标识
    if (!tableId) {
      const dataId =
        container.getAttribute("data-table-id") ||
        container.getAttribute("data-id") ||
        container.getAttribute("data-key") ||
        container.getAttribute("data-table-key");
      if (dataId && dataId.trim()) {
        tableId = dataId.trim();
      }
    }

    // 策略4: 从aria属性获取标识
    if (!tableId) {
      const ariaLabel = container.getAttribute("aria-label") || container.getAttribute("aria-labelledby");
      if (ariaLabel && ariaLabel.trim()) {
        // 将aria标签转换为合适的id格式
        tableId = ariaLabel.trim().replace(/\s+/g, "-").toLowerCase();
      }
    }

    // 策略5: 从父容器获取标识
    if (!tableId) {
      let parent = container.parentElement;
      let level = 0;
      while (parent && level < 3) {
        if (parent.id && parent.id.trim()) {
          tableId = `${parent.id.trim()}-table`;
          break;
        }
        const parentDataId = parent.getAttribute("data-id") || parent.getAttribute("data-key");
        if (parentDataId && parentDataId.trim()) {
          tableId = `${parentDataId.trim()}-table`;
          break;
        }
        parent = parent.parentElement;
        level++;
      }
    }

    // 策略6: 基于位置和特征生成唯一标识
    if (!tableId) {
      const rect = container.getBoundingClientRect();
      const positionHash = Math.abs(Math.round(rect.top + rect.left)).toString(36);
      const classHash = container.className
        ? container.className
            .split(" ")
            .filter(
              (cls) =>
                cls &&
                !cls.includes("table") &&
                !cls.includes("ant-") &&
                !cls.includes("el-") &&
                !cls.includes("v-") &&
                !cls.includes("Mui"),
            )
            .join("-")
        : "";

      tableId = classHash ? `table-${classHash}-${positionHash}` : `table-${index + 1}-${positionHash}`;
    }

    // 2. 获取tableClass - 过滤和优化
    let tableClass: string | null = null;
    if (container.className && container.className.trim()) {
      // 过滤掉框架相关的通用类名，保留有意义的类名
      const meaningfulClasses = container.className.split(" ").filter((cls) => {
        if (!cls || cls.trim() === "") return false;

        // 排除框架通用类名
        const excludePatterns = [
          /^(el-table|ant-table|v-data-table|v-table|MuiTable|MuiTableContainer)$/,
          /^(table|wrapper|container|root)$/i,
          /^(css-|makeStyles-)/,
          /^\d+$/, // 纯数字类名
        ];

        return !excludePatterns.some((pattern) => pattern.test(cls.trim()));
      });

      if (meaningfulClasses.length > 0) {
        tableClass = meaningfulClasses.join(" ");
      } else {
        // 如果没有有意义的类名，保留原始className作为备选
        tableClass = container.className.trim();
      }
    }

    return { tableId, tableClass };
  };

  /**
   * 提取表格数据
   */
  const extractTableData = (tableInfo: DetectedTableInfo, index: number): TableExtractionResult | null => {
    const tableName = getTableName(tableInfo.container, index);
    const { tableId, tableClass } = getTableIdentifiers(tableInfo.container, index);

    let headers: string[] = [];
    let rows: TableCellInfo[][] = [];

    if (tableInfo.isSeparated) {
      // 处理分离的表格结构（如Element UI）
      if (tableInfo.headerTable) {
        headers = getTableHeaders(tableInfo.headerTable);
      }
      if (tableInfo.bodyTable) {
        rows = getTableRows(tableInfo.bodyTable, headers);
        // 更新每个单元格的表格标识信息
        rows.forEach((row) => {
          row.forEach((cell) => {
            cell.tableName = tableName;
            cell.tableId = tableId;
            cell.tableClass = tableClass;
          });
        });
      }
    } else {
      // 处理统一的表格结构
      if (tableInfo.headerElement) {
        headers = getTableHeaders(tableInfo.headerElement);
      }
      if (tableInfo.bodyElement) {
        rows = getTableRows(tableInfo.bodyElement, headers);
        // 更新每个单元格的表格标识信息
        rows.forEach((row) => {
          row.forEach((cell) => {
            cell.tableName = tableName;
            cell.tableId = tableId;
            cell.tableClass = tableClass;
          });
        });
      }
    }

    // 如果没有数据行但有表头，生成默认的空行数据
    if (rows.length === 0 && headers.length > 0) {
      const defaultRow: TableCellInfo[] = headers.map((header, columnIndex) => ({
        tableName,
        tableId,
        tableClass,
        rowIndex: 0,
        columnIndex,
        columnHeader: header,
        value: null,
        isInTableHeader: false,
        formElement: null,
      }));
      rows = [defaultRow];
    }

    // 即使没有数据行，也要返回表格结构（只要有表头或表格容器存在）
    const allCells = rows.flat();

    // 生成表格级别的元数据
    const tableContainer = tableInfo.container;
    const tableRect = tableContainer.getBoundingClientRect();
    const frameworkInfo = detectUIFramework(tableContainer);
    const computedStyle = window.getComputedStyle(tableContainer);

    // 统计表单元素数量
    const formElementCount = allCells.filter((cell) => cell.formElement).length;

    // 生成父容器选择器链
    const parentSelectors: string[] = [];
    let parent = tableContainer.parentElement;
    let level = 0;
    while (parent && level < 5) {
      // 最多追踪5层父容器
      try {
        const selector = generateCSSSelector(parent);
        if (selector) {
          parentSelectors.push(selector);
        }
      } catch (e) {
        // 忽略生成选择器时的错误
      }
      parent = parent.parentElement;
      level++;
    }

    const tableMetadata = {
      // DOM定位信息
      tableSelector: generateCSSSelector(tableContainer),
      tableXPath: generateXPath(tableContainer),
      tableAttributes: getElementAttributes(tableContainer),

      // 结构特征
      structuralFingerprint: {
        headerCount: headers.length,
        rowCount: rows.length,
        columnCount: headers.length,
        headerTexts: [...headers], // 创建副本
        hasFormElements: formElementCount > 0,
        formElementCount: formElementCount,
      },

      // 框架特定信息
      frameworkInfo: {
        framework: frameworkInfo.framework,
        componentType: frameworkInfo.componentType,
        version: undefined, // 暂时不检测版本
      },

      // 位置和可见性信息
      positionInfo: {
        boundingRect: {
          x: Math.round(tableRect.x),
          y: Math.round(tableRect.y),
          width: Math.round(tableRect.width),
          height: Math.round(tableRect.height),
        },
        isVisible:
          tableRect.width > 0 &&
          tableRect.height > 0 &&
          computedStyle.display !== "none" &&
          computedStyle.visibility !== "hidden",
        parentSelectors: parentSelectors,
        zIndex: computedStyle.zIndex !== "auto" ? parseInt(computedStyle.zIndex) : undefined,
      },

      // 提取上下文信息
      extractedAt: Date.now(),
      extractionMethod: "extractFrameworkTableData",
      pageUrl: window.location.href,
    };

    return {
      tableName,
      tableId,
      tableClass,
      headers,
      rows,
      allCells,
      tableMetadata,
    };
  };

  /**
   * 查找元素的通用函数，依次尝试所有选择器
   */
  const findElement = (container: HTMLElement | Document, selectors: string[]): HTMLElement | null => {
    for (const selector of selectors) {
      const element = container.querySelector(selector) as HTMLElement;
      if (element) return element;
    }
    return null;
  };

  /**
   * 检查元素是否可见
   */
  const isElementVisible = (element: HTMLElement): boolean => {
    // 检查元素是否在DOM中
    if (!element.isConnected) {
      return false;
    }

    // 检查元素的样式
    const style = window.getComputedStyle(element);
    if (style.display === "none" || style.visibility === "hidden" || style.opacity === "0") {
      return false;
    }

    // 检查元素的尺寸
    const rect = element.getBoundingClientRect();
    if (rect.width === 0 && rect.height === 0) {
      return false;
    }

    // 检查父元素是否隐藏
    let parent = element.parentElement;
    while (parent && parent !== document.body) {
      const parentStyle = window.getComputedStyle(parent);
      if (parentStyle.display === "none" || parentStyle.visibility === "hidden") {
        return false;
      }
      parent = parent.parentElement;
    }

    return true;
  };

  /**
   * 查找所有匹配的元素（只返回可见的元素）
   */
  const findAllElements = (container: HTMLElement | Document, selectors: string[]): HTMLElement[] => {
    const elements: HTMLElement[] = [];
    for (const selector of selectors) {
      const found = Array.from(container.querySelectorAll(selector)) as HTMLElement[];
      // 过滤出可见的元素
      const visibleElements = found.filter((element) => isElementVisible(element));
      elements.push(...visibleElements);
    }
    // 去重
    return Array.from(new Set(elements));
  };

  /**
   * 获取表格名称
   */
  const getTableName = (table: HTMLElement, index: number): string => {
    // 尝试从caption获取
    const caption = table.querySelector("caption");
    if (caption && caption.textContent?.trim()) {
      return caption.textContent.trim();
    }

    // 尝试从前面的标题元素获取
    const prevElement = table.previousElementSibling;
    if (prevElement && (prevElement.tagName.match(/^H[1-6]$/) || prevElement.classList.contains("table-title"))) {
      const title = prevElement.textContent?.trim();
      if (title) return title;
    }

    // 尝试从表格的id获取
    if (table.id) {
      return table.id;
    }

    // 尝试从表格的class获取（过滤掉框架相关的class）
    if (table.className) {
      const classNames = table.className
        .split(" ")
        .filter(
          (cls) =>
            cls &&
            !cls.includes("table") &&
            !cls.includes("ant-") &&
            !cls.includes("el-") &&
            !cls.includes("v-") &&
            !cls.includes("Mui"),
        );
      if (classNames.length > 0) {
        return classNames[0];
      }
    }

    // 使用默认名称
    return `表格${index + 1}`;
  };

  /**
   * 获取表头信息 - 支持多种UI框架
   */
  const getTableHeaders = (tableContainer: HTMLElement): string[] => {
    const headerContainer = findElement(tableContainer, headerSelectors);
    if (!headerContainer) {
      console.warn("未能找到表格的表头容器");
      return [];
    }

    const headerCells = Array.from(headerContainer.querySelectorAll("th, td")) as HTMLElement[];
    return headerCells.map((cell) => cell.textContent?.trim() || "");
  };

  /**
   * 获取表体行数据 - 支持多种UI框架
   */
  const getTableRows = (tableContainer: HTMLElement, headers: string[]): TableCellInfo[][] => {
    const bodyContainer = findElement(tableContainer, bodySelectors);
    if (!bodyContainer) {
      console.warn("未能找到表格的表体容器");
      return [];
    }

    const rows = Array.from(bodyContainer.querySelectorAll("tr")) as HTMLElement[];
    const tableName = getTableName(tableContainer, 0);
    const tableId = tableContainer.id || null;
    const tableClass = tableContainer.className || null;

    return rows.map((row, rowIndex) => {
      const cells = Array.from(row.querySelectorAll("td, th")) as HTMLElement[];
      return cells.map((cell, columnIndex) => {
        const cellInfo: TableCellInfo = {
          tableName,
          tableId,
          tableClass,
          rowIndex: rowIndex,
          columnIndex: columnIndex,
          columnHeader: headers[columnIndex] || null,
          value: cell.textContent?.trim() || "",
          isInTableHeader: cell.tagName.toLowerCase() === "th",
        };

        // 检查单元格内是否有表单元素（支持更多类型和UI框架组件）
        const formElementSelectors = [
          "input",
          "textarea",
          "select", // 原生表单元素
          ".el-input input",
          ".el-textarea textarea", // Element UI 输入框
          ".el-select input",
          ".el-select .el-input__inner", // Element UI 选择器
          ".el-checkbox input",
          ".el-radio input", // Element UI 复选框和单选框
          ".ant-input",
          ".ant-select-selector input", // Ant Design 输入框
          ".ant-checkbox-input",
          ".ant-radio-input", // Ant Design 复选框和单选框
          ".v-text-field input",
          ".v-select input", // Vuetify 输入框
          ".MuiInput-input",
          ".MuiSelect-select", // Material-UI 输入框
        ];

        let formElement: HTMLElement | null = null;
        for (const selector of formElementSelectors) {
          formElement = cell.querySelector(selector) as HTMLElement;
          if (formElement) break;
        }

        if (formElement) {
          const tagName = formElement.tagName.toLowerCase();
          const inputElement = formElement as HTMLInputElement;
          const textareaElement = formElement as HTMLTextAreaElement;
          const selectElement = formElement as HTMLSelectElement;

          cellInfo.formElement = formElement;

          // 如果有表单元素，优先使用表单元素的值作为单元格值
          const formValue = getFormElementValue(formElement);
          if (formValue) {
            cellInfo.value = formValue;
          }
        } else {
          // 如果没有找到标准表单元素，尝试提取UI框架组件的值
          const frameworkValue = getUIFrameworkComponentValue(cell);
          if (frameworkValue) {
            cellInfo.value = frameworkValue;
            // 为UI框架组件设置formElement为包含该组件的单元格
            cellInfo.formElement = cell;
          }
        }

        // 生成详细的DOM定位元数据
        const cellRect = cell.getBoundingClientRect();
        const frameworkInfo = detectUIFramework(cell);
        const isVisible = cellRect.width > 0 && cellRect.height > 0 && window.getComputedStyle(cell).display !== "none";

        cellInfo.cellMetadata = {
          // 单元格DOM定位信息
          cellSelector: generateCSSSelector(cell),
          cellXPath: generateXPath(cell),
          cellAttributes: getElementAttributes(cell),
          cellPosition: {
            x: Math.round(cellRect.x),
            y: Math.round(cellRect.y),
            width: Math.round(cellRect.width),
            height: Math.round(cellRect.height),
          },

          // 表格结构定位信息
          tableSelector: generateCSSSelector(tableContainer),
          tableXPath: generateXPath(tableContainer),
          rowSelector: generateCSSSelector(row),
          rowXPath: generateXPath(row),

          // UI框架特定信息
          framework: frameworkInfo.framework,
          componentType: frameworkInfo.componentType,

          // 提取时的上下文信息
          extractionTimestamp: Date.now(),
          extractionMethod: "getTableRows",
          isVisible: isVisible,
          hasFormElements: !!formElement,
        };

        // 如果有表单元素，添加表单元素的详细定位信息
        if (formElement) {
          const formRect = formElement.getBoundingClientRect();
          const formFrameworkInfo = detectUIFramework(formElement);

          cellInfo.cellMetadata.formElementSelector = generateCSSSelector(formElement);
          cellInfo.cellMetadata.formElementXPath = generateXPath(formElement);
          cellInfo.cellMetadata.formElementAttributes = getElementAttributes(formElement);
          cellInfo.cellMetadata.formElementType = formElement.tagName.toLowerCase();
          cellInfo.cellMetadata.formElementName = (formElement as any).name || null;
          cellInfo.cellMetadata.formElementId = formElement.id || null;

          // 如果表单元素有不同的框架信息，更新组件类型
          if (formFrameworkInfo.componentType) {
            cellInfo.cellMetadata.componentType = formFrameworkInfo.componentType;
          }
        }

        return cellInfo;
      });
    });
  };

  // 4. 检测表格框架类型并查找容器
  const detectedTables = detectAndExtractTables(tableContainer, tableContainerSelectors);

  if (detectedTables.length === 0) {
    console.log("未找到任何表格容器");
    return extractedTables;
  }

  // 5. 处理检测到的表格
  detectedTables.forEach((tableInfo, index) => {
    try {
      const result = extractTableData(tableInfo, index);
      // 只要能提取到表格结构就添加，不管是否有数据行
      if (result) {
        extractedTables.push(result);
        console.log(`✅ 成功提取表格 "${result.tableName}" 结构:`, {
          headers: result.headers.length,
          rows: result.rows.length,
          cells: result.allCells.length,
          hasData: result.rows.length > 0,
        });
      }
    } catch (error) {
      console.error(`处理表格时出错:`, error);
    }
  });

  return extractedTables;
}

/**
 * 获取表单元素的值（支持各种类型的表单元素）
 */
function getFormElementValue(element: HTMLElement): string {
  const tagName = element.tagName.toLowerCase();

  switch (tagName) {
    case "input":
      const inputElement = element as HTMLInputElement;
      if (inputElement.type === "checkbox" || inputElement.type === "radio") {
        return inputElement.checked ? inputElement.value || "true" : "";
      }
      return inputElement.value || "";

    case "textarea":
      return (element as HTMLTextAreaElement).value || "";

    case "select":
      const selectElement = element as HTMLSelectElement;
      const selectedOption = selectElement.options[selectElement.selectedIndex];
      return selectedOption ? selectedOption.text || selectedOption.value : "";

    default:
      // 对于UI框架组件，尝试获取其内部的实际表单元素
      const innerInput = element.querySelector("input, textarea, select") as
        | HTMLInputElement
        | HTMLTextAreaElement
        | HTMLSelectElement;
      if (innerInput) {
        return getFormElementValue(innerInput);
      }

      // 如果都没有，返回元素的文本内容
      return element.textContent?.trim() || "";
  }
}

/**
 * 增强的UI框架组件值提取函数
 * 专门处理复杂的UI框架组件（如Element UI、Ant Design等）
 */
function getUIFrameworkComponentValue(cell: HTMLElement): string {
  // Element UI 组件值提取
  const elInputWrapper = cell.querySelector(".el-input");
  if (elInputWrapper) {
    const elInput = elInputWrapper.querySelector("input, textarea") as HTMLInputElement | HTMLTextAreaElement;
    if (elInput) return elInput.value || "";
  }

  const elSelectWrapper = cell.querySelector(".el-select");
  if (elSelectWrapper) {
    const selectedText = elSelectWrapper.querySelector(".el-input__inner") as HTMLInputElement;
    if (selectedText) return selectedText.value || "";
    // 备选：查找选中的选项
    const selectedOption = elSelectWrapper.querySelector(".el-select-dropdown__item.selected");
    if (selectedOption) return selectedOption.textContent?.trim() || "";
  }

  const elCheckbox = cell.querySelector(".el-checkbox");
  if (elCheckbox) {
    const checkbox = elCheckbox.querySelector('input[type="checkbox"]') as HTMLInputElement;
    if (checkbox) {
      return checkbox.checked ? checkbox.value || "true" : "false";
    }
  }

  const elRadio = cell.querySelector(".el-radio");
  if (elRadio) {
    const radio = elRadio.querySelector('input[type="radio"]') as HTMLInputElement;
    if (radio && radio.checked) {
      return radio.value || "true";
    }
  }

  // Ant Design 组件值提取
  const antInput = cell.querySelector(".ant-input") as HTMLInputElement;
  if (antInput) return antInput.value || "";

  const antSelectSelector = cell.querySelector(".ant-select-selector");
  if (antSelectSelector) {
    const selectedValue = antSelectSelector.querySelector(".ant-select-selection-item");
    if (selectedValue) return selectedValue.textContent?.trim() || "";
    // 备选：查找输入框
    const selectInput = antSelectSelector.querySelector("input") as HTMLInputElement;
    if (selectInput) return selectInput.value || "";
  }

  const antCheckbox = cell.querySelector(".ant-checkbox");
  if (antCheckbox) {
    const checkbox = antCheckbox.querySelector('input[type="checkbox"]') as HTMLInputElement;
    if (checkbox) {
      return checkbox.checked ? checkbox.value || "true" : "false";
    }
  }

  const antRadio = cell.querySelector(".ant-radio");
  if (antRadio) {
    const radio = antRadio.querySelector('input[type="radio"]') as HTMLInputElement;
    if (radio && radio.checked) {
      return radio.value || "true";
    }
  }

  // Vuetify 组件值提取
  const vTextField = cell.querySelector(".v-text-field input") as HTMLInputElement;
  if (vTextField) return vTextField.value || "";

  const vSelect = cell.querySelector(".v-select input") as HTMLInputElement;
  if (vSelect) return vSelect.value || "";

  // Material-UI 组件值提取
  const muiInput = cell.querySelector(
    ".MuiInput-input, .MuiOutlinedInput-input, .MuiFilledInput-input",
  ) as HTMLInputElement;
  if (muiInput) return muiInput.value || "";

  const muiSelect = cell.querySelector(".MuiSelect-select") as HTMLElement;
  if (muiSelect) return muiSelect.textContent?.trim() || "";

  // 如果没有找到特定的UI框架组件，返回空字符串
  return "";
}

/**
 * 从特定表格容器中提取单个表格的数据
 * @param tableContainer 表格容器元素
 * @returns 表格提取结果，如果失败返回null
 */
export function extractSingleTableData(tableContainer: HTMLElement): TableExtractionResult | null {
  const results = extractFrameworkTableData(tableContainer);
  return results.length > 0 ? results[0] : null;
}

/**
 * 获取表格中指定行的数据
 * @param tableContainer 表格容器元素
 * @param rowIndex 行索引（从0开始）
 * @returns 行数据数组
 */
export function extractTableRowData(tableContainer: HTMLElement, rowIndex: number): TableCellInfo[] | null {
  const tableData = extractSingleTableData(tableContainer);
  if (!tableData || rowIndex < 0 || rowIndex >= tableData.rows.length) {
    return null;
  }
  return tableData.rows[rowIndex];
}

/**
 * 获取表格中包含表单元素的单元格信息
 * @param tableContainer 表格容器元素或整个文档
 * @returns 包含表单元素的单元格信息数组
 */
export function extractTableFormElements(tableContainer: HTMLElement | Document = document): TableCellInfo[] {
  const tables = extractFrameworkTableData(tableContainer);
  const formCells: TableCellInfo[] = [];

  tables.forEach((table) => {
    table.allCells.forEach((cell) => {
      if (cell.formElement) {
        formCells.push(cell);
      }
    });
  });

  return formCells;
}

/**
 * 检测页面中是否存在UI框架表格
 * @param container 容器元素，默认为document
 * @returns 是否存在框架表格
 */
export function hasFrameworkTables(container: HTMLElement | Document = document): boolean {
  const frameworkSelectors = [
    ".el-table", // Element UI
    ".ant-table-wrapper", // Ant Design 包装器
    ".ant-table", // Ant Design 直接表格
    ".v-data-table", // Vuetify
    ".MuiTable-root", // Material-UI
    '[class*="el-table"]', // Element UI 动态类名
    '[class*="ant-table"]', // Ant Design 动态类名
    '[class*="v-data-table"]', // Vuetify 动态类名
    '[class*="MuiTable"]', // Material-UI 动态类名
  ];

  return frameworkSelectors.some((selector) => {
    try {
      return container.querySelector(selector) !== null;
    } catch (error) {
      console.warn(`检测框架表格时选择器 "${selector}" 出错:`, error);
      return false;
    }
  });
}
