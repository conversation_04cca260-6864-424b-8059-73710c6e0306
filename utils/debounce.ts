/**
 * 创建一个防抖函数
 * @param func 需要防抖的函数
 * @param wait 等待时间，单位毫秒
 * @param immediate 是否立即执行
 * @returns 返回一个防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate: boolean = false,
): (...funcArgs: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;

  return function (...args: Parameters<T>) {
    const context = this;

    const later = () => {
      timeout = null;
      if (!immediate) func.apply(context, args);
    };

    const callNow = immediate && !timeout;
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);

    if (callNow) func.apply(context, args);
  };
}

/**
 * 创建一个节流函数
 * @param func 需要节流的函数
 * @param wait 等待时间，单位毫秒
 * @returns 返回一个节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
): (...funcArgs: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;
  let previous = 0;

  return function (...args: Parameters<T>) {
    const context = this;
    const now = Date.now();

    if (!previous) previous = now;
    const remaining = wait - (now - previous);

    if (remaining <= 0 || remaining > wait) {
      if (timeout) {
        clearTimeout(timeout);
        timeout = null;
      }
      previous = now;
      func.apply(context, args);
    } else if (!timeout) {
      timeout = setTimeout(() => {
        previous = Date.now();
        timeout = null;
        func.apply(context, args);
      }, remaining);
    }
  };
}

/**
 * API请求防重复调用工具
 * 基于请求的唯一标识符来防止重复调用
 */
class ApiCallManager {
  private pendingCalls = new Map<string, Promise<any>>();
  private callTimestamps = new Map<string, number>();
  private readonly minInterval: number;

  constructor(minInterval: number = 1000) {
    this.minInterval = minInterval;
  }

  /**
   * 生成请求的唯一标识符
   */
  private generateKey(api: string, params: any): string {
    return `${api}_${JSON.stringify(params)}`;
  }

  /**
   * 检查是否可以发起新的请求
   */
  private canMakeCall(key: string): boolean {
    const lastCallTime = this.callTimestamps.get(key);
    if (!lastCallTime) return true;

    return Date.now() - lastCallTime >= this.minInterval;
  }

  /**
   * 执行API调用，防止重复调用
   */
  async call<T>(api: string, params: any, apiFunction: (params: any) => Promise<T>): Promise<T> {
    const key = this.generateKey(api, params);

    // 如果已有相同的请求在进行中，返回该请求的Promise
    if (this.pendingCalls.has(key)) {
      console.debug(`API ${api} 请求已在进行中，返回现有Promise`);
      return this.pendingCalls.get(key)!;
    }

    // 检查是否在最小间隔内
    if (!this.canMakeCall(key)) {
      console.debug(`API ${api} 调用过于频繁，跳过此次调用`);
      throw new Error(`API ${api} 调用过于频繁，请稍后再试`);
    }

    // 记录调用时间
    this.callTimestamps.set(key, Date.now());

    // 创建新的请求Promise
    const promise = apiFunction(params)
      .finally(() => {
        // 请求完成后清理
        this.pendingCalls.delete(key);
      });

    // 存储正在进行的请求
    this.pendingCalls.set(key, promise);

    return promise;
  }

  /**
   * 清理过期的时间戳记录
   */
  cleanup(): void {
    const now = Date.now();
    for (const [key, timestamp] of this.callTimestamps.entries()) {
      if (now - timestamp > this.minInterval * 10) { // 保留10倍间隔时间的记录
        this.callTimestamps.delete(key);
      }
    }
  }
}

// 创建全局API调用管理器实例
export const apiCallManager = new ApiCallManager(1000); // 1秒最小间隔

// 定期清理过期记录
setInterval(() => {
  apiCallManager.cleanup();
}, 60000); // 每分钟清理一次
