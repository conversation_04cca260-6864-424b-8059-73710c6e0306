import { Plugin } from "prosemirror-state";
import { Decoration, DecorationSet } from "prosemirror-view";
import { Node } from "prosemirror-model";

// 判断段落是否为空（没有任何文本内容）
const isEmptyParagraph = (node: Node) => {
  return node.type.name === "paragraph" && node.nodeSize === 2; // 段落类型且没有内容
};

export const placeholderPlugin = (placeholder: string) => {
  return new Plugin({
    props: {
      decorations(state) {
        const doc = state.doc;
        const firstChild = doc.firstChild;

        // 检查文档是否只有一个空段落
        if (firstChild && isEmptyParagraph(firstChild) && doc.childCount === 1) {
          const from = 0; // 文档开始位置
          const to = firstChild.nodeSize; // 第一个段落的结束位置

          // 创建装饰，使用 from 和 to 来表示节点的位置范围
          const decoration = Decoration.node(from, to, {
            "data-placeholder": placeholder,
          });

          return DecorationSet.create(doc, [decoration]);
        }

        // 如果存在多个段落，不显示占位符
        return null;
      },
    },
  });
};
