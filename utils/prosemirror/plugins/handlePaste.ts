// 内置 ctrl + v 粘贴段落时改为纯文本粘贴 【自定义插件/处理粘贴事件】
export default (view, event, slice) => {
  const { state, dispatch } = view;
  const { $from } = state.selection;

  // 检查光标是否在段落中
  if ($from.parent.type.name === "paragraph") {
    event.preventDefault();
    const text = (event.clipboardData || window.clipboardData).getData("text/plain");
    dispatch(state.tr.insertText(text));
    return true;
  }
  return false;
};
