import { message } from "antd";
import { Plugin, Plugin<PERSON><PERSON> } from "prosemirror-state";
import { Decoration, DecorationSet } from "prosemirror-view";

/**
 *
 * @param {String} mentionTrigger
 * @param {bool} allowSpace
 * @returns {Object}
 */
export function getRegexp(mentionTrigger, allowSpace) {
  // 通过手动列出字母和数字的范围来替代 \p{L}
  var mention = allowSpace
    ? new RegExp("(^|\\s)" + mentionTrigger + "([a-zA-Z0-9\u4e00-\u9fa5\\-\\+]+\\s?[a-zA-Z0-9\u4e00-\u9fa5\\-\\+]*)?$")
    : new RegExp("(^|\\s)" + mentionTrigger + "([a-zA-Z0-9\u4e00-\u9fa5\\-\\+]+)?$");

  return {
    mention: mention,
  };
}

/**
 *
 * @param {ResolvedPosition} $position https://prosemirror.net/docs/ref/#model.Resolved_Positions
 * @param {JSONObject} opts
 * @returns {JSONObject}
 */
export function getMatch($position, opts) {
  // take current para text content upto cursor start.
  // this makes the regex simpler and parsing the matches easier.
  var parastart = $position.before();
  const text = $position.doc.textBetween(parastart, $position.pos, "\n", "\0");

  var regex = getRegexp(opts.mentionTrigger, opts.allowSpace);

  // only one of the below matches will be true.
  var mentionMatch = text.match(regex.mention);

  var match = mentionMatch;

  // set type of match
  var type;
  if (mentionMatch) {
    type = "mention";
  }

  // if match found, return match with useful information.
  if (match) {
    // adjust match.index to remove the matched extra space
    match.index = match[0].startsWith(" ") ? match.index + 1 : match.index;
    match[0] = match[0].startsWith(" ") ? match[0].substring(1, match[0].length) : match[0];

    // The absolute position of the match in the document
    var from = $position.start() + match.index;
    var to = from + match[0].length;

    var queryText = match[2];

    return {
      range: { from: from, to: to },
      queryText: queryText || "",
      text: opts.mentionTrigger + (queryText || ""),
      type: type,
    };
  }
  // else if no match don't return anything.
}

/**
 * Util to debounce call to a function.
 * >>> debounce(function(){}, 1000, this)
 */
export const debounce = (function () {
  var timeoutId = null;
  return function (func, timeout, context, ...args) {
    context = context || this;
    clearTimeout(timeoutId);
    timeoutId = setTimeout(function () {
      func.apply(context, args);
    }, timeout);

    return timeoutId;
  };
})();

var getNewState = function () {
  return {
    active: false,
    range: {
      from: 0,
      to: 0,
    },
    type: "", //mention or tag
    text: "",
    queryText: "",
    suggestions: [],
    index: 0, // current active suggestion index
  };
};

/**
 * @param {JSONObject} opts
 * @returns {Plugin}
 */
export function getMentionsPlugin(opts) {
  // default options
  var defaultOpts = {
    mentionTrigger: "@",
    allowSpace: false,
    getSuggestions: (cb) => {
      cb([]);
    },
    activeClass: "suggestion-item-active",
    suggestionTextClass: "prosemirror-suggestion",
    maxNoOfSuggestions: 10,
    delay: 500,
  };

  var opts = Object.assign({}, defaultOpts, opts);

  // timeoutId for clearing debounced calls
  var showListTimeoutId = null;

  let _view = null;
  let _state = null;

  // 添加 message 事件监听
  window.addEventListener("message", function (event) {
    let type = event?.data?.type || "";
    if (type === "onSelect") {
      if (_view.dom.innerHTML.includes("note_rel_group") && event.data.data.type === "note_rel_group") {
        let text = "已经加入组了，一个便签只能加入一个组";
        const urlParams = new URLSearchParams(window.location.search);
        // 获取 "noteId" 参数的值
        const noteId = urlParams.get("noteId");
        window.parent.postMessage({ type: "prompt", data: text, noteId }, "*");
        return;
      }
      select(_view, _state, opts, event.data.data);
      _view.focus();
    } else if (type === "hide") {
      clearTimeout(showListTimeoutId);
    }
  });

  let count = 1;

  const showList = function (view, state, opts) {
    count++;
    var node = view.domAtPos(view.state.selection.$from.pos);
    var paraDOM = node.node;
    var textDOM = paraDOM.querySelector("." + opts.suggestionTextClass);
    if (!textDOM) return;

    var offset = textDOM.getBoundingClientRect();

    _view = view;
    _state = state;
    postMessage("showList", {
      offset: {
        left: offset.left,
        top: offset.top,
      },
      count,
      state,
      noteId: opts.options.id,
      textDOMOffsetHeight: textDOM.offsetHeight,
    });
  };

  const postMessage = (type, data) => {
    // 使用 URLSearchParams 解析查询参数
    const urlParams = new URLSearchParams(window.location.search);
    // 获取 "noteId" 参数的值
    const noteId = urlParams.get("noteId");
    window.parent.postMessage({ type, data, noteId }, "*");
  };

  var select = function (view, state, opts, item) {
    var attrs = {
      id: item.objId,
      name: item.name,
      icon: item.icon,
      data: JSON.stringify(item),
    };
    var node = view.state.schema.nodes[state.type].create(attrs);
    var tr = view.state.tr.replaceWith(state.range.from, state.range.to, node);
    //var newState = view.state.apply(tr);
    //view.updateState(newState);
    view.dispatch(tr);
  };

  /**
   * See https://prosemirror.net/docs/ref/#state.Plugin_System
   * for the plugin properties spec.
   */
  return new Plugin({
    key: new PluginKey("autosuggestions"),

    // we will need state to track if suggestion dropdown is currently active or not
    state: {
      init() {
        return getNewState();
      },

      apply(tr, state) {
        // compute state.active for current transaction and return
        var newState = getNewState();
        var selection = tr.selection;
        if (selection.from !== selection.to) {
          return newState;
        }

        const $position = selection.$from;
        const match = getMatch($position, opts);

        // if match found update state
        if (match) {
          newState.active = true;
          newState.range = match.range;
          newState.type = match.type;
          newState.text = match.text;
          newState.queryText = match.queryText;
        }

        return newState;
      },
    },

    // We'll need props to hi-jack keydown/keyup & enter events when suggestion dropdown
    // is active.
    props: {
      // to decorate the currently active @mention text in ui
      decorations(editorState) {
        const { active, range } = this.getState(editorState);

        if (!active) return null;

        return DecorationSet.create(editorState.doc, [
          Decoration.inline(range.from, range.to, {
            nodeName: "span",
            class: opts.suggestionTextClass,
          }),
        ]);
      },
    },

    // To track down state mutations and add dropdown reactions
    view() {
      return {
        update: (view) => {
          var state = this.key.getState(view.state);
          if (!state.text) {
            postMessage("hideList", {});
            clearTimeout(showListTimeoutId);
            return;
          }
          // debounce the call to avoid multiple requests
          showListTimeoutId = debounce(
            function () {
              // 创建一个临时的 DOM 元素来解析 HTML 字符串
              const tempDiv = document.createElement("div");
              tempDiv.innerHTML = view.dom.innerHTML;
              // 获取所有 <a> 标签
              const links = tempDiv.querySelectorAll("a");
              // 提取所有 <a> 标签的 data-id 属性
              const dataIds = Array.from(links).map((link) => link.getAttribute("data-id"));
              // get suggestions and set new state
              opts.getSuggestions(function (suggestions) {
                // update `state` argument with suggestions
                state.suggestions = suggestions.filter(
                  (x) => x.name.includes(state.queryText) && !dataIds.includes(x.objId),
                );
                showList(view, state, opts);
              });
            },
            opts.delay,
            this,
          );
        },
      };
    },
  });
}
