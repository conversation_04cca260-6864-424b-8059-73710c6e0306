import { getMentionsPlugin as originalGetMentionsPlugin } from "./mentionPlugin";
import { Plugin, PluginKey } from "prosemirror-state";

// 声明全局变量类型
declare global {
  interface Window {
    __mentionSuggestions?: any[];
    __mentionCallbacks?: Array<(suggestions: any[]) => void>;
    __mentionLastUpdate?: number; // 添加时间戳以跟踪最后更新时间
  }
}

// 初始化全局回调函数数组和提及数据
if (!window.__mentionCallbacks) {
  window.__mentionCallbacks = [];
}

if (!window.__mentionSuggestions) {
  window.__mentionSuggestions = [];
}

// 设置初始更新时间戳
window.__mentionLastUpdate = Date.now();

/**
 * 扩展的提及插件，支持从全局变量获取提及数据
 */
export function getMentionsPlugin(opts: any) {
  // 创建原始的提及插件
  const originalOpts = { ...opts };
  const originalGetSuggestions = originalOpts.getSuggestions;

  // 重写 getSuggestions 函数，优先使用全局变量中的提及数据
  originalOpts.getSuggestions = (callback: (suggestions: any[]) => void) => {
    // 将回调函数添加到全局回调函数数组中
    if (window.__mentionCallbacks && !window.__mentionCallbacks.includes(callback)) {
      window.__mentionCallbacks.push(callback);
      console.log("注册了新的提及回调函数，当前回调数量:", window.__mentionCallbacks.length);
    }

    // 如果全局变量中有提及数据，直接使用
    if (window.__mentionSuggestions && window.__mentionSuggestions.length > 0) {
      console.log("使用全局提及数据，数据条数:", window.__mentionSuggestions.length);
      setTimeout(() => {
        callback(window.__mentionSuggestions);
      }, 0);
    } else {
      // 否则使用原始的 getSuggestions 函数
      console.log("全局提及数据为空，使用原始 getSuggestions 函数");
      originalGetSuggestions(callback);
    }
  };

  // 使用原始的提及插件
  const plugin = originalGetMentionsPlugin(originalOpts);

  return plugin;
}

/**
 * 更新提及数据，而不需要重新创建编辑器
 * 直接调用所有已注册的回调函数，将新的提及数据传递给它们
 */
export function updateMentionSuggestions(suggestions: any[]) {
  try {
    if (!suggestions || !Array.isArray(suggestions)) {
      console.error("提及数据无效:", suggestions);
      return;
    }

    // 更新全局变量中的提及数据
    window.__mentionSuggestions = suggestions;
    window.__mentionLastUpdate = Date.now();

    console.log("更新提及数据，数据条数:", suggestions.length);
    console.log("已注册的回调函数数量:", window.__mentionCallbacks?.length || 0);

    // 直接调用所有已注册的回调函数
    if (window.__mentionCallbacks && window.__mentionCallbacks.length > 0) {
      window.__mentionCallbacks.forEach((callback, index) => {
        try {
          console.log(`调用第 ${index + 1} 个回调函数`);
          callback(suggestions);
        } catch (error) {
          console.error(`调用第 ${index + 1} 个提及回调函数失败:`, error);
        }
      });
    } else {
      console.warn("没有注册的回调函数，提及数据已更新但不会立即生效");
    }
  } catch (error) {
    console.error("更新提及数据时发生错误:", error);
  }
}
