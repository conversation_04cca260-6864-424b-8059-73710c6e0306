import React, { useEffect, useState } from "react";
import { Empty, Flex, Tabs, TabsProps, theme, Typography } from "antd";
import IconFont from "@/components/IconFont";
import { CloseOutlined, MedicineBoxOutlined, UserOutlined } from "@ant-design/icons";
import "./mentionReact.less";
const { useToken } = theme;

const MentionReact: React.FC<{
  noteId: string; // 确保唯一性
  query: string; // 搜索词
  suggestions: any[]; // 提示词列表
  noteType?: string; // 类型
  onSelect: Function;
  onHide: Function;
  count: number;
}> = ({ noteId, query, suggestions, onSelect, onHide, count, noteType }) => {
  const { token } = useToken();
  const [items, setitems] = useState<TabsProps["items"]>([]);
  const [dataList, setDataList] = useState([]);
  const [queryStr, setQueryStr] = useState(query);
  useEffect(() => {
    onSearchChange("note_rel_all");
  }, [count]);

  useEffect(() => {}, [query]);

  // 搜索值变化时更新建议列表
  const onSearchChange = (key?: string) => {
    let regex = new RegExp(query, "g");
    let highlightedArr = (key === "note_rel_all" ? suggestions : suggestions.filter((x) => x.type === key)).map(
      (item) => {
        return {
          ...item,
          nameStyle: noteType ? item.name : item.name.replace(regex, `<span style='color: #1888FF;'>${query}</span>`),
        };
      },
    );
    // Tabs 配置
    let items: TabsProps["items"] = [
      {
        key: "note_rel_all",
        label: "全部",
      },
      {
        key: "note_rel_contact",
        label: "联系人",
      },
      {
        key: "note_rel_note",
        label: "便签",
      },
      {
        key: "note_rel_group",
        label: "便签组",
      },
    ];

    // 查找索引
    let hasNote = suggestions.findIndex((x) => x.type === "note_rel_note");
    let hasContet = suggestions.findIndex((x) => x.type === "note_rel_contact");
    // 根据查找结果进行过滤
    if (hasNote === -1) {
      items = items.filter((item) => item.key !== "note_rel_note");
    }
    if (hasContet === -1) {
      items = items.filter((item) => item.key !== "note_rel_contact");
    }
    setitems(items);
    setDataList(highlightedArr);
  };

  const onChange = (key: string) => {
    onSearchChange(key);
  };
  return (
    <>
      <Flex vertical className="no-arrow-popover" style={{ width: "300px" }}>
        <CloseOutlined
          onClick={() => {
            onHide();
          }}
          style={{
            fontSize: token.fontSizeLG,
            color: token.colorText,
            position: "absolute",
            right: "16px",
            top: "12px",
          }}
        />
        {queryStr ? (
          dataList.length > 0 ? (
            <>
              <Flex align="center" justify="space-between">
                <Flex className="left" justify="space-between" align="center">
                  <Tabs defaultActiveKey="note_rel_all" items={items} onChange={onChange} size="small" />
                </Flex>
              </Flex>
              <div className="select-info">
                {dataList.map((item) => {
                  return (
                    <div
                      onClick={() => {
                        onSelect(item);
                      }}
                      key={item.objId}
                      className="select-info-sign"
                    >
                      {item.type === "note_rel_contact" && <UserOutlined />}
                      {item.type === "note_rel_note" && <IconFont type={"NoteOutlined"} />}
                      {item.type === "note_rel_group" && <MedicineBoxOutlined />}
                      <span
                        className="user-name"
                        dangerouslySetInnerHTML={{ __html: item.nameStyle ? item.nameStyle : item.name }}
                      ></span>
                    </div>
                  );
                })}
              </div>
            </>
          ) : (
            <Empty description={<Typography.Text>暂无数据</Typography.Text>}></Empty>
          )
        ) : (
          <Empty description={<Typography.Text>请继续输入关键词搜索</Typography.Text>}></Empty>
        )}
      </Flex>
    </>
  );
};

export default React.memo(MentionReact);
