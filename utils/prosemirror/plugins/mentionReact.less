@import "@/assets/styles/variables";
.no-arrow-popover {
  padding: var(--ant-padding-xxs) var(--ant-padding);
  background: var(--ant-color-bg-container);
  border-radius: var(--ant-border-radius-lg);
  width: 300px;
  overflow-y: auto;
  position: relative;
  background-color: #fff;
  box-shadow: @Shadow-1;
  min-height: 240px;
  .ant-tabs-nav {
    &::before {
      border-bottom: 0px !important;
    }
  }
}
.select-info {
  height: 182px;
  overflow-y: auto;
  gap: var(--ant-margin-xxs);
  .select-info-sign {
    display: flex;
    align-items: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: var(--ant-line-height);
    cursor: pointer;
    height: var(--ant-line-height);
    font-size: var(--ant-font-size);
    color: var(--ant-color-text);
    height: var(--ant-line-height);
    padding: var(--ant-padding-xxs) 0px;
    .anticon {
      color: var(--ant-color-text);
      margin-right: var(--ant-margin-xxs);
      font-size: var(--ant-font-size-lg);
    }
    .user-name {
      margin-left: var(--ant-margin-xs);
    }
    &:hover {
      color: var(--ant-color-primary-text);
      background: #fffcf8;
    }
  }
  > p:last-child {
    margin-bottom: 0px;
  }
}
