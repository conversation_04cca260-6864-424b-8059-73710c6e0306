export async function getToken(): Promise<string> {
  const res = await browser.storage.local.get(["token"]);
  return res.token;
}

export async function getRefereshToken(): Promise<string> {
  const res = await browser.storage.local.get(["refreshTokenKey"]);
  return res.refreshTokenKey;
}

export async function getTenantId(): Promise<string> {
  const res = await browser.storage.local.get(["tenantId"]);
  return res.tenantId;
}

export async function getUserInfo(): Promise<UserInfo | null> {
  const res = await browser.storage.local.get(["userInfo"]);
  if (!res["userInfo"] || JSON.parse(res["userInfo"]) === "{}") {
    return null;
  } else {
    return JSON.parse(res.userInfo);
  }
}

export type UserInfo = {
  id?: string;
  avatar?: string;
  gender?: string;
  nickName?: string;
  position?: string;
  deptName?: string;
  mobile?: string;
  bizMail?: string;
  email?: string;
  corpName?: string;
};
