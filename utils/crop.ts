export const crop = (href, { width, height, left, top }, mode = "normal") =>
  new Promise((resolve) => {
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    const img = new Image();
    img.onload = () => {
      canvas.width = width || img.width;
      canvas.height = height || img.height;
      if (width && height) {
        ctx.drawImage(img, left, top, width, height, 0, 0, width, height);
      } else {
        ctx.drawImage(img, 0, 0);
      }
      if (mode === "invert" || mode === "gray") {
        ctx.globalCompositeOperation = mode === "gray" ? "saturation" : "difference";
        ctx.fillStyle = "#fff";
        ctx.globalAlpha = 1;
        ctx.fillRect(0, 0, canvas.width, canvas.height);
      }

      resolve(canvas.toDataURL());
    };
    img.src = href;
  });
