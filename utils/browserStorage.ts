// 存储数据到浏览器扩展本地存储
export const cacheSet = async (key: string, value: any): Promise<void> => {
  try {
    if (!browser?.storage?.local) {
      console.error("当前浏览器环境不支持扩展程序存储API，或者未授予存储权限。");
      return;
    }
    await browser.storage.local.set({ [key]: value });
  } catch (error) {
    console.error(`存储数据时发生错误：${error}`);
  }
};

// 从浏览器扩展本地存储中获取数据
export const cacheGet = async (key: string): Promise<any> => {
  try {
    if (!browser?.storage?.local) {
      console.error("当前浏览器环境不支持扩展程序存储API，或者未授予存储权限。");
      return null;
    }
    const result = await browser.storage.local.get(key);
    if (result[key] !== undefined) {
      return result[key];
    } else {
      console.warn(`未找到对应的值：key = ${key}`);
      return null;
    }
  } catch (error) {
    console.error(`获取数据时发生错误：${error}`);
    return null;
  }
};
