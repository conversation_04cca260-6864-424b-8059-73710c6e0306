export const NOTE_MODIFY_STORAGE_KEY = "changedNote";
export const NOTE_DETAIL_STORAGE_KEY = "openNoteDetail";
export const MENU_NAME_STORAGE_KEY = "menuName";
export const SET_PLACE_TOP_ID = "setPlaceTopId";

const setModifyItem = (key: string, item: any) => {
  if (!browser || !browser.storage) {
    console.error("当前浏览器环境不支持扩展程序存储API，请检查程序逻辑或扩展程序权限设置情况。");
    return;
  }
  let sinoKey = sessionStorage.getItem("sino-tap-key") || "";
  return browser.storage.local.set({
    [key + sinoKey]: item,
  });
};

export default setModifyItem;
