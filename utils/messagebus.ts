import { addComment, delComment, pageComments } from "@/api/comment.ts";
import { baiduocr } from "@/api/tools/ocr.ts";
import {
  addPrompt,
  cancelPromptCollectionStatus,
  delPrompt,
  editPrompt,
  pagePrompts,
  updatePromptCollectionStatus,
  updatePromptShareStatus,
} from "@/api/prompt.ts";
import useEventSource from "@/hooks/useEventSource.ts";
import { addNote, delNote, editNote, pageNote } from "@/api/note.ts";

const apiList = {
  addComment,
  delComment,
  pageComments,
  // 截图OCR
  baiduocr,
  // 提示词
  pagePrompts,
  delPrompt,
  updatePromptCollectionStatus,
  cancelPromptCollectionStatus,
  updatePromptShareStatus,
  addPrompt,
  editPrompt,
  pageNote,
  addNote,
  editNote,
  delNote,
};

/**  background给页面发送消息 */
export function sendMessageToPopup(data) {
  console.log("background给页面发送消息", data);
  browser.tabs
    .query({ active: true, lastFocusedWindow: true })
    .then((tabs) => {
      if (tabs && tabs.length > 0) {
        const activeTab = tabs[0];
        if (activeTab && activeTab.id) {
          // console.log(`Sending message to tab with id: ${activeTab.id}`);
          browser.tabs.sendMessage(activeTab.id, data);
        }
      } else {
        console.error("No active tabs found.");
      }
    })
    .catch((error) => {
      console.error("Error querying tabs:", error);
    });
}

/**  background给所有tab页发送消息 */
export function sendMessageToAllPopup(data) {
  browser.tabs.query({ active: true }).then((tabs) => {
    if (tabs && tabs.length > 0) {
      tabs.forEach((tab) => {
        console.log(`窗口ID: ${tab.windowId}, 标签页ID: ${tab.id}, 标签页标题: ${tab.title}`);
        browser.tabs
          .sendMessage(tab.id, data)
          .then(() => {
            console.log(`消息已发送到标签页ID: ${tab.id}`);
          })
          .catch((error) => {
            console.error(`发送消息到标签页ID: ${tab.id} 时出错:`, error);
          });
      });
    } else {
      console.error("没有找到任何活动的标签页");
    }
  });
}

/**  background收到消息后调用接口 */
export function handlePublicApi(api, params) {
  apiList[api](params).then((res) => {
    sendMessageToPopup({
      fetchType: "fetch",
      api: api,
      res: res,
      type: "on",
    });
  });
}

/**  background收到消息后调用接口 SSE */
export function handlePublicApiSSE(url, headers, body, query, instruct) {
  const eventSource = useEventSource({
    url: `${import.meta.env["VITE_AI_API_BASE"]}${url}`,
    method: "POST",
    headers: headers,
    body: body,
    onMessage: (message) => {
      sendMessageToPopup({
        fetchType: "fetchSSE",
        url: url,
        process: "onmessage",
        type: "on",
        ...message,
      });
    },
    onMessageFile: (message) => {
      sendMessageToPopup({
        fetchType: "fetchSSE",
        url: url,
        process: "onmessagefile",
        type: "on",
        ...message,
      });
    },
    onClose: () => {
      sendMessageToPopup({
        fetchType: "fetchSSE",
        url: url,
        process: "finished",
        type: "on",
      });
    },
    onError: () => {
      sendMessageToPopup({
        fetchType: "fetchSSE",
        url: url,
        process: "error",
        type: "on",
      });
    },
  });
  instruct == "start" && eventSource.start(query);
  instruct == "stop" &&
    sendMessageToPopup({
      fetchType: "fetchSSE",
      url: url,
      instruct: "stop",
      process: "finished",
      type: "on",
    });
}
