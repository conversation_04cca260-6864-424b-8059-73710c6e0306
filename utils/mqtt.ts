import mqtt from 'mqtt';

export class Mqtt {
  host: string;
  port: number;
  username: string;
  password: string;
  path: string;
  client?: mqtt.MqttClient;

  constructor() {
    this.host = import.meta.env.VITE_MQTT_HOST;
    this.port = parseInt(import.meta.env.VITE_MQTT_PORT, 10);
    this.username = import.meta.env.VITE_MQTT_USERNAME;
    this.password = import.meta.env.VITE_MQTT_PASSWORD;
    this.path = import.meta.env.VITE_MQTT_PATH;
    this.connect();
  }

  connect() {
    console.log('------', location.protocol)
    this.client = mqtt.connect({
      host: this.host,
      port: this.port,
      username: this.username,
      password: this.password,
      path: this.path,
      // protocol: location.protocol === 'http:' ? 'ws' : 'wss',
      protocol: import.meta.env.VITE_MQTT_PROTOCOL || '',
      keepalive: 15,
      reconnectPeriod: 5000, // 5秒后重连
      // rejectUnauthorized: false // 如果使用自签名证书
    });

    this.client!.on("connect", () => {
      console.log("MQTT connected");
    });

    this.client!.on("error", (err) => {
      console.error("MQTT error:", err);
    });

    this.client!.on("message", (topic, payload) => {
      console.log(`Received message on ${topic}: ${payload.toString()}`);
    });

    this.client!.on("close", () => {
      console.log("MQTT connection closed");
    });

    this.client!.on("reconnect", () => {
      console.log("MQTT reconnecting...");
    });
  }

  subscribe(topic: string, callback: (message: string) => void) {
    this.client?.subscribe(topic, (err) => {
      if (err) console.error("Subscribe error:", err);
    });
    this.client?.on("message", (t, payload) => {
      if (t === topic) callback(payload.toString());
    });
  }

  publish(topic: string, message: string) {
    this.client?.publish(topic, message, { qos: 1 });
  }
  // 添加销毁 MQTT 连接的方法
  destroy() {
    if (this.client) {
      this.client.end(true, () => {
        console.log("MQTT 连接已关闭");
      });
    }
  }
}