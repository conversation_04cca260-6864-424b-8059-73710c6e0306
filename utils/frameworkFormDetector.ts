/**
 * UI框架表单元素类型检测工具
 * 支持Element UI、Ant Design、Vuetify、Material-UI等主流框架
 * 解决不同UI框架编译到浏览器后DOM结构差异导致的表单类型识别不准确问题
 */

interface FrameworkFormElementInfo {
  type: string;
  framework?: string;
  componentType?: string;
  value?: string;
  placeholder?: string;
  label?: string;
  isRequired?: boolean;
  isDisabled?: boolean;
  isReadonly?: boolean;
}

/**
 * 检测UI框架类型
 */
function detectUIFramework(element: HTMLElement): { framework?: string; componentType?: string } {
  // 首先尝试使用各个框架的具体检测函数来判断框架类型
  // 这样可以更准确地处理深层嵌套的元素

  // Element UI - 使用与getElementUIFormInfo相同的查找策略
  const elWrapper = element.closest(
    ".el-input, .el-select, .el-textarea, .el-checkbox, .el-radio, .el-switch, .el-date-picker, .el-time-picker, .el-cascader, .el-slider, .el-rate, .el-upload, .el-input-number, .el-autocomplete, .el-color-picker",
  );
  if (elWrapper) {
    const componentMatch = elWrapper.className.match(/el-([a-z-]+)/);
    return {
      framework: "elementUI",
      componentType: componentMatch ? componentMatch[0] : "el-unknown",
    };
  }

  // Ant Design - 使用与getAntDesignFormInfo相同的查找策略
  const antWrapper = element.closest(
    ".ant-input, .ant-select, .ant-checkbox, .ant-radio, .ant-switch, .ant-date-picker, .ant-time-picker, .ant-cascader, .ant-slider, .ant-rate, .ant-upload, .ant-input-number, .ant-auto-complete, .ant-mentions, .ant-tree-select",
  );
  if (antWrapper) {
    const componentMatch = antWrapper.className.match(/ant-([a-z-]+)/);
    return {
      framework: "antDesign",
      componentType: componentMatch ? componentMatch[0] : "ant-unknown",
    };
  }

  // Vuetify - 使用与getVuetifyFormInfo相同的查找策略
  const vWrapper = element.closest(
    ".v-text-field, .v-select, .v-checkbox, .v-radio, .v-switch, .v-date-picker, .v-time-picker, .v-slider, .v-rating, .v-textarea, .v-autocomplete, .v-combobox, .v-file-input",
  );
  if (vWrapper) {
    const componentMatch = vWrapper.className.match(/v-([a-z-]+)/);
    return {
      framework: "vuetify",
      componentType: componentMatch ? componentMatch[0] : "v-unknown",
    };
  }

  // Material-UI - 使用与getMaterialUIFormInfo相同的查找策略
  const muiWrapper = element.closest(
    ".MuiTextField-root, .MuiSelect-root, .MuiCheckbox-root, .MuiRadio-root, .MuiSwitch-root, .MuiSlider-root, .MuiAutocomplete-root, .MuiRating-root",
  );
  if (muiWrapper) {
    const componentMatch = muiWrapper.className.match(/Mui([A-Z][a-z]+)/);
    return {
      framework: "materialUI",
      componentType: componentMatch ? componentMatch[0] : "Mui-unknown",
    };
  }

  // 如果上述特定组件检测都失败，则使用原来的通用检测逻辑作为后备
  const className = element.className || "";
  const parentClassName = element.parentElement?.className || "";
  const containerClassName =
    element.closest('[class*="el-"], [class*="ant-"], [class*="v-"], [class*="mui-"]')?.className || "";

  // Element UI 通用检测
  if (className.includes("el-") || parentClassName.includes("el-") || containerClassName.includes("el-")) {
    const componentMatch = (className + " " + parentClassName + " " + containerClassName).match(/el-([a-z-]+)/);
    return {
      framework: "elementUI",
      componentType: componentMatch ? componentMatch[0] : "el-unknown",
    };
  }

  // Ant Design 通用检测
  if (className.includes("ant-") || parentClassName.includes("ant-") || containerClassName.includes("ant-")) {
    const componentMatch = (className + " " + parentClassName + " " + containerClassName).match(/ant-([a-z-]+)/);
    return {
      framework: "antDesign",
      componentType: componentMatch ? componentMatch[0] : "ant-unknown",
    };
  }

  // Vuetify 通用检测
  if (className.includes("v-") || parentClassName.includes("v-") || containerClassName.includes("v-")) {
    const componentMatch = (className + " " + parentClassName + " " + containerClassName).match(/v-([a-z-]+)/);
    return {
      framework: "vuetify",
      componentType: componentMatch ? componentMatch[0] : "v-unknown",
    };
  }

  // Material-UI 通用检测
  if (className.includes("Mui") || parentClassName.includes("Mui") || containerClassName.includes("Mui")) {
    const componentMatch = (className + " " + parentClassName + " " + containerClassName).match(/Mui([A-Z][a-z]+)/);
    return {
      framework: "materialUI",
      componentType: componentMatch ? componentMatch[0] : "Mui-unknown",
    };
  }

  return {};
}

/**
 * 获取Element UI组件的表单信息
 */
function getElementUIFormInfo(element: HTMLElement): FrameworkFormElementInfo | null {
  const wrapper = element.closest(
    ".el-input, .el-select, .el-textarea, .el-checkbox, .el-radio, .el-switch, .el-date-picker, .el-time-picker, .el-cascader, .el-slider, .el-rate, .el-upload",
  );
  if (!wrapper) return null;

  const className = wrapper.className;

  // Input 输入框
  if (className.includes("el-input")) {
    const input = wrapper.querySelector("input") as HTMLInputElement;
    if (input) {
      return {
        type: input.type || "text",
        framework: "elementUI",
        componentType: "el-input",
        value: input.value,
        placeholder: input.placeholder,
        isDisabled: input.disabled,
        isReadonly: input.readOnly,
      };
    }
  }

  // Textarea 文本域
  if (className.includes("el-textarea")) {
    const textarea = wrapper.querySelector("textarea") as HTMLTextAreaElement;
    if (textarea) {
      return {
        type: "textarea",
        framework: "elementUI",
        componentType: "el-textarea",
        value: textarea.value,
        placeholder: textarea.placeholder,
        isDisabled: textarea.disabled,
        isReadonly: textarea.readOnly,
      };
    }
  }

  // Select 选择器
  if (className.includes("el-select")) {
    const input = wrapper.querySelector(".el-input__inner") as HTMLInputElement;
    return {
      type: "select",
      framework: "elementUI",
      componentType: "el-select",
      value: input?.value || "",
      placeholder: input?.placeholder,
      isDisabled: wrapper.classList.contains("is-disabled"),
    };
  }

  // Checkbox 复选框
  if (className.includes("el-checkbox")) {
    const checkbox = wrapper.querySelector('input[type="checkbox"]') as HTMLInputElement;
    return {
      type: "checkbox",
      framework: "elementUI",
      componentType: "el-checkbox",
      value: checkbox?.checked ? checkbox.value || "true" : "false",
      isDisabled: checkbox?.disabled,
    };
  }

  // Radio 单选框
  if (className.includes("el-radio")) {
    const radio = wrapper.querySelector('input[type="radio"]') as HTMLInputElement;
    return {
      type: "radio",
      framework: "elementUI",
      componentType: "el-radio",
      value: radio?.checked ? radio.value || "true" : "",
      isDisabled: radio?.disabled,
    };
  }

  // Switch 开关
  if (className.includes("el-switch")) {
    const switchInput = wrapper.querySelector('input[type="checkbox"]') as HTMLInputElement;
    return {
      type: "switch",
      framework: "elementUI",
      componentType: "el-switch",
      value: switchInput?.checked ? "true" : "false",
      isDisabled: switchInput?.disabled,
    };
  }

  // Date Picker 日期选择器
  if (className.includes("el-date-picker")) {
    const input = wrapper.querySelector("input") as HTMLInputElement;
    return {
      type: "date",
      framework: "elementUI",
      componentType: "el-date-picker",
      value: input?.value,
      placeholder: input?.placeholder,
      isDisabled: input?.disabled,
      isReadonly: input?.readOnly,
    };
  }
  if (className.includes("el-date-editor")) {
    const input = wrapper.querySelector("input") as HTMLInputElement;
    return {
      type: "date",
      framework: "elementUI",
      componentType: "el-date-editor",
      value: input?.value,
      placeholder: input?.placeholder,
      isDisabled: input?.disabled,
      isReadonly: input?.readOnly,
    };
  }
  

  // Time Picker 时间选择器
  if (className.includes("el-time-picker")) {
    const input = wrapper.querySelector("input") as HTMLInputElement;
    return {
      type: "time",
      framework: "elementUI",
      componentType: "el-time-picker",
      value: input?.value,
      placeholder: input?.placeholder,
      isDisabled: input?.disabled,
      isReadonly: input?.readOnly,
    };
  }

  return null;
}

/**
 * 获取Ant Design组件的表单信息
 */
function getAntDesignFormInfo(element: HTMLElement): FrameworkFormElementInfo | null {
  const wrapper = element.closest(
    ".ant-input, .ant-select, .ant-checkbox, .ant-radio, .ant-switch, .ant-date-picker, .ant-time-picker, .ant-cascader, .ant-slider, .ant-rate, .ant-upload, .ant-input-number",
  );
  if (!wrapper) return null;

  const className = wrapper.className;

  // Input 输入框
  if (className.includes("ant-input") && !className.includes("ant-input-number")) {
    const input =
      wrapper.tagName === "INPUT"
        ? (wrapper as HTMLInputElement)
        : (wrapper.querySelector("input") as HTMLInputElement);
    if (input) {
      return {
        type: input.type || "text",
        framework: "antDesign",
        componentType: "ant-input",
        value: input.value,
        placeholder: input.placeholder,
        isDisabled: input.disabled,
        isReadonly: input.readOnly,
      };
    }
  }

  // Input Number 数字输入框
  if (className.includes("ant-input-number")) {
    const input = wrapper.querySelector("input") as HTMLInputElement;
    return {
      type: "number",
      framework: "antDesign",
      componentType: "ant-input-number",
      value: input?.value,
      isDisabled: wrapper.classList.contains("ant-input-number-disabled"),
    };
  }

  // Select 选择器
  if (className.includes("ant-select")) {
    const selector = wrapper.querySelector(".ant-select-selector");
    const input = selector?.querySelector("input") as HTMLInputElement;
    const selectedItem = selector?.querySelector(".ant-select-selection-item");
    return {
      type: "select",
      framework: "antDesign",
      componentType: "ant-select",
      value: selectedItem?.textContent || input?.value || "",
      placeholder: selector?.querySelector(".ant-select-selection-placeholder")?.textContent,
      isDisabled: wrapper.classList.contains("ant-select-disabled"),
    };
  }

  // Checkbox 复选框
  if (className.includes("ant-checkbox")) {
    const checkbox = wrapper.querySelector('input[type="checkbox"]') as HTMLInputElement;
    return {
      type: "checkbox",
      framework: "antDesign",
      componentType: "ant-checkbox",
      value: checkbox?.checked ? checkbox.value || "true" : "false",
      isDisabled: checkbox?.disabled,
    };
  }

  // Radio 单选框
  if (className.includes("ant-radio")) {
    const radio = wrapper.querySelector('input[type="radio"]') as HTMLInputElement;
    return {
      type: "radio",
      framework: "antDesign",
      componentType: "ant-radio",
      value: radio?.checked ? radio.value || "true" : "",
      isDisabled: radio?.disabled,
    };
  }

  // Switch 开关
  if (className.includes("ant-switch")) {
    const switchInput = wrapper.querySelector("input") as HTMLInputElement;
    return {
      type: "switch",
      framework: "antDesign",
      componentType: "ant-switch",
      value: wrapper.classList.contains("ant-switch-checked") ? "true" : "false",
      isDisabled: wrapper.classList.contains("ant-switch-disabled"),
    };
  }

  // Date Picker 日期选择器
  if (className.includes("ant-date-picker")) {
    const input = wrapper.querySelector("input") as HTMLInputElement;
    return {
      type: "date",
      framework: "antDesign",
      componentType: "ant-date-picker",
      value: input?.value,
      placeholder: input?.placeholder,
      isDisabled: input?.disabled,
      isReadonly: input?.readOnly,
    };
  }

  // Time Picker 时间选择器
  if (className.includes("ant-time-picker")) {
    const input = wrapper.querySelector("input") as HTMLInputElement;
    return {
      type: "time",
      framework: "antDesign",
      componentType: "ant-time-picker",
      value: input?.value,
      placeholder: input?.placeholder,
      isDisabled: input?.disabled,
      isReadonly: input?.readOnly,
    };
  }

  return null;
}

/**
 * 获取Vuetify组件的表单信息
 */
function getVuetifyFormInfo(element: HTMLElement): FrameworkFormElementInfo | null {
  const wrapper = element.closest(
    ".v-text-field, .v-select, .v-checkbox, .v-radio, .v-switch, .v-date-picker, .v-time-picker, .v-slider, .v-rating",
  );
  if (!wrapper) return null;

  const className = wrapper.className;

  // Text Field 文本输入框
  if (className.includes("v-text-field")) {
    const input = wrapper.querySelector("input, textarea") as HTMLInputElement | HTMLTextAreaElement;
    if (input) {
      return {
        type: input.tagName.toLowerCase() === "textarea" ? "textarea" : (input as HTMLInputElement).type || "text",
        framework: "vuetify",
        componentType: "v-text-field",
        value: input.value,
        placeholder: input.placeholder,
        isDisabled: input.disabled,
        isReadonly: input.readOnly,
      };
    }
  }

  // Select 选择器
  if (className.includes("v-select")) {
    const input = wrapper.querySelector("input") as HTMLInputElement;
    return {
      type: "select",
      framework: "vuetify",
      componentType: "v-select",
      value: input?.value || "",
      placeholder: input?.placeholder,
      isDisabled: wrapper.classList.contains("v-input--is-disabled"),
    };
  }

  // Checkbox 复选框
  if (className.includes("v-checkbox")) {
    const checkbox = wrapper.querySelector('input[type="checkbox"]') as HTMLInputElement;
    return {
      type: "checkbox",
      framework: "vuetify",
      componentType: "v-checkbox",
      value: checkbox?.checked ? checkbox.value || "true" : "false",
      isDisabled: checkbox?.disabled,
    };
  }

  // Date Picker 日期选择器
  if (className.includes("v-date-picker")) {
    const input = wrapper.querySelector("input") as HTMLInputElement;
    return {
      type: "date",
      framework: "vuetify",
      componentType: "v-date-picker",
      value: input?.value,
      placeholder: input?.placeholder,
      isDisabled: input?.disabled,
      isReadonly: input?.readOnly,
    };
  }

  // Time Picker 时间选择器
  if (className.includes("v-time-picker")) {
    const input = wrapper.querySelector("input") as HTMLInputElement;
    return {
      type: "time",
      framework: "vuetify",
      componentType: "v-time-picker",
      value: input?.value,
      placeholder: input?.placeholder,
      isDisabled: input?.disabled,
      isReadonly: input?.readOnly,
    };
  }

  return null;
}

/**
 * 获取Material-UI组件的表单信息
 */
function getMaterialUIFormInfo(element: HTMLElement): FrameworkFormElementInfo | null {
  const wrapper = element.closest(
    ".MuiTextField-root, .MuiSelect-root, .MuiCheckbox-root, .MuiRadio-root, .MuiSwitch-root, .MuiSlider-root, .MuiDatePicker, .MuiTimePicker, .MuiDateTimePicker",
  );
  if (!wrapper) return null;

  const className = wrapper.className;

  // TextField 文本输入框
  if (className.includes("MuiTextField")) {
    const input = wrapper.querySelector("input, textarea") as HTMLInputElement | HTMLTextAreaElement;
    if (input) {
      return {
        type: input.tagName.toLowerCase() === "textarea" ? "textarea" : (input as HTMLInputElement).type || "text",
        framework: "materialUI",
        componentType: "MuiTextField",
        value: input.value,
        placeholder: input.placeholder,
        isDisabled: input.disabled,
        isReadonly: input.readOnly,
      };
    }
  }

  // Select 选择器
  if (className.includes("MuiSelect")) {
    const select = wrapper.querySelector('select, [role="button"]') as HTMLSelectElement | HTMLElement;
    return {
      type: "select",
      framework: "materialUI",
      componentType: "MuiSelect",
      value: select?.textContent || (select as HTMLSelectElement)?.value || "",
      isDisabled: wrapper.classList.contains("Mui-disabled"),
    };
  }

  // Date Picker 日期选择器
  if (className.includes("MuiDatePicker")) {
    const input = wrapper.querySelector("input") as HTMLInputElement;
    return {
      type: "date",
      framework: "materialUI",
      componentType: "MuiDatePicker",
      value: input?.value,
      placeholder: input?.placeholder,
      isDisabled: input?.disabled,
      isReadonly: input?.readOnly,
    };
  }

  // Time Picker 时间选择器
  if (className.includes("MuiTimePicker")) {
    const input = wrapper.querySelector("input") as HTMLInputElement;
    return {
      type: "time",
      framework: "materialUI",
      componentType: "MuiTimePicker",
      value: input?.value,
      placeholder: input?.placeholder,
      isDisabled: input?.disabled,
      isReadonly: input?.readOnly,
    };
  }

  // DateTime Picker 日期时间选择器
  if (className.includes("MuiDateTimePicker")) {
    const input = wrapper.querySelector("input") as HTMLInputElement;
    return {
      type: "datetime-local",
      framework: "materialUI",
      componentType: "MuiDateTimePicker",
      value: input?.value,
      placeholder: input?.placeholder,
      isDisabled: input?.disabled,
      isReadonly: input?.readOnly,
    };
  }

  return null;
}

/**
 * 获取原生HTML表单元素信息
 */
function getNativeFormInfo(element: HTMLElement): FrameworkFormElementInfo | null {
  const tagName = element.tagName.toLowerCase();

  // 直接检查当前元素是否为表单元素
  if (tagName === "input") {
    const input = element as HTMLInputElement;
    return {
      type: input.type || "text",
      framework: "native",
      componentType: "input",
      value: input.value,
      placeholder: input.placeholder,
      isDisabled: input.disabled,
      isReadonly: input.readOnly,
      isRequired: input.required,
    };
  }

  if (tagName === "textarea") {
    const textarea = element as HTMLTextAreaElement;
    return {
      type: "textarea",
      framework: "native",
      componentType: "textarea",
      value: textarea.value,
      placeholder: textarea.placeholder,
      isDisabled: textarea.disabled,
      isReadonly: textarea.readOnly,
      isRequired: textarea.required,
    };
  }

  if (tagName === "select") {
    const select = element as HTMLSelectElement;
    return {
      type: "select",
      framework: "native",
      componentType: "select",
      value: select.value,
      isDisabled: select.disabled,
      isRequired: select.required,
    };
  }

  // 如果当前元素不是表单元素，尝试查找其内部的表单元素
  // 这对于处理嵌套结构很有用
  const nestedInput = element.querySelector("input") as HTMLInputElement;
  if (nestedInput) {
    return {
      type: nestedInput.type || "text",
      framework: "native",
      componentType: "input",
      value: nestedInput.value,
      placeholder: nestedInput.placeholder,
      isDisabled: nestedInput.disabled,
      isReadonly: nestedInput.readOnly,
      isRequired: nestedInput.required,
    };
  }

  const nestedTextarea = element.querySelector("textarea") as HTMLTextAreaElement;
  if (nestedTextarea) {
    return {
      type: "textarea",
      framework: "native",
      componentType: "textarea",
      value: nestedTextarea.value,
      placeholder: nestedTextarea.placeholder,
      isDisabled: nestedTextarea.disabled,
      isReadonly: nestedTextarea.readOnly,
      isRequired: nestedTextarea.required,
    };
  }

  const nestedSelect = element.querySelector("select") as HTMLSelectElement;
  if (nestedSelect) {
    return {
      type: "select",
      framework: "native",
      componentType: "select",
      value: nestedSelect.value,
      isDisabled: nestedSelect.disabled,
      isRequired: nestedSelect.required,
    };
  }

  return null;
}

/**
 * 主要的表单元素类型检测函数
 * 根据不同UI框架的DOM结构特征来准确识别表单元素类型
 */
export function detectFormElementType(element: HTMLElement): FrameworkFormElementInfo {
  // 首先检测UI框架
  const frameworkInfo = detectUIFramework(element);

  // 根据检测到的框架使用对应的检测函数
  let formInfo: FrameworkFormElementInfo | null = null;

  switch (frameworkInfo.framework) {
    case "elementUI":
      formInfo = getElementUIFormInfo(element);
      break;
    case "antDesign":
      formInfo = getAntDesignFormInfo(element);
      break;
    case "vuetify":
      formInfo = getVuetifyFormInfo(element);
      break;
    case "materialUI":
      formInfo = getMaterialUIFormInfo(element);
      break;
    default:
      // 尝试原生HTML表单元素检测
      formInfo = getNativeFormInfo(element);
      break;
  }

  // 如果没有检测到特定框架信息，使用原生检测作为后备
  if (!formInfo) {
    formInfo = getNativeFormInfo(element);
  }

  // 如果仍然没有检测到，返回默认信息
  if (!formInfo) {
    return {
      type: "unknown",
      framework: "unknown",
      componentType: element.tagName.toLowerCase(),
      value: (element as any).value || element.textContent?.trim() || "",
    };
  }

  return formInfo;
}

/**
 * 检测元素是否为表单元素
 */
export function isFormElement(element: HTMLElement): boolean {
  const tagName = element.tagName.toLowerCase();

  // 原生表单元素
  if (["input", "textarea", "select"].includes(tagName)) {
    return true;
  }

  // UI框架表单组件
  const className = element.className || "";
  const parentClassName = element.parentElement?.className || "";
  const containerClassName =
    element.closest('[class*="el-"], [class*="ant-"], [class*="v-"], [class*="mui-"]')?.className || "";

  const allClassNames = className + " " + parentClassName + " " + containerClassName;

  // Element UI 表单组件
  if (
    /el-(input|select|textarea|checkbox|radio|switch|date-picker|time-picker|cascader|slider|rate|upload|input-number|autocomplete|color-picker)/.test(
      allClassNames,
    )
  ) {
    return true;
  }

  // Ant Design 表单组件
  if (
    /ant-(input|select|checkbox|radio|switch|date-picker|time-picker|cascader|slider|rate|upload|input-number|auto-complete|mentions|tree-select)/.test(
      allClassNames,
    )
  ) {
    return true;
  }

  // Vuetify 表单组件
  if (
    /v-(text-field|select|checkbox|radio|switch|date-picker|time-picker|slider|rating|textarea|autocomplete|combobox|file-input)/.test(
      allClassNames,
    )
  ) {
    return true;
  }

  // Material-UI 表单组件
  if (/Mui(TextField|Select|Checkbox|Radio|Switch|Slider|Autocomplete|Rating)/.test(allClassNames)) {
    return true;
  }

  // 通用框架组件检测（基于类名前缀）
  if (/\b(el-|ant-|v-|Mui)[a-zA-Z-]+\b/.test(allClassNames)) {
    // 进一步检查是否包含表单相关的内部元素
    const hasFormInput = element.querySelector("input, textarea, select") !== null;
    if (hasFormInput) {
      return true;
    }
  }

  return false;
}

/**
 * 获取表单元素的标签文本
 */
export function getFormElementLabel(element: HTMLElement): string | null {
  // 1. 查找关联的label元素
  const elementId = element.id;
  if (elementId) {
    const label = document.querySelector(`label[for="${elementId}"]`);
    if (label) {
      return label.textContent?.trim() || null;
    }
  }

  // 2. 查找父级label元素
  const parentLabel = element.closest("label");
  if (parentLabel) {
    return parentLabel.textContent?.trim() || null;
  }

  // 3. 查找前一个兄弟元素中的label
  let sibling = element.previousElementSibling;
  while (sibling) {
    if (sibling.tagName.toLowerCase() === "label") {
      return sibling.textContent?.trim() || null;
    }
    sibling = sibling.previousElementSibling;
  }

  // 4. 查找UI框架特定的标签
  const formItem = element.closest(".el-form-item, .ant-form-item, .v-input, .MuiFormControl-root");
  if (formItem) {
    const label = formItem.querySelector(".el-form-item__label, .ant-form-item-label, .v-label, .MuiFormLabel-root");
    if (label) {
      return label.textContent?.trim() || null;
    }
  }

  // 5. 查找placeholder作为后备
  const placeholder = (element as HTMLInputElement).placeholder;
  if (placeholder) {
    return placeholder;
  }

  return null;
}
