// 遍历树，取cmtPersonPhoto和cmtPersonName，放到一个数组中并去重，如果得到limit个值则退出遍历
export const traverseAndCollect = (data, limit = 4, collected = []) => {
  // 遍历当前节点
  for (let item of data) {
    // 获取 cmtPersonPhoto 和 cmtPersonName
    const personInfo = {
      id: item.id,
      cmtPersonPhoto: item.cmtPersonPhoto,
      cmtPersonName: item.cmtPersonName,
    };

    // 检查该组合是否已存在于 collected 中
    if (
      !collected.some(
        (existing) =>
          existing.cmtPersonPhoto === personInfo.cmtPersonPhoto && existing.cmtPersonName === personInfo.cmtPersonName,
      )
    ) {
      collected.push(personInfo);
    }

    // 如果已收集到指定数量的组合，则返回
    if (collected.length >= limit) {
      return collected;
    }

    // 递归遍历子节点
    if (item.child && item.child.length > 0) {
      const result = traverseAndCollect(item.child, limit, collected);
      if (result.length >= limit) {
        return result;
      }
    }
  }
  return collected;
};
