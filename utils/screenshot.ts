//截图事件
export function base64ToBlob(base64Data, mimeType = "") {
  // 分割Base64字符串的Data URL部分和实际数据
  const parts = base64Data.split(";base64,");

  // 如果传入的base64Data包含Data URL前缀（如 "data:image/png;base64,ABC123..."）
  if (parts.length > 1) {
    mimeType = parts[0].split(":")[1]; // 提取MIME类型（如 "image/png"）
    base64Data = parts[1]; // 提取纯Base64数据部分
  }

  // 解码Base64字符串为二进制
  const byteCharacters = atob(base64Data);
  const byteArrays = [];

  // 将字符逐个转换为Uint8Array
  for (let offset = 0; offset < byteCharacters.length; offset += 512) {
    const slice = byteCharacters.slice(offset, offset + 512);
    const byteNumbers = new Array(slice.length);
    for (let i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.charCodeAt(i);
    }
    byteArrays.push(new Uint8Array(byteNumbers));
  }

  // 生成Blob对象
  return new Blob(byteArrays, { type: mimeType || "application/octet-stream" });
}

export function blobToBase64(blob) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      // 从结果中提取Base64部分（去掉Data URL前缀）
      const base64Data = reader.result?.toString().split(",")[1];
      console.log(base64Data);
      resolve(base64Data);
    };
    reader.onerror = (error) => {
      reject(error);
    };
    reader.readAsDataURL(blob);
  });
}
