// 文件大小显示 保留一位小数，四舍五入
// 显示规则不满512B 单位显示为B
// 大于等于512B, 不满512KB 单位显示为KB
// 大于等于512KB, 不满512MB 单位显示为MB
// 大于等于512MB 单位显示为GB
export const formatFileSize = (sizeInBytes: number | string) => {
  let sizeNum = Number(sizeInBytes);
  if (sizeNum < 512) {
    return `${sizeNum}B`;
  } else if (sizeNum < 512 * 1024) {
    const sizeInKB = (sizeNum / 1024).toFixed(1);
    return `${sizeInKB}KB`;
  } else if (sizeNum < 512 * 1024 * 1024) {
    const sizeInMB = (sizeNum / (1024 * 1024)).toFixed(1);
    return `${sizeInMB}MB`;
  } else {
    const sizeInGB = (sizeNum / (1024 * 1024 * 1024)).toFixed(1);
    return `${sizeInGB}GB`;
  }
};
