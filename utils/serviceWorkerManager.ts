/**
 * Service Worker 状态管理器
 * 用于解决 Manifest V3 中 Service Worker 重启导致的重复初始化问题
 */

// 全局状态标志
declare global {
  var __SW_INIT_STATE__:
    | {
        backgroundInitialized: boolean;
        fetchHandlerRegistered: boolean;
        sseHandlerRegistered: boolean;
        messageListenerRegistered: boolean;
        tabsListenerRegistered: boolean;
        lastInitTime: number;
      }
    | undefined;
}

export class ServiceWorkerManager {
  private static instance: ServiceWorkerManager;

  private constructor() {
    this.initializeState();
  }

  public static getInstance(): ServiceWorkerManager {
    if (!ServiceWorkerManager.instance) {
      ServiceWorkerManager.instance = new ServiceWorkerManager();
    }
    return ServiceWorkerManager.instance;
  }

  private initializeState() {
    if (!globalThis.__SW_INIT_STATE__) {
      globalThis.__SW_INIT_STATE__ = {
        backgroundInitialized: false,
        fetchHandlerRegistered: false,
        sseHandlerRegistered: false,
        messageListenerRegistered: false,
        tabsListenerRegistered: false,
        lastInitTime: 0,
      };
    }
  }

  /**
   * 检查是否已经初始化
   */
  public isInitialized(component: keyof typeof globalThis.__SW_INIT_STATE__): boolean {
    if (component === "lastInitTime") return false;
    return globalThis.__SW_INIT_STATE__?.[component] || false;
  }

  /**
   * 标记组件已初始化
   */
  public markInitialized(component: keyof typeof globalThis.__SW_INIT_STATE__, value: boolean = true) {
    if (!globalThis.__SW_INIT_STATE__) {
      this.initializeState();
    }

    if (component === "lastInitTime") {
      globalThis.__SW_INIT_STATE__!.lastInitTime = Date.now();
    } else {
      (globalThis.__SW_INIT_STATE__ as any)[component] = value;
    }

    console.debug(`ServiceWorker组件 ${component} 状态更新为: ${value}`);
  }

  /**
   * 重置所有状态
   */
  public resetAll() {
    globalThis.__SW_INIT_STATE__ = {
      backgroundInitialized: false,
      fetchHandlerRegistered: false,
      sseHandlerRegistered: false,
      messageListenerRegistered: false,
      tabsListenerRegistered: false,
      lastInitTime: 0,
    };
    console.debug("ServiceWorker状态已重置");
  }

  /**
   * 检查是否需要重新初始化（基于时间）
   */
  public shouldReinitialize(maxAge: number = 30000): boolean {
    const lastInit = globalThis.__SW_INIT_STATE__?.lastInitTime || 0;
    const now = Date.now();
    return now - lastInit > maxAge;
  }

  /**
   * 获取当前状态
   */
  public getState() {
    return { ...globalThis.__SW_INIT_STATE__ };
  }

  /**
   * 安全执行初始化函数，防止重复执行
   */
  public safeInit(
    component: keyof typeof globalThis.__SW_INIT_STATE__,
    initFunction: () => void,
    forceReinit: boolean = false,
  ): boolean {
    if (component === "lastInitTime") return false;

    if (!forceReinit && this.isInitialized(component)) {
      console.debug(`组件 ${component} 已经初始化，跳过重复初始化`);
      return false;
    }

    try {
      console.debug(`开始初始化组件: ${component}`);
      initFunction();
      this.markInitialized(component, true);
      this.markInitialized("lastInitTime");
      console.debug(`组件 ${component} 初始化完成`);
      return true;
    } catch (error) {
      console.error(`组件 ${component} 初始化失败:`, error);
      this.markInitialized(component, false);
      return false;
    }
  }
}

// 导出单例实例
export const swManager = ServiceWorkerManager.getInstance();

/**
 * 端口连接状态管理器
 * 用于管理和检测端口连接状态
 */
export class PortConnectionManager {
  private static connections = new Map<
    string,
    {
      port: chrome.runtime.Port | null;
      lastActivity: number;
      isActive: boolean;
    }
  >();

  /**
   * 注册端口连接
   */
  public static registerPort(name: string, port: chrome.runtime.Port) {
    this.connections.set(name, {
      port,
      lastActivity: Date.now(),
      isActive: true,
    });

    // 监听端口断开
    port.onDisconnect.addListener(() => {
      this.markInactive(name);
    });

    console.debug(`端口 ${name} 已注册`);
  }

  /**
   * 检查端口是否活跃
   */
  public static isPortActive(name: string): boolean {
    const connection = this.connections.get(name);
    if (!connection) return false;

    try {
      // 尝试检查端口状态
      if (!connection.port || !connection.port.name) {
        this.markInactive(name);
        return false;
      }
      return connection.isActive;
    } catch (error) {
      this.markInactive(name);
      return false;
    }
  }

  /**
   * 标记端口为非活跃状态
   */
  public static markInactive(name: string) {
    const connection = this.connections.get(name);
    if (connection) {
      connection.isActive = false;
      connection.port = null;
      console.debug(`端口 ${name} 已标记为非活跃`);
    }
  }

  /**
   * 更新端口活动时间
   */
  public static updateActivity(name: string) {
    const connection = this.connections.get(name);
    if (connection) {
      connection.lastActivity = Date.now();
    }
  }

  /**
   * 清理非活跃连接
   */
  public static cleanup(maxAge: number = 300000) {
    // 5分钟
    const now = Date.now();
    for (const [name, connection] of this.connections.entries()) {
      if (now - connection.lastActivity > maxAge) {
        this.connections.delete(name);
        console.debug(`清理过期端口连接: ${name}`);
      }
    }
  }

  /**
   * 获取所有连接状态
   */
  public static getConnectionStates() {
    const states: Record<string, any> = {};
    for (const [name, connection] of this.connections.entries()) {
      states[name] = {
        isActive: connection.isActive,
        lastActivity: connection.lastActivity,
        hasPort: !!connection.port,
      };
    }
    return states;
  }
}

// 定期清理端口连接
setInterval(() => {
  PortConnectionManager.cleanup();
}, 60000); // 每分钟清理一次
